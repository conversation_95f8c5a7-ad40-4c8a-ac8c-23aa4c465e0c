
# INSTALL

## 1. Checkout:
    ```shell
    <NAME_EMAIL>:nZoomStack/alvis.git
    ```
## 2. Setup access to BGService GitLab.
Setup access to BGService GitLab so the composer to be able to download NzoomClient and others.

1. Generate ssh key.
    ```
    ssh-keygen -t ed25519 -C "<EMAIL>"
    ```
    For more information check this [page](https://gitlab.bgservice.net/help/ssh/README#generating-a-new-ssh-key-pair).

2. Copy the public key from: `~/.ssh/id_ed25519.pub`

3. Go to gitlab.bgservice.net and login (you should have access).

4. Go to the user menu (top right), then: `Settings` > `SSH Keys`.

5. Paste your public key and press the `Add key` button.

6. Then into the terminal check if it's all set using this command:
    ```
    ssh -T ************************
    ```
## 3. Setup composer
1. Download composer.phar or install global composer command

2. To download composer.phar execute the following command into the terminal:
    ```
    bin/get-composer
    ```
    For more information check this [page](https://getcomposer.org/download/).

3. Install dependencies:
    ```
    ./composer.phar install
    ```
## 4. Setup Apache
  For Apache set:
> mod_rewrite enabled
> 
> AllowOverride All

## 5. Setup directory permissions
 Give write permissions to directory `data/cache/` using this command:
 ```
 sudo chmod -R 0775 data/cache/
 ```

 Give write permissions to directory `data/cache/cookiejars/` using this command:
 ```
 sudo chown www-data:www-data -R data/cache/cookiejars/
 ```

## 6. Setup ALVIS
1. Copy and rename config/config.php.dist to config/config.php
  ```shell
  cp config/config.php.dist config/config.php
  ```
Note: There is a local.real.php.dist to use with the advanceaddres installation 
(the config.php.dist is for test.advanceaddress)

2. Then edit the file config/config.php with the correct configuration.

## 7. coockiejar / sessions / Crontab
Over time Alvis will accumulate session files that are used to bridge user sessions with nzoom. 
It's important to clean them up from time to time!

* **a)** To do this you can execute the included script from time to time manually
  ```shell
  bin/cleanup-coockiejar.sh
  ```
* **b)** Setup the script to run regurally via crontab
  Use a user that will have permision to delete the files created by the php
  
  ```shell
   crontab -e
  ```
  
  Add this line at the end and put the proper path to the script
  
  ```shell
  2 3 * * * /var/www/alvis/bin/cleanup-coockiejar.sh >/dev/null 2>&1
  ```
  
  Save and close the file
    
  This will run the script every day at 04:02 AM. 

## 8. DONE!
