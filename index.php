<?php
include 'vendor/autoload.php';

$config =\Bgservice\Dbimport\Config::all();
$sourceFactory = new \Bgservice\Dbimport\SourceFactory();
$targetFactory = new \Bgservice\Dbimport\TargetFactory();

set_time_limit(0);
ini_set("memory_limit", '3G');

$target = $targetFactory($config['target']);
$localList = $target->getDbList();

$server_key = $_GET['server']??null;
$delete_downloaded_dump_file = null;
$delete_sql_routines_files = null;

if ($server_key) {
    $dbSource = $sourceFactory($config['sources'][$server_key]);
    $remoteList = $dbSource->fetchDbList();
}

if ($_GET['source']??false) {
    $dbCode = $_GET['source'];
    $dumpFile = $dbSource->fetchDbDump($dbCode);

    if ($_GET['download']??false) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header("Cache-Control: no-cache, must-revalidate");
        header("Expires: 0");
        header('Content-Disposition: attachment; filename="'.basename($dumpFile).'"');
        header('Content-Length: ' . filesize($dumpFile));
        header('Pragma: public');
        flush();
        readfile($dumpFile);
        if ($dumpFile) unlink($dumpFile);
        exit();
    }

    if ($_GET['target_db_type'] ?? false) {
        if($_GET['target_db_type'] === 'new') {
            $targetDbType = 'new';
            $targetDbName = $_GET['target_new']??false;
            if ($targetDbName) {
                $target->createDb($targetDbName);
            } else {
                echo 'Please, enter new DB name';
            }
        } else {
            $targetDbType = 'existing';
            $targetDbName = $_GET['target']??false;
        }
    }


    if ($targetDbName ?? false) {
        $deleteTmpFiles = $_GET['delete_tmp_files']??false;

        $target->importSqlFile($targetDbName, $dumpFile);
        if ($deleteTmpFiles) unlink($dumpFile);

        foreach ($config['afterImport']??[] as $file) {
            $target->importSqlFile($targetDbName, $file);
        }

        $routinesPath = $dbSource->fetchRoutines($dbCode);
        if ($routinesPath) {
            $target->importSqlFile($targetDbName, $routinesPath);
            if ($deleteTmpFiles) unlink($routinesPath);
        }

        $remoteDbData = $remoteList->getRecordByCode($dbCode);
        $changesPath = $dbSource->fetchChanges($dbCode, $remoteDbData->getBuild(), $remoteDbData->getSqlFile());
        if ($changesPath) {
            $target->importSqlFile($targetDbName, $changesPath);
            if ($deleteTmpFiles) unlink($changesPath);
        }

        header('Location: ' . $_SERVER['SCRIPT_NAME']);
        exit();
    }
}

include 'form.phtml';
