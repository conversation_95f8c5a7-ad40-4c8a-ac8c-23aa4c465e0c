<?php

/**
 * Global Configuration Override
 *
 * You can use this file for overriding configuration values from modules, etc.
 * You would place values in here that are agnostic to the environment and not
 * sensitive to security.
 *
 * NOTE: In practice, this file will typically be INCLUDED in your source
 * control, so do not include passwords or other sensitive information in this
 * file.
 */

use Wizatour\Command\CacheCommand;

return [
    'laminas-cli' => [
        'commands' => [
            'wizatour:cache' => CacheCommand::class,
        ],
    ],
    'session_storage' => [
        'type' => 'SessionArrayStorage',
        'options' => [
            'name' => 'wizatour2',
        ],
    ],

    'session_config' => [
        'remember_me_seconds' => 90000,
        'cache_expire' => 90000,
        'cookie_lifetime' => 0,
    ],

    'navigation' => [
        'default' => [
            [
                'label' => 'Home',
                'route' => 'control-panel/main',
                'controller' => 'index',
                'pages' => [
                    [
                        'label' => 'Потребители',
                        'route' => 'control-panel/main',
                        'controller' => 'user',
                        'action' => 'list',
                        'pages' => [
                            [
                                'label' => 'Добавяне на потребител',
                                'route' => 'control-panel/main',
                                'controller' => 'user',
                                'action' => 'add',
                            ]
                        ]
                    ]
                ]
            ],
        ],
    ],
];
