<?php
return [
    'nzoom_endpoint' => '<host_uri>',
    'nzoom_request_timeout' => '60',
    'session_time' => '30',   // the length of the session as set in nZoom (minutes)
    'session_expiration_notification' => '2', // how much time before expiring to show the notification (minutes)
    'error_showTechnicalInfo' => false, // true in Dev, Test and Review environments. false in prod.
    'error_logPath' => './data/logs', // false for disabling logs
    'dev-mode' => 'cookie', // false for disabling dev mode | 'cookie' for cookie based activation.
    'dev-cookie-name' => 'alvis_dev', // cookie name for enabling dev mode (only if 'dev-mode' = 'cookie')
    'dev-cookie-value' => 'dzpF3AQc9nwGegGn', // cookie value for enabling dev mode (only if 'dev-mode' = 'cookie')
    'log_nzoom_requests' => false, //save requests to nZoom in log_path
    'log_path' => './data/logs/', //directory of log
];
