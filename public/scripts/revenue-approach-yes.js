$(() => {
    let submitButton = $('.button-submit');
    submitButton.off('click submit');

    submitButton.click(function (e) {
        let button = $(this);
        let metaData = {
            event : e,
            target: button
        };

        initiateSubmit(metaData, undefined, {
            'serial_data': prepSerialData,
        });
    });

    function prepSerialData(formData){
        const form = $('form');
        let serialData = [];
    
        let table = {
            type: "table",
            name: "income_approach_calc_group_table",
            code: '',
            label: '',
            rows: [],
        };
    
        $('table[name="income_approach_calc_group_table"]  tr:visible').each(function(indexTr, tr) {
            rowBody = {
                    type: "body",
                    cells: [],
            }
            table.rows.push(rowBody);
            // first coll
            $(tr).find('th').each(function(indexTd, th) {
                $node = $(th);
                let cell = serial_getCellFromTd($node);
                cell.type = "text",
                cell.options = ['highlight'];
    
                rowBody.cells.push(cell);
            });
            // data colls
            $(tr).find('td').each(function(indexTd, td) {
                let cell = {
                    options: [],
                };
                $node = $(td);
                cell = serial_getCellFromTd($node);
    
                rowBody.cells.push(cell);
            });
        });
    
        serialData.push(table);
        formData.append('serial_data', JSON.stringify(serialData));
    }

    let sideTable = $("table[name='income_approach_calc_group_table']");
    let objects = sideTable.find('tbody tr:first-child td');
    let columns = Math.max(1, objects.length);
    sideTable.css('width', (columns * 250 + 300).toString() + 'px');

    let secondToLastColumn = $("table[name='income_approach_calc_group_table'] tbody tr td:last-child");
    let removeAdditionalColumn = $('.fa.fa-window-close');
    let addAdditionalColumn = $('.fa.fa-plus-square');


    removeAdditionalColumn.click(function () {
        secondToLastColumn.find('input').prop('disabled', true);
        secondToLastColumn.hide();
        addAdditionalColumn.show();
        removeAdditionalColumn.hide();
    });

    addAdditionalColumn.click(function () {
        secondToLastColumn.find('input').prop('disabled', false);
        secondToLastColumn.show();
        addAdditionalColumn.hide();
        removeAdditionalColumn.show();
    });

    let incomeContainer = $('#incomeApproachCalcGroupContainer');

    if(typeof incomeContainer.data('hideextracolumn') !== 'undefined') {
        if(incomeContainer.data('hideextracolumn')) {
            removeAdditionalColumn.trigger('click');
        } else {
            addAdditionalColumn.trigger('click');
        }
    }

    formulas();
});


function formulas() {
    if ($('#fourth_setting').val() == '2') {
        createFormula({
            resultFieldName: 'rent_EUR_month',
            sourceFieldsNames: [
                'leasing_area',
                'rent_EURsqm',
                'rental_correct_coef',
                'expenses_exploitation'
            ],
            tooltip: '[rent_EUR_month] = [leasing_area] * [rent_EURsqm] * [rental_correct_coef] + (([leasing_area] * [rent_EURsqm] * [rental_correct_coef]) * [expenses_exploitation] / 100)',
            resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
                let leasingArea          = sourceFieldsValues['leasing_area'][resultFieldIndex];
                let rentEurSqm           = sourceFieldsValues['rent_EURsqm'][resultFieldIndex];
                let rentalCorrectCoef    = sourceFieldsValues['rental_correct_coef'][resultFieldIndex];
                let expensesExploitation = sourceFieldsValues['expenses_exploitation'][resultFieldIndex];

                let result = leasingArea * rentEurSqm * rentalCorrectCoef;
                result += result * (expensesExploitation / 100);

                return result;
            }
        });
    } else {
        createFormula({
            resultFieldName: 'rent_EUR_month',
            sourceFieldsNames: [
                'leasing_area',
                'rent_EURsqm',
                'rental_correct_coef'
            ],
            tooltip: '[rent_EUR_month] = [leasing_area] * [rent_EURsqm] * [rental_correct_coef]',
            resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
                let leasingArea          = sourceFieldsValues['leasing_area'][resultFieldIndex];
                let rentEurSqm           = sourceFieldsValues['rent_EURsqm'][resultFieldIndex];
                let rentalCorrectCoef    = sourceFieldsValues['rental_correct_coef'][resultFieldIndex];

                let result = leasingArea * rentEurSqm * rentalCorrectCoef;

                return result;
            }
        });
    }

    createFormula({
        resultFieldName: 'rent_EUR_year',
        sourceFieldsNames: ['rent_EUR_month', 'other_earnings'],
        tooltip: '[rent_EUR_year] = ([rent_EUR_month] + [other_earnings]) * 12',
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            let rent_EUR_month = sourceFieldsValues['rent_EUR_month'][resultFieldIndex];
            let other_earnings = sourceFieldsValues['other_earnings'][resultFieldIndex]

            return (rent_EUR_month + other_earnings) * 12;
        }
    });

    createFormula({
        resultFieldName: 'object_multiplier',
        sourceFieldsNames: ['capital_rate', 'residual_economic_life'],
        tooltip: '[object_multiplier] = (1 - (1 / (1 + ([capital_rate] / 100)) ^ [residual_economic_life])) / ([capital_rate] / 100)',
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            let capital_rate = sourceFieldsValues['capital_rate'][resultFieldIndex];
            let residual_economic_life = sourceFieldsValues['residual_economic_life'][resultFieldIndex];

            return (1 - (1 / Math.pow(1 + (capital_rate / 100), residual_economic_life))) / (capital_rate / 100);
        }
    });

    createFormula({
        resultFieldName: 'net_annual_rev_eur',
        sourceFieldsNames: [
            'rent_EUR_year',
            'risk_out_of_rent',
            'expenses_exploitation',
            'expenses_to_owner'
        ],
        tooltip: '[net_annual_rev_eur] = [rent_EUR_year] * (1 - [risk_out_of_rent] / 100) * (1 - [expenses_exploitation] / 100 - [expenses_to_owner] / 100)',
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            let rent_EUR_year         = sourceFieldsValues['rent_EUR_year'][resultFieldIndex];
            let risk_out_of_rent      = sourceFieldsValues['risk_out_of_rent'][resultFieldIndex];
            let expenses_exploitation = sourceFieldsValues['expenses_exploitation'][resultFieldIndex];
            let expenses_to_owner     = sourceFieldsValues['expenses_to_owner'][resultFieldIndex];

            return rent_EUR_year * (1 - risk_out_of_rent / 100) * (1 - expenses_exploitation / 100 - expenses_to_owner / 100);
        }
    });

    createFormula({
        resultFieldName: 'value_eur',
        sourceFieldsNames: [
            'net_annual_rev_eur',
            'land_value_eur',
            'land_interest_rate',
            'object_multiplier',
            'repairs_need',
            'market_ratio',
        ],
        getSourceFields: function (currentSourceField) {
            let sfNames = [
                'net_annual_rev_eur',
                'object_multiplier',
                'repairs_need',
                'market_ratio',
            ];
            if ($.inArray($('#second_setting').val(), [
                NOMENCLATURES_COEFFICIENTS.direct_capitalization_without_land.toString(), 
                NOMENCLATURES_COEFFICIENTS.capitalization_with_economic_life_without_land.toString()
                ]) === -1) {
                sfNames.push('land_value_eur');
                sfNames.push('land_interest_rate');
            }
            let sfNamesSelector = '';
            const sourceFieldIndex = extractFieldIndex(currentSourceField);
            if (sourceFieldIndex) {
                sfNamesSelector = sfNames.map(
                    sfName => `[name="${sfName}"],
                    [name="${sfName}[${sourceFieldIndex}]"]`
                ).join(', ');
            } else {
                sfNamesSelector = sfNames.map(
                    sfName => `[name="${sfName}"],
                    [name^="${sfName}["]`
                ).join(', ');
            }

            return $(sfNamesSelector);
        },
        tooltip: function () {
            if ($.inArray($('#second_setting').val(), [
                NOMENCLATURES_COEFFICIENTS.direct_capitalization_without_land.toString(), 
                NOMENCLATURES_COEFFICIENTS.capitalization_with_economic_life_without_land.toString()
                ]) !== -1) {
                return '[value_eur] = ([net_annual_rev_eur] * [object_multiplier] - [repairs_need]) * [market_ratio]';
            } else {
                return '[value_eur] = (([net_annual_rev_eur] - [land_value_eur] * [land_interest_rate] / 100) * [object_multiplier] - [repairs_need]) * [market_ratio] + [land_value_eur]';
            }
        },
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            const net_annual_rev_eur = sourceFieldsValues['net_annual_rev_eur'][resultFieldIndex];
            const object_multiplier  = sourceFieldsValues['object_multiplier'][resultFieldIndex];
            const repairs_need       = sourceFieldsValues['repairs_need'][resultFieldIndex];
            const market_ratio       = sourceFieldsValues['market_ratio'][resultFieldIndex];

            if ($.inArray($('#second_setting').val(), [
                NOMENCLATURES_COEFFICIENTS.direct_capitalization_without_land.toString(), 
                NOMENCLATURES_COEFFICIENTS.capitalization_with_economic_life_without_land.toString()
                ]) !== -1) {
                return (net_annual_rev_eur * object_multiplier - repairs_need) * market_ratio;
            } else {
                const land_value_eur     = sourceFieldsValues['land_value_eur'][resultFieldIndex];
                const land_interest_rate = sourceFieldsValues['land_interest_rate'][resultFieldIndex];
                return ((net_annual_rev_eur - land_value_eur * land_interest_rate / 100) * object_multiplier - repairs_need) * market_ratio + land_value_eur;
            }
        }
    });

    createFormula({
        resultFieldName: 'value_bgn',
        sourceFieldsNames: ['value_eur'],
        tooltip: '[value_bgn] = [value_eur] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            let value_eur = sourceFieldsValues['value_eur'][resultFieldIndex];

            return value_eur * getCurrencyFixing();
        }
    });

    if ($('[name^="land_interest_rate["].have_formula').length) {
        createFormula({
            resultFieldName: 'land_interest_rate',
            sourceFieldsNames: [
                'net_annual_rev_eur',
                'capital_rate'
            ],
            tooltip: '[land_interest_rate] = SUMPRODUCT([net_annual_rev_eur], [capital_rate]) / SUM([net_annual_rev_eur])',
            resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
                let capital_rate = $('[name^="capital_rate["]');
                let sumProductOfNetAnnualRevEurAndCapitalRate = 0;
                let netAnnualRevEurSum = 0;
                $('[name^="net_annual_rev_eur["]').each(function (index) {
                    sumProductOfNetAnnualRevEurAndCapitalRate += (parseFloat($(this).fullValue()) || 0) * (parseFloat(capital_rate.eq(index).fullValue()) || 0);
                    netAnnualRevEurSum += parseFloat($(this).fullValue()) || 0;
                });

                return netAnnualRevEurSum == 0 ? 0 : Math.round(sumProductOfNetAnnualRevEurAndCapitalRate / netAnnualRevEurSum, 2);
            }
        });
    } else if ($('[name^="land_interest_rate_hidden_max["]').length) {
        createFormula({
            resultFieldName: 'land_interest_rate_hidden_max',
            sourceFieldsNames: [
                'net_annual_rev_eur',
                'capital_rate'
            ],
            tooltip: '[land_interest_rate_hidden_max] = SUMPRODUCT([net_annual_rev_eur], [capital_rate]) / SUM([net_annual_rev_eur])',
            resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
                let capital_rate = $('[name^="capital_rate["]');
                let sumProductOfNetAnnualRevEurAndCapitalRate = 0;
                let netAnnualRevEurSum = 0;
                $('[name^="net_annual_rev_eur["]').each(function (index) {
                    sumProductOfNetAnnualRevEurAndCapitalRate += parseFloat($(this).fullValue() || 0) * parseFloat($(capital_rate[index]).fullValue() || 0);
                    netAnnualRevEurSum += parseFloat($(this).fullValue() || 0);
                });

                let result = (sumProductOfNetAnnualRevEurAndCapitalRate / netAnnualRevEurSum);

                if (isNaN(result) || result === 0) {
                    $(`[name="land_interest_rate[${resultFieldIndex}]"]`).removeData('max-value');
                } else {
                    $(`[name="land_interest_rate[${resultFieldIndex}]"]`).data('max-value', result);
                }

                return result;
            }
        });
    }
    $('[name^="net_annual_rev_eur["]').keyup();

    // if ($('[name^="land_value_eur["].have_formula').length) {
    //     createFormula({
    //         resultFieldName: 'land_value_eur',
    //         sourceFieldsNames: ['hidden_land_meters', 'hidden_average_arithmetic_value'],
    //         tooltip: '[land_value_eur] = [hidden_land_meters] * [hidden_average_arithmetic_value]',
    //         resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
    //             let hidden_land_meters = sourceFieldsValues['hidden_land_meters'][resultFieldIndex];
    //             let hidden_average_arithmetic_value = sourceFieldsValues['hidden_average_arithmetic_value'][resultFieldIndex];
    //
    //             return hidden_land_meters * hidden_average_arithmetic_value;
    //         }
    //     });
    //     $('[name^="hidden_average_arithmetic_value["]').keyup();
    // }

    // Trigger all formulas
    triggerFormulas();
}
