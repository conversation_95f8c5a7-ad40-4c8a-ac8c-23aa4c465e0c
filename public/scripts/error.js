$(function(){
	
	hideLoading();
	
	$('.error-info-toggle').on('click', function(){
		$(this).closest('.error-info-wrapper').toggleClass('opened');
	});
	
	function hideLoading(duration = 0) {
		hideLoader();
		hideOverlay(duration)
	}
	
	/**
	 * Show loading image and text
	 */
	function showLoader() {
	    $('#loadingWrapper').show(0);
	}
	
	/**
	 * Hide loading image and text
	 */
	function hideLoader() {
	    $('#loadingWrapper').hide(0);
	}

	/**
	 * Show overlay
	 */
	function showOverlay(duration = null) {
	    $('#overlay').stop().fadeIn(duration);
	}
	/**
	 * Hide overlay
	 */
	function hideOverlay(duration = null) {
	    $('#overlay').stop().fadeOut(duration);
	}
	
	function initChoicesDialog() {
	    let dialog = $("#choicesDialog");

	    dialog.dialog({
	        resizable: false,
	        height: "auto",
	        width: 400,
	        modal: true,
	        autoOpen: false,
	        open: function () {
	            showOverlay(0);
	        },
	        close: function () {
	            hideOverlay(0);
	        }
	    });

	    dialog.on('click', '#loginButton', function (e) {
	        e.preventDefault();
	        let username = $('#username');
	        let password = $('#password');

	        if (username.val() !== '' && password.val() !== '') {
	            $.ajax({
	                method: 'POST',
	                url: 'authentication/index',
	                data: {username: username.val(), password: password.val(), fromModal: true},
	                beforeSend: function () {
	                    showLoading();
	                    dialog.parent('.ui-dialog').css('z-index', 999);
	                },
	                success: function () {
	                    dialog.dialog('close');
	                    sessionStorage.setItem('invalidCreds', false);
	                },
	                error: function () {
	                    sessionStorage.setItem('invalidCreds', true);
	                },
	                complete: [
	                    hideLoader,
	                    function () {
	                        dialog.parent('.ui-dialog').css('z-index', '');
	                    }
	                ]
	            })
	        }
	    })
	}
	
	initChoicesDialog();
	
	function createConfirmDialogbox(selector, message, buttons) {
	    $(selector).click(function () {
	        let dialog = $("#choicesDialog");
	        dialog.html(message).dialog('option', 'buttons', buttons);
	        dialog.dialog("open");
	    })
	}
	
	function logoutClickListener() {
	    let buttons = {
	        'OK': function () {
	            $(this).dialog("close");
	            window.sessionStorage.removeItem('hasSession');
	            window.location = 'authentication/logout';
	        },
	        "Отказ": function () {
	            $(this).dialog("close");
	        }
	    };
	    let message = "Сигурни ли сте, че искате да напуснете системата?Натиснете бутона OK, за да потвърдите.";
	    createConfirmDialogbox("#logout", message, buttons);
	}
	logoutClickListener();
});