function r18Formulas() {
    // Крайна пазарна стойност
    // Стойност на обекта
    createFormula({
        resultFieldName: 'performance7_object_value_bgn',
        sourceFieldsNames: ['final_object_value_bgn', 'performance7_correction'],
        tooltip: `[performance7_object_value_bgn] = [final_object_value_bgn] - ([final_object_value_bgn] * ([performance7_correction] / 100))`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.final_object_value_bgn[resultFieldIndex]
                - sourceFieldsValues.final_object_value_bgn[resultFieldIndex]
                * parseFloat(sourceFieldsValues.performance7_correction[resultFieldIndex]) / 100;
        }
    });

    createFormula({
        resultFieldName: 'performance7_object_value_eur',
        sourceFieldsNames: ['performance7_object_value_bgn'],
        tooltip: '[performance7_object_value_eur] = [performance7_object_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.performance7_object_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });

    // Стойност на кв.м.
    createFormula({
        resultFieldName: 'performance7_sqm_value_bgn',
        sourceFieldsNames: ['final_sqm_value_bgn', 'performance7_correction'],
        tooltip: `[performance7_sqm_value_bgn] = [final_sqm_value_bgn] - ([final_sqm_value_bgn] * ([performance7_correction] / 100))`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.final_sqm_value_bgn[resultFieldIndex]
                - sourceFieldsValues.final_sqm_value_bgn[resultFieldIndex]
                * parseFloat(sourceFieldsValues.performance7_correction[resultFieldIndex]) / 100;
        }
    });

    createFormula({
        resultFieldName: 'performance7_sqm_value_eur',
        sourceFieldsNames: ['performance7_sqm_value_bgn'],
        tooltip: '[performance7_sqm_value_eur] = [performance7_sqm_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.performance7_sqm_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });


    // Стойност при принудителна продажба
    // Стойност на обекта
    createFormula({
        resultFieldName: 'performance7_land_forced_sale_object_value_bgn',
        sourceFieldsNames: ['forced_sale_object_value_bgn', 'performance7_correction'],
        tooltip: `[performance7_land_forced_sale_object_value_bgn] = [forced_sale_object_value_bgn] - ([forced_sale_object_value_bgn] * ([performance7_correction] / 100))`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.forced_sale_object_value_bgn[resultFieldIndex]
                - sourceFieldsValues.forced_sale_object_value_bgn[resultFieldIndex]
                * parseFloat(sourceFieldsValues.performance7_correction[resultFieldIndex]) / 100;
        }
    });

    createFormula({
        resultFieldName: 'performance7_land_forced_sale_object_value_eur',
        sourceFieldsNames: ['performance7_land_forced_sale_object_value_bgn'],
        tooltip: '[performance7_land_forced_sale_object_value_eur] = [performance7_land_forced_sale_object_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.performance7_land_forced_sale_object_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });


    // Стойност на кв.м.
    createFormula({
        resultFieldName: 'performance7_forced_sale_sqm_value_bgn',
        sourceFieldsNames: ['forced_sale_sqm_value_bgn', 'performance7_correction'],
        tooltip: `[performance7_forced_sale_sqm_value_bgn] = [forced_sale_sqm_value_bgn] - ([forced_sale_sqm_value_bgn] * ([performance7_correction] / 100))`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.forced_sale_sqm_value_bgn[resultFieldIndex]
                - sourceFieldsValues.forced_sale_sqm_value_bgn[resultFieldIndex]
                * parseFloat(sourceFieldsValues.performance7_correction[resultFieldIndex]) / 100;
        }
    });

    createFormula({
        resultFieldName: 'performance7_forced_sale_sqm_value_eur',
        sourceFieldsNames: ['performance7_forced_sale_sqm_value_bgn'],
        tooltip: '[performance7_forced_sale_sqm_value_eur] = [performance7_forced_sale_sqm_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.performance7_forced_sale_sqm_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });


    // Общо
    // Крайна пазарна стойност
    createFormula({
        resultFieldName: 'performance7_total_final_object_value_bgn',
        sourceFieldsNames: ['performance7_object_value_bgn'],
        tooltip: `[performance7_total_final_object_value_bgn] = SUM([performance7_object_value_bgn])`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sumColumn('[name^="performance7_object_value_bgn["]:not([disabled="disabled"])');
        }
    });

    createFormula({
        resultFieldName: 'performance7_total_final_object_value_eur',
        sourceFieldsNames: ['performance7_total_final_object_value_bgn'],
        tooltip: '[performance7_total_final_object_value_eur] = [performance7_total_final_object_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.performance7_total_final_object_value_bgn * getCurrencyFixing();
        }
    });

    // Стойност при принудителна продажба
    createFormula({
        resultFieldName: 'performance7_total_forced_sale_object_value_bgn',
        sourceFieldsNames: ['performance7_land_forced_sale_object_value_bgn'],
        tooltip: `[performance7_total_forced_sale_object_value_bgn] = SUM([performance7_land_forced_sale_object_value_bgn])`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sumColumn('[name^="performance7_land_forced_sale_object_value_bgn["]:not([disabled="disabled"])');
        }
    });

    createFormula({
        resultFieldName: 'performance7_total_forced_sale_object_value_eur',
        sourceFieldsNames: ['performance7_total_forced_sale_object_value_bgn'],
        tooltip: '[performance7_total_forced_sale_object_value_eur] = [performance7_total_forced_sale_object_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.performance7_total_forced_sale_object_value_bgn * getCurrencyFixing();
        }
    });
}

    // LAND
function r18LandFormulas() {

    // Крайна пазарна стойност
    // Стойност на обекта
    createFormula({
        resultFieldName: 'per7_land_final_object_value_bgn',
        sourceFieldsNames: ['land_final_object_value_bgn'],
        tooltip: `[per7_land_final_object_value_bgn] = [land_final_object_value_bgn]`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            if (typeof sourceFieldsValues.land_final_object_value_bgn === 'undefined') {
                return 0;
            }
            return sourceFieldsValues.land_final_object_value_bgn[resultFieldIndex];
        }
    });

    createFormula({
        resultFieldName: 'per7_land_final_object_value_eur',
        sourceFieldsNames: ['per7_land_final_object_value_bgn'],
        tooltip: '[per7_land_final_object_value_eur] = [per7_land_final_object_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.per7_land_final_object_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });

    // Стойност на кв.м.
    createFormula({
        resultFieldName: 'per7_land_final_sqm_value_bgn',
        sourceFieldsNames: ['land_final_sqm_value_bgn'],
        tooltip: `[per7_land_final_sqm_value_bgn] = [land_final_sqm_value_bgn]`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.land_final_sqm_value_bgn[resultFieldIndex];
        }
    });


    createFormula({
        resultFieldName: 'per7_land_final_sqm_value_eur',
        sourceFieldsNames: ['per7_land_final_sqm_value_bgn'],
        tooltip: '[per7_land_final_sqm_value_eur] = [per7_land_final_sqm_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.per7_land_final_sqm_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });


    // Стойност при принудителна продажба
    // Стойност на обекта
    createFormula({
        resultFieldName: 'per7_land_forced_sale_object_value_bgn',
        sourceFieldsNames: ['land_forced_sale_object_value_bgn'],
        tooltip: `[per7_land_forced_sale_object_value_bgn] = [land_forced_sale_object_value_bgn]`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.land_forced_sale_object_value_bgn[resultFieldIndex];
        }
    });

    createFormula({
        resultFieldName: 'per7_land_forced_sale_object_value_eur',
        sourceFieldsNames: ['per7_land_forced_sale_object_value_bgn'],
        tooltip: '[per7_land_forced_sale_object_value_eur] = [per7_land_forced_sale_object_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.per7_land_forced_sale_object_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });

    // Стойност на кв.м.
    createFormula({
        resultFieldName: 'per7_land_forced_sale_sqm_value_bgn',
        sourceFieldsNames: ['land_forced_sale_sqm_value_bgn'],
        tooltip: `[per7_land_forced_sale_sqm_value_bgn] = [land_forced_sale_sqm_value_bgn]`,
        resultFormula: function (sourceFieldsValues, resultFieldIndex, formulaParams) {
            return sourceFieldsValues.land_forced_sale_sqm_value_bgn[resultFieldIndex];
        }
    });

    createFormula({
        resultFieldName: 'per7_land_forced_sale_sqm_value_eur',
        sourceFieldsNames: ['per7_land_forced_sale_sqm_value_bgn'],
        tooltip: '[per7_land_forced_sale_sqm_value_eur] = [per7_land_forced_sale_sqm_value_bgn] * фиксинг',
        resultFormula: function (sourceFieldsValues, resultFieldIndex) {
            return sourceFieldsValues.per7_land_forced_sale_sqm_value_bgn[resultFieldIndex] * getCurrencyFixing();
        }
    });
}
