/*global $:false */
$(() => {
    'use strict';

    let submitButton = $('.button-submit');
    let form = $('#edit_residential_buildings');

    submitButton.off('click submit');

    /**
     * Submit button
     */
    submitButton.on('click', function (e) {
        e.preventDefault();

        form.submit();
    });
    /**
     * Cancel button
     * Also removes query string from the URL: ?nomenclature=123
     */
    $('.button-cancel').on('click', function (e) {
        e.preventDefault();
        let url = $('#backUrl').val();

        window.location = url.split('?')[0];
    });
});