$(() => {

    const sidebarBeginningTab = $('#sidebarBeginningTab');
    const sidebarA = $('#sidebar a');
    const certainTds = $('#currentReports thead tr th:nth-child(2), #supervisionReports thead tr th:nth-child(2)');
    const statusesTableContainer = $('#statusesTableContainer');

    /**
     * The columns definitions of the reports table.
     * The columns are in the order they are displayed in the table.
     * The targets property of each object is the index of the column in the table.
     * The className property of each object is the class that will be added to the td of the column.
     * The class is used to style the column.
     * The visible property of the first object is set to false, so the column is not visible in the table.
     * The searchable property of the first object is set to false, so the column is not searchable.
     *
     * @type {[{targets: number[], visible: boolean, searchable: boolean},{className: string, targets: number[]},{className: string, targets: number[]},{className: string, targets: number[]},{className: string, targets: number[]},null,null,null,null,null,null,null,null,null]}
     */
    const columnsDefinitions = [
        {targets: [0], visible: false, searchable: false},
        {className: "important", targets: [ 0 ]},
        {className: "num", targets: [ 1 ]},
        {className: "type", targets: [ 2 ]},
        {className: "bank", targets: [ 3 ]},
        {className: "assignor_name", targets: [ 4 ]},
        {className: "loan_applicant", targets: [ 5 ]},
        {className: "object_type", targets: [ 6 ]},
        {className: "object_subtype", targets: [ 7 ]},
        {className: "evaluaters", targets: [ 8 ]},
        {className: "supervisors", targets: [ 9 ]},
        {className: "date", targets: [ 10, 11, 13]},
        {className: "system", targets: [ 12 ]},
        {className: "tags", targets: [ 14 ]}
    ];
    /**
     * The columns of the reports table.
     * The columns are in the order they are displayed in the table.
     * The data property of each object is the key of the data that will be displayed in the column.
     * The key is the same as the key of the data that is returned from the server.
     *
     * @type {[{data: string},{data: string},{data: string},{data: string},{data: string},null,null,null,null,null,null,null,null,null,null]}
     */
    const columnsList = [
        {data: 'important'},
        {data: 'full_num'},
        {data: 'rating_type'},
        {data: 'bank_name'},
        {data: 'assignor_name'},
        {data: 'loan_applicant_name'},
        {data: 'object_name_type'},
        {data: 'object_subtype_name'},
        {data: '_evaluaters'},
        {data: '_supervisors'},
        {data: 'award_date'},
        {data: 'deadline'},
        {data: 'where'},
        {data: 'certification_date'},
        {data: '_tags'},
    ];

    sidebarBeginningTab.addClass('selected');

    // Make all sidebar URLs not clickable (users cannot go there just yet)
    sidebarA.slice(1).removeAttr('href').css({
        'color': 'gray',
        'cursor': 'not-allowed'
    });

    //No need for on hover change color here
    removeHoverEffect('#sidebar li');

    //change width of certain td
    certainTds.width('60px');

    // Cache the trs of the first two tables.
    let tableRows = $('#currentReports tbody tr, #supervisionReports tbody tr');

    // Cache the report table.
    const dataTable = $('#indexPageReportsTable');

    // Make change cursor to pointer on hover on the table rows
    tableRows.css("cursor", "pointer");

    /**
     * This is the event listener for the click event on the rows of the report table.
     */
    dataTable.on('mouseup', 'tbody tr', function (e) {
        if (![1, 2].includes(e.which)) {
            return;
        }

        const $row = $(this);
        const reportId = $row.data('reportId');
        const isAlvis = $row.data('system_made_by') === 'ALVIS';

        $('.loader').fadeIn('fast');

        if (reportId === undefined) {
            return false;
        }

        const href = isAlvis ? `report/${reportId}` : `redirect/launch-documents|documents-view|view-${reportId}`;

        showLoading();

        // If clicked with the middle mouse button (the mouse wheel)
        if (e.which === 2) {
            window.open(href, '_blank');
            hideLoading();
        } else {
            window.location.href = href;
        }
    });

    /**
     * This is the event listener for the click event on the rows of the statuses table(Current reports and Supervision).
     * It is used to load the reports with the selected status
     *
     * When a row is clicked, get the status and reportIds of the clicked row and load the reports with that status.
     * @param e
     *
     * @returns void
     */
    statusesTableContainer.on('click', 'tbody tr', function (e) {
        const $clickedRow = $(this);
        const status = $clickedRow.data('status');
        const reportIds = $clickedRow.data('reportids');
        const statusText = $clickedRow.find('td:nth-child(1)').text();
        const reportsCount = $clickedRow.find('td:nth-child(2)').text();

        $('#statusesTableContainer .selected').removeClass('selected');
        $clickedRow.addClass('selected');

        // Empty the reports table( new one's will be added)
        dataTable.find('tbody').empty();

        const url = new URL(window.location);
        url.searchParams.set('s', status);
        window.history.pushState(
            {
                status: status,
                reportids: reportIds
            },
            'Начало - ' + statusText,
            url
        );

        if (reportsCount === '0') {
            dataTable.attr('hidden', true);
            showInfo('Нямате налични доклади с този статус!');
            return;
        }

        loadReports(status, reportIds);
    });

    let loadReports = function(status, reportids) {
        dataTable.find('tbody').empty();

        dataTable.DataTable().destroy();
        dataTable.dataTable({
            searching: true,
            "oLanguage": {
                "sSearch": "Търсене: "
            },
            paging: true,
            bInfo: false,
            pagingType: 'numbers',
            lengthMenu: [[20, 50, -1], [20, 50, "All"]],
            deferRender: true,
            language: {
                loadingRecords: "Зареждане на доклади...",
                lengthMenu: "Показват се _MENU_ записа на страница",
                zeroRecords: "Нищо не е намерено",
                info: "Страница _PAGE_ от _PAGES_",
                infoEmpty: "Няма записи",
                infoFiltered: "(Филтрирани от общо _MAX_ записа)",
            },
            ajax: {
                url: 'ajax/documents2',
                dataSrc: (json) => {
                    dataTable.attr('hidden', false);
                    return json;
                },
                data: {doc_type: status, reportIds: reportids},
                error: function () {
                    dataTable.attr('hidden', true);
                    $('#statusesTableContainer .selected').removeClass('selected');
                },
                complete: [this.complete, function() {
                    tableRows.css("pointer-events", "auto");
                    resetSessionTimer();
                }]
            },
            columnDefs: columnsDefinitions,
            orderFixed: {
                pre: [0, 'desc']
            },
            columns: columnsList,
            createdRow : function ( row, data, index ) {
                const $row = $(row);

                if (data.important === 1) {
                    $row.addClass('important');
                }

                if (['ALVIS', 'nZoom'].includes(data.where)) {
                    $row.addClass('system_' + data.where)
                        .data({
                            system_made_by: data.where,
                            reportId: data.where !== 'ALVIS' ? data.id : data.document13_id
                        });
                }
            }
        });
    };

    if(window.location.search !== ''){
        const url = new URL(window.location);

        if(url.searchParams.get('s')){
            const $clickedRow = $('#statusesTableContainer tr[data-status="'+url.searchParams.get('s')+'"]');

            $('#statusesTableContainer .selected').removeClass('selected');
            $clickedRow.addClass('selected');

            if ($clickedRow.find('td:nth-child(2)').text() === '0') {
                showInfo('Нямате налични доклади с този статус!');
            } else {
                loadReports($clickedRow.data('status'), $clickedRow.data('reportids'));
            }
        }
    }

    window.addEventListener('popstate', (event) => {
        // clear the results table
        if(event.state == null){
            $('#statusesTableContainer .selected').removeClass('selected');

            dataTable.find('tbody').empty();
            dataTable.attr('hidden', true);
            dataTable.DataTable().destroy();

            return;
        }

        if(event.state.status) {
            const $clickedRow = $('#statusesTableContainer tr[data-status="' + event.state.status + '"]');

            $('#statusesTableContainer .selected').removeClass('selected');

            $clickedRow.addClass('selected');

            if ($clickedRow.find('td:nth-child(2)').text() === '0') {
                dataTable.find('tbody').empty();
                dataTable.attr('hidden', true);
                dataTable.DataTable().destroy();
                showInfo('Нямате налични доклади с този статус!');

                return;
            }

            loadReports($clickedRow.data('status'), $clickedRow.data('reportids'));
        }

    });
});
