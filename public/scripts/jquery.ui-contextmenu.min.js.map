{"version": 3, "sources": ["jquery.ui-contextmenu.js"], "names": ["factory", "define", "amd", "j<PERSON><PERSON><PERSON>", "$", "supportSelectstart", "document", "createElement", "match", "ui", "menu", "version", "uiVersion", "major", "parseInt", "minor", "isLTE110", "isLTE111", "widget", "options", "addClass", "closeOnWindowBlur", "autoFocus", "autoTrigger", "delegate", "hide", "effect", "duration", "ignoreParentSelect", "position", "preventContextMenuForPopup", "preventSelect", "show", "taphold", "uiMenuOptions", "beforeOpen", "noop", "blur", "close", "create", "createMenu", "focus", "open", "select", "_create", "cssText", "eventNames", "targetId", "opts", "this", "$headStyle", "$menu", "menuIsTemp", "currentTarget", "extraData", "previousFocus", "error", "element", "is", "uniqueId", "attr", "prop", "appendTo", "html", "e", "styleSheet", "on", "eventNamespace", "event", "preventDefault", "_createUiMenu", "proxy", "_openMenu", "_destroy", "off", "remove", "menuDef", "ct", "ed", "isOpen", "_closeMenu", "removeClass", "isArray", "moogle", "contextmenu", "createMenuMarkup", "extend", "items", "retval", "isParent", "isMenu", "item", "actionHandler", "data", "cmd", "target", "_trigger", "call", "recursive", "res", "promise", "posOption", "self", "manualTrigger", "isTrigger", "_extraData", "originalEvent", "result", "isFunction", "done", "which", "keyCode", "ESCAPE", "closest", "length", "window", "my", "at", "of", "undefined", "pageX", "collision", "_updateEntries", "css", "left", "top", "_show", "$first", "children", "not", "first", "immediately", "hideOpts", "_hide", "_setOption", "key", "value", "replaceMenu", "Widget", "prototype", "apply", "arguments", "_getMenuEntry", "find", "each", "i", "o", "$entry", "fn", "type", "enableEntry", "showEntry", "setTitle", "flag", "toggleClass", "getEntry", "getEntryWrapper", "addBack", "getMenu", "targetOrEvent", "isEvent", "Event", "get", "pageY", "trigger", "setEntry", "entry", "$ul", "$entryLi", "console", "warn", "empty", "createEntryMarkup", "setIcon", "icon", "updateEntry", "uiIcon", "title", "toggle", "$icon", "$wrapper", "updateTitle", "tooltip", "removeAttr", "append", "disabled", "hasClass", "setClass", "$parentLi", "test", "href", "<PERSON><PERSON><PERSON><PERSON>", "isPlainObject", "text", "$parentUl", "$li", "has", "replaceFirstTextNodeChild", "elem", "$icons", "detach"], "mappings": ";;CAUC,SAAUA,GACV,YACuB,mBAAXC,SAAyBA,OAAOC,IAE3CD,QAAS,SAAU,6BAA+BD,GAGlDA,EAASG,SAET,SAAUC,GAEZ,YAEA,IAAIC,GAAqB,iBAAmBC,UAASC,cAAc,OAClEC,EAAQJ,EAAEK,GAAGC,KAAKC,QAAQH,MAAM,gBAChCI,GACCC,MAAOC,SAASN,EAAM,GAAI,IAC1BO,MAAOD,SAASN,EAAM,GAAI,KAE3BQ,EAAaJ,EAAUC,MAAQ,GAAKD,EAAUG,OAAS,GACvDE,EAAaL,EAAUC,MAAQ,GAAKD,EAAUG,OAAS,EAExDX,GAAEc,OAAO,sBACRP,QAAS,WACTQ,SACCC,SAAU,iBACVC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACbC,SAAU,KACVC,MAAQC,OAAQ,UAAWC,SAAU,QACrCC,oBAAoB,EACpBlB,KAAM,KACNmB,SAAU,KACVC,4BAA4B,EAE5BC,eAAe,EACfC,MAAQN,OAAQ,YAAaC,SAAU,QACvCM,SAAS,EACTC,iBAEAC,WAAY/B,EAAEgC,KACdC,KAAMjC,EAAEgC,KACRE,MAAOlC,EAAEgC,KACTG,OAAQnC,EAAEgC,KACVI,WAAYpC,EAAEgC,KACdK,MAAOrC,EAAEgC,KACTM,KAAMtC,EAAEgC,KACRO,OAAQvC,EAAEgC,MAGXQ,QAAS,WACR,GAAIC,GAASC,EAAYC,EACxBC,EAAOC,KAAK9B,OAYb,IAVA8B,KAAKC,WAAa,KAClBD,KAAKE,MAAQ,KACbF,KAAKG,YAAa,EAClBH,KAAKI,cAAgB,KACrBJ,KAAKK,aACLL,KAAKM,cAAgB,KAEA,MAAjBP,EAAKxB,UACRpB,EAAEoD,MAAM,uDAELR,EAAKjB,cAAe,CAIvBgB,GAAY3C,EAAE6C,KAAKQ,SAASC,GAAGpD,UAAYF,EAAE,QAC1C6C,KAAKQ,SAASE,WAAWC,KAAK,MACjCf,EAAU,IAAME,EAAW,IAAMC,EAAKxB,SAAW,8HAOjDyB,KAAKC,WAAa9C,EAAE,8CAClByD,KAAK,OAAQ,YACbC,SAAS,OAEX,KACCb,KAAKC,WAAWa,KAAKlB,GACpB,MAAQmB,GAETf,KAAKC,WAAW,GAAGe,WAAWpB,QAAUA,EAGrCxC,GACH4C,KAAKQ,QAAQS,GAAG,cAAgBjB,KAAKkB,eAAgBnB,EAAKxB,SACnD,SAAS4C,GACfA,EAAMC,mBAITpB,KAAKqB,cAActB,EAAKtC,MAExBoC,EAAa,cAAgBG,KAAKkB,eAC9BnB,EAAKf,UACRa,GAAc,WAAaG,KAAKkB,gBAEjClB,KAAKQ,QAAQS,GAAGpB,EAAYE,EAAKxB,SAAUpB,EAAEmE,MAAMtB,KAAKuB,UAAWvB,QAGpEwB,SAAU,WACTxB,KAAKQ,QAAQiB,IAAIzB,KAAKkB,gBAEtBlB,KAAKqB,cAAc,MAEfrB,KAAKC,aACRD,KAAKC,WAAWyB,SAChB1B,KAAKC,WAAa,OAIpBoB,cAAe,SAASM,GACvB,GAAIC,GAAIC,EACP9B,EAAOC,KAAK9B,OAGT8B,MAAK8B,WAERF,EAAK5B,KAAKI,cACVyB,EAAK7B,KAAKK,UAEVL,KAAK+B,YAAW,GAChB/B,KAAKI,cAAgBwB,EACrB5B,KAAKK,UAAYwB,GAEd7B,KAAKG,WACRH,KAAKE,MAAMwB,SACD1B,KAAKE,OACfF,KAAKE,MACHzC,KAAK,WACLuE,YAAYhC,KAAK9B,QAAQC,UACzBK,OAEHwB,KAAKE,MAAQ,KACbF,KAAKG,YAAa,EAGZwB,IAEKxE,EAAE8E,QAAQN,IACpB3B,KAAKE,MAAQ/C,EAAE+E,OAAOC,YAAYC,iBAAiBT,GACnD3B,KAAKG,YAAa,GACW,gBAAZwB,GACjB3B,KAAKE,MAAQ/C,EAAEwE,GAEf3B,KAAKE,MAAQyB,EAGd3B,KAAKE,MACH1B,OACAL,SAAS4B,EAAK5B,UAEdV,KAAKN,EAAEkF,QAAO,KAAUtC,EAAKd,eAC7BqD,MAAO,4BACPlD,KAAMjC,EAAEmE,MAAMvB,EAAKX,KAAMY,MACzBV,OAAQnC,EAAEmE,MAAMvB,EAAKR,WAAYS,MACjCR,MAAOrC,EAAEmE,MAAMvB,EAAKP,MAAOQ,MAC3BN,OAAQvC,EAAEmE,MAAM,SAASH,EAAO3D,GAE/B,GAAI+E,GACHC,EAAWrF,EAAE+E,OAAOC,YAAYM,OAAOjF,EAAGkF,MAC1CC,EAAgBnF,EAAGkF,KAAKE,KAAK,gBAE9BpF,GAAGqF,IAAMrF,EAAGkF,KAAK/B,KAAK,gBACtBnD,EAAGsF,OAAS3F,EAAE6C,KAAKI,eACnB5C,EAAG6C,UAAYL,KAAKK,UAEdmC,GAAazC,EAAKpB,qBACvB4D,EAASvC,KAAK+C,SAASC,KAAKhD,KAAM,SAAUmB,EAAO3D,GAC9CmF,IACJJ,EAASI,EAAcK,KAAKhD,KAAMmB,EAAO3D,IAErC+E,KAAW,GACfvC,KAAK+B,WAAWiB,KAAKhD,MAEtBmB,EAAMC,mBAELpB,WAINuB,UAAW,SAASJ,EAAO8B,GAC1B,GAAIC,GAAKC,EAAS3F,EACjBuC,EAAOC,KAAK9B,QACZkF,EAAYrD,EAAKnB,SACjByE,EAAOrD,KACPsD,IAAkBnC,EAAMoC,SAEzB,IAAMxD,EAAKzB,aAAgBgF,EAA3B,CAaA,GARAnC,EAAMC,iBAENpB,KAAKI,cAAgBe,EAAM2B,OAC3B9C,KAAKK,UAAYc,EAAMqC,eAEvBhG,GAAOC,KAAMuC,KAAKE,MAAO4C,OAAQ3F,EAAE6C,KAAKI,eAAgBC,UAAWL,KAAKK,UACpEoD,cAAetC,EAAOuC,OAAQ,OAE5BT,EAAY,CAIjB,GAHAC,EAAMlD,KAAK+C,SAAS,aAAc5B,EAAO3D,GACzC2F,EAAW3F,EAAGkG,QAAUvG,EAAEwG,WAAWnG,EAAGkG,OAAOP,SAAY3F,EAAGkG,OAAS,KACvElG,EAAGkG,OAAS,KACPR,KAAQ,EAEZ,MADAlD,MAAKI,cAAgB,MACd,CACD,IAAK+C,EAOX,MAJAA,GAAQS,KAAK,WACZP,EAAK9B,UAAUJ,GAAO,KAEvBnB,KAAKI,cAAgB,MACd,CAER5C,GAAGC,KAAOuC,KAAKE,MAIhB/C,EAAEE,UAAU4D,GAAG,UAAYjB,KAAKkB,eAAgB,SAASC,GACnDA,EAAM0C,QAAU1G,EAAEK,GAAGsG,QAAQC,QACjCV,EAAKtB,eAEJd,GAAG,YAAcjB,KAAKkB,eAAiB,cAAgBlB,KAAKkB,eAC7D,SAASC,GAEJhE,EAAEgE,EAAM2B,QAAQkB,QAAQ,iBAAiBC,QAC9CZ,EAAKtB,eAGP5E,EAAE+G,QAAQjD,GAAG,OAASjB,KAAKkB,eAAgB,SAASC,GAC9CpB,EAAK3B,mBACTiF,EAAKtB,eAKH5E,EAAEwG,WAAWP,KAChBA,EAAYA,EAAUjC,EAAO3D,IAE9B4F,EAAYjG,EAAEkF,QACb8B,GAAI,WACJC,GAAI,cAEJC,GAAqBC,SAAhBnD,EAAMoD,MAAuBpD,EAAM2B,OAAS3B,EACjDqD,UAAW,OACTpB,GAGHpD,KAAKyE,eAAezE,KAAKE,OAGzBF,KAAKE,MACHnB,OACA2F,KACA9F,SAAU,WACV+F,KAAM,EACNC,IAAK,IACHhG,SAASwE,GACX5E,OAEGuB,EAAKlB,4BACTmB,KAAKE,MAAMe,GAAG,cAAgBjB,KAAKkB,eAAgB,SAASC,GAC3DA,EAAMC,mBAGRpB,KAAK6E,MAAM7E,KAAKE,MAAOH,EAAKhB,KAAM,WACjC,GAAI+F,EAGC/E,GAAK1B,YACTgF,EAAK/C,cAAgBnD,EAAEgE,EAAM2B,QAE7BgC,EAASzB,EAAKnD,MACZ6E,SAAS,mBACTC,IAAI,sBACJC,QACF5B,EAAKnD,MAAMzC,KAAK,QAAS,KAAMqH,GAAQtF,SAExC6D,EAAKN,SAASC,KAAKK,EAAM,OAAQlC,EAAO3D,OAI1CuE,WAAY,SAASmD,GACpB,GAAI7B,GAAOrD,KACVmF,GAAWD,GAAsBlF,KAAK9B,QAAQM,KAC9ChB,GAAOC,KAAMuC,KAAKE,MAAO4C,OAAQ3F,EAAE6C,KAAKI,eAAgBC,UAAWL,KAAKK,UAGzElD,GAAEE,UACAoE,IAAI,YAAczB,KAAKkB,gBACvBO,IAAI,aAAezB,KAAKkB,gBACxBO,IAAI,UAAYzB,KAAKkB,gBACvB/D,EAAE+G,QACAzC,IAAI,OAASzB,KAAKkB,gBAEpBmC,EAAKjD,cAAgB,KACrBiD,EAAKhD,aACAL,KAAKE,OACTF,KAAKE,MACHuB,IAAI,cAAgBzB,KAAKkB,gBAC3BlB,KAAKoF,MAAMpF,KAAKE,MAAOiF,EAAU,WAC3B9B,EAAK/C,gBACT+C,EAAK/C,cAAcd,QACnB6D,EAAK/C,cAAgB,MAEtB+C,EAAKN,SAAS,QAAS,KAAMvF,MAG9B6F,EAAKN,SAAS,QAAS,KAAMvF,IAI/B6H,WAAY,SAASC,EAAKC,GACzB,OAAQD,GACR,IAAK,OACJtF,KAAKwF,YAAYD,GAGlBpI,EAAEsI,OAAOC,UAAUL,WAAWM,MAAM3F,KAAM4F,YAG3CC,cAAe,SAAShD,GACvB,MAAO7C,MAAKE,MAAM4F,KAAK,mBAAqBjD,EAAM,MAGnDxD,MAAO,WACFW,KAAK8B,UACR9B,KAAK+B,cAIP0C,eAAgB,WACf,GAAIpB,GAAOrD,KACVxC,GACCC,KAAMuC,KAAKE,MAAO4C,OAAQ3F,EAAE6C,KAAKI,eAAgBC,UAAWL,KAAKK,UAEnElD,GAAE4I,KAAK/F,KAAKE,MAAM4F,KAAK,iBAAkB,SAASE,EAAGC,GACpD,GAAIC,GAAS/I,EAAE8I,GACdE,EAAKD,EAAOtD,KAAK,mBACjBM,EAAMiD,EAAKA,GAAKC,KAAM,YAAc5I,GAAM,IAE3CA,GAAGkF,KAAOwD,EACV1I,EAAGqF,IAAMqD,EAAOvF,KAAK,gBAET,MAAPuC,IACJG,EAAKgD,YAAY7I,EAAGqF,KAAMK,GAC1BG,EAAKiD,UAAU9I,EAAGqF,IAAa,SAARK,IAGxBiD,EAAKD,EAAOtD,KAAK,gBACjBM,EAAMiD,EAAKA,GAAKC,KAAM,SAAW5I,GAAM,KAC3B,MAAP0F,GACJG,EAAKkD,SAAS/I,EAAGqF,IAAK,GAAKK,GAG5BiD,EAAKD,EAAOtD,KAAK,kBACjBM,EAAMiD,EAAKA,GAAKC,KAAM,WAAa5I,GAAM,KAC7B,MAAP0F,GACJgD,EAAOvF,KAAK,QAAS,GAAKuC,MAK7BmD,YAAa,SAASxD,EAAK2D,GAC1BxG,KAAK6F,cAAchD,GAAK4D,YAAY,oBAAsBD,KAAS,IAGpEE,SAAU,SAAS7D,GAClB,MAAO7C,MAAK6F,cAAchD,IAO3B8D,gBAAiB,SAAS9D,GACzB,MAAO7C,MAAK6F,cAAchD,GAAKiD,KAAK,oBAAoBc,QAAQ,oBAGjEC,QAAS,WACR,MAAO7G,MAAKE,OAGb4B,OAAQ,WAEP,QAAS9B,KAAKE,SAAWF,KAAKI,eAK/BX,KAAM,SAASqH,EAAezG,GAE7BA,EAAYA,KAEZ,IAAI0G,GAAWD,GAAiBA,EAAcV,MAAQU,EAAchE,OACnE3B,EAAS4F,EAAUD,KACnBhE,EAASiE,EAAUD,EAAchE,OAASgE,EAC1C/F,EAAI7D,OAAO8J,MAAM,eAChBlE,OAAQ3F,EAAE2F,GAAQmE,IAAI,GACtB1C,MAAOpD,EAAMoD,MACb2C,MAAO/F,EAAM+F,MACbzD,cAAesD,EAAUD,EAAgBxC,OACzCd,WAAYnD,GAEd,OAAOL,MAAKQ,QAAQ2G,QAAQpG,IAG7ByE,YAAa,SAAS5C,GACrB5C,KAAKqB,cAAcuB,IAGpBwE,SAAU,SAASvE,EAAKwE,GACvB,GAAIC,GACHC,EAAWvH,KAAK6F,cAAchD,EAE/B,OAAqB,gBAAVwE,IACVnD,OAAOsD,SAAWtD,OAAOsD,QAAQC,KAChC,2FACwBJ,EAAQ,eAC1BrH,KAAKuG,SAAS1D,EAAKwE,KAE3BE,EAASG,QACTL,EAAMxE,IAAMwE,EAAMxE,KAAOA,EACzB1F,EAAE+E,OAAOC,YAAYwF,kBAAkBN,EAAOE,GAC1CpK,EAAE8E,QAAQoF,EAAMtC,YACnBuC,EAAMnK,EAAE,SAAS0D,SAAS0G,GAC1BpK,EAAE+E,OAAOC,YAAYC,iBAAiBiF,EAAMtC,SAAUuC,IAGvDC,EAASvF,YAAY,oBACrBhC,MAAK6G,UAAUpJ,KAAK,aAGrBmK,QAAS,SAAS/E,EAAKgF,GACtB,MAAO7H,MAAK8H,YAAYjF,GAAOkF,OAAQF,KAGxCtB,SAAU,SAAS1D,EAAKmF,GACvB,MAAOhI,MAAK8H,YAAYjF,GAAOmF,MAAOA,KAOvC1B,UAAW,SAASzD,EAAK2D,GACxBxG,KAAK6F,cAAchD,GAAKoF,OAAOzB,KAAS,IAGzCsB,YAAa,SAASjF,EAAKwE,GAC1B,GAAIa,GAAOC,EACVZ,EAAWvH,KAAK6F,cAAchD,EAEVyB,UAAhB+C,EAAMW,OACV7K,EAAE+E,OAAOC,YAAYiG,YAAYb,EAAU,GAAKF,EAAMW,OAEhC1D,SAAlB+C,EAAMgB,UACa,OAAlBhB,EAAMgB,QACVd,EAASe,WAAW,SAEpBf,EAAS5G,KAAK,QAAS0G,EAAMgB,UAGT/D,SAAjB+C,EAAMU,SACVI,EAAWnI,KAAK2G,gBAAgB9D,GAChCqF,EAAQC,EAASrC,KAAK,gBAAgBd,IAAI,iBAC1CkD,EAAMxG,SACD2F,EAAMU,QACVI,EAASI,OAAOpL,EAAE,4BAA4BgB,SAASkJ,EAAMU,UAG3CzD,SAAf+C,EAAM7I,KACV+I,EAASU,QAAQZ,EAAM7I,MACG8F,SAAf+C,EAAMtI,MAEjBwI,EAASU,SAASZ,EAAMtI,MAKLuF,SAAf+C,EAAMzE,MACV2E,EAAS3E,KAAKyE,EAAMzE,MAIG0B,SAAnB+C,EAAMmB,WACVnB,EAAMmB,SAAWjB,EAASkB,SAAS,sBAE/BpB,EAAMqB,UACLnB,EAASkB,SAAS,kBACtBpB,EAAMqB,UAAY,iBAEnBnB,EAASvF,cACTuF,EAASpJ,SAASkJ,EAAMqB,WACbrB,EAAMlJ,UACjBoJ,EAASpJ,SAASkJ,EAAMlJ,UAEzBoJ,EAASd,YAAY,sBAAuBY,EAAMmB,aAUpDrL,EAAEkF,OAAOlF,EAAE+E,OAAOC,aAEjBwF,kBAAmB,SAASN,EAAOsB,GAClC,GAAIR,GAAW,IAEfQ,GAAUhI,KAAK,eAAgB0G,EAAMxE,KAE/B,sBAAsB+F,KAAMvB,EAAMW,QAIlCjK,EAEJoK,EAAWhL,EAAE,QACX2D,KAAM,GAAKuG,EAAMW,MACjBa,KAAM,MACJhI,SAAS8H,GAEF3K,GAEX2K,EAAU7H,KAAK,GAAKuG,EAAMW,OAC1BG,EAAWQ,GAIXR,EAAWhL,EAAE,UACX2D,KAAM,GAAKuG,EAAMW,QACfnH,SAAS8H,GAETtB,EAAMU,QACVI,EAASI,OAAOpL,EAAE,4BAA4BgB,SAASkJ,EAAMU,SAG9D5K,EAAE4I,MAAQ,SAAU,WAAY,QAAS,WAAa,SAASC,EAAGrF,GAC5DxD,EAAEwG,WAAW0D,EAAM1G,KACvBgI,EAAU/F,KAAKjC,EAAO,UAAW0G,EAAM1G,MAGpC0G,EAAMmB,YAAa,GACvBG,EAAUxK,SAAS,qBAEfkJ,EAAMyB,UACVH,EAAUxK,SAAS,oBAEfkJ,EAAMlJ,UACVwK,EAAUxK,SAASkJ,EAAMlJ,UAErBhB,EAAE4L,cAAc1B,EAAMzE,OAC1B+F,EAAU/F,KAAKyE,EAAMzE,MAEQ,gBAAlByE,GAAMgB,SACjBM,EAAUhI,KAAK,QAAS0G,EAAMgB,UA1C/BM,EAAUK,KAAK3B,EAAMW,QA+CvB5F,iBAAkB,SAASlE,EAAS+K,GACnC,GAAIjD,GAAGvI,EAAM6J,EAAK4B,CAIlB,KAHkB,MAAbD,IACJA,EAAY9L,EAAE,mCAAmC0D,SAAS,SAEtDmF,EAAI,EAAGA,EAAI9H,EAAQ+F,OAAQ+B,IAC/BvI,EAAOS,EAAQ8H,GACfkD,EAAM/L,EAAE,SAAS0D,SAASoI,GAE1B9L,EAAE+E,OAAOC,YAAYwF,kBAAkBlK,EAAMyL,GAExC/L,EAAE8E,QAAQxE,EAAKsH,YACnBuC,EAAMnK,EAAE,SAAS0D,SAASqI,GAC1B/L,EAAE+E,OAAOC,YAAYC,iBAAiB3E,EAAKsH,SAAUuC,GAGvD,OAAO2B,IAGRxG,OAAQ,SAASC,GAChB,MAAK3E,GACG2E,EAAKyG,IAAI,4BAA4BlF,OAAS,EAC1CjG,EACJ0E,EAAKjC,GAAG,0BAERiC,EAAKyG,IAAI,8BAA8BlF,OAAS,GAIzDmF,0BAA2B,SAASC,EAAMvI,GACzC,GAAIwI,GAASD,EAAKvD,KAAK,6BAA6ByD,QAEpDF,GACE3B,QACA5G,KAAKA,GACLyH,OAAOe,IAGVlB,YAAa,SAAS1F,EAAMsF,GACtBjK,EACJZ,EAAE+E,OAAOC,YAAYiH,0BAA0BjM,EAAE,IAAKuF,GAAOsF,GAClDhK,EACXb,EAAE+E,OAAOC,YAAYiH,0BAA0B1G,EAAMsF,GAErD7K,EAAE+E,OAAOC,YAAYiH,0BAA0BjM,EAAE,MAAOuF,GAAOsF", "file": "jquery.ui-contextmenu.min.js"}