/* globals excelRound, $:false */
$(() => {
    'use strict';

    $(`[name="town_name"],
            [name="location_name"],
            [name="functional_spec_name"],
            [name="quality_name"],
            [name="size_name"]`
    ).on('change', function () {
        let objectsSelect = parseFloat($('#objectsSelect option:selected').data('extended-value') || 0) / 100;

        let town_name = parseFloat($('[name="town_name"] option:selected').data('extended-value') || 0);
        let location_name = parseFloat($('[name="location_name"] option:selected').data('extended-value') || 0);
        let functional_spec_name = parseFloat($('[name="functional_spec_name"] option:selected').data('extended-value') || 0);
        let size_name = parseFloat($('[name="size_name"] option:selected').data('extended-value') || 0);
        let quality_name = parseFloat($('[name="quality_name"] option:selected').data('extended-value') || 0);

        let sum = objectsSelect * (town_name * 0.6 + location_name * 0.2 + functional_spec_name * 0.05 + size_name * 0.05 + quality_name * 0.1);
        let capital_rate = objectsSelect + sum;
        let long_term_growth = parseFloat($('[name="long_term_growth"]').fullValue() || 0) / 100;
        let discount_rate = capital_rate + long_term_growth;

        $('#baseCapitalizationNorm').fullValue(excelRound(objectsSelect * 100, 3));
        $('[name="capital_rate"]').fullValue(excelRound(capital_rate * 100, 3)).keyup();
        $('[name="discount_rate"]').fullValue(discount_rate * 100).keyup();
    });
});
