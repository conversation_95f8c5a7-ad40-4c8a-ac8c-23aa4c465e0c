$(() => {
    $('#main').on('click', '#searchAnalogues', function () {
        $(this).closest('form').submit();
    });
    /*$('.search').on('change', '[name="analog_object_report_type"]', function (e){
    	if($(this).val() == ''){
    		return;
    	}

    	const action = $('form').prop('action');
        const object = $('[name="object"]').val();
        const analog_object_report_type = $('[name="analog_object_report_type"]').val();
        const analog_object_name_type_id = $('[name="analog_object_name_type_id"]').val();
        const analog_object_name_type = $('[name="analog_object_name_type"]').val();
    	
    	window.location = action+'?object=' + object
    		//+ '&analog_object_report_type=' + analog_object_report_type
                + '&analog_object_name_type_id=' + analog_object_name_type_id
                + '&analog_object_name_type=' + analog_object_name_type;
    });*/
    
    // The AK triggers change on the ID field. 
    // As it is a hidden type, no other condition may trigger a change event on it
    $('.search').on('change', '[name="analog_object_name_type_id"]', function (e){
    	if($(this).val() == ''){
    		return;
    	}

        setTimeout(function(){
            const action = $('form').prop('action');
            const object = $('[name="object"]').val();
            //const analog_object_report_type = $('[name="analog_object_report_type"]').val();
            const analog_object_name_type_id = $('[name="analog_object_name_type_id"]').val();
            const analog_object_name_type = $('[name="analog_object_name_type"]').val();
             window.onbeforeunload = null; 
            window.location = action+'?object=' + object
                //+ '&analog_object_report_type=' + analog_object_report_type
                + '&analog_object_name_type_id=' + analog_object_name_type_id
                + '&analog_object_name_type=' + analog_object_name_type;
        }, 50);
    });
    
    $('.search-result-table').on('click', 'input[type="checkbox"]', function (e) {
    	const selectedNum = $('.search-result-table input[type="checkbox"]:checked').length;
    	const analogAdderWrapper = $('.analog-adder-wrapper');

        if(selectedNum > 0 && analogAdderWrapper.is('.hidden')){
        	analogAdderWrapper.removeClass('hidden');
        } else if(selectedNum == 0 && !analogAdderWrapper.is('.hidden')) {
        	analogAdderWrapper.addClass('hidden');
        }
    });
});