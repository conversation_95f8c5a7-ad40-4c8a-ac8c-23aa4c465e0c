<?php
chdir(dirname(dirname(__DIR__)));
$config = include 'config/local.php';
$logPath = $config['log_path'] ?? 'data/logs/';
session_start();
if(!isset($_SESSION['_log_access']) || !$_SESSION['_log_access']){
    if(!empty($_POST) && isset($_POST['username'])){
        if($_POST['username'] == 'diag' && $_POST['password'] == 'diag%alvis13'){
            $_SESSION['_log_access'] = true;
            header('Location: logs.php');
            exit;
        }
    }
?>
?><html>
<head>
    <meta charset="utf-8">
</head>
<body>
    <form action="" method="post">
        <fieldset>
            <legend>Log in</legend>
            <div><label>Username: <input type="text" name="username" value="<?=$_POST['username']??''?>"/></label></div>
            <div><label>Password: <input type="password" name="password" value=""/></label></div>
            <div><button>Log in</button></div>
        </fieldset>
    </form>
<?php
} elseif(isset($_GET['a']) && $_GET['a'] == 'view' && isset($_GET['logfile'])) {
    $logFile = $logPath.$_GET['logfile'];
    if(file_exists($logFile)){
        echo "<pre>".file_get_contents($logFile)."</pre>";
    }
} elseif(isset($_GET['a']) && $_GET['a'] == 'deleteAll'){
    $i=0;
    $f=0;
    $deleted='';
    $failed='';
    foreach(scandir($logPath) as $v) {
        if($v == '.' || $v == '..') {
            continue;
        }
        if(unlink($logPath.$v)){
            $deleted .= $logPath.$v.PHP_EOL;
            $i++;
        }else {
            $failed .= $logPath.$v.PHP_EOL;
            $f++;
        }


    }
    echo "<pre>Deleted {$i} files</pre>";
    echo "<pre>".$deleted."</pre>";
    echo "<pre>Failed {$f} files</pre>";
    echo "<pre>".$failed."</pre>";

}else{
?>
    <form>
        <fieldset>
            <legend></legend>
            <div><label>Term: <input type="text" name="q" value="<?=$_GET['q']??''?>"/></label></div>
            <div><button>Search</button></div>
        </fieldset>
        <div><a href="javascript:if(confirm('Are you absolutely sure?')){window.location='?a=deleteAll';}">Delete all logs!</a></div>
    </form>
    <div>
        <div>path: <?=realpath($logPath)?></div>
        <ul>
            <?php
            if(isset($_GET['q'])){
                $wd = 1000;
                foreach(scandir($logPath) as $v) {
                    if($wd-- <= 0){
                        break;
                    }
                    if($v == '.' || $v == '..' || !preg_match("/{$_GET['q']}/",$v,$m)){
                        continue;
                    }
                ?>
                    <li><a href="?a=view&logfile=<?=$v?>"><?=$v?></a></li>
                <?php }
            }?>
        </ul>
        <?= (isset($wd) && $wd<=0) ? '<div>...List is truncated to 1000 results!</div>' : ''?>
    </div>
    <?php
}
 ?>
</body>
</html>
<?php
