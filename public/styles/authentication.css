body {
    font-size: 20px;
}

#imageHolder {
    box-sizing: border-box;
    grid-area: imageHolder;
    display: grid;
    width: 100vw;
    grid-template-columns: 1fr 6fr;
    margin-top: 0;
    padding-left: 35px;
    border-bottom: 4px solid rgb(67, 91, 68);
}
#imageHolder div:first-child img {
    padding: 7px 0 0 0;
    width: 169px;
}
#imageHolder div:last-child {
    text-align: right;
}

#imageHolder div:last-child img {
    padding: 4px 58px 0 0;
    width: 583px;
}

.wrapper {
    min-height: 99vh;
    display: grid;
    grid-template-rows: 76px auto 26px;
    grid-template-columns: 150px 10fr;
    grid-template-areas:
            "imageHolder imageHolder"
            "loginContainer loginContainer"
            "footer footer";
}

#loginButton {
    background-color: rgb(42, 177, 75);
    border: 0;
    font-family: Verdana;
    padding: 3px 5px;
    border-radius: 5px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 1rem;
    cursor: pointer;
    color: rgb(255, 255, 255);
    width: 166px;
    font-weight: 900;
    margin: 0 auto 0 auto;
}
#content {
    box-sizing: border-box;
    background-image: url("../images/background_login.jpg?v=13");
    background-repeat: no-repeat;
    background-size: 100vw calc(100vw / 1.78);
    background-position: bottom -40px center;
    background-color: #1D1D1D;
    text-align: center;
    width: 100vw;
    display: flex;
}
#content_logo{
    background-image: url("../images/background_logo.png?v=13");
    background-repeat: no-repeat;
    background-size: calc(100vw / 3.0) Calc(calc(100vw / 3.0) / 2.833);
    background-position: calc(100% - calc(100vw / 20.0)) calc(100% - calc(100vw / 15.0));
    flex: 1;
}



@media only screen and (max-width: 600px) {
    #content_logo{
        background-size: calc(100vw / 2.1) Calc(calc(100vw / 2.1) / 2.833);
        background-position: calc(100% - calc(100vw / 20.0)) calc(100% - calc(100vw / 15.0));
        flex: 1;
    }
}

#loginContainer {
    max-width: 260px;
    text-align: center;
    margin: calc(100vw / 19) auto 0;
    grid-area: loginContainer;
}

#loginForm {
    display: grid;
    grid-template-rows: 36px 47px 36px 25px 33px calc(100vw / 30);
    padding: 20px 0;
    border-radius: 10px;
    align-items: center;
}

#loginForm input {
    background: #f2f2f2;
    border: 0;
    border-radius: 5px;
    width: 100%;
    max-width: 255px;
    height: 34px;
    padding: 5px;
    margin: 0 auto;
    font-family: FontAwesome,Verdana;
    font-size: 1.0rem;
}

#passRestore {
    font-size: 0.8em;
    text-decoration: none;
    color: #fff;
    cursor: pointer;
}

@media only screen and (max-width: 1050px) {
    .wrapper {
        grid-template-rows: calc(100vw / 14) auto 30px;
    }
    #imageHolder {
        grid-template-columns: 1fr 4.8fr;
    }
    #imageHolder img {
        box-sizing: border-box;
        padding:  4px 0 0 0;
        margin: 0 0 0 0;
        width: 95% !important;
    }

    #imageHolder div:last-child img {
        width: 76% !important;
    }
}
@media only screen and (max-width: 700px) {
    .wrapper {
        grid-template-rows: calc(100vw / 7.2) auto 30px;
    }
    #imageHolder {
        grid-template-columns: 2fr 3.5fr;
    }
    #imageHolder div:last-child img {
        padding:  calc(100vw / 29) 16px 0 0;
        width: 90% !important;
    }
}

p.error {
    color: red;
    padding: 16px;
    border-radius: 4px;
    background: rgba(255, 0, 0, 0.3);
}

#footer {
    grid-area: footer;
    color: rgb(67, 91, 68);
    border-top: 5px solid rgb(67, 91, 68);
    text-align: center;
    font-weight: 700;
    font-size: 0.6rem;
    margin-bottom: 3px;
}

#errorBox {
    font-size: 0.8em;
}
