.custom-table {
    --image-container-width: 445px;
    --image-max-width: 400px;
    --image-max-height: 250px;
}

.header-row {
    display: grid;
    grid-template-columns: var(--image-container-width) 1fr var(--image-container-width) 1fr;
    font-weight: bold;
    text-align: center;
}

.th-style {
    background-color: var(--generalizing-color);
    border: 1px solid black;
    color: var(--text-color);
}

.table-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.td-style {
    background-color: var(--section-color);
    display: grid;
    grid-template-columns: var(--image-container-width) 1fr;
    grid-template-areas: "picture pics" "selects selects";
}

.gridPicture {
    grid-area: picture;
}

.gridPicture img {
    margin-right: 5px;
}
.gridPicture img:not([src]) {
    width: var(--image-max-width);
}

.gridPicture,
.gridPicks {
    height: var(--image-max-height);
}

.gridPicks {
    grid-area: pics;
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 2fr 2fr;
}

.gridPicks input[type="radio"] {
    margin-right: 5px;
}

.gridSelects {
    grid-area: selects;
}

.td-style > div {
    padding: 3px;
    border: 1px solid black;
}

.td-style textarea {
    width: 100%;
}

.td-style input {
    margin-bottom: 5px;
    margin-top: 5px;
}

.td-style label {
    text-align: right;
}

input[name^="image_position["],
select[name^="image_main["] {
    width: 60px;
    margin: 0 0 0 5px;
}

label[for="image_upload_main_1"] {
    display: none;
}

#filesDocuments .group-table-div {
    width: 1050px;
}

[name="doc_group"] {
    width: 1000px;
}

[name="doc_group"] thead th:nth-child(2) {
    width: 250px !important;
}

[name="doc_group"] thead th:nth-child(3) {
    width: 400px !important;
}

[name="doc_group"] thead th:nth-child(4) {
    width: 150px !important;
}

[name="doc_group"] thead th:nth-child(5) {
    /*width: 150px !important;*/
}

.doc_comments {
    display: grid;
    grid-template-columns: 2fr 40px;
    grid-column-gap: 10px;
}

.doc_comments textarea[name="doc_comments"] {
    width: calc(100% - 7px);
}


@media screen and (max-width: 1750px) {
    .custom-table {
        --image-container-width: 390px;
        --image-max-width: 350px;
        --image-max-height: 200px;
    }

    .header-row {
        grid-template-columns: var(--image-container-width) 1fr var(--image-container-width) 1fr;
    }

    .td-style {
        grid-template-columns: var(--image-container-width) 1fr;
    }

    .gridPicture,
    .gridPicks {
        height: var(--image-max-height);
    }
}

@media screen and (max-width: 1450px) {
    .gridPicks > div:nth-child(1),
    .gridPicks > div:nth-child(2) {
        display: none;
    }
    .gridPicks {
        grid-template-rows: 1fr 1fr 1fr 1fr;
    }

    .custom-table {
        --image-container-width: 290px;
        --image-max-width: 250px;
        --image-max-height: 150px;
    }

    .header-row {
        grid-template-columns: var(--image-container-width) 1fr var(--image-container-width) 1fr;
    }

    .td-style {
        grid-template-columns: var(--image-container-width) 1fr;
    }


    .gridPicture,
    .gridPicks {
        height: var(--image-max-height);
    }
}
