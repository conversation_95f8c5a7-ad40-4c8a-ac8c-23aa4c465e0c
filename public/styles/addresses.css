#mapErrorsContainer {
    margin-top: 5px;
    width: 100%;
    height: calc(100% - 40px);
    text-align: center;
    color: red;
}

#map {
    margin-top: 5px;
    margin-bottom: 15px;
    width: 100%;
    height: calc(100% - 40px);
    z-index: 1001;
}

#address .row-one {
    grid-template-columns: 1fr 1fr 1fr;
    margin-bottom: 5px;
}

#address .combobox-input {
    width: calc(100% - 29px);
}
#address input:not(.combobox-input) {
    width: calc(100% - 37px);
}
#address textarea {
    width: calc(100% - 28px);
}

#address .row-two {
    grid-template-columns: 1fr 2fr 2fr 3fr 1fr 1fr;
}

.row-two .latLonContainer {
    grid-template-columns: 1fr 1fr;
}

.gpsButtonsContainer {
    display: flex;
    justify-content: center;
}
.row-two .mapButtonContainer {
    text-align: center;
}

#mapButton, #mapButton1, #mapButton2, #mapButton3, #sameAddressReportsButton {
    background-color: var(--generalizing-color);
    border-radius: 4px;
    padding: 4px 8px;
    width: 100%;
    cursor: pointer;
}

#sameAddressReportsButton {
    color: #f00;
}

textarea[name="full_description"],
textarea[name="market_desc_info"] {
    height: 9em;
}

textarea[name="disas_check_desc_hidden"] {
    height: 9em;
}
.optionsRow {
    margin-top: 5px;
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.map_buttons {
    display: flex;
}

.insert_coordinates_btn {
    flex: 1;
    text-align: right;
}

#disasters .row-one {
    grid-template-columns: repeat(4, 1fr);
    row-gap: 5px;
}

#verifyMessage{
    display: inline-block;
    float: left;
    margin-right: 5px;
}

#legend .info-legend {
    margin: 5px 0 5px 0;
    padding: 5px;
    background-color: var(--section-color);
}