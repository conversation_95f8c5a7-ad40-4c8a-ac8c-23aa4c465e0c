table[name="buildings_table"] th:nth-child(1) {
    width: 40px;
}

.residential-building-search-container {
    width: 50%;
}

.filter-fields {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 5px;
    grid-row-gap: 5px;
    margin-bottom: 5px;
}
.buttons-container:after {
    content: '';
    clear: both;
    display: table;
}

.buttons-container .form-group {
    display: inline-block;
}

.buttons-container .form-group:last-child {
    margin-right: 0;
    float: right;
}

.noresults {
    text-align: center;
    color: var(--text-color);
    font-weight: bold;
    background-color: var(--generalizing-color);
    padding: 1px 0 3px;
}
