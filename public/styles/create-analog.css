table .ac-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center
}
table .ac-container i {
  flex: 15px;
  margin-left: 10px;
}

table tr.showDropdown td.analog-value [name^="ind_analog_text["]{
  display: none;
}
table tr.showText td.analog-value .ac-container {
  display: none;
}

.row {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	grid-column-gap: 4px;
	grid-row-gap: 4px;
}

.checkboxgroup {
	grid-template-columns: 1fr 1fr;
	grid-gap: 8px;
}
.checkboxgroup>div>input {
	width: 16px;
	height: 16px;
	margin: 0px;
	vertical-align: middle;
}
.checkboxgroup>div>span {
	vertical-align: middle;
}

input[name^="analog_other_info["] {
	vertical-align: middle;
}