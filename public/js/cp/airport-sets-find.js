onReady().then(()=>{
    cpPrepAirportFilterForSet();
});

function cpPrepAirportFilterForSet() {
    document.querySelectorAll('.inp-airport:not(._ak--ready)').forEach(el => {
        const val = el .value;
        const text = el .dataset.text;
        let firstRun = true;
        const dm = new ej.data.DataManager({url: el.dataset.endpoint, adaptor: new ej.data.UrlAdaptor()});
        const airportEj = new ej.dropdowns.AutoComplete({
            dataSource: dm,
            query: new ej.data.Query().select(['value']).take(20),
            enablePersistence: false,
            allowCustom: false,
            highlight: true,
            htmlAttributes: true,
            minLength: 3,
            ignoreAccent: true,
            label: el .closest('label'),
            placeholder: el .placeholder,
            fields: {value: 'value', text: 'label'},
            dataBound: function (e) {
                if (firstRun && val) {
                    this.addItem({value: val, label: text});
                    this.value = val;
                    firstRun = false;
                }
            },
            select: function (e) {
                const input = document.querySelector(".airport-codes-input");
                if (input.value.length !== 0 && input.value.slice(-1) !== ",") {
                    input.value += "," + e.itemData.value;
                } else {
                    input.value += e.itemData.value;
                }
                this.hidePopup();
                setTimeout(() => {
                    this.clear();
                }, 100);
            },
            popupWidth: '100%',
            noRecordsTemplate:"<span class='wz_option wz_option--norecord'>Няма намерени летища</span>",
            actionFailureTemplate:"<span class='wz_option wz_option--action-failure'>Моля поправете въведеният текст</span>",
            itemTemplate: '<span class="wz_option"><span class="wz_option--main">${label}</span></span>',
        });
        airportEj.appendTo(el);
        if (val) {
            airportEj.value = val;
        }

        el.classList.add('_ak--ready')
    });
}
