<?php

/**
 * Local Configuration Override
 *
 * This configuration override file is for overriding environment-specific and
 * security-sensitive configuration information. Copy this file without the
 * .dist extension at the end and populate values as needed.
 *
 * NOTE: This file is ignored from Git by default with the .gitignore included
 * in laminas-mvc-skeleton. This is a good practice, as it prevents sensitive
 * credentials from accidentally being committed into version control.
 */

return [
    'db' => [
        'driver' => 'Pdo',
        'dsn'    => 'mysql:dbname=$DB_SCHEMA;host=$DB_HOST;charset=utf8',
        'username'   => '$DB_USERNAME',
        'password'   => '$DB_PASSWORD'
    ],

    'wizapi' => [
        'endpoint' => "http://wizapi.fiestatravel.bg/?controller=api&u=wiza_api&p=911389P7l84tqlP",
        'logRequests' => true,
    ],

    'devIps' => [
        '**************'
    ],

    'commands' => [
        'cache' => [
            'log' => true,
        ],
    ],

    'control-panel' => [
        'session' => [
            'name' => 'wizatour-cp'
        ]
    ]
];
