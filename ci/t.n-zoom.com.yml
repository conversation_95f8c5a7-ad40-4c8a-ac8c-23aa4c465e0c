cache: &global_cache
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - vendor/

stages:
  - build
  - review
  - deploy

########## t.n-zoom.com (linux)

build:t:
  stage: build
  script:
    - echo "Stage build"
    - composer --quiet --no-dev install
  # see: https://docs.gitlab.com/ee/ci/jobs/job_rules.html
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_REF_NAME =~ /^v[0-9]{2}\.[0-9]{2}\.?[0-9]{0,2}$/
    - if: $CI_COMMIT_BRANCH == 'alvis_1.0'
      when: never
  tags:
    - t

review:t:
  stage: review
  cache:
    # inherit all global cache settings
    <<: *global_cache
    policy: pull
  needs: ["build:t"]
  script:
    - echo "Stage review"
    - chmod -R 777 ./data/cache
    - mkdir ./data/logs
    - chmod -R 777 ./data/logs
    - echo $'<?php\nreturn "'"$CI_COMMIT_SHA"$'";\n' > ./data/revision.php
    - cat "$LOCAL_PHP" | envsubst > ./config/local.php
    - mkdir -p $DEPLOY_PATH/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG
    - rsync -a --delete --exclude '.git' --exclude 'bin' --exclude 'data/cache/cookiejars/*' . $DEPLOY_PATH/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG
  environment:
    name: t/$CI_COMMIT_REF_NAME
    url: $DEPLOY_URL/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/public
    on_stop: stop:review:t
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'alvis_1.0'
      when: never
  tags:
    - t

stop:review:t:
  stage: review
  script:
    - echo "Stop review"
    - rm -rf $DEPLOY_PATH/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
  environment:
    name: t/$CI_COMMIT_REF_NAME
    action: stop
  tags:
    - t

# review2 is test, and will be connected to different nZoom installation (test.advanceaddress).
variables:
  REVIEW2_INSTALL_DIR: "alvistmp"

review2:t:
  stage: review
  cache:
    # inherit all global cache settings
    <<: *global_cache
    policy: pull
  needs: ["build:t"]
  script:
    - echo "Stage review2"
    - chmod -R 777 ./data/cache
    - mkdir ./data/logs
    - chmod -R 777 ./data/logs
    - echo $'<?php\nreturn "'"$CI_COMMIT_SHA"$'";\n' > ./data/revision.php
    - cat "$LOCAL_PHP" | envsubst > ./config/local.php
    - mkdir -p $DEPLOY_PATH/$REVIEW2_INSTALL_DIR/$CI_COMMIT_REF_SLUG
    - rsync -a --delete --exclude '.git' --exclude 'bin' --exclude 'data/cache/cookiejars/*' . $DEPLOY_PATH/$REVIEW2_INSTALL_DIR/$CI_COMMIT_REF_SLUG
  environment:
    name: t/${CI_COMMIT_REF_SLUG}_tmp
    url: $DEPLOY_URL/$REVIEW2_INSTALL_DIR/$CI_COMMIT_REF_SLUG/public
    on_stop: stop:review2:t
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'alvis_1.0'
      when: never
  tags:
    - t

stop:review2:t:
  stage: review
  script:
    - echo "Stop review2"
    - rm -rf $DEPLOY_PATH/$REVIEW2_INSTALL_DIR/$CI_COMMIT_REF_SLUG
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
  environment:
    name: t/${CI_COMMIT_REF_SLUG}_tmp
    action: stop
  tags:
    - t

deploy:t:
  stage: deploy
  when: manual
  cache:
    # inherit all global cache settings
    <<: *global_cache
    policy: pull
  needs: ["build:t"]
  script:
    - echo "Stage deploy"
    - chmod -R 777 ./data/cache
    - mkdir ./data/logs
    - chmod -R 777 ./data/logs
    - echo $'<?php\nreturn "'"$CI_COMMIT_REF_SLUG"$'";\n' > ./data/revision.php
    - cat "$LOCAL_PHP" | envsubst > ./config/local.php
    - mkdir -p $DEPLOY_PATH/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG
    - rsync -a --delete --exclude '.git' --exclude 'bin' --exclude 'data/cache/cookiejars/*' --exclude 'data/logs/*' . $DEPLOY_PATH/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG
  environment:
    name: t/$CI_COMMIT_REF_NAME
    url: $DEPLOY_URL/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/public
    on_stop: stop:deploy:t
  rules:
    # e.g. v20.03, v20.03.01
    # see: https://gitlab.bgservice.net/nZoomStack/alvis/-/wikis/%D0%A2%D0%B0%D0%B3%D0%BE%D0%B2%D0%B5%20%D0%B8%20%D1%80%D0%B5%D0%B2%D0%B8%D0%B7%D0%B8%D0%B8
    - if: $CI_COMMIT_REF_NAME =~ /^v[0-9]{2}\.[0-9]{2}\.?[0-9]{0,2}$/
  tags:
    - t

stop:deploy:t:
  stage: deploy
  variables:
    GIT_STRATEGY: none
  script:
    - echo "Stop deploy"
    - rm -rf $DEPLOY_PATH/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG
  when: manual
  environment:
    name: t/$CI_COMMIT_REF_NAME
    action: stop
  rules:
    # e.g. v20.03, v20.03.01
    # see: https://gitlab.bgservice.net/nZoomStack/alvis/-/wikis/%D0%A2%D0%B0%D0%B3%D0%BE%D0%B2%D0%B5%20%D0%B8%20%D1%80%D0%B5%D0%B2%D0%B8%D0%B7%D0%B8%D0%B8
    - if: $CI_COMMIT_REF_NAME =~ /^v[0-9]{2}\.[0-9]{2}\.?[0-9]{0,2}$/
  tags:
    - t



