cache: &global_cache
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - vendor/

stages:
  - build
  - review
  - deploy

########## tt.n-zoom.com (windows)

build:tt:
  stage: build
  script:
    - echo "Stage build"
    - composer --quiet --no-dev install
  # see: https://docs.gitlab.com/ee/ci/jobs/job_rules.html
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_REF_NAME =~ /^v[0-9]{2}\.[0-9]{2}\.?[0-9]{0,2}$/
    - if: $CI_COMMIT_BRANCH == 'alvis_1.0'
      when: never
  tags:
    - tt

review:tt:
  stage: review
  cache:
    # inherit all global cache settings
    <<: *global_cache
    policy: pull
  needs: ["build:tt"]
  script:
    - echo "Stage review"
    - php -r "file_put_contents('data\revision.php', '<?php`nreturn \'$CI_COMMIT_SHA\';');"
    # Use PHP to get the local configuration, and remove any BOM in the file.
    - php -r "file_put_contents(`'config/local.php`', str_replace(chr(239).chr(187).chr(191), `'`', file_get_contents(`'$LOCAL_PHP`')));"
    # replaced because of BOM: - cp "$LOCAL_PHP" config\local.php
    - $deployPath = "$DEPLOY_PATH\$CI_PROJECT_NAME\$CI_COMMIT_REF_SLUG"
    - if (!(test-path $deployPath)) {New-Item -ItemType Directory -Force -Path $deployPath}
    # see: https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/robocopy
    # /mir applies /e (recursive subdirectories) + /purge (deletes missing folders and directories)
    # /xd dir1 dir2 - exclude directories
    # /nfl - file names are not to be logged.
    # /ndl - directory names are not to be logged
    # robocopy exits with error code 1 - so change the error level
    - (robocopy . $deployPath /nfl /ndl /mir /xd bin .git ) -or ($LASTEXITCODE=0)
  environment:
    name: tt/$CI_COMMIT_REF_NAME
    url: $DEPLOY_URL/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/public
    on_stop: stop:review:tt
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'alvis_1.0'
      when: never
  tags:
    - tt

stop:review:tt:
  stage: review
  script:
    - echo "Stop review"
    - $deployPath = "$DEPLOY_PATH\$CI_PROJECT_NAME\$CI_COMMIT_REF_SLUG"
    - if (test-path $deployPath) {Remove-Item -Recurse -Force $deployPath}
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
  environment:
    name: tt/$CI_COMMIT_REF_NAME
    action: stop
  tags:
    - tt

# review2 is test, and will be connected to different nZoom installation (test.advanceaddress).
variables:
  REVIEW2_INSTALL_DIR: "alvistmp"

review2:tt:
  stage: review
  cache:
    # inherit all global cache settings
    <<: *global_cache
    policy: pull
  needs: ["build:tt"]
  script:
    - echo "Stage review2"
    - php -r "file_put_contents('data\revision.php', '<?php`nreturn \'$CI_COMMIT_SHA\';');"
    # Use PHP to get the local configuration, and remove any BOM in the file.
    - php -r "file_put_contents(`'config/local.php`', str_replace(chr(239).chr(187).chr(191), `'`', file_get_contents(`'$LOCAL_PHP`')));"
    # replaced because of BOM: - cp "$LOCAL_PHP" config\local.php
    - $deployPath = "$DEPLOY_PATH\$REVIEW2_INSTALL_DIR\$CI_COMMIT_REF_SLUG"
    - if (!(test-path $deployPath)) {New-Item -ItemType Directory -Force -Path $deployPath}
    # see: https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/robocopy
    # /mir applies /e (recursive subdirectories) + /purge (deletes missing folders and directories)
    # /xd dir1 dir2 - exclude directories
    # /nfl - file names are not to be logged.
    # /ndl - directory names are not to be logged
    # robocopy exits with error code 1 - so change the error level
    - (robocopy . $deployPath /nfl /ndl /mir /xd bin .git ) -or ($LASTEXITCODE=0)
  environment:
    name: tt/${CI_COMMIT_REF_SLUG}_tmp
    url: $DEPLOY_URL/$REVIEW2_INSTALL_DIR/$CI_COMMIT_REF_SLUG/public
    on_stop: stop:review2:tt
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'alvis_1.0'
      when: never
  tags:
    - tt

stop:review2:tt:
  stage: review
  script:
    - echo "Stop review2"
    - $deployPath = "$DEPLOY_PATH\$REVIEW2_INSTALL_DIR\$CI_COMMIT_REF_SLUG"
    - if (test-path $deployPath) {Remove-Item -Recurse -Force $deployPath}
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
  environment:
    name: tt/${CI_COMMIT_REF_SLUG}_tmp
    action: stop
  tags:
    - tt

deploy:tt:
  stage: deploy
  when: manual
  cache:
    # inherit all global cache settings
    <<: *global_cache
    policy: pull
  needs: ["build:tt"]
  script:
    - echo "Stage deploy"
    - php -r "file_put_contents('data\revision.php', '<?php`nreturn \'$CI_COMMIT_SHA\';');"
    # Use PHP to get the local configuration, and remove any BOM in the file.
    - php -r "file_put_contents(`'config/local.php`', str_replace(chr(239).chr(187).chr(191), `'`', file_get_contents(`'$LOCAL_PHP`')));"
    # replaced because of BOM: - cp "$LOCAL_PHP" config\local.php
    - $deployPath = "$DEPLOY_PATH\$CI_PROJECT_NAME\$CI_COMMIT_REF_SLUG"
    - if (!(test-path $deployPath)) {New-Item -ItemType Directory -Force -Path $deployPath}
    # see: https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/robocopy
    # /mir applies /e (recursive subdirectories) + /purge (deletes missing folders and directories)
    # /xd dir1 dir2 - exclude directories
    # /nfl - file names are not to be logged.
    # /ndl - directory names are not to be logged
    # robocopy exits with error code 1 - so change the error level
    - (robocopy . $deployPath /nfl /ndl /mir /xd bin .git ) -or ($LASTEXITCODE=0)
  environment:
    name: tt/$CI_COMMIT_REF_NAME
    url: $DEPLOY_URL/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/public
    on_stop: stop:deploy:tt
  rules:
    # e.g. v20.03, v20.03.01
    # see: https://gitlab.bgservice.net/nZoomStack/alvis/-/wikis/%D0%A2%D0%B0%D0%B3%D0%BE%D0%B2%D0%B5%20%D0%B8%20%D1%80%D0%B5%D0%B2%D0%B8%D0%B7%D0%B8%D0%B8
    - if: $CI_COMMIT_REF_NAME =~ /^v[0-9]{2}\.[0-9]{2}\.?[0-9]{0,2}$/
  tags:
    - tt

stop:deploy:tt:
  stage: deploy
  variables:
    GIT_STRATEGY: none
  script:
    - echo "Stop deploy"
    - $deployPath = "$DEPLOY_PATH\$CI_PROJECT_NAME\$CI_COMMIT_REF_SLUG"
    - if (test-path $deployPath) {Remove-Item -Recurse -Force $deployPath}
  when: manual
  environment:
    name: tt/$CI_COMMIT_REF_NAME
    action: stop
  rules:
    # e.g. v20.03, v20.03.01
    # see: https://gitlab.bgservice.net/nZoomStack/alvis/-/wikis/%D0%A2%D0%B0%D0%B3%D0%BE%D0%B2%D0%B5%20%D0%B8%20%D1%80%D0%B5%D0%B2%D0%B8%D0%B7%D0%B8%D0%B8
    - if: $CI_COMMIT_REF_NAME =~ /^v[0-9]{2}\.[0-9]{2}\.?[0-9]{0,2}$/
  tags:
    - tt



