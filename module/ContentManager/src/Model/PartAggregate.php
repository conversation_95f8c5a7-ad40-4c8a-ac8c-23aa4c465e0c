<?php

namespace ContentManager\Model;

class PartAggregate
{
    private $callable;
    private array $params;

    public function __construct(callable $callable, array $params)
    {
        $this->callable = $callable;
        $this->params = $params;
    }

    public function getResource()
    {
        return call_user_func_array($this->callable, $this->params);
    }

    public function __serialize(): array
    {
        return [
            'callable' => $this->callable,
            'params' => $this->params
        ];
    }
}
