<?php
namespace Wizatour\Model;

use <PERSON><PERSON>\Db\Sql\ExpressionInterface;
use <PERSON><PERSON>\Db\Sql\Join;
use <PERSON><PERSON>\Db\Sql\Predicate\Between;
use <PERSON><PERSON>\Db\Sql\Predicate\Expression;
use <PERSON><PERSON>\Db\Sql\Predicate\In;
use <PERSON><PERSON>\Db\Sql\Predicate\Operator;
use <PERSON><PERSON>\Db\Sql\Predicate\Predicate;
use Laminas\Db\Sql\Predicate\PredicateSet;
use <PERSON>inas\Db\Sql\Select;
use <PERSON><PERSON>\Db\Sql\Update;
use Laminas\Db\TableGateway\TableGateway;

class DiscountCodeWithBatchTable extends DiscountCodeTable
{
    protected function createSelect(): Select
    {
        $select = parent::createSelect();
        $this->decorateSelectForBatch($select);
        return $select;
    }

    private function decorateSelectForBatch(Select $select): void
    {
        $select->join(['dcb'=>'discount_code_batch'], "dcb.code={$this->tbl}.batch", [
            'batch_name'=>'name',
            'batch_description'=>'description',
            'batch_discount_type'=>'discount_type',
            'batch_conditions'=>'conditions',
            'batch_valid_after'=>'valid_after',
            'batch_valid_until'=>'valid_until',
            'batch_active'=>'active',
            'batch_created'=>'created',
            'batch_updated'=>'updated',
        ], Join::JOIN_INNER);
    }

    private function getValidityPredicate(\DateTime $date): Predicate
    {
        $formattedDate = $date->format('Y-m-d H:i:s');
        $validity = new Predicate(null,Predicate::COMBINED_BY_AND);
        $validity->lessThan('dcb.valid_after', $formattedDate);
        $validity->greaterThan('dcb.valid_until', $formattedDate);
        return $validity;
    }

    private function getIsActivePredicate($value=1): Predicate
    {
        $activity = new Predicate();
        $activity->equalTo("{$this->tbl}.active", $value);
        $activity->equalTo('dcb.active', $value);
        return $activity;
    }

    private function getIsNotSpentPredicate(): Predicate
    {
        $activity = new Predicate(null, PredicateSet::COMBINED_BY_OR);
        $activity->isNull("{$this->tbl}.max_number_of_uses");
        $activity->greaterThan("{$this->tbl}.max_number_of_uses", "{$this->tbl}.number_of_uses",
                        ExpressionInterface::TYPE_IDENTIFIER, ExpressionInterface::TYPE_IDENTIFIER);
        return $activity;
    }

    public function fetchAllValid(?array $code = null, $sort=['created'=>'DESC'], $limit=100, $offset=0): \Laminas\Db\ResultSet\ResultSetInterface
    {
        $select = $this->createSelect();
        $predicate = new PredicateSet();
        if ($code) {
            $predicate->addPredicate(new In("{$this->tbl}.{$this->idCol}", $code));
        }
        $predicate->addPredicate($this->getValidityPredicate(new \DateTime('now')));
        $predicate->addPredicate($this->getIsActivePredicate());
        $predicate->addPredicate($this->getIsNotSpentPredicate());

        return $this->execSelect($select, $predicate, $sort, $limit, $offset);
    }

    public function fetchIfValid(string $code, array $batches=[]) : ?DiscountCodeWithBatch
    {
        $select = $this->createSelect();
        $predicate = new PredicateSet();
        $predicate->addPredicate(new Operator("{$this->tbl}.{$this->idCol}",Operator::OPERATOR_EQUAL_TO, $code));
        $predicate->addPredicate($this->getValidityPredicate(new \DateTime('now')));
        $predicate->addPredicate($this->getIsActivePredicate());
        $predicate->addPredicate($this->getIsNotSpentPredicate());
        if (!empty($batches)) {
            $predicate->addPredicate(new In("{$this->tbl}.batch", $batches));
        }
        return $this->execSelect($select, $predicate)->current();
    }

    public function update(DiscountCode $discountCode) : void
    {
        $data = $discountCode->asArray();

        if (array_key_exists('_batch', $data)) {
            unset($data['_batch']);
        }

        $this->tableGateway->update($data, new Operator("code", Operator::OPERATOR_EQUAL_TO, $discountCode->code));
    }

    public function updateBatch(DiscountCodeBatch $discountCodeBatch) : void
    {
        $batchData = $discountCodeBatch->asArray();
        $batchUpdate = new Update('discount_code_batch');
        $batchUpdate->set($batchData);
        $batchUpdate->where(new Operator("code", Operator::OPERATOR_EQUAL_TO, $discountCodeBatch->code));
        $batchStatement = $this->tableGateway->getSql()->prepareStatementForSqlObject($batchUpdate);
        $batchStatement->execute();
    }
}
