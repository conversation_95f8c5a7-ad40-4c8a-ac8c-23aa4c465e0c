<?php

namespace Wizatour\Model;

use ReturnTypeWillChange;

class AirportSetAirport extends \ArrayObject
{
    public $airport_set_ident;
    public $airport_code;
    public $priority;
    public $airport_name;
    public $i18n_airport_name;
    public $airport_location_name;
    public $set_name;
    public $country_name;
    public $i18n_country_name;
    public $region_name;
    public $i18n_region_name;
    public $city_name;
    public $i18n_city_name;

    #[ReturnTypeWillChange]
    public function exchangeArray($array): void
    {
        $this->airport_set_ident = !empty($array['airport_set_ident']) ? $array['airport_set_ident'] : null;
        $this->airport_code  = !empty($array['airport_code']) ? $array['airport_code'] : null;
        $this->priority = !empty($array['priority']) ? $array['priority'] : 1;
        $this->airport_name = !empty($array['airport_name']) ? $array['airport_name'] : null;
        $this->i18n_airport_name = !empty($array['i18n_airport_name']) ? $array['i18n_airport_name'] : null;
        $this->airport_location_name = !empty($array['airport_location_name']) ? $array['airport_location_name'] : null;
        $this->set_name = !empty($array['set_name']) ? $array['set_name'] : null;
        $this->country_name = !empty($array['country_name']) ? $array['country_name'] : null;
        $this->i18n_country_name  = !empty($array['i18n_country_name']) ? $array['i18n_country_name'] : null;
        $this->region_name  = !empty($array['region_name']) ? $array['region_name'] : null;
        $this->i18n_region_name  = !empty($array['i18n_region_name']) ? $array['i18n_region_name'] : null;
        $this->city_name  = !empty($array['city_name']) ? $array['city_name'] : null;
        $this->i18n_city_name  = !empty($array['i18n_city_name']) ? $array['i18n_city_name'] : null;
    }

    public function getAirportName() {
        return $this->i18n_airport_name ?: $this->airport_name;
    }

    public function getAirportLocationName() {
        return $this->airport_location_name;
    }

    /**
     * @return mixed
     */
    public function getCountryName()
    {
        return $this->i18n_country_name??$this->country_name;
    }

    /**
     * @return mixed
     */
    public function getRegionName()
    {
        return $this->i18n_region_name??$this->region_name;
    }

    /**
     * @return mixed
     */
    public function getCityName()
    {
        return $this->i18n_city_name??$this->city_name;
    }

}
