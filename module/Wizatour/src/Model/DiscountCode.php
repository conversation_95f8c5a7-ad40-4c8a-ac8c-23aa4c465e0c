<?php

namespace Wizatour\Model;

use ReturnTypeWillChange;

class DiscountCode extends \ArrayObject
{
    public static $fields = [
        'code'=>'code',
        'discount_value'=>'discountValue',
        'conditions'=>'conditions',
        'batch'=>'batch',
        'max_number_of_uses'=>'maxNumberOfUses',
        'number_of_uses'=>'numberOfUses',
        'active'=>'active',
        'updated'=>'updated',
        'created'=>'created',
    ];

    public $code;
    public $discountValue;
    public $conditions;
    public $batch;
    public $maxNumberOfUses;
    public $numberOfUses;
    public $active;
    public $updated;
    public $created;

    #[ReturnTypeWillChange]
    public function exchangeArray($array): void
    {
        foreach (self::$fields as $k=>$v) {
            if (array_key_exists($k, $array)) {
                if ($array[$k] === '') {
                    continue;
                }
                $this->$v = $array[$k];
            }
        }
    }

    public function asArray() : array
    {
        $array = [];
        foreach (self::$fields as $k=>$v) {
            $array[$k] = $this->$v??null;
        }
        return $array;
    }


}
