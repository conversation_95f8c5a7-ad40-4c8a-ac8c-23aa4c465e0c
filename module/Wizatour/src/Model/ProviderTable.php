<?php
namespace Wizatour\Model;

use Laminas\Db\TableGateway\TableGatewayInterface;

class ProviderTable
{
    private $tableGateway;

    public function __construct(TableGatewayInterface $tableGateway)
    {
        $this->tableGateway = $tableGateway;
    }

    public function fetchAll()
    {
        return $this->tableGateway->select();
    }

    public function getProvider($ident)
    {
        $rowset = $this->tableGateway->select(['ident' => $ident]);
        $row = $rowset->current();
        if (! $row) {
            throw new \RuntimeException(sprintf(
                'Could not find row with identifier %s',
                $ident
            ));
        }

        return $row;
    }

    public function addProvider(Provider $provider){
        $data = [
            'ident' => $provider->ident,
            'name'  => $provider->name,
            'type'  => $provider->type,
            'config'  => $provider->config,
            'credentials'  => $provider->credentials,
        ];

        $this->tableGateway->insert($data);
    }

    public function saveProvider(Provider $provider)
    {
        $data = [
            'ident' => $provider->ident,
            'name'  => $provider->name,
            'type'  => $provider->type,
            'config'  => $provider->config,
            'credentials'  => $provider->credentials,
        ];

        $ident = $provider->ident;

        try {
            $this->getProvider($ident);
        } catch (\RuntimeException $e) {
            throw new \RuntimeException(sprintf(
                'Cannot update album with identifier %s; does not exist',
                $ident
            ));
        }

        $this->tableGateway->update($data, ['ident' => $ident]);
    }

    public function deleteProvider($ident)
    {
        $this->tableGateway->delete(['ident' => $ident]);
    }
}
