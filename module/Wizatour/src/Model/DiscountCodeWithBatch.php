<?php

namespace Wizatour\Model;

use ReturnTypeWillChange;
use Symfony\Component\Yaml\Yaml;
use function Amp\Iterator\merge;

/**
 * Decorates the DiscountCode with a batc
 */
class DiscountCodeWithBatch extends DiscountCode
{
    #[ReturnTypeWillChange]
    public function exchangeArray($array): void
    {

        parent::exchangeArray($array);

        if (array_key_exists('batch_name', $array)) {
            $batchArray = $this->cleanSubObjectArray($array, 'batch_');
            $batchArray['code'] = $array['batch'];
            $this->batch = new DiscountCodeBatch();
            $this->batch->exchangeArray($batchArray);
        }
    }

    public function cleanSubObjectArray(array $array, $prefix) : array
    {
        $batchArray = [];
        $prefixLen = strlen($prefix);
        foreach ($array as $k=>$v) {
            if (substr($k, 0, $prefixLen) === $prefix) {
                $batchArray[substr($k, $prefixLen)] = $v;
            }
        }
        return $batchArray;
    }

    public function convertSub2Array($sub, $prefix='') : array
    {
        $array = [];
        foreach ($sub->asArray() as $k=>$v) {
            $array[$prefix.$k] = $v;
        }
        return $array;
    }

    public function asArray() : array
    {
        $array = parent::asArray();
        $array['batch'] = $this->batch->code;
        $array['_batch'] = $this->convertSub2Array($this->batch, '');
        return $array;
    }

    public function getFormattedValue() : string
    {
        if ($this->batch->discountType === DiscountCodeBatch::DISCOUNT_TYPE_CURRENCY) {
            return ($this->discountValue%1 ? $this->discountValue : round($this->discountValue)).'&nbsp;&euro;';
        }
        if ($this->batch->discountType === DiscountCodeBatch::DISCOUNT_TYPE_PERCENT) {
            return ($this->discountValue%1 ? $this->discountValue : round($this->discountValue)).'&nbsp;%';
        }

        return '';
    }

    public function calculateDiscountForPrice($price) : float
    {
        if ($this->batch->discountType === DiscountCodeBatch::DISCOUNT_TYPE_CURRENCY) {
            return (float) $this->discountValue;
        }
        if ($this->batch->discountType === DiscountCodeBatch::DISCOUNT_TYPE_PERCENT) {
            return (float) $this->discountValue * $price / 100;
        }
        return 0.0;
    }

    public function calculateDiscountedPrice($price) : float
    {
        return (float) $price - $this->calculateDiscountForPrice($price);
    }

    /**
     * @throws \Exception
     */
    public function validateConditions($price, $date) : bool
    {
        if ($this->conditions ?? false) {
            $conditions = Yaml::parse($this->conditions);
            if ($conditions && ! $this->checkConditions($conditions, $price, $date)) {
                return false;
            }
        }

        if ($this->batch->conditions ?? false) {
            $conditions = Yaml::parse($this->batch->conditions);
            if ($conditions) {
                return $this->checkConditions($conditions, $price, $date);
            }
        }

        return true;
    }

    private function checkConditions($conditions, $price, $date): bool
    {
        if (($conditions['minPrice'] ?? false) && $conditions['minPrice'] > $price) {
            return false;
        }
        if (($conditions['maxPrice'] ?? false) && $conditions['maxPrice'] < $price) {
            return false;
        }

        if (($conditions['maxReturnDate'] ?? false && strlen($conditions['maxReturnDate']) > 6)) {
            $maxReturnDate = new \DateTime(trim($conditions['maxReturnDate']));
            if ($date > $maxReturnDate) {
                return false;
            }
        }
        return true;
    }
}
