<?php

namespace Wizatour\Model;

use ReturnTypeWillChange;

class GeoRegion extends Region
{
    public $geo_giata_ref;

    #[ReturnTypeWillChange]
    public function exchangeArray($array): void
    {
        parent::exchangeArray($array);
        $this->geo_giata_ref  = !empty($array['geo_giata_ref']) ? $array['geo_giata_ref'] : null;
    }

    public function getGeoGiataRef()
    {
        return $this->geo_giata_ref;
    }

    public function setGeoGiataRef($geo_giata_ref)
    {
        $this->geo_giata_ref = $geo_giata_ref;
    }

}
