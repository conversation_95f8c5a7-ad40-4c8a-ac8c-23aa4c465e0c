<?php

namespace Wizatour\Model;

use ReturnTypeWillChange;
use Symfony\Component\Yaml\Yaml;

class Ibe extends \ArrayObject
{
    public $ident;
    public $ibeType;
    public $providerIdent;
    public $emailProviderIdent;
    public $theme;
    public $companyIdent;
    public $airportSetIdent;
    public $config;
    public $vouchersPool;
    public $active;

    public $provider;
    public $emailProvider;

    public $_siteTerms = [];
    public $_operatorTerms = [];

    private $downPaymentOptions;
    public ?Company $company = null;

    #[ReturnTypeWillChange]
    public function exchangeArray($array): void
    {
        $this->ident = ! empty($array['ident']) ? $array['ident'] : null;
        $this->ibeType = ! empty($array['ibeType']) ? $array['ibeType'] : null;
        $this->providerIdent  = ! empty($array['providerIdent']) ? $array['providerIdent'] : null;
        $this->emailProviderIdent  = ! empty($array['emailProviderIdent']) ? $array['emailProviderIdent'] : null;
        $this->theme  = ! empty($array['theme']) ? $array['theme'] : null;
        $this->companyIdent  = ! empty($array['companyIdent']) ? $array['companyIdent'] : null;
        $this->airportSetIdent  = ! empty($array['airportSetIdent']) ? $array['airportSetIdent'] : null;
        $this->config  = ! empty($array['config']) ? Yaml::parse($array['config']) : null;
        $this->vouchersPool  = ! empty($array['vouchersPool']) ? $array['vouchersPool'] : null;
        $this->active  = ! empty($array['active']) ? $array['active'] : null;

        if (array_key_exists('theme_name', $array)) {
            $themeArray = [
                'ident' => $array['theme'],
                'name' => $array['theme_name'],
                'config' => $array['theme_config'],
            ];
            $this->theme = new Theme();
            $this->theme->exchangeArray($themeArray);
        }

        if (array_key_exists('provider_name', $array)) {
            $providerArray = [
                'ident' => $array['providerIdent'],
                'name' => $array['provider_name'],
                'type' => $array['provider_type'],
                'config' => $array['provider_config'],
                'credentials' => $array['provider_credentials'],
            ];
            $this->provider = new Provider();
            $this->provider->exchangeArray($providerArray);
        }
        if (array_key_exists('emailprovider_name', $array)) {
            $providerArray = [
                'ident' => $array['emailProviderIdent'],
                'name' => $array['emailprovider_name'],
                'type' => $array['emailprovider_type'],
                'config' => $array['emailprovider_config'],
                'credentials' => $array['emailprovider_credentials'],
            ];
            $this->emailProvider = new Provider();
            $this->emailProvider->exchangeArray($providerArray);
        }
    }

    public function getSiteTerms(): ?Terms
    {
        if (! empty($this->_siteTerms)) {
            return $this->_siteTerms;
        }

        $siteTerms = $this->config['booking']['terms']['site'] ?? null;
        return $this->_siteTerms = ($siteTerms ? new Terms($siteTerms['label'] ?? '', $siteTerms['url'] ?? '') : null);
    }

    public function getOperatorTerms(string $operatorCode): ?Terms
    {
        if (! empty($this->_operatorTerms[$operatorCode])) {
            return $this->_operatorTerms[$operatorCode];
        }

        $operatorCode = strtolower(trim($operatorCode));
        $operatorsTerms = $this->config['booking']['terms']['operators'] ?? [];

        foreach ($operatorsTerms as $v) {
            if (! array_key_exists('codes', $v)) {
                return $this->_operatorTerms[$operatorCode] = new Terms(
                    $v['label'] ?? '',
                    $v['url'] ?? '',
                    ($v['showSiteTerms']??true) ? $this->getSiteTerms() : null);
            }

            $codesArr = array_map(
                static function ($val) {
                    return strtolower(trim($val));
                },
                $v['codes']
            );
            if (in_array($operatorCode, $codesArr, true)) {
                return $this->_operatorTerms[$operatorCode] = new Terms(
                    $v['label'] ?? '',
                    $v['url'] ?? '',
                    ($v['showSiteTerms'] ?? true) ? $this->getSiteTerms() : null);
            }
        }

        return $this->_operatorTerms[$operatorCode] = null;
    }

    /**
     * @return DownPaymentOption[]|null
     */
    public function getDownpaymetOptions(): ?array
    {
        if (! is_null($this->downPaymentOptions)) {
            return $this->downPaymentOptions;
        }

        if (! array_key_exists('downpayment', $this->config)) {
            return null;
        }

        $dpDefinitions = $this->config['downpayment'];
        $this->downPaymentOptions = [];
        foreach ($dpDefinitions ?? [] as $k => $dpData) {
            $this->downPaymentOptions[$k] = self::downpaymentOptionFactory($dpData);
        }
        return $this->downPaymentOptions;
    }

    function checkForDownpaymentEligibility(\DateTime $date, $price) : ?Downpayment
    {
        $dps = $this->getDownpaymetOptions();
        foreach ($dps ?? [] as $k => $v) {
            $dp = new Downpayment($v, $date, $price);
            if ($dp->isValid()) {
                return $dp;
            }
        }
        return null;
    }

    private static function downpaymentOptionFactory($dpData)
    {
        $dp = new DownPaymentOption();
        $dp->setAmount(new Amount($dpData['amount']['type'], $dpData['amount']['value']));
        $dp->setDueDays((int) $dpData['dueDays']);

        if (array_key_exists('minPrice', $dpData)) {
            $dp->setMinPrice($dpData['minPrice']);
        }

        if (array_key_exists('maxPrice', $dpData)) {
            $dp->setMaxPrice($dpData['maxPrice']);
        }

        if (array_key_exists('minDaysToDepart', $dpData)) {
            $dp->setMinDaysToDepart($dpData['minDaysToDepart']);
        }

        if (array_key_exists('maxDaysToDepart', $dpData)) {
            $dp->setMaxDaysToDepart($dpData['maxDaysToDepart']);
        }

        if (array_key_exists('intent', $dpData)) {
            $dp->setIntent($dpData['intent']);
        }

        return  $dp;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function setCompany(?Company $company): void
    {
        $this->company = $company;
    }
}
