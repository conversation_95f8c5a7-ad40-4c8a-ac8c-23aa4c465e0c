<?php
namespace Wizatour\Model;

use Laminas\Db\TableGateway\TableGatewayInterface;

class SettingsTable
{
    private $tableGateway;

    public function __construct(TableGatewayInterface $tableGateway)
    {
        $this->tableGateway = $tableGateway;
    }

    public function fetchAll()
    {
        return $this->tableGateway->select();
    }

    public function getSetting($id)
    {
        $id = (int) $id;
        $rowset = $this->tableGateway->select(['id' => $id]);
        $row = $rowset->current();
        if (! $row) {
            throw new \RuntimeException(sprintf(
                'Could not find row with identifier %d',
                $id
            ));
        }

        return $row;
    }

    public function saveSetting(Settings $setting)
    {
        $data = [
            'id' => $setting->id,
            'ibeIdent'  => $setting->ibeIdent,
            'companyIdent'  => $setting->companyIdent,
            'name'  => $setting->name,
            'value'  => $setting->value,
        ];

        $id = (int) $setting->id;

        if ($id === 0) {
            $this->tableGateway->insert($data);
            return;
        }

        try {
            $this->getSetting($id);
        } catch (\RuntimeException $e) {
            throw new \RuntimeException(sprintf(
                'Cannot update album with identifier %d; does not exist',
                $id
            ));
        }

        $this->tableGateway->update($data, ['id' => $id]);
    }

    public function deleteSetting($id)
    {
        $this->tableGateway->delete(['id' => (int) $id]);
    }
}
