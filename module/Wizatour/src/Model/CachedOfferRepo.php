<?php
namespace Wizatour\Model;

use Lam<PERSON>\Cache\Exception\ExceptionInterface;
use Laminas\Cache\Storage\Adapter\Filesystem;

class CachedOfferRepo
{
    private $storage;
    const IDENT_PREFIX_DATA = 'D';
    const IDENT_PREFIX_REVERSE = 'R';
    /**
     * @var string
     */
    private $prefix;

    public function __construct(Filesystem $storage, $prefix = '')
    {
        $this->storage = $storage;
        $this->prefix = $prefix;
    }

    public function checkForDuplications($offer): ?string
    {
        $rawData = $this->storage->getItem($this->keyRev($offer->getEncoded()));
        return empty($rawData) ? null : $rawData;
    }

    private function key($ident): string
    {
        return sha1(self::IDENT_PREFIX_DATA.$this->prefix.$ident);
    }

    private function keyRev($ident): string
    {
        return sha1(self::IDENT_PREFIX_REVERSE.$this->prefix.$ident);
    }

    /**
     * @throws ExceptionInterface
     */
    public function fetchOffer($ident): ?CachedOffer
    {
        try {
            $rawData = $this->storage->getItem($this->key($ident));
            if (!$rawData) {
                return null;
            }
            return unserialize($rawData, ['allowed_classes'=>true]);
        } catch(\Exception $e) {
            return null;
        }
    }

    /**
     * Generates a key of 15 characters, that is unused in the cache yet
     * @throws ExceptionInterface
     */
    public function generateUniqueKey(): string
    {
        do {
            $key = bin2hex(random_bytes(7));
        } while($this->storage->hasItem($key));

        return $key;
    }

    /**
     * @throws ExceptionInterface
     */
    public function saveOffer(CachedOffer $offer)
    {
        if (!$offer->ident) {
            $offer->ident = $this->generateUniqueKey();
        }

        $this->storage->setItem($this->keyRev($offer->getEncoded()), $offer->ident);
        $this->storage->setItem($this->key($offer->ident), serialize($offer));
    }

    public function cleanExpiredCache(): void
    {
        $this->storage->clearExpired();
    }
}
