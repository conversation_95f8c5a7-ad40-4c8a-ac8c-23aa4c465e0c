<?php declare(strict_types=1);
namespace Wizatour\Command;

use Laminas\Cache\Storage\Adapter\AbstractAdapter;
use <PERSON><PERSON>\Cache\Storage\Adapter\Filesystem;
use Laminas\Cache\Storage\Adapter\FilesystemOptions;
use <PERSON><PERSON>\Cache\Storage\IterableInterface;
use <PERSON><PERSON>\Cache\Storage\StorageInterface;
use <PERSON><PERSON>\Log\Logger;
use Laminas\Stdlib\ErrorHandler;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class CacheCommand extends Command
{
    private $ibeTbl;
    protected static $defaultName = 'cache';

    /** @var StorageInterface[]  */
    private $cacheList = [
        'FilesystemCache' => null,
        'WizapiCache' => null,
        'PeakworkCache' => null,
        'TrafficsCache' => null,
        'OfferCache' => null,
        'DiscountCodeCache' => null,
        'GtGatewayCache' => null,
        'LocationGatewayCache' => null,
        'TouroperatorCache' => null,
        'InterfaceCache' => null,
        'AirlineCache' => null,
        'ContentManagerCache' => null,
        'AirportsCache' => null,
    ];
    /**
     * @var Logger
     */
    private $log;

    private $outputBuffer = [];
    private $config;


    public function __construct(
        array $conf,
        StorageInterface $FilesystemCache,
        StorageInterface $PeakworkCache,
        StorageInterface $TrafficsCache,
        StorageInterface $OfferCache,
        StorageInterface $DiscountCodeCache,
        StorageInterface $WizapiCache,
        StorageInterface $GtGatewayCache,
        StorageInterface $LocationGatewayCache,
        StorageInterface $TouroperatorCache,
        StorageInterface $InterfaceCache,
        StorageInterface $AirlineCache,
        StorageInterface $ContentManagerCache,
        StorageInterface $AirportsCache,
        Logger $log
    ) {
        parent::__construct(null);

        $this->config = $conf;
        foreach ($this->cacheList as $k=>$v) {
            $this->cacheList[$k] = ${$k};
        }
        $this->log = $log;
    }

    protected function configure() : void
    {
        $this->setName(self::$defaultName);
        //$this->addOption('name', null, InputOption::VALUE_REQUIRED, 'Module name');
        $this->addArgument('job',null, "Job to run. Valid jobs are: status, clearExpired, flushAll", 'status');
        $this->addArgument('cacheName',InputArgument::OPTIONAL, "Cache name to be used with the required Job", '');
    }

    protected function execute(InputInterface $input, OutputInterface $output) : int
    {
        $this->writeln($output, 'Wizatour Cache');
        $job = ucfirst($input->getArgument('job'));
        $cacheName = $input->getArgument('cacheName');
        $jobAction = [$this, "{$job}Job"];
        if (!is_callable($jobAction)) {
            $this->writeln($output, 'No such Job: ' . $job . ($cacheName? " for cache '{$cacheName}'" : ''));
            return 1;
        }

        $this->writeln($output, 'Running: ' . $job . ($cacheName? "({$cacheName})" : ''));
        $response = $cacheName ? $jobAction($cacheName) : $jobAction();
        foreach ($response as $k => $v) {
            $this->writeln($output, '  - ' .$k . ': ' . var_export($v, true));
        }

        if (array_key_exists('log', $this->config) && $this->config['log']) {
            $this->log->info(
                'Cache Command executed',
                [
                    'command' => implode(' ', $_SERVER['argv'] ?? []),
                    'output' => implode("\n", $this->getWrittenLines())
                ]
            );
        }

        return 0;
    }

    private function writeLn(OutputInterface $output, $msg)
    {
        $this->outputBuffer[] = $msg;
        $output->writeln($msg);
    }

    private function getWrittenLines()
    {
        return $this->outputBuffer;
    }

    private function clearWrittenLines()
    {
        return $this->outputBuffer;
    }

    private function StatusJob ()
    {
        $statusSets = [];
        /**
         * @var string $k
         * @var StorageInterface $v
         */
        foreach ($this->cacheList as $k=>$v) {
            if (is_a($v, IterableInterface::class)) {
                $items = iterator_to_array($v->getIterator());
            } else {
                $items = [];
            }

            $enabled = is_a($v, AbstractAdapter::class) && $v->getCaching() ;

            $numberOfItems = count($items);
            $statusSets[] = [
                "({$k}) Enabled" => $enabled,
                /*"  ({$k}) TotalSpace" => $v->getTotalSpace() / 1024 / 1024 / 1024,
                "  ({$k}) AvailableSpace" => $v->getAvailableSpace() / 1024 / 1024 / 1024,
                "  ({$k}) dir" => $v->getOptions()->getCacheDir(),
                "  ({$k}) dirLevel" => $v->getOptions()->getDirLevel(),
                "  ({$k}) permissions (dir:file)" => $v->getOptions()->getDirPermission() .':'. $v->getOptions()->getFilePermission(),
                "  ({$k}) ttl" => $v->getOptions()->getTtl(),
                "  ({$k}) MinTTL" => $v->getCapabilities()->getMinTtl(),
                "  ({$k}) MaxTTL" => $v->getCapabilities()->getMaxTtl(),
                "  ({$k}) namespace" => $v->getOptions()->getNamespace(),
                "  ({$k}) NamespaceSeparator" => $v->getCapabilities()->getNamespaceSeparator(),
                "  ({$k}) NamespacesPrefix" => $v->getCapabilities()->getNamespaceIsPrefix(),
                "  ({$k}) Number of Items" => $numberOfItems,
                "  ({$k}) First 5 Items" => $numberOfItems ? array_slice($items, 0, 5) : ' - ',*/
                //'capabilities' => $v->getCapabilities(),
            ];
        }
        return array_merge([], ...$statusSets);
    }


    private function ClearExpiredCacheJob ($cacheName)
    {
        $output = ['Clear status' => "[$cacheName]"];
        /**
         * @var string $k
         * @var Filesystem $v
         */
        if (empty($this->cacheList[$cacheName])) {
            $output["  Cache '{$cacheName}'"] = 'Not found';
            return $output;
        }
        $result = $this->clearExpiredCache($this->cacheList[$cacheName], $cacheName);
        $output = array_merge($output, $result);

        return $output;
    }
    private function cleanupEmptyDirs($dir)
    {
/*        if (preg_match('/[\/\\\]$/', $dir)) {
            $dir = substr($dir, 0, -1);
        }*/
        $result = `find "{$dir}" -empty -type d -delete`;
        if ($result) {
            $result = "\n{$result}";
        }
        return $result;
    }

    /**
     * @param StorageInterface $cache
     * @param $cacheName
     * @return array
     */
    private function clearExpiredCache(StorageInterface $cache, $cacheName): array
    {
        if (!is_callable([$cache, 'clearExpired'])) {
            return ["  Cache '{$cacheName}'" => 'Not supported (clearExpired)'];
        }

        $cache->clearExpired();
        $options = $cache->getOptions();
        $key = "  Cache '{$cacheName}'";
        $keyLen = strlen($key) + 8;
        $tabCount = (int)ceil(($keyLen)/8);
        $tabsStr = str_repeat("\t", $tabCount);
        $output = [$key => "\tCleared expired items successfully!"];
        if ($options instanceof FilesystemOptions) {
            $dir = $options->getCacheDir();
            $resultEmptyDirs = $this->cleanupEmptyDirs($dir);
            $output[$key] .= "\n      {$tabsStr}Cleared empty directories successfully!{$resultEmptyDirs}";
        }
        return $output;
    }

    private function ClearExpiredJob ()
    {
        $output = ['Clear status' => 'by cache'];
        /**
         * @var string $k
         * @var Filesystem $v
         */
        $results = [];
        foreach ($this->cacheList as $k=>$v) {
            $results[] = $this->clearExpiredCache($this->cacheList[$k], $k);

        }
        $output = array_merge($output, ...$results);
        $output[' '] =  'done';

        return $output;
    }

    /**
     * @param $dir
     * @param $flags
     * @return void
     * @throws \ErrorException
     */
    private function flushDir($dir, $flags = \GlobIterator::SKIP_DOTS | \GlobIterator::CURRENT_AS_PATHNAME) {
        $it = new \GlobIterator($dir . DIRECTORY_SEPARATOR . '*', $flags);
        foreach ($it as $pathname) {
            if ($it->isDir()) {
                $this->flushDir($pathname);
                rmdir($pathname);
            } else {
                // remove the file by ignoring errors if the file doesn't exist afterwards
                // to fix a possible race condition if another process removed the file already.
                ErrorHandler::start();
                unlink($pathname);
                $err = ErrorHandler::stop();
                if ($err && file_exists($pathname)) {
                    throw new \Exception("Failed to remove file '{$pathname}'", 0, $err);
                }
            }
        }
    }

    private function FlushCacheJob ($cacheName)
    {
        $output = ['Flush ' => "[$cacheName]"];
        $cache = $this->cacheList[$cacheName]??false;
        if (!$cache) {
            $output["  Cache '{$cacheName}'"] = 'Not found';
            return $output;
        }
        $options = $cache->getOptions();
        if (!$options instanceof FilesystemOptions) {
            $output["  Cache '{$cacheName}'"] = 'Does not support flushing';
            return $output;
        }

        $dir = $options->getCacheDir();
        try {
            $this->flushDir($dir);
            $output["  Cache '{$cacheName}'"] = "\n      Flushed cached files successfully!{$dir}";
        } catch (\Exception $e) {
            $output["  Cache '{$cacheName}'"] = "Failed to flush cache: {$e->getMessage()} in {$e->getFile()} on line {$e->getLine()}";
            return $output;
        }

        $this->cleanupEmptyDirs($dir);
        $output["  Cache '{$cacheName}'"] .= "\n      Cleared empty directories successfully!{$dir}";

        return $output;
    }

    private function FlushAllJob ()
    {
        $output = ['Flush status' => 'by cache'];
        $flags = \GlobIterator::SKIP_DOTS | \GlobIterator::CURRENT_AS_PATHNAME;
        $dir   = 'data/cache';

        /**
         * @var string $k
         * @var string $v
         */
        foreach (new \GlobIterator($dir . DIRECTORY_SEPARATOR . '*', $flags) as $k=>$v) {
            $this->flushDir($v, $flags);
            /*$result = $v->flush();
            $output["  {$k}"] = $result?'Success':'Failed';*/
        }

        return $output;
    }
}
