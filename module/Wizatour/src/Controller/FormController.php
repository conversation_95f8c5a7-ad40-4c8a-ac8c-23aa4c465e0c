<?php

namespace Wizatour\Controller;

use <PERSON><PERSON>\Cache\Service\StorageAdapterFactoryInterface;
use <PERSON>inas\Cache\Storage\Adapter\Filesystem;
use Laminas\Cache\Storage\StorageInterface;
use Laminas\Json\Json;
use Lam<PERSON>\Session\Container;
use Laminas\Session\SessionManager;
use Laminas\View\Model\ViewModel;
use Wizatour\Model\AirportTable;
use Wizatour\Model\Ibe;
use Wizatour\Model\IbeTable;
use Wizatour\Model\StayOptionsList;
use Wizatour\Service\FormProviderService\FormProviderService;

class FormController extends \Laminas\Mvc\Controller\AbstractActionController
{
    private $ibeTbl;
    /** @var Container */
    private $session;
    private $interfaceConfig;
    private StorageInterface $cache;

    public function __construct(IbeTable $ibeTbl, SessionManager $sessionMngr, StorageInterface $cache)
    {
        // This will fix the broken session data and lean it up
        try {
            $sessionMngr->start();
        } catch(\Exception $e) {
            $sessionMngr->destroy();
            $sessionMngr->start();
        }

        $this->ibeTbl = $ibeTbl;
        $this->session = new Container('wz_search', $sessionMngr);
        $this->cache = $cache;


        $this->interfaceConfig = include dirname(__DIR__, 2) .'/config/interface.search.php';
    }

    public function indexAction()
    {
        $expireCacheDate = new \DateTime('now +1 day');
        $maxAge = $expireCacheDate->diff(new \DateTime());
        $maxAgeSec = $maxAge->s + $maxAge->i * 60 + $maxAge->h * 3600 + $maxAge->d * 86400;
        $expireCacheDate->setTimezone(new \DateTimeZone('GMT'));

        $this->getResponse()->getHeaders()->addHeaders([
            'Pragma' => 'cache',
            'Cache-Control' => "public, max-age={$maxAgeSec}, immutable",
            'Expires' => $expireCacheDate->format('D, d M Y H:i:s') . ' GMT',
        ]);

        $this->layout()->setVariable('skipGlobals', $this->params()->fromQuery('skipGlobals', false));

        $cacheEnabled = (bool)$this->params()->fromQuery('cache', true);
        $pageCachekey = 'form_' . sha1($this->getRequest()->getRequestUri());
        if ($cacheEnabled) {
            $cacheHit = $this->cache->getItem($pageCachekey);
            if ($cacheHit) {
                $r = unserialize($cacheHit, ['allowed_classes' => true]);
                return $r;
            }
        }

        $this->Interface()->preGet($this->interfaceConfig['view']);
        $submitTo = $this->params()->fromQuery('submitTo', false);
        $formId = $this->params()->fromQuery('formId', uniqid('wz', false));
        $lang = $this->params()->fromRoute('lang', 'bg_BG');
        $destination = $this->params()->fromQuery('destination', '');
        $airports = $this->params()->fromQuery('airports', '');
        $arrivalAirports = $this->params()->fromQuery('arrivalAirports', '');
        $ibeIdent = $this->params()->fromRoute('ibe');
        //$ibe = $this->ibeTbl->getIbeFull($ibeIdent);
        /** @var Ibe $ibe */
        $ibe = $this->Table('ibeInstance');
        $this->layout()->setTemplate('layout/wizatour');
        /** @var StorageAdapterFactoryInterface $storageFactory */
        // $storageFactory = $container->get(StorageAdapterFactoryInterface::class);
        //$storage = $storageFactory->create(Memory::class);
        $viewData = [];

        if ($this->session->step1Url) {
            $this->session->step1Url[$formId] = $this->getRequest()->getUri()->toString();
        } else {
            $this->session->step1Url = [$formId => $this->getRequest()->getUri()->toString()];
        }

        // Save submit url
        if ($this->session->submitTo) {
            $this->session->submitTo[$formId] = $submitTo;
        } else {
            $this->session->submitTo = [$formId => $submitTo];
        }

        //$pws =  new PeakworkService('', $ibe->ident, $storage);
        $viewData['formId'] = $formId;
        $viewData['ibeIdent'] = $ibeIdent;
        $viewData['lang'] = $lang;
        $viewData['ibe'] = $ibeIdent;
        $viewData['ibeType'] = $ibe->ibeType;
        $viewData['duration'] = $params['duration'] ?? ($ibe->ibeType === 'hotel' ? '7' : '6-8');

        /** @var StayOptionsList $stayOptionsList */
        $stayOptionsList = $this->Table(StayOptionsList::class);
        $viewData['stayOptions'] = $stayOptionsList->getListForType($ibe->ibeType);
        $viewData['period'] = (new \DateTime('now +2 days'))->format('d.m.Y');
        $viewData['period'] .= ' - ' . (new \DateTime('now +6 months'))->format('d.m.Y');
        $viewData['version'] = $this->layout()->getVariable('version', '0');
        $viewData['action'] = $this->url()->fromRoute('wizatour/form', ['action' => 'submit'], ['force_canonical' => true], true);

        if ($destination) {
            $viewData['destination'] = $this->params()->fromQuery('destination', '');
            $viewData['destinationName'] = $this->getDestinationName($ibe->ibeType, $viewData['destination'], $lang);
        }

        if ($airports) {
            $viewData['airports'] = $airports;
            $viewData['airportsName'] = $this->getAirportName($airports, $lang);
        }

        if ($arrivalAirports) {
            $viewData['arrivalAirports'] = $arrivalAirports;
            $viewData['arrivalAirportsName'] = $this->getAirportName($arrivalAirports, $lang);
        }
        $config = include dirname(__DIR__, 2) .'/config/interface.search.php';
        $i18nSet = $this->Interface()->getValues($config['js']);
        $viewData['i18nSet'] = Json::encode($i18nSet);
        $viewModel = new ViewModel($viewData);
        $this->cache->setItem($pageCachekey, serialize($viewModel));

        return $viewModel;
    }

    public function submitAction()
    {
        $formId = $this->params()->fromQuery('formId', 0);
        //$ibeIdent = $this->params()->fromRoute('ibe');
        //$ibe = $this->ibeTbl->getIbeFull($ibeIdent);
        /** @var Ibe $ibe */
        $ibe = $this->Table('ibeInstance');
        //$this->layout()->setTemplate('layout/wizatour');
        /** @var StorageAdapterFactoryInterface $storageFactory */
        // $storageFactory = $container->get(StorageAdapterFactoryInterface::class);
        //$storage = $storageFactory->create(Memory::class);
        $viewData = [];
        $data = $this->params()->fromQuery();
        //$data['step1'] = $this->session->step1Url[$formId];
        $submitTo = $this->session->submitTo[$formId]??false;

        if(!$submitTo && array_key_exists('urls', $ibe->config)) {
            $submitTo = $ibe->config['urls']['search'] ?? $submitTo;
        }

        if(array_key_exists('submitTo', $data)) {
            unset($data['submitTo']);
        }

        if(array_key_exists('step1', $data)) {
            unset($data['step1']);
        }

        if(array_key_exists('bookingPage', $data)) {
            unset($data['bookingPage']);
        }

        if(array_key_exists('skipGlobals', $data)) {
            unset($data['skipGlobals']);
        }

        if(array_key_exists('airports', $data)) {
            $data['airports'] = implode(',', $data['airports']);
        }

        if(array_key_exists('arrivalAirports', $data)) {
            $data['arrivalAirports'] = implode(',', $data['arrivalAirports']);
        }

        if($submitTo) {
            $submitTo = str_replace('skipGlobals=true', '', $submitTo);
            //$data['searchUrl'] = $submitTo;
            $queryStr = http_build_query($data);
            $submitTo .= (strpos($submitTo, '?') === false ? '?' : '&') . $queryStr;
            return $this->redirect()->toUrl($submitTo);
        }

        return $this->redirect()->toRoute(
            'wizatour/form',
            ['controller' => 'Search', 'action'=>'hotels'],
            ['query' => $data],
            true
        );
    }
    private function getDestinationName($ibeType, $destination, $lang): string
    {
        $destinationParts = explode(':', $destination);

        $id = $destinationParts[1] ?? null;
        if (! $id) {
            return '';
        }

        $lang2 = substr($lang, 0, 2);
        switch ($destinationParts[0]) {
            case 'tree':
                /** @var FormProviderService $provider */
                $provider = $this->Provider();
                $dest = $provider->getDestinations($ibeType, $lang);
                if ($dest) {
                    foreach ($dest as $v) {
                        if ($id == $v->Id) {
                            return $v->Name;
                        }
                    }
                }
                break;
        }

        return '';
    }

    private function getAirportName($airport, $lang): string
    {
        /** @var AirportTable $airportTable */
        $airportTable = $this->Table(AirportTable::class);
        $airportModel = $airportTable->fetch($airport, $lang);

        return $airportModel->getName()??'';
    }
}
