<?php

namespace Wizatour\Service\FormProviderService\Adapter\Traffics\Models;

class FlightInfo
{

    private $outgoing;
    private $return;
    private $legs;


    public function __construct(FlightInfoFlight $outgoing, ?FlightInfoFlight $return)
    {

        $this->outgoing = $outgoing;
        $this->return = $return;
        if ($return) {
            $this->legs = array_merge($outgoing->getLegs(), $return->getLegs());
        } else {
            $this->legs = $outgoing->getLegs();
        }


    }

    /**
     * @return mixed
     */
    public function getOutgoing()
    {
        return $this->outgoing;
    }

    /**
     * @return mixed
     */
    public function getReturn()
    {
        return $this->return;
    }

    /**
     * @return mixed
     */
    public function getLegs()
    {
        return $this->legs;
    }


}
