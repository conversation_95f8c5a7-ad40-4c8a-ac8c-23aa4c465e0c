<?php

namespace Wizatour\Service\FormProviderService\Models;

interface BookDataInterface
{
    public function isBookingOk(): bool;
    public function getSubstatus(): ?string;
    public function getMessage();
    public function getData();
    public function getBookingData();
    public function getBookingCode();
    public function getTransactionKey();
    public function getTransactionKey2();
}
