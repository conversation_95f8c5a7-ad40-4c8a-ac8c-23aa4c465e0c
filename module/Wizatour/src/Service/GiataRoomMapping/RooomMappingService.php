<?php

namespace Wizatour\Service\GiataRoomMapping;

use GuzzleHttp\Client;

class RooomMappingService
{
    const SERVICE_ENTRYPOINT = 'https://stagingapi.roommapping.com';

    private $client;

    /**
     * @var AuthCredentials
     */
    private $auth;

    public function __construct(AuthCredentials $auth, Client $client = null)
    {

        $this->auth = $auth;
        $this->client = $client;
    }

    /**
     * @param array $roomCodes
     * @return array
     */
    public function fetchRoomDescriptions(array $roomCodes) : \Psr\Http\Message\ResponseInterface
    {
        $props = [];
        $n = 0;
        foreach ($roomCodes as $giata => $rooms) {
            $currentProp = ['name'=>'n'.$n];
            if(!empty($giata)) {
                $currentProp["giataId"] = $giata;
            }
            $currentProp['roomTypes'] = [];
            if(is_iterable($rooms)) {
                foreach ($rooms as $v) {
                    $currentProp['roomTypes'][] = $v;
                }
            } else {
                $currentProp['roomTypes'][] = $rooms;
            }

            $props[] = $currentProp;
            $n++;
        }
        return $this->makeRequest($props);
    }

    private function getClient() : Client
    {
        if(is_null($this->client)) {
            $this->client = new Client([
                'base_uri' => self::SERVICE_ENTRYPOINT,
                'auth' => $this->auth->get(),
            ]);
        }
        return $this->client;
    }

    private function makeRequest($props) : \Psr\Http\Message\ResponseInterface
    {
        $client = $this->getClient();
        $payload = [
            'json'    => [
                'properties' => $props
            ]
        ];
        d($payload);
        return $client->post('/Map', $payload);
    }
}
