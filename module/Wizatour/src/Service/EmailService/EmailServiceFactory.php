<?php

namespace Wizatour\Service\EmailService;

use Interop\Container\ContainerInterface;
use Interop\Container\Exception\ContainerException;
use Laminas\ServiceManager\Exception\ServiceNotCreatedException;
use Laminas\ServiceManager\Exception\ServiceNotFoundException;
use Laminas\ServiceManager\Factory\FactoryInterface;
use Symfony\Component\Yaml\Yaml;

class EmailServiceFactory implements FactoryInterface
{

    public function __invoke(ContainerInterface $container, $requestedName, ?array $options = null)
    {
        if ($options && $options['provider']) {
            $provider = $options['provider'];
        } else {
            $ibe = $container->get('ibeInstance');
            $provider = $ibe->emailProvider;
        }
        $config = Yaml::parse($provider->config, Yaml::PARSE_OBJECT_FOR_MAP);

        $serviceConfig = new EmailServiceConfig((array) $config->smtp, $config->from->address, $config->from->name);

        $logger = $container->get('EmailLogger');

        return new EmailService($serviceConfig, $logger);
    }
}
