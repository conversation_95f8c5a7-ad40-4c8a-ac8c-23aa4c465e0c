<?php

namespace Wizatour\Service\GiataHotelGuide;

class TextSet implements \Iterator, \ArrayAccess
{
    /** @var Text[] */
    private array $texts = [];
    private int $index = 0;




    /**
     * @param Text[] $texts
     * @return void
     */
    public function setTexts(array $texts): void
    {
        $this->texts = $texts;
    }

    /**
     * @param Text $text
     * @return void
     */
    public function add(Text $text): void
    {
        $this->texts[] = $text;
    }

    /* \Iterator*/
    public function current(): ?Text
    {
        return $this[$this->index]??null;
    }

    public function next(): void
    {
        $this->index++;
    }

    public function key(): int
    {
        return $this->index;
    }

    public function valid(): bool
    {
        return isset($this[$this->index]);
    }

    public function rewind(): void
    {
        $this->index = 0;
    }

    /* \ArrayAccess */

    public function offsetExists($offset)
    {
        return array_key_exists($offset, $this->texts);
    }

    public function offsetGet($offset)
    {
        return $this->texts[$offset]??null;
    }

    /**
     * @param int $offset
     * @param Text $value
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        $this->texts[$offset] = $value;
    }

    public function offsetUnset($offset)
    {
        unset($this->texts[$offset]);
    }
}
