<?php

namespace ApplicationTest\Controller;

use Application\Controller\IndexController;
use Laminas\Session\SessionManager;
use Laminas\Stdlib\ArrayUtils;
use Laminas\Test\PHPUnit\Controller\AbstractHttpControllerTestCase;
use Wizatour\Controller\PaymentController;
use PHPUnit\Framework\TestCase;

class PaymentControllerTest extends AbstractHttpControllerTestCase
{

   /* public function setUp(): void
    {
        if ( !isset( $_SESSION ) ) $_SESSION = [];

        // The module configuration should still be applicable for tests.
        // You can override configuration here with test case specific values,
        // such as sample view templates, path stacks, module_listener_options,
        // etc.
        $configOverrides = [

        ];

        $this->setApplicationConfig(ArrayUtils::merge(
            include __DIR__ . '/../../../../config/application.config.php',
            $configOverrides
        ));

        parent::setUp();
    }


    public function testRegisterAction()
    {
        $this->getApplicationServiceLocator()->setAllowOverride(true);
        //$this->getApplicationServiceLocator()->setService(ConfigInterface::class, new StandardConfig());
        $this->getApplicationServiceLocator()->setAllowOverride(false);

        $this->dispatch('/wt/bg_BG/derPackets/Payment/Register', 'GET');
        $this->assertResponseStatusCode(200);
        $this->assertModuleName('wizatour');
        $this->assertControllerName(PaymentController::class); // as specified in router's controller name alias
        $this->assertControllerClass('PaymentController');
        $this->assertMatchedRouteName('wizatour/form');
    }
   */
/*
    public function testLandingAction()
    {
        $this->getApplicationServiceLocator()->setAllowOverride(true);
        //$this->getApplicationServiceLocator()->setService(ConfigInterface::class, new StandardConfig());
        $this->getApplicationServiceLocator()->setAllowOverride(false);

        $this->dispatch('/', 'GET');
        $this->assertResponseStatusCode(302);
        $this->assertModuleName('application');
        $this->assertControllerName(IndexController::class); // as specified in router's controller name alias
        $this->assertControllerClass('IndexController');
        $this->assertMatchedRouteName('home');
    }

    public function testIndexAction()
    {

    }


    public function testNotifyAction()
    {

    }

    public function test__construct()
    {

    }

    public function testWaitForPaymentAction()
    {

    }

    public function testCheckPaymentStatusAction()
    {

    }
*/
}
