<?php
use Wizatour\Model\Terms;
?><div class="wz-booking-section wz-section-termsAndConditions">
    <div class="wz-booking-section__header"><?=$this->Interface('booking.partial.terms.header')?></div>
    <div class="wz-booking-section__content">
        <div class="wz-terms">
            <input type="checkbox" name="operatorTerms" value="1" id="operatorTerms-inp" />
            <label class="wz-terms-label" for="operatorTerms-inp"><?=$this->Interface('booking.partial.terms.agreeP1')?>
                <?php if ($this->operatorTerms instanceof Terms) {
                    ?><a href="<?=$this->operatorTerms->getUrl()?>" target="_blank"><?=$this->operatorTerms->getLabel()?></a><?php
                } ?>
                <?php if ($this->operatorTerms instanceof Terms && $this->operatorTerms->getAdditionalTerms() instanceof Terms) { ?><?=$this->Interface('booking.partial.terms.agreeP2')?><?php } ?>
                <?php if ($this->operatorTerms instanceof Terms && $this->operatorTerms->getAdditionalTerms() instanceof Terms) {
                    ?><a href="<?=$this->operatorTerms->getAdditionalTerms()->getUrl()?>" target="_blank"><?=$this->operatorTerms->getAdditionalTerms()->getLabel()?></a><?php
                } ?>
                <?=$this->Interface('booking.partial.terms.agreeP3')?>
            </label>
        </div>
        <?php if($this->showSubscription??false) { ?>
            <div class="wz-newsletter">
                <input type="checkbox" name="subscription" value="1" id="subscription-inp" />
                <label class="wz-subscription-label" for="subscription-inp">
                    <?=$this->Interface('booking.partial.terms.newsletter')?>
                </label>
            </div>
        <?php } ?>
    </div>
</div>
