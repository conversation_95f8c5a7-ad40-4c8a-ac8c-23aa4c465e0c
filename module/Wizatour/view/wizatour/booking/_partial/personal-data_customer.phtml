<div class="wz-booking-block">
    <div class="wz-booking-block__title"><?=$this->Interface('booking.partial.p-data_customer.title')?></div>
    <div class="wz-booking-block__content">
        <div class="wz-note"><?=$this->Interface('booking.partial.p-data_customer.note')?></div>
        <div class="wz-customer-col-wrapper">
            <div class="wz-customer-col">
                <div class="wz-customer-row<?=($this->errors['customer_fields']['salutation']??false) ? ' validation-invalid' : ''?>">
                    <label for="inp-salutation"><?=$this->Interface('booking.partial.p-data_customer.salutationLabel')?></label>
                    <select id="inp-salutation" class="wz-booking-dropdown" name="salutation" data-value="<?=$this->formData['salutation']??''?>">
                        <option value=""></option>
                        <option value="mr"><?=$this->Interface('booking.partial.p-data_customer.mrLabel')?></option>
                        <option value="ms"><?=$this->Interface('booking.partial.p-data_customer.msLabel')?></option>
                    </select>
                </div>
                <div class="wz-customer-row">
                                <span class="wz-textinput-wrapper e-float-input e-outline">
                                    <input type="text" id="inp-firstname" name="firstname" class="wz-textfield" required
                                           data-validationhint="<?=$this->Interface('booking.partial.p-data_customer.validationHint')?>"
                                           autocomplete="given-name"
                                           pattern="[a-zA-Z]{1}[0-9a-zA-Z\s\.\-']+"
                                           minlength="2"
                                           value="<?=$this->formData['lastname']??''?>"
                                    />
                                    <span class="e-float-line"></span>
                                    <label class="e-float-text" for="inp-firstname">
                                        <?=$this->Interface('booking.partial.p-data_customer.firstNameLabel')?>
                                    </label>
                                </span>
                </div>
                <div class="wz-customer-row">
                                <span class="wz-textinput-wrapper e-float-input e-outline<?=($this->errors['street']['lastname']??false) ? ' e-error' : ''?>">
                                    <input type="text" id="inp-lastname" name="lastname" class="wz-textfield" required
                                           data-validationhint="<?=$this->Interface('booking.partial.p-data_customer.validationHint')?>"
                                           autocomplete="family-name"
                                           pattern="[a-zA-Z]{1}[0-9a-zA-Z\s\.\-']+"
                                           minlength="2"
                                           value="<?=$this->formData['lastname']??''?>"
                                    />
                                    <span class="e-float-line"></span>
                                    <label class="e-float-text" for="inp-lastname">
                                        <?=$this->Interface('booking.partial.p-data_customer.lastNameLabel')?>
                                    </label>
                                </span>
                </div>
                <div class="wz-customer-row">
                                 <span class="wz-textinput-wrapper e-float-input e-outline<?=($this->errors['street']['city']??false) ? ' e-error' : ''?>">
                                    <input id="inp-street" name="street" class="wz-textfield" required
                                           data-validationhint="<?=$this->Interface('booking.partial.p-data_customer.validationHint')?>"
                                           autocomplete="street-address"
                                           pattern="[a-zA-Z]{1}[0-9a-zA-Z\s.\-,\\\/\(\)#&*;:]+"
                                           minlength="5"
                                           value="<?=$this->formData['street']??''?>"
                                    />
                                    <span class="e-float-line"></span>
                                    <label class="e-float-text" for="inp-street">
                                        <?=$this->Interface('booking.partial.p-data_customer.streetLabel')?>
                                    </label>
                                </span>
                </div>
                <div class="wz-customer-row">
                                <span class="wz-textinput-wrapper e-input-group e-float-input e-outline post-code-wrapper<?=($this->errors['customer_fields']['postCode']??false) ? ' e-error' : ''?>">
                                    <span class="e-input-group-icon">-</span>
                                    <input id="inp-postCode" name="postCode" class="wz-textfield required" required
                                           data-validationhint="<?=$this->Interface('booking.partial.p-data_customer.postCodeValidation')?>"
                                           title="<?=$this->Interface('booking.partial.p-data_customer.postCodeValidation')?>"
                                           pattern="[0-9a-zA-Z]+"
                                           autocomplete="postal-code"
                                           minlength="4"
                                           value="<?=$this->formData['postCode']??''?>"
                                    />
                                    <span class="e-float-line"></span>
                                    <label class="e-float-text" for="inp-postCode">
                                        <?=$this->Interface('booking.partial.p-data_customer.postCodeLabel')?>
                                    </label>
                                </span>
                    <span class="wz-textinput-wrapper e-float-input e-outline<?=($this->errors['customer_fields']['city']??false) ? ' validation-error' : ''?>">
                                    <input id="inp-city" name="city" value="<?=$this->formData['city']??''?>"  pattern="[a-zA-Z]{1}[0-9a-zA-Z\s]+" class="wz-textfield" required />
                                    <span class="e-float-line"></span>
                                    <label class="e-float-text" for="inp-city">
                                        <?=$this->Interface('booking.partial.p-data_customer.cityLabel')?>
                                    </label>
                                </span>
                </div>
                <div class="wz-customer-row">
                    <label for="inp-country"><?=$this->Interface('booking.partial.p-data_customer.countryLabel')?></label>
                    <select id="inp-country" class="wz-booking-dropdown" name="country" data-value="<?=$this->formData['country']??$this->defaultCountry??''?>">
                        <option value=""></option>
                        <?php foreach ($this->countriesOptions as $k=>$v) { ?>
                            <option value="<?=$k?>"<?=$k===($this->formData['country']??$this->defaultCountry??'')?' selected':''?>><?=$v?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
            <div class="wz-customer-col">
                <div class="wz-customer-row wz-customer-phone-wrapper">
                    <label for="inp-mobilephone-code"><?=$this->Interface('booking.partial.p-data_customer.countryPhoneCodeLabel')?> *</label>
                    <select id="inp-mobilephone-code" class="wz-booking-dropdown" name="mobilephone_code"
                            autocomplete="tel-country-code" data-value="<?=$this->formData['mobilephone_code']??$this->defaultPhoneCode??''?>">
                        <option value=""></option>
                        <?php foreach ($this->countryPhoneCodes as $country => $phoneCode) {?>
                            <option value="<?=$phoneCode?>"<?=$phoneCode==($this->formData['mobilephone_code']??$this->defaultPhoneCode??'')?' selected':''?>><?=$country?> (<?=$phoneCode?>)</option>
                        <?php } ?>
                    </select>
                     <span class="wz-textinput-wrapper e-float-input e-outline<?=($this->errors['customer_fields']['mobilephone']??false) ? ' e-error' : ''?>">
                        <input id="inp-mobilephone" name="mobilephone" value="<?=$this->formData['mobilephone']??''?>" class="wz-textfield"
                               data-validationhint="<?=$this->Interface('booking.partial.p-data_customer.phoneValidation')?>"
                               title="<?=$this->Interface('booking.partial.p-data_customer.phoneValidation')?>"
                               autocomplete="tel-national" pattern="[\d]{7,15}" type="tel" required />
                        <span class="e-float-line"></span>
                         <label class="e-float-text" for="inp-mobilephone">
                             <?=$this->Interface('booking.partial.p-data_customer.phoneLabel')?>
                         </label>
                    </span>
                </div>
                <div class="wz-customer-row wz-customer-phone-wrapper">
                    <label for="inp-workphone_code"><?=$this->Interface('booking.partial.p-data_customer.countryPhoneCodeLabel')?></label>
                    <select id="inp-workphone_code" class="wz-booking-dropdown" name="workphone_code"
                            autocomplete="tel-country-code" data-value="<?=$this->formData['workphone_code']??$this->defaultPhoneCode??''?>">
                        <option value=""></option>
                        <?php foreach ($this->countryPhoneCodes as $country => $phoneCode) { ?>
                            <option value="<?=$phoneCode?>"<?=$phoneCode===($this->formData['workphone_code']??$this->defaultPhoneCode??'')?' selected':''?>><?=$country?> (<?=$phoneCode?>)</option>
                        <?php } ?>
                    </select>
                     <span class="wz-textinput-wrapper e-float-input e-outline<?=($this->errors['customer_fields']['workphone']??false) ? ' e-error' : ''?>">
                        <input id="inp-workphone" name="workphone" value="<?=$this->formData['workphone']??''?>" class="wz-textfield"
                               data-validationhint="<?=$this->Interface('booking.partial.p-data_customer.phoneValidation')?>"
                               title="<?=$this->Interface('booking.partial.p-data_customer.phoneValidation')?>"
                               autocomplete="tel-national" pattern="[\d]{7,15}" type="tel"/>
                        <span class="e-float-line"></span>
                         <label class="e-float-text" for="inp-workphone">
                             <?=$this->Interface('booking.partial.p-data_customer.workPhoneLabel')?>
                         </label>
                    </span>
                </div>
                <div class="wz-customer-row">
                                 <span class="wz-textinput-wrapper e-float-input e-outline<?=($this->errors['customer_fields']['email']??false) ? ' validation-invalid' : ''?>">
                                    <input type="email" id="inp-email" name="email" value="<?=$this->formData['email']??''?>" class="wz-textfield" required />
                                    <span class="e-float-line"></span>
                                     <label class="e-float-text" for="inp-email">
                                         <?=$this->Interface('booking.partial.p-data_customer.emailLabel')?>
                                     </label>
                                </span>
                </div>
                <div class="wz-customer-row">
                                 <span class="wz-textinput-wrapper e-float-input e-outline multiline">
                                     <textarea id="inp-notes" name="notes" class="wz-textarea" ></textarea>
                                    <span class="e-float-line"></span>
                                     <label class="e-float-text" for="inp-notes">
                                         <?=$this->Interface('booking.partial.p-data_customer.notesLabel')?>
                                     </label>
                                </span>
                </div>
            </div>
        </div>
    </div>
</div>
