<?php

namespace Application;

class Php<PERSON>enderer implements TemplateRenderingInterface
{
    /**
     * @var ?FilePathResolverInterface
     */
    private $templateResolver;

    public function __construct(FilePathResolverInterface $templateResolver = null)
    {
        $this->templateResolver = $templateResolver;
    }

    /**
     * @return FilePathResolverInterface|null
     */
    public function getTemplateResolver(): ?FilePathResolverInterface
    {
        return $this->templateResolver;
    }

    /**
     * This method renders a view or a template and returns the rendered string
     *
     * @param RendererViewInterface|string $view the View or a template path to render
     * @param array $data Only used if the first parameter is string. The data to include in the template
     * @return string return the rendered string
     */
    public function render($view, $data = []): string
    {
        if (is_a($view,RendererViewInterface::class)) {
            return $this->renderView($view);
        }

        return $this->renderTemplate($view, $data);
    }

    private function renderView(RendererViewInterface $view): string
    {
        return $this->renderTemplate($view->getTemplate(), $view->getVars());
    }

    private function renderTemplate($template, $data = []): string
    {
        $templateFile = '';
        $resolver = $this->getTemplateResolver();

        $templateFile = $resolver ? $resolver->resolve($template) : $template;

        foreach ($data as $k => $v) {
            $$k = $v;
        }

        ob_start();
        include $templateFile;
        return ob_get_clean();
    }

    private function partial($templateFile, $data = []): string
    {
        return $this->render($templateFile, $data);
    }

}
