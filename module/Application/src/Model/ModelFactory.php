<?php
namespace Application\Model;

use Bgservice\NzoomClient\Entity\NzoomModelEntity;

class ModelFactory
{
    private $modelMap;
    private $type2ClassMap = [];
    private $prototypes = [];

    public function setModelMap(array $modelMap) : void
    {
        $this->modelMap = $modelMap;
    }

    public function __invoke($data) : NzoomModelEntity
    {
        $modelClass = $this->getModelClass($data['type']);

        $model = clone $this->getModelPrototype($modelClass);
        foreach ($data as $k=>$v) {
            $model->$k = $v;
        }
        return $model;
    }

    private function getModelPrototype($modelClass)
    {
        if(!isset($this->prototypes[$modelClass])){
            $this->prototypes[$modelClass] = new $modelClass();
        }

        return $this->prototypes[$modelClass];
    }

    private function getModelClass($type)
    {
        if(isset($this->type2ClassMap[$type])){
            return $this->type2ClassMap[$type];
        }

        $modelClass = isset($this->modelMap[$type]) ? $this->modelMap[$type] : NzoomModelEntity::class;

        if(!class_exists($modelClass)) {
            $modelClass = NzoomModelEntity::class;
        }

        $this->type2ClassMap[$type] = $modelClass;
        return $modelClass;
    }
}