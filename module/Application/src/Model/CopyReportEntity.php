<?php

namespace Application\Model;

use Bgservice\NzoomClient\Entity\NzoomModelEntity;

/**
 *
 */
class CopyReportEntity extends NzoomModelEntity
{
    /**
     * @return string
     */
    public function getLastReportUrl(): string
    {
        return 'downloadfile/' . $this->report_id . '/' . $this->last_report_id;
    }

    /**
     * @return string
     */
    public function getLastReportIcon(): string
    {
        return ($this->last_report_not_exist === false) ? 'file-pdf-o' : 'exclamation-circle';
    }

    /**
     * @return string
     */
    public function getObjectsName(): string
    {
        $type = explode(',', $this->object_name_type);
        $subtype = explode(',', $this->object_subtype_name);
        $names = [];
        foreach ($type as $key => $item) {
            $names[] = $subtype[$key] . ' ' . $item;
        }
        return implode(', ', $names);
    }
}
