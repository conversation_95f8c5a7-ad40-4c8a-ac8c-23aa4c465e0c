<?php

namespace Application\Model;

use Application\Model\Traits\OptionVar;

/**
 * Residential building model
 */
class ResidentialBuildingModel extends NomenclatureModel
{
    use OptionVar;

    /**
     * Object identifier field
     *
     * @var array|string[]
     */
    public array $object_identifier = [
        'name' => 'object_identifier',
        'type' => 'text',
        'source' => 'alvis_required := 0',
        'required' => '1',
        'hidden' => '0',
        'readonly' => '0',
        'label' => 'Идентификатор:',
    ];
}
