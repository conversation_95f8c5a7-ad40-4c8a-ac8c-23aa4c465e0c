<?php

namespace Application\Model\ViewModel;

use Application\RendererViewInterface;

abstract class AbstractViewModel implements RendererViewInterface
{
    /** @var string */
    protected $template;
    protected $terminal = false;
    /** @var iterable */
    protected $vars = [];

    /**
     * @param string $templatePath
     * @return void
     */
    public function setTemplate(string $templatePath): void
    {
        $this->template = $templatePath;
    }

    /**
     * @return string
     */
    public function getTemplate(): string
    {
        return $this->template;
    }

    /**
     * @return iterable
     */
    public function getVars(): iterable
    {
        return $this->vars;
    }

    /**
     * @param iterable $vars
     */
    public function setVars(iterable $vars): void
    {
        $this->vars = $vars;
    }

    /**
     * @return bool
     */
    public function isTerminal(): bool
    {
        return $this->terminal;
    }

    /**
     * @param bool $terminal
     */
    public function setTerminal(bool $terminal): void
    {
        $this->terminal = $terminal;
    }
}
