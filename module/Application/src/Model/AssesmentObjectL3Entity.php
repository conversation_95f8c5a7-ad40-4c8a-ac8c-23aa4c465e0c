<?php
namespace Application\Model;

class AssesmentObjectL3Entity extends AssesmentObjectEntity
{
    private static $varMap = [
        'piping_installation' => 'plumbing_info',
    ];

    private function getRealVarName($name){
        return isset(self::$varMap[$name]) ? self::$varMap[$name] : $name;
    }

    public function getValue($name, $defaultValue = null) {
        switch($name) {
            case 'object_floor':
                return $this->getObjectFloor();
                break;
            default:
                $varName = $this->getRealVarName($name);
                return parent::getValue($varName, $defaultValue);
        }

    }

    public function field(string $varName, array $options = []) : string
    {
        $varName = $this->getRealVarName($varName);
        return parent::field($varName, $options);
    }

    public function __isset($varName){
        return property_exists($this, $varName) || (isset(self::$varMap[$varName]) && property_exists($this, self::$varMap[$varName]));

    }

    public function &__get($varName) {
        if(property_exists($this, $varName)) {
            return $this->$varName;
        }
        if(array_key_exists($varName, self::$varMap)) {
            $realVarName = $this->getRealVarName($varName);;
            if(isset($this->$realVarName)) {
                $this->$varName = $this->$realVarName;
                $this->$varName['name'] == $varName;
                return $this->$varName;
            }
        }
        $n = NULL;
        return $n;
    }

    public function getObjectFloor() : string
    {
        return empty($this->object_floor['value'])
            ? ''
            : (($this->object_floor['value']) .
                (empty($this->getValue('floor_count')) ?
                    '' : " от {$this->getValue('floor_count')}"));
    }

    public function getControlData(string $varName) : array
    {
        $data = parent::getControlData($varName);
        switch($varName) {
            case 'other_info':
            case 'object_outbuildings':
                $data['disabled'] = '1';
                break;
        }

        return $data;
    }

    /**
     * Rerturns the perfect part area. The value in the first row for 'land_meters'
     * If the mask is not saved this will return '0';
     *
     * @return string
     */
    public function getArea() : string
    {
        if(empty($this->_area)) {
            $col_land_meters = $this->getGroupColIndexByname('area_group_table', 'land_meters');
            if(isset($this->area_group_table['values']) && isset($this->area_group_table['values'][1])){
                $this->_area = $this->area_group_table['values'][1][$col_land_meters] ?? '0';
            } else {
                $this->_area = '0';
            }
        }
        return $this->_area;
    }

    public function getZp() : string
    {
        if(empty($this->zpVal)) {
            $groupName = 'area_appr_group_table';
            $this->zpVal = $this->$groupName['values']['1'][$this->getGroupColIndexByname($groupName, 'zp_acc_area')] ?? '';
        }
        return $this->zpVal;
    }

    public function getRzp(string $approach) : string
    {
        if(empty($this->rzpVal)) {
            switch($approach){
                case 'cost' :
                    $rzpVar = 'total_area_expense';
                    break;

                case 'revenue' :
                    $rzpVar = 'total_area_income';
                    break;

                case 'market' :
                    $rzpVar = 'total_area_market';
                    break;
            }

            $this->rzpVal = $this->getValue($rzpVar, '');
        }
        return $this->rzpVal;
    }
}