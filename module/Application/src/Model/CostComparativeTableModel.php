<?php
namespace Application\Model;

use Application\Exception\Analogue\BadAnalogSetting;
use Application\Service\LockReportService;

class CostComparativeTableModel extends ComparativeTableModel
{
    public function getCorrAnalogNames() : array
    {
        return [
            'correction_analogue_price_eur_sqm',
            'correction_absolute_price',
            'correction_percentage',
            'correction_analogue_weight',
        ];
    }

    public function getDefaultRowNames() : array
    {
        return [
            'comparison_element_id',
            'comparison_element',
            'date_analogue',
            'property_area',
            'price_analog_eur',
            'price_analog_eur_sqm',
        ];
    }

    public function fillInObjectLandGroup($objects, $distributeAll = true) : void
    {
        $groupName = 'object_land_group';

        $col_id = $this->getGroupColIndexByname($groupName, 'object_id');
        $col_sqm = $this->getGroupColIndexByname($groupName, 'object_sqm');
        $col_zp = $this->getGroupColIndexByname($groupName, 'object_zp');
        $col_rzp = $this->getGroupColIndexByname($groupName, 'object_rzp');

        $this->$groupName['types'][$col_id] = 'text';

        $this->$groupName['objectNames'] = [];
        $oldValues = $this->$groupName['values'] ?? [];
        $this->$groupName['values'] = [];

        $newObjects = [];
        // Search for objects already in the group and update the ZP and RZP values
        $i = 1;
        foreach ($objects as $k => $v) {
            $found = false;
            foreach ($oldValues as $k1=>$v1) {
                if($v->id == $v1[$col_id]){
                    $this->$groupName['values'][$i] = $v1;
                    $this->$groupName['values'][$i][$col_zp] = $v->getZp();
                    $this->$groupName['values'][$i][$col_rzp] = $v->getRzp('cost');
                    $this->$groupName['objectNames'][$i] = $v->getTitle();
                    if(!$distributeAll){
                        try {
                            $this->$groupName['values'][$i][$col_sqm] = $v->getArea();
                        } catch(\Error $e) {
                            // The reason is that the needed value is not present.
                            // There is lttle chance this erro ocuring as the object is already added and this was checked
                            throw new \Exception("Моля попълненте таблицата с площи за обект „{$v->getTitle()}“!", null, $e);
                        }
                    }
                    $found = true;
                    $i++;
                    break;
                }
            }
            if(!$found) {
                $newObjects[] = $v;
            }
        }
        if(count($newObjects)){
            $col_percentage = $this->getGroupColIndexByname($groupName, 'object_percentage');
            $col_sqm = $this->getGroupColIndexByname($groupName, 'object_sqm');
            $col_final_percentage = $this->getGroupColIndexByname($groupName, 'object_final_percentage');
            $col_final_sqm = $this->getGroupColIndexByname($groupName, 'object_final_sqm');
            $col_land_corrected_value = $this->getGroupColIndexByname($groupName, 'object_land_corrected_value');

            // Fill in the new objects with default values for calculated fields
            $i = count($this->$groupName['values']) + 1;
            foreach ($newObjects as $k=>$v) {
                if(!$distributeAll) {
                    // Check if the getArea() method will fail
                    try {
                        $v->getArea();
                    } catch(\Error $e) {
                        // The reason is that the needed value is not present.
                        throw new \Exception("Моля попълненте таблицата с площи за обект „{$v->getTitle()}“!", null, $e);
                    }
                }
                $this->$groupName['values'][$i] = [
                    $col_id => $v->id,
                    $col_zp => $v->getZp(),
                    $col_rzp => $v->getRzp('cost'),
                    $col_percentage => 0,
                    $col_sqm => $distributeAll ? 0 : $v->getArea(),
                    $col_final_percentage => 0,
                    $col_final_sqm => 0,
                    $col_land_corrected_value => 0,
                ];
                $this->$groupName['objectNames'][$i] = $v->getTitle();
                $i++;
            }
        }
    }

    public function loadMainValues($assesmentObject, $analogSettingsByVarName, $analogs, $numberOfOldAnalogs ) : array
    {
        $groupAnalogsName2Index = array_flip($this->group_analogue['names']);

        $compElementIndex = $groupAnalogsName2Index['comparison_element_id'];
        $analogGroup = $this->group_analogue['values'];
        $analogsIds = [];
        $countAnalogGroup = count($analogGroup);
        // Start from 2 to skip the assessment object.
        for ($i = 2; $i <= $countAnalogGroup; $i++) {
            $analogsIds[$i-1] = $analogGroup[$i][$compElementIndex];
        }

        $mainParamsName2index = array_flip($this->mainparams_group['names']);
        $settingsInTheGroup = [];
        $newMainParams = [];
        $r = 1;
        $development_parametersVal = '';
        $objectLocation = '';
        foreach ($this->mainparams_group['values'] ?? [] as $v) {
            $varName = $v[$mainParamsName2index['param']];
            if (array_key_exists($varName, $analogSettingsByVarName)) {
                $objectVarName = str_replace('analog_', '', $varName);
                $settingsInTheGroup[] = $varName;
                $row = $v;

                switch ($varName) {
                    case 'analog_development_parameters':
                        $development_parametersVal = $v[$mainParamsName2index['param_object']] ?? '';
                        break;
                    case 'analog_location':
                        $objectLocation = $v[$mainParamsName2index['param_object']] ?? '';
                        break;
                    case 'analog_pi_area':
                        $row[$mainParamsName2index['param_object']] = $assesmentObject->accepted_area['value'];
                        break;
                    default:
                        $row[$mainParamsName2index['param_object']] = $assesmentObject->getValue($objectVarName);
                }

                foreach ($analogsIds as $k1 => $v1) {
                    $rIndex_ = $mainParamsName2index["param_analog{$k1}"];
                    $rIndex = $mainParamsName2index["param_analog{$k1}_corr"];
                    $optionLabelAsValue = in_array($varName, ['analog_development_parameters']);
                    if ($k1 > $numberOfOldAnalogs || !isset($v[$rIndex]) || $v[$rIndex] == '' || $analogs[$v1]->_refresh) {
                        $row[$rIndex_] = !$optionLabelAsValue && in_array($analogs[$v1]->$varName['type'], ['dropdown'])
                            ? $analogs[$v1]->$varName['value']
                            : $analogs[$v1]->getValue($varName);
                        if (!$analogs[$v1]->_refresh) {
                            $row[$rIndex] = '0';
                        }
                    } elseif ($varName == 'analog_type_' && !LockReportService::getInstance()->isLocked()) {
                        $row[$rIndex_] = $analogs[$v1]->$varName['value'];
                    } elseif ($varName == 'analog_other_info'
                        || $varName == 'analog_object_outbuildings') {
                        $row[$rIndex_] = $analogs[$v1]->getValue($varName);
                    }
                }

                $newMainParams[$r++] = $row;
            }
        }

        reset($analogs);
        $this->setAnalogRefs($analogs);
        $exAnalog = current($analogs);
        $emptyRow = array_fill(0, count($mainParamsName2index), '');
        // Note the var $r just continues from above.
        foreach ($analogSettingsByVarName as $k => $v) {
            $objectVarName = str_replace('analog_', '', $k);

            switch ($objectVarName) {
                case 'type_': // this is a fix for the var 'type' as it is basic
                    $assesmentObject->$objectVarName = $exAnalog->$k;
                    $assesmentObject->$objectVarName['label'] = $v['analog_nz_var_name'];
                    $assesmentObject->$objectVarName['value'] = '';
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    $assesmentObject->$objectVarName['hidden'] = '1';
                    break;
                case 'location':
                    $assesmentObject->$objectVarName = $exAnalog->$k;
                    $assesmentObject->$objectVarName['name'] = $objectVarName;
                    $assesmentObject->$objectVarName['label'] = $v['analog_nz_var_name'];
                    $assesmentObject->$objectVarName['value'] = $objectLocation;
                    $assesmentObject->$objectVarName['readonly'] = '0';
                    break;
                case 'pi_area':
                    $assesmentObject->$objectVarName = $assesmentObject->accepted_area;
                    $assesmentObject->$objectVarName['label'] = $v['analog_nz_var_name'];
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    break;
                case 'electricity_info':
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    break;
                case 'piping_installation':
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    break;
                case 'sewage_installation':
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    break;
                case 'eng_infrasructure':
                    $assesmentObject->$objectVarName = $assesmentObject->communication_distance;
                    //$eng_infrasructure = $assesmentObject->electricity_info->
                    $assesmentObject->$objectVarName['label'] = $v['analog_nz_var_name'];
                    $assesmentObject->$objectVarName['value'] = $assesmentObject->getEngInfrasructure();
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    break;
                case 'development_parameters':
                    $assesmentObject->$objectVarName = $assesmentObject->communication_distance;
                    $assesmentObject->$objectVarName['label'] = $v['analog_nz_var_name'];
                    $assesmentObject->$objectVarName['value'] = $development_parametersVal;
                    $assesmentObject->$objectVarName['readonly'] = '0';
                    break;
                case 'buildings_status':
                    $assesmentObject->$objectVarName['readonly'] = '1';
                    break;

            }

            if(in_array($k, $settingsInTheGroup)) {
                continue;
            }

            $row = $emptyRow;
            $row[$mainParamsName2index['param']] = $k;
            if(!isset($assesmentObject->$objectVarName)){
                throw new BadAnalogSetting("The {$objectVarName} is not available in objects of type {$assesmentObject->type}");
            }
            $row[$mainParamsName2index['param_object']] = $assesmentObject->$objectVarName['value'];
            foreach ($analogsIds as $k1=>$v1){
                $optionLabelAsValue = in_array($k, ['analog_development_parameters']);
                $row[$mainParamsName2index["param_analog{$k1}"]] = !$optionLabelAsValue && in_array($assesmentObject->$objectVarName['type'], ['dropdown'])
                    ? $analogs[$v1]->$k['value']
                    : $analogs[$v1]->getValue($k);
                $row[$mainParamsName2index["param_analog{$k1}_corr"]] = '0';
            }

            $row[$mainParamsName2index['param_showrow']] = 'shown';
            $newMainParams[$r++] = $row;
        }

        $this->mainparams_group['values'] = $newMainParams;

        // Satisfy the return type, as this is overloded method
        return [];
    }
}
