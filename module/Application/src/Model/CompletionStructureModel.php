<?php
namespace Application\Model;

use Application\Model\Exception\IdIsNotRegistered;
use Application\Model\Exception\IdIsNotValue;

class CompletionStructureModel
{
    /**
     * @var array
     */
    private $struct = array();
    /**
     * @var array
     */
    private $cache = array();
    /**
     * @var array
     */
    private $cacheList = array();


    public function __sleep()
    {
        return ['struct'];
    }

    /**
     * Put a CompleationValue at a specific ID/path
     *
     * @param string $id
     * @param CompletionValue $entity
     * @return self
     */
    public function put(string $id, CompletionValue $entity) : self
    {
        $idMap = self::parseId($id);
        $place = &$this->struct;
        $idTail = '';
        foreach ($idMap as $v){
            if(!isset($place[$v])){
                $place[$v] = array();
            }
            $idTail .= ($idTail=='' ? '' : '/') . $v;
            if(isset($this->cacheList[$idTail])){
                unset($this->cacheList[$idTail]);
            }
            $place = &$place[$v];
        }

        $place['_entity'] = $entity;

        return $this;
    }

    /**
     * Return a CompleationValue with the ID or throws an exception on failure
     *
     * @param string $id
     * @throws IdIsNotRegistered
     * @throws IdIsNotValue
     * @return CompletionValue
     */
    public function get(string $id) : CompletionValue
    {
        if(isset($this->cache[$id])){
            return $this->cache[$id];
        }
        $idMap = self::parseId($id);
        $place = &$this->struct;
        foreach ($idMap as $v){
            if(!isset($place[$v])){
                throw new IdIsNotRegistered("The '{$v}' part of the  id '{$id}' is missing in '".__CLASS__."'!");
            }
            $place = &$place[$v];
        }
        if(!isset($place['_entity'])){
            throw new IdIsNotValue("The id '{$id}' is not an Entity in '".__CLASS__."'!");
        }

        $this->cache[$id] = $place['_entity'];
        return $this->cache[$id];
    }

    /**
     * Returns a list of all the children CompletionValue objects.
     *
     * @param string $id
     * @throws IdIsNotRegistered
     * @return CompletionValue[]
     */
    public function getList(string $id) : array
    {
        if(isset($this->cacheList[$id])){
            return $this->cacheList[$id];
        }
        $idMap = self::parseId($id);
        $place = &$this->struct;
        foreach ($idMap as $v){
            if(!isset($place[$v])){
                throw new IdIsNotRegistered("The '{$v}' part of the  id '{$id}' is missing in '".__CLASS__."'!");
            }

            $place = &$place[$v];
        }

        $this->cacheList[$id] = self::_getEntitiesFromArray($id, $place);
        return $this->cacheList[$id];
    }

    private static function _getEntitiesFromArray(string $id, $array) : array
    {
        $list = array();
        foreach($array as $k=>$v){
            if(is_a($v, CompletionValue::class)){
                $list[$id] = $v;
                continue;
            }
            if(is_array($v)){
                $list = array_merge($list, self::_getEntitiesFromArray("{$id}/{$k}",$v));
            }
        }
        return $list;
    }

    /**
     * Calculates completion values from children of the id/path
     *
     * @param string $id
     * @return int
     */
    public function calcCompletion(string $id) : int
    {
        try{
            $entities = $this->getList($id);
        }catch(IdIsNotRegistered $e){
            $entities = [];
        }

        $compleation = 0;
        foreach ($entities as $v){
            $compleation += $v->completion();
        }
        return $compleation;
    }

    /**
     * Calculates the average of the completionValues of children of the id/path
     *
     * @param string $id
     * @return int
     */
    public function calcCompletionAvg(string $id) : int
    {
        try{
            $numberOfItems = count($this->getList($id));
            $avg = $numberOfItems == 0 ? 0 : ($this->calcCompletion($id) / $numberOfItems);
        }catch(IdIsNotRegistered $e){
            $avg = 0;
        }
        return (int)$avg;
    }

    /**
     * Remove entity or children of the id/path
     * @param string $id
     * @throws IdIsNotRegistered
     * @return self
     */
    public function remove(string $id) : self
    {
        $idMap = self::parseId($id);
        $len = count($idMap);
        $place = &$this->struct;
        foreach ($idMap as $k => $v){
            if(!isset($place[$v])){
                throw new IdIsNotRegistered("The '{$v}' part of the  id '{$id}' is missing in '".__CLASS__."'!");
            }
            if($k == $len-1){
                unset($place[$v]);
                return $this;
            }
            $place = &$place[$v];
        }
        return $this;
    }

    private static function parseId(string $id) : array
    {
        return explode('/', $id);
    }

}
