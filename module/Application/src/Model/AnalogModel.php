<?php

namespace Application\Model;

use Application\Model\Traits\OptionVar;
use Application\Model\Traits\ControllGenerator;
use Application\RenderTemplateTrait;
use Application\ConfigAccessTrait;
use Application\Model\Traits\SettingsParser;

use function Composer\Autoload\includeFile;

/**
 *
 */
class AnalogModel extends NomenclatureModel
{
    use OptionVar;
    use ControllGenerator;
    use ConfigAccessTrait;
    use RenderTemplateTrait;
    use SettingsParser;

    /**
     * @var string[]
     */
    private static $basicVars = [
        'id',
        'name',
        'date',
    ];

    /**
     * @var string[]
     */
    private static $i18nVars = [
        'name',
    ];

    /**
     * @var string[]
     */
    private static $sortableVars = [
        'name',
        'analog_object_report_type',
        'analog_date',
        'analog_date',
        'analog_price_eur',
        'analog_price_per_sq_eur',
    ];

    /**
     * @param $varName
     *
     * @return bool
     */
    public function __isset($varName)
    {
        return isset($this->$varName) || $varName == 'analog_type_';
    }

    /**
     * @param $varName
     *
     * @return null
     */
    public function &__get($varName)
    {
        if (property_exists($this, $varName)) {
            return $this->$varName;
        }
        // This is a fix for using 'analog_type'
        if ($varName == 'analog_type_') {
            $this->analog_type_ = $this->analog_type;
            $this->analog_type_['name'] = 'analog_type_';
            return $this->analog_type;
        }
        $n = null;
        return $n;
    }

    /**
     * Accepts one parameter the actual field name and
     * returns the search name, like 'a__some_var_name' or 'n.name'
     *
     * @param string $varName
     */
    public function varSearchName(string $varName): string
    {
        return (in_array($varName, self::$basicVars) ? "n." : "a__") . $varName;
    }

    /**
     * @param string $varName
     *
     * @return string
     */
    public function varSortName(string $varName): string
    {
        return (in_array($varName, self::$i18nVars) ? "ni18n.{$varName}" : $this->varSearchName($varName));
    }

    /**
     * @param string $varName
     *
     * @return bool
     */
    public function _isVarSortable(string $varName): bool
    {
        return in_array($varName, self::$sortableVars);
    }

    /**
     * @param $varName
     *
     * @return mixed|void
     */
    public function getValueLabel($varName)
    {
        $var = $this->$varName;
        foreach ($var['options'] as $o) {
            if ($o['option_value'] == $var['value']) {
                return $o['label'];
            }
        }
    }

    /**
     * @param $varName
     * @param $defaultValue
     *
     * @return mixed|string|null
     */
    public function getValue($varName, $defaultValue = null)
    {
        if (!isset($this->$varName)) {
            return $defaultValue;
        }
        if (is_array($this->$varName)) {
            $type = $this->$varName['type'];
            switch ($type) {
                case 'date':
                    return \DateTime::createFromFormat('Y-m-d', $this->$varName['value'])->format('d.m.Y');
            }
            $name = $this->$varName['name'];
            // Start with exceptions by name
            switch ($name) {
                case 'analog_object_report_type':
                    return $this->getValueLabel($varName);
            }
            switch ($this->$varName['type']) {
                case 'dropdown':
                    return $this->getValueLabel($varName);
            }
            return parent::getValue($varName, $defaultValue);
        } else {
            return $this->$varName;
        }
    }

    /**
     * @param $varName
     * @param $defaultValue
     *
     * @return mixed|string|null
     * @throws \Application\Exception\TemplateNotFound
     */
    public function getReadonlyValue($varName, $defaultValue = null)
    {
        // Basic var, no exceptions, so shortcirquit here
        if (!is_array($this->$varName)) {
            return $this->getValue($varName, $defaultValue);
        }

        $name = $this->$varName['name'];
        // Start with exceptions by name
        switch ($name) {
            case 'analog_object_report_type':
                return $this->getValueLabel($varName);
            default:
                // Start with exceptions by type
                switch ($this->$varName['type']) {
                    case 'dropdown':
                        return $this->getValueLabel($varName);
                }
                $settings = self::parseSettings($this->$varName['source'], "/^alvis_.+$/");
                if (isset($settings['alvis_view_mode']) && $settings['alvis_view_mode']) {
                    switch ($settings['alvis_view_mode']) {
                        case 'link':
                            // alvis_view_mode := link
                            $val = $this->getValue($varName, $defaultValue);
                            if (empty($val)) {
                                return '';
                            }

                            $label = '<span class="fa fa-external-link"></span> ';
                            $label .= preg_replace('/(https?:\/\/[^\/]+\/)(.*)/', '$1...', $val);

                            return $this->rendrTemplate('partial_views/anchor.php', [
                                'href' => trim(htmlentities($val)),
                                'attributes' => [
                                    'target' => '_blank',
                                    'title' => htmlentities($val),
                                ],
                                'label' => $label,
                            ]);
                    }
                }
        }

        // if no conditions are met above this is a fallback for normal behaviour
        return $this->getValue($varName, $defaultValue);
    }

    /**
     * Returns HTML code for representing the image file.
     * This is only for vewing and can't be using for manipulating the file
     *
     * @param bool $showThumb If set to true, it will show thumbnail
     * @param int  $thumbSize if the $showThumb is true then this will controll how big the thumbnail will be.
     *
     * @return string
     */
    public function fieldFile(bool $showThumb = false, int $thumbSize = 90): string
    {
        $fileVar = $this->analog_file;
        if (!isset($fileVar['value']['properties']['id']) || !empty($fileVar['value']['properties']['deleted_by'])) {
            return '';
        }

        $fileUrl = "viewfile/{$fileVar['model_id']}/{$fileVar['value']['properties']['id']}?nom=1";

        $html = "<a href=\"{$fileUrl}\" target=\"_blank\">";
        if ($showThumb) {
            $thumbSrc = "viewfile/{$fileVar['model_id']}/{$fileVar['value']['properties']['id']}?maxwidth={$thumbSize}&maxheight={$thumbSize}&nom=1";
            $html .= "<img src=\"{$thumbSrc}\" />";
        } else {
            $fileTitle = '<i class="fa fa-image"></i>';
            $html .= "<span>{$fileTitle}</span>";
        }
        $html .= "</a>";

        return $html;
    }
}
