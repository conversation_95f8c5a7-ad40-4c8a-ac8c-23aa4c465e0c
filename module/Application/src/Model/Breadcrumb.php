<?php
namespace Application\Model;

class Breadcrumb
{
    private $href;
    private $label;
    private $classes;
    public function __construct($href, $label, array $classes = [])
    {
        $this->href = $href;
        $this->label = $label;
        $this->classes = $classes;
    }

    public function getHref() {
        return $this->href;
    }
    public function getLabel() {
        return $this->label;
    }
    public function getClasses() {
        return $this->classes;
    }

    public function renderClassAttr() {
        return 'class="'.htmlspecialchars(implode(' ', $this->classes)).'"';
    }
}