<?php
namespace Application\Model\FormFields;

interface FormFieldInterface
{

    public function render() : string;
    public function addClass(string $class) : void;
    public function addAttribute(string $attribute, string $value) : void;
    public function addContainerClass(string $class) : void;
    public function addContainerAttribute(string $attribute, string $value) : void;

    public function renderContainerClasses() : string;
    public function renderContainerAttributes() : string;
    public function renderClasses() : string;
    public function renderAttributes() : string;
    public function setLabel(string $label) : void;
    public function getLabel() : string;

}