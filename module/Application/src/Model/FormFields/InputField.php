<?php
namespace Application\Model\FormFields;

class InputField extends BaseField implements FormFieldInterface
{
    private static $template = 'form_fields/input.phtml';


    function render() : string
    {
        $template = self::$template;
        $vars = [
            'label' => $this->_label,
            'type' => $this->_type,
            'name' => $this->_name,
            'value' => $this->_value,
        ];
        if($this->_type == 'hidden'){
            $this->addContainerAttribute('style', 'display:none;');
        }
        foreach ($vars as $k => $v) {
            $$k = $v;
        }
        ob_start();
        include self::getTemplatesPath()."/{$template}";
        return ob_get_clean();
    }
}
