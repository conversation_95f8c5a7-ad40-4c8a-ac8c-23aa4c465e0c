<?php

namespace Application\Gateway;

use Bgservice\NzoomClient\Entity\NzoomModelEntity;
use Application\Model\AuditEntity;

/**
 *
 */
class AuditGateway extends AbstractGateway implements GatewayInterface
{
    /**
     *
     */
    public const MODULE = 'reports';
    /**
     *
     */
    public const ACTION = 'generate_report';
    /**
     *
     */
    public const MODEL_CLASS = AuditEntity::class;
    /**
     *
     */
    public const REPORT_TYPE = 'rest_history_alvis';
    /**
     *
     */
    public const TYPE = 'audit';


    /**
     * @param  array $filters
     * @param  array|null $filterVars
     * @param  bool $force
     * @return array|NzoomModelEntity[]
     */
    public function fetchAll(array $filters = [], ?array $filterVars = null, bool $force = false): array
    {
        $id = $filters['id'];
        $type = $filters['model_type'];
        $request = $this->generateFetchRequest($id, []);
        $request->addData('view', null);
        $request->addData(self::ACTION, null);
        $request->addData(self::MODULE, self::ACTION);
        $request->addData('report_type', self::REPORT_TYPE);
        $request->addData('type', self::TYPE);
        $request->addData('parent_id', $id);
        $request->addData('model_type', $type);
        $request->addData('display', 100000);
        $response = $this->execute($request);

        $data = $response->getData();
        $records = [];

        if ($data['results']) {
            $entityClass = self::MODEL_CLASS;
            $proto = new $entityClass();
            foreach ($data['results']['vars'] as $v) {
                //d($v);
                $entity = clone $proto;
                foreach ($v as $k1 => $v1) {
                    $entity->$k1 = $v[$k1];
                }
                $records[] = $entity;
            }
        }

        return $records;
    }

    /**
     * @param  int $id
     * @param  array $data
     * @return void
     */
    public function push(int $id, array $data)
    {
    }
}
