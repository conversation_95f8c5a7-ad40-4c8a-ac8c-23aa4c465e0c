<?php

namespace Application\Gateway;

use Application\Model\CopyReportEntity;
use Bgservice\NzoomClient\Entity\NzoomModelEntity;

/**
 * Извличане на данни от nzoom
 */
class CopyReportGateway extends AbstractGateway implements GatewayInterface
{
    /**
     * Име на модул
     *
     * @var string
     */
    public const MODULE = 'reports';
    /**
     * Име
     *
     * @var string
     */
    public const ACTION = 'generate_report';
    /**
     * Клас с резултати
     *
     * @var string
     */
    public const MODEL_CLASS = CopyReportEntity::class;
    /**
     * Id на справка
     *
     * @var string
     */
    public const REPORT_TYPE = 'advance_report_copy';

    /**
     * Извличане на всички записи отговарящи на зададените критерии във $filters
     *
     * @param array $filters Критерии за търсене
     * @param $filterVars
     * @param bool  $force
     *
     * @return array|NzoomModelEntity[]
     */
    public function fetchAll(array $filters = [], ?array $filterVars = null, bool $force = false): array
    {
        $request = $this->generateFetchRequest(null, []);
        $request->setDataFormat('form');
        $request->setPost();
        $request->addData('view', null);
        $request->addData(self::ACTION, null);
        $request->addData(self::MODULE, self::ACTION);
        $request->addData('report_type', self::REPORT_TYPE);
        $request->addData('display', 20);

        foreach ($filters as $key => $filter) {
            $request->addData($key, $filter);
        }

        $response = $this->execute($request);
        $data = $response->getData();
        $records = [];

        if ($data === null || !isset($data['results'])) {
            return $records;
        }

        $entityClass = self::MODEL_CLASS;
        $proto = new $entityClass();
        foreach ($data['results'] as $v) {
            $entity = clone $proto;
            foreach ($v as $k1 => $v1) {
                $entity->$k1 = $v1;
            }
            $records[] = $entity;
        }
        return $records;
    }

    /**
     * Send request to nzoom for copy
     *
     * @param array $params
     *
     * @return array|null Array of messages Error($data['errors'])/Success($data['messages'])
     */
    public function copyReport(array $params): ?array
    {
        $request = $this->generateFetchRequest(null, []);
        $request->setDataFormat('form');
        $request->setGet();
        $request->addData('view', null);
        $request->addData('display', null);
        $request->addData('filter_vars', null);
        $request->addData('launch', self::MODULE);
        $request->addData('report_type', self::REPORT_TYPE);
        $request->addData(self::MODULE, 'copy_report');

        foreach ($params as $key => $value) {
            $request->addData($key, $value);
        }

        $response = $this->execute($request);

        return $response->getData();
    }

    /**
     * Get last response page count
     *
     * @return int
     */
    public function getLastResponsePageCount(): int
    {
        return $this->lastResponse()->getData()['pagination']['pages'] ?? 0;
    }

    /**
     * Get last response record count
     *
     * @return int
     */
    public function getLastResponseRecordCount(): int
    {
        return $this->lastResponse()->getData()['pagination']['total'] ?? 0;
    }

    /**
     * Get rows per page
     *
     * @return int
     */
    public function getRowsPerPage(): int
    {
        return $this->lastResponse()->getData()['pagination']['rpp'] ?? 10;
    }

    /**
     * Push
     *
     * @param int   $id
     * @param array $data
     *
     * @return void
     */
    public function push(int $id, array $data)
    {
    }
}
