<?php

namespace Application\Gateway;

use Application\Model\CommentModel;
use Bgservice\NzoomClient\Entity\NzoomModelEntity;
use Application\Model\ComunicationEntity;
use Bgservice\NzoomClient\Request;
use Bgservice\NzoomClient\Response;

/**
 *
 */
class DocumentComunicationsGateway extends AbstractGateway implements GatewayInterface
{
    /**
     *
     */
    public const MODULE = 'communications';
    /**
     *
     */
    public const ACTION = 'ajax_list_communications';
    /**
     *
     */
    public const MODEL_CLASS = ComunicationEntity::class;
    /**
     *
     */
    public const TYPE = 'emails';

    /**
     * @param  $id
     * @param  $emailTemplate
     * @return string
     */
    public function fetchTemplate($id, $emailTemplate): string
    {
        $request = new Request();
        $request->setResponseClass(Response::class);
        $this->_decorateNzoomRequest($request);
        $request->setGet();
        $request->addData('launch', self::MODULE);
        $request->addData(self::MODULE, 'ajax_change_email');
        $request->addData('ajax_change_email', $id);
        $request->addData('real_controller', 'documents');
        $request->addData('email_content', $emailTemplate);
        $request->addData('module', 'documents');
        $response = $this->execute($request);
        return $response->getBodyString();
    }

    /**
     * @param  int $page
     * @param  int $ppr
     * @param  array $filters
     * @param  $filterVars
     * @param  bool $force
     * @return array|NzoomModelEntity[]
     */
    public function fetchPage(
        int $page = 1,
        int $ppr = 10,
        array $filters = [],
        $filterVars = null,
        bool $force = false
    ): array {
        $id = $filters['id'];
        $request = $this->generateFetchRequest($id, []);
        $request->addData('view', null);
        $request->addData(self::ACTION, null);
        $request->addData(self::MODULE, self::ACTION);
        $request->addData('communication_type', self::TYPE);
        $request->addData('module', 'documents');
        $request->addData('model_id', $id);
        $request->addData('sort', 'date');
        $request->addData('order', 'DESC');
        $request->addData('page', $page);
        $request->addData('display', $ppr);
        $response = $this->execute($request);
        return $response->getModels(self::MODEL_CLASS);
    }

    /**
     * @return mixed
     */
    public function getLastResponsePageCount()
    {
        return $this->lastResponse()->getData()['pagination']['pages'];
    }

    /**
     * @return mixed
     */
    public function getLastResponseRecordCount()
    {
        return $this->lastResponse()->getData()['pagination']['total'];
    }

    /**
     * @param  int $id
     * @param  array $data
     * @return void
     */
    public function push(int $id, array $data)
    {
    }

    /**
     * @param  $id
     * @param  $modelType
     * @param  $bodyStr
     * @param  array $emailTo
     * @param  array $emailCc
     * @param  $subject
     * @param  $attachments
     * @return bool
     */
    public function post($id, $modelType, $bodyStr, array $emailTo, array $emailCc, $subject, $attachments)
    {
        $request = new Request();
        $request->setResponseClass(Response::class);
        $this->_decorateNzoomRequest($request);
        $request->setPost();
        $request->setDataFormat('json');
        $request->addHeader('Content-type: application/json');

        $query = [
            'launch' => static::MODULE,
            static::MODULE => 'ajax_send_communication_email',
            'module' => 'documents',
        ];
        $request->setUrl('index.php?' . http_build_query($query));

        //$bodyStr = $this->fetchTemplate($id, $emailTemplate);
        $attachmentArray = [];
        foreach ($attachments as $k => $v) {
            $attachmentArray[] = "file_{$v}";
        }

        $request->addData('model_id', $id);
        $request->addData('model', 'Document');
        $request->addData('type', "{$modelType}");
        $request->addData('model_lang', 'bg');
        $request->addData('email_template', '');
        $request->addData('email_subject', $subject);
        $request->addData('customer_email', $emailTo);
        $request->addData('read_receipt_email', '<EMAIL>');
        $request->addData('add_signature', 1);
        $request->addData('body', $bodyStr);
        $request->addData('attached_files', $attachmentArray);
        if (!empty($emailCc)) {
            $request->addData('customer_email_cc', $emailCc);
        }
        $response = $this->execute($request);
        $data = $response->getData();

        return !isset($data['errors']);
    }
}
