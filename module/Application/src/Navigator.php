<?php

namespace Application;

class Navigator
{
    public static function fetchBaseUrl(): string
    {
        $dir = dirname($_SERVER['SCRIPT_NAME']);
        $uri = $_SERVER['REQUEST_URI'];

        // Ensure $dir is not just "/" (root directory)
        if ($dir !== '/' && strpos($uri, $dir) === 0) {
            // Remove $dir only from the beginning of $uri
            $uri = substr($uri, strlen($dir));
        }

        return $uri;
    }
}
