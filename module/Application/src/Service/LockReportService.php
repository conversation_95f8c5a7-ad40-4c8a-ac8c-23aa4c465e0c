<?php
namespace Application\Service;

use Application\Client\Advanceaddress\Utilities\Utilities;

class LockReportService
{
    private static $instances;
    private $isLocked;

    private $config;

    private function __construct()
    { }

    public static function getInstance(): self
    {
        return self::$instances ?? (self::$instances = new self());
    }

    private function getConfig ()
    {
        if(is_null($this->config)) {
            $conf = ConfigService::getInstance();
            $this->config = $conf->valueOf();
        }
        return $this->config;
    }

    private function determinLocked() : bool
    {
        $reportId = ParamReportIdService::getInstance()->valueOf();

        // Not logged in or not selected a report yet
        if ($reportId === 0 || !isset($_SESSION['user_data'])) {
            return false;
        }

        $userRole = $_SESSION['user_data']['real_role'];
        $config = $this->getConfig();

        $isAdministrator = in_array($userRole, $config['administration_roles']);
        $isSupervisor = in_array($userRole, $config['supervision_roles']);

        try {
            $substatus = MainReportService::getInstance($reportId)->getResourceParent()->getValue('substatus');
        } catch(\Exception $e) {
            // This exception can be caused if a report is not selected, no no locks
            return false;
        }

        $confLocks = $config['locks'];

        // If no exception from above returned, this is the common logic
        // Hard lock statuses are locking out everybody
        // Soft lock statuses are locking out only non administrators, and non supervisors.
        return in_array($substatus, $confLocks['hard'])
            || (
                in_array($substatus, $confLocks['soft'])
                && !$isAdministrator
                && !$isSupervisor);
    }

    public function isLocked($force = false) : bool
    {
        if(is_null($this->isLocked) || $force) {
            $this->isLocked = $this->determinLocked();

        }

        return $this->isLocked;
    }
}
