<?php
namespace Application\Service;

class ConfigService
{
    private static $instances;
    private $config;

    /**
     * Private Constructor makes sure the objects of this type are
     * created by a static constructor.
     */
    private function __construct()
    { }

    /**
     * Retreaves or creates instance of ConfigService.
     *
     * This is a static constructor, that makes sure only one instance exists
     * This is like singelton
     * This is saving work and memory.
     *
     * @return self
     */
    public static function getInstance(): self
    {
        return self::$instances ?? (self::$instances = new self());
    }

    /**
     * Reads the configuration from disk.
     * We read .php files from the '/config' dir.
     * The files should return arrays.
     * The array that is returned is a merged array of all the files.
     *
     * @return array
     */
    private function readConfigFromDisk() : array
    {
        $config = array();
        foreach(glob('config/*.php') as $v) {
            $c = include $v;
            if(is_array($c)){
                $config = array_merge($config, $c);
            }
        }
        return $config;
    }

    /**
     * Returns the config array.
     * This function will read the config on first use.
     * Subsequent calls will return the same config.
     *
     * @return array
     */
    public function valueOf() : array
    {
        if(is_null($this->config)){
            $this->config = $this->readConfigFromDisk();
        }
        return $this->config;
    }
}
