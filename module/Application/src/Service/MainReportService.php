<?php

namespace Application\Service;

use Application\Controller\AbstractController;
use Application\Model\ReportBaseModel;
use Bgservice\NzoomClient\Request;
use Bgservice\NzoomClient\Response;
use Exception;

use function get_object_vars;

/**
 * This service provides closed and modular approach to handling big models
 * which are expensive to get. The main purpose is to encapsulate the getting
 * of these objects without giving up control.
 * The main use is to get an instance from the static factory and then to get
 * the resource.
 * Internally a resource will be cached both in the instance and in the session
 * for 10 minutes. In subsequent calls the resource will be provided from the session.
 * There is a method to invalidate the cache but leave the runtime data. Also,
 * There is a destroyResource method that will destroy all data in the instance.
 *
 * The static factory makes sure to return the same instance for the same ID,
 * so this can be used in different part of the code, without neet to pass it on.
 * That's the reason it is a service, it functions independently.
 *
 * <AUTHOR>
 */
class MainReportService
{
    /**
     * Client for nZoom
     *
     * @var
     */
    private static $client;

    /**
     * Instances of the service
     *
     * @var array
     */
    private static array $instances = [];

    /**
     * Configuration array
     *
     * @var array
     */
    private array $config;

    /**
     * ID of the report
     *
     * @var int
     */
    public int $id;

    /**
     * Parent id of the report
     *
     * @var int|null
     */
    private ?int $parentId = null;

    /**
     * Fresh flag
     *
     * @var bool
     */
    private bool $fresh = false;

    /**
     * Report model
     *
     * @var null|ReportBaseModel
     */
    private ?ReportBaseModel $report = null;

    /**
     * Report parent model
     *
     * @var ReportBaseModel|null
     */
    private ?ReportBaseModel $reportParent = null;

    /**
     * Private Constructor makes sure the objects of this type are
     * created by a static constructor.
     *
     * @param int $id
     */
    private function __construct(int $id)
    {
        $this->id = $id;
        $this->setConfig(AbstractController::getConfig());
    }

    /**
     * Retrieves or creates instance of MainReportService.
     * Valid IDs are positive integers. 0 is not acceptable ID.
     *
     * This is a static constructor, that makes sure only one instance exists for a given ID
     * This is almost like singleton but with a condition.
     * This is saving work and memory.
     *
     * @param int $id
     *            The ID of the report. It should be >0.
     *
     * @return self
     * @throws Exception
     */
    public static function getInstance(int $id): self
    {
        if ($id <= 0) {
            throw new Exception("Can't create MainReportService instance for ID = {$id}");
        }
        return self::$instances[$id] ?? (self::$instances[$id] = new self($id));
    }

    /**
     * Returns the report model.
     *
     * @param bool $forceFresh
     *
     * @return ReportBaseModel
     */
    public function getResource($forceFresh = false): ReportBaseModel
    {
        if (empty($this->report) && !$forceFresh) {
            $this->report = $this->fetchReportFromSession($this->id);
        }

        // If the report is not found in session or is forcing
        if (empty($this->report) || $forceFresh) {
            $this->report = $this->fetchResourceFromNzoom($this->id);
            $this->pushResourceInSession($this->id, $this->report);
            $this->fresh = true;
        }

        if (!empty($this->report) && $this->report->type == 13) {
            $this->parentId = $this->report->created_from_document_id['value'];
        }

        return $this->report;
    }

    /**
     * Returns the report model.
     *
     * @param bool $forceFresh
     *
     * @return ReportBaseModel
     */
    public function getResourceParent($forceFresh = false): ?ReportBaseModel
    {
        if (empty($this->reportParent) && !$forceFresh) {
            $resource = $this->getResource();
            if (!isset($resource->created_from_document_id)) {
                return null;
            }
            $resourceParentId = $resource->created_from_document_id['value'];
            $this->reportParent = $this->fetchReportFromSession($resourceParentId);
        }

        // If the report is not found in session or is forcing
        if (empty($this->reportParent) || $forceFresh) {
            $resource = $this->getResource();
            if (!isset($resource->created_from_document_id)) {
                return null;
            }
            $resourceParentId = $resource->created_from_document_id['value'];
            $this->reportParent = $this->fetchResourceFromNzoom($resourceParentId);

            $this->pushResourceInSession($resourceParentId, $this->reportParent);
            $this->fresh = true;
        }

        // Set the delivery status
        $this->reportParent->setDelivery(
            in_array(
                $this->reportParent->substatus,
                $this->config['report_substatuses']['delivery']
            )
        );

        // Set the supervision status
        $this->reportParent->setSupervision(
            in_array(
                $this->reportParent->substatus,
                $this->config['report_substatuses']['supervision']
            )
        );

        return $this->reportParent;
    }

    /**
     * Makes the resource cache invalid, so next time it is retrieved
     * a fresh object will be fetched
     *
     * @return void
     */
    public function invalidateResource(): void
    {
        $this->invalidateResourceInSession($this->id);
        if (!is_null($this->parentId)) {
            $this->invalidateResourceInSession($this->parentId);
        }
    }

    /**
     * Destroys the resource in this instance and invalidates any cache
     * The next call of self::getResource() will fetch it from nZoom
     *
     * @return void
     */
    public function destroyResource(): void
    {
        $this->invalidateResource();
        $this->report = null;
        $this->reportParent = null;
        $this->fresh = false;
    }

    /**
     * Returns true if the record is fresh, false if the record is from cache
     *
     * @return bool
     */
    public function isFresh(): bool
    {
        return $this->fresh;
    }

    /**
     * Sets the configurations for n-Zoom so we can retrieve the resource
     *
     * @param array $config
     */
    public function setConfig(array $config): void
    {
        $this->config = $config;
    }

    /**
     * Makes sure to use only one client, so not to waste memory
     */
    private function getClient()
    {
        if (empty(self::$client)) {
            self::$client = NzoomClientService::getInstance()->newClient();
        }

        return self::$client;
    }

    /**
     * Fetches the record from session if exists or returns NULL
     * If the record has expired it also returns NULL
     *
     * @return ReportBaseModel
     */
    private function fetchReportFromSession($id): ?ReportBaseModel
    {
        $report = null;
        if (isset($_SESSION["_main_report_{$id}"])) {
            if (microtime(true) - $_SESSION["_main_report_{$id}_time"] < 600) {
                $report = unserialize($_SESSION["_main_report_{$id}"]);
            }
        }

        return $report;
    }

    /**
     * Save current resource in cache.
     *
     * @return void
     */
    public function saveResource(): void
    {
        $this->pushResourceInSession($this->report->id, $this->report);
    }

    /**
     * Saved a report model in session.
     *
     * @param int             $id
     * @param ReportBaseModel $report
     */
    private function pushResourceInSession(int $id, ReportBaseModel $report): void
    {
        $_SESSION["_main_report_{$id}"] = serialize($report);
        $_SESSION["_main_report_{$id}_time"] = microtime(true);
    }

    /**
     * Invalidates the resource in session.
     *
     * @param int $id
     *
     * @return void
     */
    private function invalidateResourceInSession(int $id): void
    {
        $_SESSION["_main_report_{$id}"] = null;
        $_SESSION["_main_report_{$id}_time"] = null;
        unset($_SESSION["_main_report_{$id}"], $_SESSION["_main_report_{$id}_time"]);
    }

    /**
     * Get new request object
     *
     * @param array $config
     *
     * @return Request|null
     */
    private function getNewRequest(array $config): ?Request
    {
        $request = new Request();
        $request->setResponseClass(Response::class);
        $request->setBaseUrl($config['nzoom_endpoint']);
        $request->setCurlOption(CURLOPT_USERAGENT, $config['nzoom_useragent']);
        $request->addHeader('User-Agent: ' . $config['nzoom_useragent']);
        $request->setCurlOption(CURLOPT_TIMEOUT, $config['nzoom_request_timeout']);
        $request->setCookieFile($config['nzoom_cookiefile']);
        return $request;
    }

    /**
     * Executes the request
     *
     * @param Request $request
     *
     * @return void
     */
    private function executeRequest(Request $request): void
    {
        $client = $this->getClient();
        $client->addRequest($request);
        $client->execute();
        $client->reset();
    }

    /**
     * Fetch the report model from nZoom and returns it.
     *
     * @param int        $id
     * @param array|null $filterVars
     *
     * @return ReportBaseModel
     */
    private function fetchResourceFromNzoom(int $id, ?array $filterVars = null): ?ReportBaseModel
    {
        $request = $this->getNewRequest($this->config);

        $request->addData('launch', 'documents');
        $request->addData('documents', 'view');
        $request->addData('view', $id);
        if ($filterVars) {
            $vars = implode(',', $filterVars);
            $request->addData('filter_vars', $vars);
        }

        $this->executeRequest($request);

        $response = $request->getResponse();
        $model = $response->getModel(ReportBaseModel::class);

        return $model ?? null;
    }

    /**
     * A generic function to update a part of the model and save it in cache
     *
     * @param int             $id         The id of the model
     * @param ReportBaseModel $model      The model to be updated
     * @param array           $filterVars the list of properties to be updated
     *
     * @return void
     */
    private function updatePart(int $id, ReportBaseModel $model, array $filterVars): void
    {
        $partialModel = $this->fetchResourceFromNzoom($id, $filterVars);

        foreach (get_object_vars($partialModel) as $key => $value) {
            $model->$key = $value;
        }

        $this->pushResourceInSession($id, $model);
    }

    /**
     * Takes a list of property names, to be fetched from server and
     * their values saved in the cashed parent model.
     *
     * @param array $filterVars a list of strings to be used as 'filter_vars'
     *
     * @return void
     */
    public function updateParentResourcePart(array $filterVars): void
    {
        $this->getResourceParent();
        $this->updatePart($this->parentId, $this->reportParent, $filterVars);
    }

    /**
     * Takes a list of property names, to be fetched from server and
     * their values saved in the cached model.
     *
     * @param array $filterVars a list of strings to be used as 'filter_vars'
     *
     * @return void
     */
    public function updateResourcePart(array $filterVars): void
    {
        $this->getResource();
        $this->updatePart($this->id, $this->report, $filterVars);
    }

    /**
     * Fetches fresh list of files
     *
     * @param int $id documentId
     *
     * @return AttachmentModel[]
     */
    public function fetchAttachments(int $id): array
    {
        $request = $this->getNewRequest($this->config);

        $request->addData('launch', 'documents');
        $request->addData('documents', 'attachments');
        $request->addData('attachments', $id);
        $request->addData('filter_vars', 'attachments, genfiles');

        $this->executeRequest($request);

        $response = $request->getResponse();
        $model = $response->getModel(ReportBaseModel::class);
        $protoAttachment = new AttachmentModel();
        $allFiles = array_merge($model->genfiles, $model->attachments);
        $attachmentsList = [];
        foreach ($allFiles as $k => $v) {
            $attachment = clone $protoAttachment;
            $attachment->id = $v['properties']['id'];
            $attachment->model = $v['properties']['model'];
            $attachment->model_id = $v['properties']['model_id'];
            $attachment->filename = $v['properties']['filename'];
            $attachment->path = $v['properties']['path'];
            $attachment->revision = $v['properties']['revision'];
            $attachment->added = $v['properties']['added'];
            $attachment->deleted = $v['properties']['deleted'];
            $attachment->parent_id = $v['properties']['parent_id'];
            $attachment->name = $v['properties']['name'];
            $attachment->description = $v['properties']['description'];
            $attachment->lang = $v['properties']['lang'];
            $attachment->added_by_name = $v['properties']['added_by_name'];
            $attachment->modified_by_name = $v['properties']['modified_by_name'];
            $attachment->deleted_by_name = $v['properties']['deleted_by_name'];
            $attachment->icon_name = $v['properties']['icon_name'];
            $attachment->icon_url = $v['properties']['icon_url'];
            $attachment->translations = $v['properties']['translations'];
            $attachment->files_count = $v['properties']['files_count'];
            $attachmentsList[] = $attachment;
        }

        return $attachmentsList;
    }
}
