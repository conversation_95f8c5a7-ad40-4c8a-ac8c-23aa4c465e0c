<?php
namespace Application;

use Application\Exception\TemplateNotFound;

trait RenderTemplateTrait
{
    /**
     * Renders a template file with only the passed vars and returns the rendered string
     * If the second parameter is:
     * [
     *   'variable1' => 'value1'
     * ]
     *
     * Then in the template the variable $variable1 is available.
     * Also the passed array is available in $_vars;
     *
     * @param string $template Full path of the template file or relative to 'view_path' configuration.
     * @param array $_vars The keys of this array gets transformed to variable names that are available in the template
     * @throws TemplateNotFound
     * @return string
     */
    private function rendrTemplate($template, array $_vars)
    {
        $templatePath = $template;
        if(!is_file($templatePath)) {
            $config = self::getConfig();
            $templatePath = $config['view_path']."/{$template}";
            if(!is_file($templatePath)) {
                throw new TemplateNotFound($template, "The specified template is not found!");
            }
        }
        foreach ($_vars as $k => $v) {
            $$k = $v;
        }
        ob_start();
        include $templatePath;
        return ob_get_clean();
    }
    private function renedrTemplate($template, array $_vars)
    {
        return $this->rendrTemplate($template, $_vars);
    }
}