<?php

namespace Application\Controller\ActionControllers;

use Application\Client\Advanceaddress\Documents\Report;
use Application\Client\Advanceaddress\Documents\Report\FetchAllRequest;
use Application\Controller\AbstractController;
use Application\Gateway\ReportListGateway;
use Application\Service\NzoomClientService;
use Bgservice\NzoomClient\AbstractResponse;
use DateTime;

/**
 *
 */
class IndexActionController extends AbstractController
{
    /**
     * Page titles
     *
     * @var array|string[]
     */
    private static array $titleMap = [
        'start' => 'Начало',
    ];

    /**
     * Index action
     * Load all reports of type two, assigned to this user with their status
     * and display them in a table
     *
     * @return void
     */
    public function indexAction()
    {
        self::$title->add(self::$titleMap['start']);

        $config = AbstractController::getConfig();
        $statusFilters = array_merge(
            array_keys($config['report_substatuses_groups']['current']),
            array_keys($config['report_substatuses_groups']['supervision'])
        );

        $statusFilters = array_merge(
            array_keys($config['report_substatuses_groups']['current']),
            array_keys($config['report_substatuses_groups']['supervision'])
        );
        $statusClosed = $config['report_substatuses']['closed'];
        $statusOpened = array_diff($statusFilters, $statusClosed);
        $isAdmin = in_array($_SESSION['user_data']['real_role'], $config['administration_roles']);
        $gateway = new ReportListGateway();

        $reportIds = array_merge(
            $gateway->getCounts(10000, $isAdmin, $statusOpened),
            $gateway->getCounts(600, $isAdmin, $statusClosed, true)
        );

        $reportIdsByStatus = [];
        foreach ($reportIds as $report) {
            $reportIdsByStatus[$report->substatus] = $report;
        }

        include_once $config['view_path'] . '/shared/frameset.php';
    }

    /**
     * Report list ajax
     * Load all reports of type two, assigned to this user with their status
     * and display them in a table
     * Filter reports by reportIds
     * Process report items
     * Pass the data to the front end
     *
     * @return void
     */
    public function reportListAjax()
    {
        $filterReportIds = array_map('intval', explode(',', $_GET['reportIds'] ?? ''));
        $reportIds = implode(',', $filterReportIds);

        if (empty($reportIds)) {
            echo json_encode([]);
            exit;
        }

        $config = $this->getConfig();
        $gateway = new ReportListGateway();
        $reportItems = $gateway->fetchAll(['id' => $reportIds]);
        $reportItemsCount = count($reportItems);

        if ($reportItemsCount === 0) {
            echo json_encode([]);
            exit;
        }

        $this->processReportItems($reportItems, $config['reportList-tagSection']);

        // Pass the data to the front end
        echo json_encode($reportItems);
    }

    /**
     * Process report items
     * - Format dates
     * - Replace semicolon with line break
     * - Process participants
     * - Process tags
     * - Assign processed data to the report object
     * - Assign tags to the report object
     *
     * @param array $reportItems
     * @param $reportListTagSection
     *
     * @return void
     */
    private function processReportItems(array &$reportItems, $reportListTagSection): void
    {
        foreach ($reportItems as $report) {
            $report->deadline = $this->formatDate($report->deadline);
            $report->award_date = $this->formatDate($report->award_date, 'Y-m-d');
            $report->certification_date = $this->formatDate($report->certification_date, 'Y-m-d');

            $report->object_name_type = $this->replaceSemicolonWithLineBreak($report->object_name_type ?? '');
            $report->object_subtype_name = $this->replaceSemicolonWithLineBreak($report->object_subtype_name ?? '');

            $this->processParticipants($report);
            $report->_tags = $this->processTags($report->tags, $reportListTagSection);
        }
    }

    /**
     * Process tags
     * - Filter tags by section
     * - Display tags
     * - Replace semicolon with line break
     * - Return tags
     *
     * @param string $tags
     * @param array  $reportListTagSection
     *
     * @return string
     */
    private function processTags(string $tags, array $reportListTagSection = []): string
    {
        $tagsArray = explode(';', $tags);
        $tagsToDisplay = [];
        foreach ($tagsArray as $tag) {
            // [section_id]-[tag_id]-[tag_label]
            $tagParts = explode('-', $tag);
            if (in_array($tagParts[0], $reportListTagSection)) {
                $tagsToDisplay[] = $tagParts[2];
            }
        }

        return implode(',<br />', $tagsToDisplay) ?? '';
    }

    /**
     * Process participants
     * - Evaluators
     * - Supervisors
     *
     * @param $report
     *
     * @return void
     */
    private function processParticipants($report): void
    {
        $evaluators = [];
        $supervisors = [];
        $recording_role = explode(';', $report->recording_role);
        $recording_role_type = explode(';', $report->recording_role_type);
        foreach ($recording_role_type as $k1 => $v1) {
            switch ($v1) {
                case 2:
                    $supervisors[] = $recording_role[$k1];
                    break;
                case 4:
                    $evaluators[] = $recording_role[$k1];
                    break;
            }
        }
        $report->_evaluaters = implode(',<br />', $evaluators);
        $report->_supervisors = implode(',<br />', $supervisors);
    }

    /**
     * Replace semicolon with line break
     *
     * @param string $string
     *
     * @return string
     */
    private function replaceSemicolonWithLineBreak(string $string): string
    {
        if (empty(trim($string))) {
            return '';
        }
        return str_replace(';', ',<br>', $string);
    }

    /**
     * Format date
     *
     * @param string|null $date
     * @param string      $format
     *
     * @return string
     */
    private function formatDate(?string $date = null, string $format = 'Y-m-d H:i:s'): string
    {
        $dateTime = DateTime::createFromFormat($format, $date ?? '');

        return $dateTime ? $dateTime->format('d.m.Y') : '';
    }
}
