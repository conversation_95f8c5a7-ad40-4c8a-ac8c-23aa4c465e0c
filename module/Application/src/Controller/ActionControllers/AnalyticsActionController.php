<?php
namespace Application\Controller\ActionControllers;

use Application\Client\Advanceaddress\Utilities\CreateControl;
use Application\Client\Advanceaddress\Utilities\Utilities;
use Application\Client\Advanceaddress\Utilities\Messages;
use Application\Controller\AbstractController;

class AnalyticsActionController extends AbstractController
{
    private static $titleMap = [
        '/' => "Допускания и анализи",
        'd' => "D. Допускания и анализи",
    ];

    public function renderAnalyticsView() {
        self::$title->add(self::$titleMap['/']);
        //self::$title->add(self::$titleMap['d']);
        $section = 'analytics';

        // Get the ID of the first analytics type document
        $analyticsId = key(Utilities::getReportRelatedDocuments(array(38)));

        if (!empty($analyticsId)) {
            $params = array(
                'module' => 'documents',
                'id' => $analyticsId,
            );
            $analyticsReport = Utilities::searchOne($params);
            $createControl = new CreateControl($analyticsReport);
        } else {
            // analytics report is guaranteed but if not
            // in this else the error message for analytics report not found should be handled.
            $_SESSION['errors']['analytics'] = sprintf(Messages::msg('report_main_data_required'), 'допускания и анализи');
        }

        $config = self::getConfig();
        include_once $config['view_path'] . '/shared/frameset.php';
    }
}