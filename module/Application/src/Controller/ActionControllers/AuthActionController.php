<?php
namespace Application\Controller\ActionControllers;

use Application\Client\Advanceaddress\Utilities\Utilities;
use Application\Client\AuthenticationAdapter;
use Application\Controller\AbstractController;

class AuthActionController extends AbstractController
{
    private static $titleMap = [
        '/' => 'Вход',
    ];

    private static $isNzoomAuthenticated = null;

    private static function _getAuthenticationAdapter() : AuthenticationAdapter {
        $config = self::getConfig();
        $adapter = new AuthenticationAdapter();
        $adapter->setUrl($config['nzoom_endpoint']);
        $adapter->setLang('bg');
        $adapter->setUserAgent($config['nzoom_useragent']);
        $adapter->setCookieFile($config['nzoom_cookiefile'], null);

        return $adapter;
    }

    public function index() {
        self::$title->add(self::$titleMap['/']);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $loginData['username'] = $_POST['username'];
            $loginData['password'] = $_POST['password'];
            $adapter = $this->_getAuthenticationAdapter();
            $adapter->setPersistent();
            $authenticationResult = $adapter->authenticate($loginData['username'], $loginData['password']);
            unset($_SESSION['errors']['invalid_credentials']);
            if ($authenticationResult->isValid()) {
                $identity = $authenticationResult->getIdentity();
                $_SESSION['user_data']['firstname'] = $identity->firstname;
                $_SESSION['user_data']['lastname'] = $identity->lastname;
                $_SESSION['user_data']['employee_id'] = $identity->employee;
                $_SESSION['user_data']['id'] = $identity->id;
                $_SESSION['user_data']['real_role'] = $identity->real_role;
                $_SESSION['is_logged'] = true;
            } else {
                $_SESSION['is_logged'] = false;
                $_SESSION['errors']['invalid_credentials'] = 'Въведохте невалидни данни! Моля, пробвайте отново!';
            }
        }
        if (self::isAuthenticated()) {
            if (empty($_GET['prev_url'])) {
                Utilities::redirect('');
            } else {
                Utilities::redirect($_GET['prev_url']);
            }
        }
    }

    public function ping() {
        $authenticated = self::isAuthenticated();

        echo json_encode([
            "authenticated" => $authenticated,
        ]);
        exit;
    }

    public static function isAuthenticated() {
        $isLogged = $_SESSION['is_logged'] ?? false;
        if ($isLogged && is_null(self::$isNzoomAuthenticated)) {
            $adapter = self::_getAuthenticationAdapter();
            $result = $adapter->authenticate();
            self::$isNzoomAuthenticated = $result->isValid();
            return self::$isNzoomAuthenticated;
        }

        return $isLogged;
    }

    public function logout() {
        $adapter = $this->_getAuthenticationAdapter();
        $adapter->logout(true);
        $_SESSION['is_logged'] = false;
        session_destroy();
        Utilities::redirect(empty($_GET['prev_url']) ? '' : $_GET['prev_url']);
    }
}