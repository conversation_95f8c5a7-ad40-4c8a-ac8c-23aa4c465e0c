<?php
namespace Application\Controller;

use Application\Client\Advanceaddress\Utilities\Utilities;
use Application\Service\MainReportService;
use Application\Service\ParamReportIdService;
use Application\Service\DevModService;

class ErrorController extends AbstractController
{
    public function __construct() {
        $devModeService = DevModService::getInstance();
        if($devModeService->isDevMode()) {
            if($devModeService->getFeatureStatus('devbar')) {
                set_error_handler(array($this, 'ErrorHandler'), E_ALL);
            }
        }

        set_exception_handler(array($this, 'ExceptionHandler'));
    }

    public function ErrorHandler($code, $description, $file=null, $line=null, $context=null) : void
    {
        $error = null;
        switch ($code) {
            case E_PARSE:
            case E_ERROR:
            case E_CORE_ERROR:
            case E_COMPILE_ERROR:
            case E_USER_ERROR:
                $error = 'Fatal Error';
                break;
            case E_WARNING:
            case E_USER_WARNING:
            case E_COMPILE_WARNING:
            case E_RECOVERABLE_ERROR:
                $error = 'Warning';
                break;
            case E_NOTICE:
            case E_USER_NOTICE:
                $error = 'Notice';
                break;
            case E_STRICT:
                $error = 'Strict';
                break;
            case E_DEPRECATED:
            case E_USER_DEPRECATED:
                $error = 'Deprecated';
                break;
            default :
                break;
        }

        $GLOBALS['errors'][] = [
            'type' => $error,
            'code' => $code,
            'description' => $description,
            'file' => $file,
            'line' => $line
        ];
    }

    public function ExceptionHandler(\Throwable $exception) : void
    {
        $config = $this->getConfig();

        if(!isset($head['styles'])){
            $head['styles'] = array();
        }
        $head['styles'][] = 'styles/error.css';

        $path = Utilities::buildPath();
        $revision = AbstractController::getRevisionInfo();
        $hasSession = $_SESSION['is_logged'] ?? false;
        $reportId = '';
        $report = null;
        if($hasSession) {
        $reportId = ParamReportIdService::getInstance()->valueOf();
        $report = $reportId == 0 ? null : MainReportService::getInstance($reportId)->getResource();
        }
        $revision = AbstractController::getRevisionInfo();
        $controller = Utilities::getRequestController();
        $action = Utilities::getRequestAction();
        $viewPath = $config['view_path'] . '/error.php';

        $showTechnicalInfo = $config['error_showTechnicalInfo'] ?? false;

        $error_id = bin2hex(random_bytes(4));
        $info = $this->genErrorArray($exception, $error_id, $hasSession);

        $message = $info['Message'];

        if(!empty($config['error_logPath']) && is_dir($config['error_logPath'])) {
            $this->saveLog($info, $config['error_logPath']);
        }
        $GLOBALS['errors'][] = [
            'type' => get_class($exception),
            'code' => $exception->getCode(),
            'description' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine()
        ];

        include $config['view_path'] . '/layout/error.phtml';
    }

    private function genErrorArray(\Throwable $exception, $id, $hasSession) : array
    {
        $info= [
            'ID' => $id,
            'Time' => date('Y-m-d H:i:s'),
            'Url' => "{$_SERVER['REQUEST_SCHEME']}://{$_SERVER['HTTP_HOST']}/{$_SERVER['REQUEST_URI']}",
            'Referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'Query' => $_SERVER['QUERY_STRING'] ?? '',
            'Post' => http_build_query($_POST ?? []),
            'Has session' => $hasSession,
            'Type' => get_class($exception),
            'Code' => $exception->getCode(),
            'File' => $exception->getFile(),
            'Line' => $exception->getLine(),
            'Message' => $exception->getMessage(),
            'Trace' => "\n".$exception->getTraceAsString(),
        ];
        return $info;
    }

    private function saveLog(array $info, $logPath) : void
    {
        $datePart = explode(' ', $info['Time']);
        $date = $datePart[0];
        $time = str_replace(':', '_', $datePart[1]);
        $fileName = "{$date}_{$time}_{$info['ID']}.txt";
        $filePath = $logPath."/{$fileName}";

        $data = "#Error# \n\n";

        foreach ($info as $k=>$v){
            $data .= "{$k}: {$v}\n";
        }
        if(!is_dir($logPath)) {
            mkdir($logPath);
            chmod($logPath, 0777);
        }


        file_put_contents($filePath, $data);
        chmod($filePath, 0777);
    }
}
