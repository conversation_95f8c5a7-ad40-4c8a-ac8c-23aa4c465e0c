<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>diaman<PERSON>
 * Date: 8/9/2017
 * Time: 4:12 PM
 */

namespace Application\Client\Advanceaddress\Nomenclatures\Analogue;

use Bgservice\NzoomClient\Request;

class AddRequest extends Request
{
    private int $nzoomType = 84;

    public function __construct()
    {
        parent::__construct();
        $this->setUrl("index.php?launch=nomenclatures&nomenclatures=add&type={$this->nzoomType}");
        $this->setDataFormat('form');
        $this->setPost();
        $this->setResponseClass(AddResponse::class);
    }
}
