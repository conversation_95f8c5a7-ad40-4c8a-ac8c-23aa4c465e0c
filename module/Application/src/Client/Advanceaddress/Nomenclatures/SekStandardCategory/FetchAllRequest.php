<?php

namespace Application\Client\Advanceaddress\Nomenclatures\SekStandardCategory;

use Bgservice\NzoomClient\SearchRequest;

class FetchAllRequest extends SearchRequest
{
    protected $nzoomModule = 'nomenclatures';
    protected $nzoomType = 48;
    protected $nzoomModuleAlias = 'n';

    public function __construct() {
        parent::__construct();
        $this->setResponseClass(FetchAllResponse::class);
    }
}