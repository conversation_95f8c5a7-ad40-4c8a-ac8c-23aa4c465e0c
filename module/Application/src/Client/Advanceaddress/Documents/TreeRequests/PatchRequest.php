<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>diaman<PERSON>
 * Date: 8/9/2017
 * Time: 4:12 PM
 */

namespace Application\Client\Advanceaddress\Documents\TreeRequests;

use Bgservice\NzoomClient\Request;

class PatchRequest extends Request
{
    public function __construct($reportId) {
        parent::__construct();
        $this->setUrl("index.php?launch=documents&documents=view&view={$reportId}&build_tree=1");
        $this->setPost();
        $this->setResponseClass(PatchResponse::class);
        $this->setDataFormat('form');
    }
}