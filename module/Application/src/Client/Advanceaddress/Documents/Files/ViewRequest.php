<?php

namespace Application\Client\Advanceaddress\Documents\Files;

use Bgservice\NzoomClient\Request;

class ViewRequest extends Request
{
    protected $nzoomModule = 'documents';
    protected $nzoomAction = 'viewfile';

    public function __construct($documentId, $fileId) {
        parent::__construct();
        $this->setDataFormat('json');
        $this->setResponseClass(ViewResponse::class);
        $this->setData(array(
            'launch'           => $this->nzoomModule,
            $this->nzoomModule => $this->nzoomAction,
            $this->nzoomAction => $documentId,
            'file'             => $fileId
        ));
    }
}