<?php if(empty($comunications)) {?>
    <div class="info">
    	<p>Докладът все още не е изпращан!</p>
    </div>
<?php } else {?>
    <table class="comunications-table">
    	<thead>
    		<tr>
    			<th>№</td>
    			<th>Дата и час</td>
    			<th>Потребител</td>
    			<th>Описание</td>
    		</tr>
    	</thead>
    	<tbody>
    		<?php $i = 1; ?>
    		<?php foreach ($comunications as $k=>$v) {?>
    		<tr class="comunications" data-recordid="<?=$v->id?>">
    			<td><?=$startNum+$i++?></td>
    			<td><?=$v->getFormatedDate($dateFormat)?></td>
    			<td><?=$v->getUsername()?></td>
    			<td>изпраща e-mail към документа</td>
    		</tr>
    		<tr class="aoudit"><td colspan="4">
    			<table name="aoudit">
    				<tr><th>№</th><th>Поле</th><th>стойност</th></tr>
    				<tr>
        				<td>1</td>
        				<td>Изпрати на</td>
        				<td><?php foreach ($v->to as $v1) { ?>
        					<div class="recipient"><?=$v1['email']?></div>
        				<?php }?></td>
    				</tr>
    				<tr>
        				<td>2</td>
        				<td>Тема на писмото</td>
        				<td><?=$v->subject?></td>
    				</tr>
    				<tr>
        				<td>3</td>
        				<td>Съдържание на писмото</td>
        				<td><?=strip_tags($v->content, '<br>')?></td>
    				</tr>
    				<tr>
        				<td>4</td>
        				<td>Прикачени файлове</td>
        				<td><?php foreach ($v->getFileslist() as $v1){ ?>
        					<div class="attachment-file"><?=$v1->getName()?> <?php if($v1->getDescription()){?>(<?=$v1->getDescription() ?>)<?php }?></div>
        				<?php }?></td>
    				</tr>
    			</table>
    		</td><tr/>
    		<?php }?>
    	</tbody>
    </table>

    <?php include "{$viewPath}/shared/pagination.php"; ?>
<?php } ?>
