<?php
use Application\Client\Advanceaddress\Utilities\Utilities;

$three_calc_construction_right_grp_names = array_flip($costApproachReport->three_calc_construction_right_grp['names']);
$objects_three_calc_construction_right_corr = array();
$three_calc_construction_right_grp_values = !empty($costApproachReport->three_calc_construction_right_grp['values']) ? $costApproachReport->three_calc_construction_right_grp['values'] : array();
$three_calc_construction_right_grp_values = array();
$rowNum = 0;

foreach ($costApproachReport->rated_object_group['values'] as $r => $v) {
    if ($costApproachReport->objectsOPS[$v[$rated_object_group_names['rated_object_name_id']]]->calc_construction_right['value'] == '3') {
        $row = array_fill_keys($three_calc_construction_right_grp_names, '');
        if(!empty($v)){
            foreach($three_calc_construction_right_grp_names as $k1 => $v1) {
                $row[$v1] = ( isset($costApproachReport->three_calc_construction_right_grp['values']) && isset($costApproachReport->three_calc_construction_right_grp['values'][$r]) )
                ? ($costApproachReport->three_calc_construction_right_grp['values'][$r][$v1] ?? '')
                : '';
            }
        }
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_rated_object_id']] = $v[$rated_object_group_names['rated_object_name_id']];
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_rated_object_name']] = $v[$rated_object_group_names['rated_object_name']];
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_city_id']] = $report->populated_place_id['value'];
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_city_name']] = $report->populated_place['value'];

        $objectMask = $costApproachReport->maskData[$v[$rated_object_group_names['rated_object_name_id']]];
        $mask = isset($objectMask->choose_mask_sec) ? $objectMask->choose_mask_sec['value'] : $objectMask->choose_mask_third['value'];
        if($mask) {
            $row[$three_calc_construction_right_grp_names['three_calc_construction_right_designation_type']] = isset($subtypeResidentialById[$mask]) ? 'residential' : 'nonresidential';
        }

        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_rzp']] = floatval( $objectMask->total_rzp_accepted_area['value'] );
        $currentBuilding = in_array($objectMask->type, $config['object_types']['level_two']) ? $objectMask : $objectMask->parent;
        $basic_value = 0;
        $construction = '';
        if (isset($currentBuilding->construction_type['value'], $constructionsById[$currentBuilding->construction_type['value']]->construction['value']) && !empty($constructionTypesById[$constructionsById[$currentBuilding->construction_type['value']]->construction['value']])){
            $constructionType = $constructionTypesById[$constructionsById[$currentBuilding->construction_type['value']]->construction['value']];
            $construction = $constructionType->id;
            $basic_value = $row[$three_calc_construction_right_grp_names['three_calc_construction_right_designation_type']] == 'nonresidential'
                ? $constructionType->construction_other['value']
                : ($objectMask->type == 21 ? $constructionType->construction_house['value'] : $constructionType->construction_apartment['value'] );
        }

        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_construction']] = $construction;
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_value']] = $basic_value;

        switch($objectMask->type){
            case 25: $basicProc = 100; break;
            case 23:
            case 42: $basicProc = 80; break;
            case 21: $basicProc = 85; break;
            default: $basicProc = 0;
        }
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_proc']]
            = empty($row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_proc']])
            ? $basicProc
            : $row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_proc']];

        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_value_total']] = $row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_value']] * $basicProc / 100;

        if($row[$three_calc_construction_right_grp_names['three_calc_construction_right_rate_increase']] == '' ){
            $row[$three_calc_construction_right_grp_names['three_calc_construction_right_rate_increase']] = '1.10';
        }

        $infstrCoefSum = 0;
        foreach ($infrastructureCoeficientMap as $k1 => $v1) {
            $infstrCoefSum += $v1[$row[$three_calc_construction_right_grp_names[$k1]]];
        }
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_coef_infrastructure']] = (1 + $infstrCoefSum);
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_years_ops']] = ( isset($costApproachReport->three_calc_construction_right_grp['values']) && isset($costApproachReport->three_calc_construction_right_grp['values'][$r]) ) ? floatval($costApproachReport->three_calc_construction_right_grp['values'][$r][$three_calc_construction_right_grp_names['three_calc_construction_right_years_ops']]) : 0;

        $coef_location = $row[$three_calc_construction_right_grp_names['three_calc_construction_right_coef_location']];

        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_coef_years_ops']] = bcsub(1, floatval(bcpow('1.05', '-' . floatval($row[$three_calc_construction_right_grp_names['three_calc_construction_right_years_ops']]), 8)), 4);
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_tax_value']] = 0.25
        * $row[$three_calc_construction_right_grp_names['three_calc_construction_right_rzp']]
        * $row[$three_calc_construction_right_grp_names['three_calc_construction_right_basic_value_total']]
        * (is_numeric($coef_location) ? $coef_location : 0)
        * $row[$three_calc_construction_right_grp_names['three_calc_construction_right_coef_infrastructure']]
        * $row[$three_calc_construction_right_grp_names['three_calc_construction_right_coef_years_ops']];

        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_ops_value_bgn']] = $row[$three_calc_construction_right_grp_names['three_calc_construction_right_tax_value']] * (1 + floatval($row[$three_calc_construction_right_grp_names['three_calc_construction_right_rate_increase']]) / 100);
        $row[$three_calc_construction_right_grp_names['three_calc_construction_right_ops_value']] = bcmul(Utilities::getRate('BGN', $report->currency_methods['value']), $row[$three_calc_construction_right_grp_names['three_calc_construction_right_ops_value_bgn']]);
        $three_calc_construction_right_grp_values[++$rowNum] = $row;
    }
}
$costApproachReport->three_calc_construction_right_grp['values'] = $three_calc_construction_right_grp_values;
//$createControl->createField('three_calc_construction_right_grp', "", null, false, $additional);
$costApproachReport->build_land_value_eur['value'] = $row[$three_calc_construction_right_grp_names['three_calc_construction_right_ops_value']];

$grp = $costApproachReport->three_calc_construction_right_grp;
?>
<div class="group-table-div">
<table class="verticalTbl" name="three_calc_construction_right_grp" style="width: <?=(300 + 250 * count($grp['values']))?>px">
<?php
$additional = array(
    'three_calc_construction_right_zone' => array(
        'option-attributes' => $zoneAditianalOptions
    ),
    'three_calc_construction_right_city_name' => array(
        'attributes' => array(
            'data-ekatte_category' => $city->ekatte_category['value'],
        )
    ),
    'three_calc_construction_right_coef_infrastructure' => array(
        'attributes' => array(
            'data-infrastructureCoeficientMap' => json_encode($infrastructureCoeficientMap),
        )
    ),
    'noRemoveRow' => true,
);

foreach ($grp['labels'] as $key => $label) {
    $row = 1;
    ?>
    <tr<?=strpos($grp['names'][$key], '_id') ? ' style="display: none;"' : ''?>>
        <th><?=$label?></th>
        <?php foreach ($grp['values'] as $k=>$v) {
            $control = $createControl->getAttributesOfGTColumn($grp['names'][$key], 'three_calc_construction_right_grp');
            echo $createControl->createField(
                $grp['names'][$key],
                'three_calc_construction_right_grp',
                $control,
                $k,
                isset($additional[$grp['names'][$key]]) ? $additional[$grp['names'][$key]]: []);
            ?>
        <?php }?>
    </tr>
<?php
}
?>
</table>
</div>
<?php

