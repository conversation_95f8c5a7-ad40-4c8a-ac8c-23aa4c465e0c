<?=$assesmentObject->field('land_distribution_principle', ['hidden'=>'1']);?>
<table name="object_land_group"<?= empty($class) ? '' : " class=\"{$class}\""?>>
	<thead>
		<tr>
			<th class="col-name"><?=$comparativeTable->groupLabel('object_land_group', 'object_id')?></th>
			<th class="col-zp"><?=$comparativeTable->groupLabel('object_land_group', 'object_zp')?></th>
			<th class="col-rzp"><?=$comparativeTable->groupLabel('object_land_group', 'object_rzp')?></th>
			<th class="col-percentage"><?=$comparativeTable->groupLabel('object_land_group', 'object_percentage')?></th>
			<th class="col-sqm"><?=$comparativeTable->groupLabel('object_land_group', 'object_sqm')?></th>
			<th class="col-final_percentage"><?=$comparativeTable->groupLabel('object_land_group', 'object_final_percentage')?></th>
			<th><?=$comparativeTable->groupLabel('object_land_group', 'object_final_sqm')?></th>
			<th><?=$comparativeTable->groupLabel('object_land_group', 'object_land_corrected_value')?></th>
		</tr>
	</thead>
	<tfoot>
		<tr>
			<th colspan="3" class="total_label"><?=$comparativeTable->label('total_objects_distribution')?></th>
			<th><?=$comparativeTable->field('total_objects_distribution');?></th>
		</tr>
		<tr>
			<th colspan="3" class="total_label"><?=$comparativeTable->label('total_objects_squaremeters')?></th>
			<th><?=$comparativeTable->field('total_objects_squaremeters');?></th>
		</tr>
	</tfoot>
	<tbody>
		<?php
		for($i=1; $i <= $numberOfRows; $i++) { ?>
		<tr>
			<td class="hidden"><?=$comparativeTable->groupField('object_land_group', 'object_id', $i);?></td>
			<td class="col-name"><?=$comparativeTable->object_land_group['objectNames'][$i];?></td>
			<td class="col-zp"><?=$comparativeTable->groupField('object_land_group', 'object_zp', $i);?></td>
			<td class="col-rzp"><?=$comparativeTable->groupField('object_land_group', 'object_rzp', $i);?></td>
			<td class="col-percentage"><?=$comparativeTable->groupField('object_land_group', 'object_percentage', $i);?></td>
			<td class="col-sqm"><?=$comparativeTable->groupField('object_land_group', 'object_sqm', $i);?></td>
			<td class="col-final_percentage"><?=$comparativeTable->groupField('object_land_group', 'object_final_percentage', $i);?></td>
			<td><?=$comparativeTable->groupField('object_land_group', 'object_final_sqm', $i);?></td>
			<td><?=$comparativeTable->groupField('object_land_group', 'object_land_corrected_value', $i);?></td>
		<?php }?>
		</tr>
	</tbody>
</table>