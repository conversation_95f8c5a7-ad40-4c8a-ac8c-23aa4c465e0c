<tbody>
	<?php foreach($analogs as $k => $v) {?>
	<tr>
		<td><input type="checkbox" name="analog[]" value="<?= $v->id ?>" /></td>
		<?php foreach ($listColumns as $colName => $colLabel) {?>
		<td class="col-<?= $colName ?>">
            <?php
                echo $v->type == 'date'
                    ? DateTime::createFromFormat('Y-m-d', $v->getReadonlyValue($colName))->format('d.m.Y')
                    : ($colName=='analog_desc'
                        ? "<div class=\"longtext\">" . $v->getReadonlyValue($colName). "</div>"
                        : $v->getReadonlyValue($colName));
            ?>
        </td>
		<?php }?>
		<td>
            <a
                target="_blank"
                class="see-analogue-anchor"
                href="redirect/launch-nomenclatures|nomenclatures-view|view-<?= $v->id?>">
                <img src="images/search.png" alt="виж" >
            </a>
        </td>
		<td>
            <a href="<?= $editAnalogUrl?>&analog=<?= $v->id?>&from=<?= urlencode(REQUEST_URL);?>">
                <i class="fa fa-edit"></i>
            </a>
        </td>
	</tr>
	<?php }?>
</tbody>