<?php
/**
 * @var MarketComparativeTableModel $comparativeTable
 */

use Application\Model\MarketComparativeTableModel;

?>
<tr <?= $comparativeTable->groupVarIsHidden('group_analogue', 'property_area') ? 'class="hidden"' : ''?>>
    <td class="col-comparecriteria">
        <?= $comparativeTable->groupLabel('group_analogue', 'property_area')?>
        <?= $comparativeTable->field('area_type')?>
    </td>
	<td class="col-assesmentobject"><?= $comparativeTable->groupField(
	    'group_analogue',
	    'property_area',
	    1,
	    [
	        'hidden' => '',
	    ])?></td>
    <?php for ($i = 2; $i <= $comparativeTable->countRows('group_analogue'); $i++) { ?>
    <td class="col-analog">
        <?= $comparativeTable->groupField(
            'group_analogue',
            'property_area',
            $i,
            [
                'attributes'=> [
                    'data-zp' => $comparativeTable->groupVal('group_analogue', 'zpVal',  $i),
                    'data-rzp' => $comparativeTable->groupVal('group_analogue', 'rzpVal',  $i),
                ]
            ]
        ); ?>
    </td>
	<?php }?>
</tr>
