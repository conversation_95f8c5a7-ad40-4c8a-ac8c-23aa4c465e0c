<?php
/**
 * @var MarketComparativeTableModel $comparativeTable
 * @var int $row
 */

use Application\Model\MarketComparativeTableModel;

$groupAnalogueCounter = $comparativeTable->countRows('group_analogue');
?>
<tr class="freeIndicatorRow">
	<td class="col-comparecriteria">
        <i class="fa fa-times removeRow" title="Премахване на реда!"></i>
        <?= $comparativeTable->groupField('additionalparameters_group', "fparam", $row)?>
    </td>
	<td class="col-assesmentobject">
        <?= $comparativeTable->groupField('additionalparameters_group', "fparam_object", $row)?>
    </td>
    <?php for ($i = 1; $i < $groupAnalogueCounter; $i++) { ?>
	    <td class="col-analog">
            <?= $comparativeTable->groupField('additionalparameters_group', "fparam_analog{$i}", $row) ?>
        </td>
	<?php }?>
</tr>
<tr class="freeIndicatorRow">
	<td class="col-comparecriteria">Корекция, %</td>
	<td class="col-assesmentobject"></td>
    <?php for ($i = 1; $i < $groupAnalogueCounter; $i++) { ?>
	    <td class="col-analog">
            <?= $comparativeTable->groupField('additionalparameters_group', "fparam_analog{$i}_corr", $row) ?>
        </td>
	<?php }?>
</tr>
