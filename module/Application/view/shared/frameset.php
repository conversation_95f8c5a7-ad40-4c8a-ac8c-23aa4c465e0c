<?php
namespace Frameset;
use Application\Client\Advanceaddress\Utilities\Utilities;
use Application\Router;
use Application\Controller\AbstractController;
use Application\Service\LockReportService;
use Application\Service\MainReportService;
use Application\Service\ParamReportIdService;
use Application\Service\DevModService;

    $config = AbstractController::getConfig();
    $path = Utilities::buildPath();
    $revision = AbstractController::getRevisionInfo();
    $controller = Utilities::getRequestController();
    $action = Utilities::getRequestAction();
    $reportId = ParamReportIdService::getInstance()->valueOf();
    $report = $reportId == 0 ? null : MainReportService::getInstance($reportId)->getResource();
    $viewPath =  "{$config['view_path']}/{$controller}.php";
    if(!isset($locked)) {
        $locked = LockReportService::getInstance()->isLocked();
    }

    $devModeService = DevModService::getInstance();
    if($devModeService->isDevMode()
        && $devModeService->getFeatureStatus('devbar')) {
            $showDevBar = true;
            $head['styles'][] = 'styles/dev-bar.css';
    }

    if (!file_exists($viewPath)) {
        throw new \Exception('Not found!', 404);
    }
?>
<!DOCTYPE html>
<html lang="en">
<?php include_once "{$config['view_path']}/shared/head.php";?>
<body data-reportid="<?= $reportId ?>"
	data-precision="<?= $report !== null ? $report->getPrecision() : 2 ?>"
	data-precision-out="<?= $report !== null ? $report->getPrecisionOut() : '' ?>"
    data-locked="<?=($locked??false) && !($lockOverride??false) ?>">
  <div id="backendData"
     data-messages="<?=htmlspecialchars(Router::getMessages());?>"
  ></div>
    <?php
        if (!in_array($controller, array('authentication', 'index'))) {
    ?>
<div class="container<?= (($locked??false) && !($lockOverride??false)) ? ' form-locked' : ''?>">
    <?php
        include_once "{$config['view_path']}/shared/header.php";
        include_once "{$config['view_path']}/shared/sidebar.php";
    ?>
    <div id="main">
        <?php include_once $viewPath;?>
    </div>
    <?php include "{$config['view_path']}/shared/footer.php";?>
</div>
    <?php
        } else {
            include_once $viewPath;
        }

    if($controller != 'authentication') {
    ?>
    <div class="session_time" style="display:none;"
        data-session_time="<?=$config['session_time'] ?>"
        data-session_expiration_notification="<?=$config['session_expiration_notification'] ?>"></div>
    <?php
    }
    ?>
    <?php if($showDevBar ?? false) {
        include_once "{$config['view_path']}/partial_views/dev-bar.php";
    }?>
</body>
</html>
