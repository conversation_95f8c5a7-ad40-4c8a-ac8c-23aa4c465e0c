<div id="top-navigation">
    <ul class="breadcrumb">
        <li<?= ' class="' .$this->completion2class($comp->calcCompletionAvg('market-analyse')).' selected"' ?>
        ><a href="market-analyse/<?php if(isset($reportId)) echo $reportId ?>"> Пазарен анализ</a></li>
    </ul>
</div>
    <form action="" method="post" data-reportid="<?=$reportId?>"  enctype="multipart/form-data" data-form-protect="1">
        <input type="hidden" name="id" value="<?=$reportId?>"/>
        <input type="hidden" name="_completionId" value="market-analyse" />

        <?php
            $createControl->createSupervisionComment(
                'a4_supervision_comments_grp',
                $this->isSupervision(),
                $this->isSupervisor()
            );
        ?>

        <div id="market-analyse" class="row">
            <?php $createControl->createField("populated_place_id", '', null, false, ['disabled' => '1'])?>
            <?php $createControl->createField("region_id", '', null, false, ['disabled' => '1'])?>
            <?php $createControl->createField("analog_quarter_id", '', null, false, ['disabled' => '1'])?>
            <div class="marketinfo-wrapper">
                <div class="input-title">
                    <p>1. Пазарна Информация</p>
                </div>
                <div class="grid">
                    <div class="form-group">
                        <div><button class="fetch-marketinfo">Извлечи пазарна информация</button></div>
                        <?php // $createControl->createField("market_desc_name"); ?>
                        <?php $createControl->createField("market_desc_info", "", null, false, array('extra-classes' =>"bigger-textarea")); ?>
                    </div>
                    <?php $createControl->createField("graphics")?>
                </div>
            </div>
            <div class="marketanalyse-wrapper">
                <div class="input-title">
                    <p>2. Пазарен анализ</p>
                </div>
                <div class="grid">
                    <?php $createControl->createField("property_price_movement")?>
                    <?php $createControl->createField("region_demand_supply")?>
                    <?php $createControl->createField("property_interest_byuing")?>
                    <?php $createControl->createField("region_economic_potential")?>
                    <?php $createControl->createField("region_market_trend")?>
                    <?php $createControl->createField("property_interest_rent")?>
                </div>
            </div>
            <?php include "{$config['view_path']}/shared/submit-buttons.php";?>
        </div>
    </form>
