<?php

/**
 * @var array $config
 * @var int $reportId
 * @var CreateControl $createControl
 */

use Application\Client\Advanceaddress\Utilities\CreateControl;

include "{$config['view_path']}/shared/top-navigation.php";
?>
<div hidden id="dialog">
    Преместили сте gps координатите, моля да актуализирате информацията за
    съставните части на адреса при необходимост.
</div>
<!-- Map -->
<div hidden id="mapErrorsContainer"></div>
<div hidden id="mapContainer">
    <div id="map"></div>
    <div class="map_buttons">
      <div class="static_map_insert_btns">
        <a id="mapButton1" data-id=1 title="Избери в Карта 1.">Избери в Карта 1</a>
        <a id="mapButton2" data-id=2 title="Избери в Карта 2">Избери в Карта 2</a>
      </div>
      <div class="insert_coordinates_btn">
        <a id="mapButton3" data-id=3 title="Попълни координати">Попълни координати</a>
      </div>
    </div>
</div>
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAWb8K-SnYh2HYa1yUn2eF60y-cG30lbg0&size=400x200&language=bg&loading=async&region=BG&callback=Function.prototype"></script>

<form action="addresses.php" method="post" data-reportid="<?=$reportId?>"
      enctype="multipart/form-data" data-form-protect="1">
    <input type="hidden" name="id" value="<?=$reportId?>"/>
    <input type="hidden" name="_completionId" value="main-data/addresses" />
    <?php $createControl->createSupervisionComment('a2_supervision_comments_grp', $this->isSupervision(), $this->isSupervisor()); ?>
    <div id="address" class="row">
        <div class="input-title">
            <p>1. Адрес</p>
        </div>
        <div>
            <div class="grid row-one">
                <?php $createControl->createField("populated_place")?>
                <?php $createControl->createField("full_adress")?>
            </div>
            <div class="grid row-one">
                <?php $createControl->createField("analog_quarter_name")?>
                <?php $createControl->createField("street_name")?>
                <?php $createControl->createField("street_number")?>
                
                
                <?php $createControl->createField("apartment_building")?>
                <?php $createControl->createField("entrance")?>
                
                <?php $createControl->createField("address_more_desc")?>
            </div>
            <div class="grid row-two">

                <?php $createControl->createField("postcode")?>
                <?php $createControl->createField("municipality_name")?>
                <?php $createControl->createField("region_name")?>
                <div class="form-group">
                    <div class="grid latLonContainer">
                      <?php $createControl->createField("place_lat", "", null, false, array('extra-classes' => "digit-input"))?>
                      <?php $createControl->createField("place_lon", "", null, false, array('extra-classes' => "digit-input"))?>
                    </div>
                    <div class="gpsButtonsContainer">
                      <div class="form-group mapButtonContainer">
                        <a id="mapButton" title="Виж координатите на картата.">виж на картата</a>
                      </div>
                      <div class="form-group mapButtonContainer">
                        <a id="sameAddressReportsButton" title="Покажи доклади на този адрес">покажи доклади на този адрес</a>
                      </div>
                    </div>
                </div>
                <?php $createControl->createField("map_one")?>
                <?php $createControl->createField("map_two")?>
             </div>
        </div>
    </div>
    <div id="residence" class="row">
        <div class="input-title">
            <p>2. Описание населено място/Квартал</p>
        </div>
        <div>
            <div class="form-group">
                <?php $createControl->createField("region_description", "", null, false, array('attributes' =>['data-array_as_list'=>'1'])); ?>
                <?php $createControl->createField("full_description", "", null, false, array('extra-classes' =>"bigger-textarea")); ?>
            </div>
            <div class="grid optionsRow">
                <?php $createControl->createField("transport_check")?>
                <?php $createControl->createField("construction_check")?>
                <?php $createControl->createField("public_service_check")?>
                <?php $createControl->createField("infrastructure_check")?>
                <?php $createControl->createField("eco_check")?>
                <?php // $createControl->createField("disas_check")?>
                <?php $createControl->createField("soc_check")?>

                <?php $createControl->createField("additional_desc_name", '', null, false, ['hidden' => '1']); ?>
                <?php $createControl->createField("additional_desc_full", "", null, false, ['extra-classes' =>"bigger-textarea"]); ?>

            </div>
        </div>
    </div>
    <div id="disasters" class="row">
        <div class="input-title">
            <p>A2.18 Природни Рискове за Населено Място</p>
        </div>
        <div class="grid row-one">
            <?php $createControl->createField("disas_check_floods"); ?>
            <?php $createControl->createField("disas_check_flash_flood"); ?>
            <?php $createControl->createField("disas_check_storm"); ?>
            <?php $createControl->createField("disas_check_sea_waves"); ?>

            <?php $createControl->createField("disas_check_fire"); ?>
            <?php $createControl->createField("disas_landslide"); ?>
            <?php $createControl->createField("disas_check_earthquakes"); ?>
            <?php $createControl->createField("disas_check_desc"); ?>
        </div>
    </div>
    <div id="legend" class="row">
        <div class="info-legend">
            Легенда:
            0 - Няма данни,
            1 - Много слабо проявление на риска,
            2 - Слабо проявление на риска,
            3 - Средно проявление на риска,
            4 - Силно проявление на риска
        </div>
    </div>
    <div class="row">
        <div class="grid row-three">
            <?php $createControl->createField(
                'disas_check_desc_hidden',
                '',
                null,
                false,
                ['extra-classes' => 'larger-textarea', 'readonly' => '1']
            ); ?>
        </div>
    </div>

    <?php include "{$config['view_path']}/shared/submit-buttons.php";?>
</form>
<div id="sameAddressContainer" hidden></div>
