<?php
    if (!empty($_SESSION['errors']['analytics'])) {
        echo '<p>' . $_SESSION['errors']['analytics'] . '</p>';
        unset($_SESSION['errors']['analytics']);
    } else {
        ?>
        <div id="top-navigation">
            <ul class="breadcrumb">
                <?php
                $compleationStr = " class=\"".$this->completion2class($comp->calcCompletionAvg('analytics/anal'))."\"";
                ?>
                <li<?=$compleationStr?>><a href="analytics/<?php if (isset($reportId)) echo $reportId; ?>">D. ДОПУСКАНИЯ АНАЛИЗИ</a></li>
            </ul>
        </div>
        <form action="analytics.php" method="post" name="report-data-form" enctype="multipart/form-data"
              data-reportid="<?=$analyticsId?>" data-form-protect="1">
            <input type="hidden" name="_completionId" value="analytics/anal" />
            <input type="hidden" name="report_name_id" value="<?=$analyticsReport->getValue('report_name_id')?>" />
            <?php $createControl->createSupervisionComment('supervision_comments_grp', $this->isSupervision(), $this->isSupervisor()); ?>
            <input type="text" name="customer"
                   value="<?=$analyticsReport->customer;?>" hidden>
            <div id="generalAssumptionsSection">
                <div class="input-title"><p>D1.1 ОБЩИ ДОПУСКАНИЯ И ОГРАНИЧЕНИЯ ЗА ОЦЕНКАТА, СПЕЦИАЛНИ ДОПУСКАНИЯ</p></div>
                <?php $createControl->createField("gen_assumptions_group"); ?>
                <?php $createControl->createField("spec_assumptions_group"); ?>
            </div>
            <div id="swotSection">
                <div class="input-title"><p>КОНСТАТАЦИИ ЗА НАЛИЧИЕ ИЛИ ЛИПСА НА СПЕЦИФИЧНИ ОБСТОЯТЕЛСТВА</p></div>
                <?php $createControl->createField("assumption_config"); ?>
            </div>
            <div id="swotSection">
                <div class="input-title"><p>D1.2 SWOT анализ</p></div>
                <?php $createControl->createField("swot_table"); ?>
                <?php $createControl->createField("swot_config"); ?>
            </div>
            <div id="differencesSection">
                <div class="input-title"><p>D1.3 УСТАНОВЕНИ РАЗЛИКИ СПРЯМО ПРЕДХОДЕН ДОКЛАД</p></div>
                <?php $createControl->createField("established_differences_table"); ?>
            </div>
            <?php include "{$config['view_path']}/shared/submit-buttons.php"; ?>
        </form>
        <?php
    }
?>
