<?php
/** @var \Laminas\View\Renderer\PhpRenderer $this */
$this->headTitle('Авиокомпании');

$this->headLink()->appendStylesheet($this->basePath('css/cp/list.css') . '?v=' . ($this->version??0));
$this->inlineScript()->appendFile($this->basePath('js/cp/user-list.js') . '?v=' . ($this->version??0));
?><section class="cp-page cp-page-company">
    <div class="cp-page-title">
        <h2>Авиокомпании</h2>
    </div>
    <div id="form" class="miniForm">
        <form method="get">
            <input type="hidden" name="page" value="<?=$this->page_number?>">
            <label>
                <span class="material-icons cp-input-icon">search</span>
                <input type="text" name="filter" value="<?=$this->filter?>">
            </label>
            <button><span class="material-icons">filter_alt</span></button>
        </form>
    </div>
    <div class="cp-list-wrapper">
        <div class="cp-list-data">
            <ul class="cp-object-list">
                <?php if (!$this->list){ ?>
                    <div class="notFound">Няма намерени резултати</div>
                <?php } else {
                    foreach ($this->list as $v) { ?>
                        <li class="cp-object-list-item">
                            <span class="cp-object-list-item__graphic">
                                <?php if($v->logo) { ?>
                                    <img alt="logo" src="<?=$v->logo?>">
                                <?php } ?>
                            </span>
                            <span class="cp-object-list-item__title"><?=$v->name?></span>
                            <span class="cp-object-list-item__data">
                                <i class="material-icons">business</i> <?=$v->code?>
                            </span>
                            <span class="cp-object-list-item__controls">
                                <a class="cp-icon-button"
                                   href="<?=$this->url('control-panel/main',
                                       ['controller' => 'airline', 'action'=>'edit'],
                                       ['query'=>['code'=>$v->code]])?>"
                                   title="Редактирай">edit</a>
                                <a class="cp-icon-button delete-button"
                                   href="<?=$this->url('control-panel/main',
                                       ['controller' => 'airline', 'action'=>'delete'],
                                       ['query'=>['code'=>$v->code]])?>"
                                   title="Изтрий">delete</a>
                            </span>
                        </li>
                    <?php }
                }?>
            </ul>
            <?php if($this->pages){ ?>
                <div class="cp-page-buttons">
                    <a class="cp-navArrowButton <?php if($this->page_number<=1) echo "hidden"?>"
                       href="<?=$this->url('control-panel/main',
                           ['controller'=>'airline', 'action'=>'list'],
                           ['query'=>['page'=>$this->page_number-1,
                               'filter'=> $this->filter]])?>">
                        <i class="material-icons">chevron_left</i>
                    </a>
                    <?php for($i=$this->begin; $i<=$this->end; $i++){ ?>
                        <a class="cp-navButton <?php if($i==$this->page_number) echo "current"?>"
                           href="<?=$this->url('control-panel/main',
                               ['controller'=>'airline', 'action'=>'list'],
                               ['query'=>['page'=>$i,
                                   'filter'=> $this->filter]])?>">
                            <?=$i?>
                        </a>
                    <?php } ?>
                    <a  class="cp-navArrowButton <?php if($this->page_number>=$this->pages) echo "hidden"?>"
                        href="<?=$this->url('control-panel/main',
                            ['controller'=>'airline', 'action'=>'list'],
                            ['query'=>['page'=>$this->page_number+1,
                                'filter'=> $this->filter]])?>">
                        <i class="material-icons">chevron_right</i>
                    </a>
                </div>
            <?php } ?>
        </div>
    </div>
</section>
<a href="<?=$this->url('control-panel/main', ['controller' => 'airline', 'action'=>'add'])?>" class="cp-fab">add</a>
