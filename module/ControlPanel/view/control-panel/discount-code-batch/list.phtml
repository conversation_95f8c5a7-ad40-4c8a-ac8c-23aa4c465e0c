<?php
/** @var \Laminas\View\Renderer\PhpRenderer $this */
$this->headTitle('Пакети с Промокодове');

$this->headLink()->appendStylesheet($this->basePath('css/cp/list.css') . '?v=' . ($this->version??0));
?><section class="cp-page">
    <div class="cp-page-title">
        <h2>Пакети с Промокодове</h2>
    </div>
    <div id="form" class="miniForm">
        <form method="get">
            <input type="hidden" name="page" value="<?=$this->page_number?>">
            <label>
                <span class="material-icons cp-input-icon">search</span>
                <input type="text" name="filter" value="<?=$this->filter?>">
            </label>
            <button><span class="material-icons">filter_alt</span></button>
        </form>
    </div>
    <div class="cp-list-wrapper">
        <div class="cp-list-data">
            <?php if ($this->list === null) { ?>
                <div class="notFound">Няма намерени резултати</div>
            <?php } else { ?>
                <ul class="cp-object-list">
                    <?php foreach ($this->list as $v) {?>
                        <li class="cp-object-list-item <?=$v->active ? '' : ' cp--inactive'?>">
                        <span class="cp-object-list-item__graphic">
                            <i class="material-icons cp-content-for-active">batch_prediction</i>
                            <i class="material-icons cp-content-for-inactive">batch_prediction</i>
                        </span>
                            <span class="cp-object-list-item__title">
                            <span class="accent"><?=$v->code?></span>
                                <?=$v->name?>
                        </span>
                        <span class="cp-object-list-item__data">
                            <?=$v->description?>
                        </span>
                            <span class="cp-object-list-item__controls">
                             <a class="cp-icon-button"
                               href="<?=$this->url('control-panel/main',
                                   ['controller' => 'discountCode', 'action'=>'list'],
                                   ['query'=>['batch'=>$v->code]])?>"
                               title="Списък с промокодове">list
                             </a>
                             <a class="cp-icon-button"
                                href="<?=$this->url('control-panel/main',
                                    ['controller' => 'discountCodeBatch', 'action'=>'edit'],
                                    ['query'=>['code'=>$v->code]])?>"
                                title="Редактирай">edit
                             </a>
                             <a class="cp-icon-button cp-control-deactivate cp-content-for-active"
                                href="<?=$this->url('control-panel/main',
                                    ['controller' => 'discountCodeBatch', 'action'=>'deactivate'],
                                    ['query'=>['code'=>$v->code]])?>"
                                title="Деактивиране">block
                             </a>
                            <a class="cp-icon-button cp-control-activate  cp-content-for-inactive"
                               href="<?=$this->url('control-panel/main',
                                   ['controller' => 'discountCodeBatch', 'action'=>'activate'],
                                   ['query'=>['code'=>$v->code]])?>"
                               title="Активиране">check_circle
                            </a>
                        </span>
                        </li>
                    <?php } ?>
                </ul>

                <div class="cp-page-buttons" <?php if($this->pages<=1) echo "hidden"?>>
                    <a class="cp-navArrowButton <?php if($this->page_number<=1) echo "hidden"?>"
                       href="<?=$this->url('control-panel/main',
                           ['controller'=>'discountCodeBatch', 'action'=>'list'],
                           ['query'=>['page'=>$this->page_number-1,
                               'filter'=> $this->filter]])?>">
                        <i class="material-icons">chevron_left</i>
                    </a>
                    <?php for($i=$this->begin; $i<=$this->end; $i++){ ?>
                        <a class="cp-navButton <?php if($i==$this->page_number) echo "current"?>"
                           href="<?=$this->url('control-panel/main',
                               ['controller'=>'discountCodeBatch', 'action'=>'list'],
                               ['query'=>['page'=>$i,
                                   'filter'=> $this->filter]])?>">
                            <?=$i?>
                        </a>
                    <?php } ?>
                    <a  class="cp-navArrowButton <?php if($this->page_number>=$this->pages) echo "hidden"?>"
                        href="<?=$this->url('control-panel/main',
                            ['controller'=>'discountCodeBatch', 'action'=>'list'],
                            ['query'=>['page'=>$this->page_number+1,
                                'filter'=> $this->filter]])?>">
                        <i class="material-icons">chevron_right</i>
                    </a>
                </div>
            <?php } ?>
        </div>
    </div>
</section>
<a href="<?=$this->url('control-panel/main', ['controller' => 'discountCodeBatch', 'action'=>'add'])?>" class="cp-fab">add</a>
