<?php
/** @var \Laminas\View\Renderer\PhpRenderer $this */
$this->headTitle($this->title);

$this->inlineScript()->appendFile($this->basePath('js/cp/forms.js') . '?v=' . ($this->version??0));
$this->headLink()->appendStylesheet($this->basePath('css/cp/forms.css') . '?v=' . ($this->version??0));
?><section class="cp-page">
    <div class="cp-page-wrapper">
        <div class="cp-page-title">
            <h2><?=$this->title?></h2>
        </div>
        <div class="cp-edit">
            <form action="" method="post">
                <fieldset>
                    <div>
                        <label>
                            <span class="cp-label">Показване:</span>
                            <input type="checkbox" name="show" value="1" <?=$this->show ? 'checked' : ''?>>
                        </label>
                        <label>
                            <input type="hidden" name="toSubmit" value="1">
                        </label>
                    </div>
                </fieldset>
                <fieldset class="cp-section">
                    <legend class="cp-add-label">Пакети:</legend>
                    <button type="button" class="add-button cp-icon-button" title="Добави"
                                data-container="#batches"
                                data-template="#batchRow">add</button>
                    <div id="batches" class="batches">
                        <?php if($this->ibeBatches) {
                            foreach ($this->ibeBatches as $el) { ?>
                                <div class="batchEl batchesGridRow" >
                                    <span>
                                        <button type="button" class="remove-button cp-icon-button" title="Изтрий"
                                                data-target-selector=".batchEl">remove</button>
                                        <input class="del" type="hidden" name="del[]" value="0">
                                    </span>
                                    <label>
                                        <select class="cp-select" type="text" name="batches[]">
                                            <?php foreach ($this->batches??[] as $k => $v){ ?>
                                                <option value="<?=$k?>" <?= array_key_exists($k, $this->activeBatches??[]) ? '' : 'disabled'?>
                                                    <?=($el == $k) ? 'selected' : ''?>>
                                                    <?=$v?></option>
                                            <?php }?>
                                        </select>
                                    </label>
                                </div>
                            <?php }
                        }?>
                    </div>
                </fieldset>
                <fieldset class="cp-form-controls">
                    <button class="button" type="submit"><i class="material-icons">save</i> Запиши</button>
                </fieldset>
            </form>
        </div>
    </div>
</section>

<script id="batchRow" type="text/x-template">
    <div class="batchEl batchesGridRow" >
        <span>
            <button type="button" class="remove-button cp-icon-button" title="Изтрий"
                    data-target-selector=".branchEl">remove</button>
            <input class="del" type="hidden" name="del[]" value="0">
        </span>
        <label>
            <select class="cp-select" type="text" name="batches[]">
                <?php foreach ($this->batches??[] as $k => $v){ ?>
                    <option value="<?=$k?>" <?= array_key_exists($k, $this->activeBatches??[]) ? '' : 'disabled'?>>
                        <?=$v?></option>
                <?php }?>
            </select>
        </label>
    </div>
</script>
