<?php
/** @var \Laminas\View\Renderer\PhpRenderer $this */
$texts = $this->item->getTexts();
?>
<div class="cp-edit">
    <div class="cp-edit-title"><h3>Текст</h3></div>
    <?php if ($texts) { ?>
        <div class="cp-tabs">
            <div class="cp-tabs-selector">
                <?php foreach ($texts as $lang => $textVariant) { ?>
                    <a  class="cp-tabs-selector__tab"
                        href="<?=$this->url(
                            'control-panel/main',
                            ['controller' => 'hotelcm-items', 'action' => 'view'],
                            ['query' => ['id' => $this->item->getGiataId()], 'fragment' => 'lang_'.$textVariant->getLang()]
                        )?>"><?=$textVariant->getLang()?></a>
                <?php } ?>
                <?= $this->partial(
                    "control-panel/hotel-cm-items/_link_add_text.phtml",
                    ['id' => $this->item->getGiataId()])?>
            </div>
            <div class="cp-tabs-content-wrapper">
                <?php foreach ($texts as $lang => $textVariant) { ?>
                    <div id="lang_<?=$textVariant->getLang()?>" class="cp-tabs-content">
                        <div class="cp-edit-meta">
                            <div class="cp-edit-meta__content">
                                Тип: <?=$textVariant->getProviderType()?><?php if ($textVariant->getProviderIdent()) { ?> (<?=$textVariant->getProviderIdent()?>)<?php }?>
                                &nbsp;&nbsp;Дата на обновяване: <?=$textVariant->getLastUpdate()->format('Y-m-d H:i:s')?>
                            </div>
                            <?= $this->partial(
                                "control-panel/hotel-cm-items/_link_edit_text.phtml",
                                ['id' => $this->item->getGiataId(), 'lang' => $textVariant->getLang()])?>
                            &nbsp;&nbsp;
                            <?= $this->partial(
                                "control-panel/hotel-cm-items/_link_delete_text.phtml",
                                ['id' => $this->item->getGiataId(), 'lang' => $textVariant->getLang()])?>
                        </div>
                        <?php foreach ($textVariant->getSections() as $section) { ?>
                            <h4><?=$section->getTitle()?></h4>
                            <p><?=$section->getPara()?></p>
                        <?php } ?>
                    </div>
                <?php } ?>
            </div>
        </div>
    <?php } else { ?>
        <div class="notFound">Няма наличен текст
            <a
               href="<?=$this->url(
                   'control-panel/main',
                   ['controller' => 'hotelcm-items', 'action' => 'addText'],
                   ['query' => ['id' => $this->item->getGiataId()]]
               )?>"
               title="Добавяне на текст">Добави текст</a>
        </div>
    <?php }?>
</div>
