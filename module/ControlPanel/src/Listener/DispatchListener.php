<?php

namespace ControlPanel\Listener;

use <PERSON><PERSON>\Authentication\AuthenticationServiceInterface;
use <PERSON>inas\EventManager\EventManagerInterface;
use Laminas\Mvc\MvcEvent;

class DispatchListener
{

    protected array $listeners;
    public function attach(EventManagerInterface $events, $priority = 1001)
    {
        $this->listeners[] = $events->attach(
            MvcEvent::EVENT_DISPATCH,
            [$this, 'protectRestrictedPaths'],
            $priority
        );
    }

    public function protectRestrictedPaths(MvcEvent $event)
    {
        $routeMatch = $event->getRouteMatch();
        if (! $routeMatch) {
            return;
        }

        $routeName = $routeMatch->getMatchedRouteName();

        if ($routeName === 'auth' || preg_match('/^(wizatour|whitelabel|travelpoint|content)/', $routeName)) {
            return;
        }
        $auth = $event->getApplication()->getServiceManager()->get(AuthenticationServiceInterface::class);
        if ($auth->hasIdentity() === true) {
            $event->getApplication()->getServiceManager()->setService('lang', 'bg_BG');
            return;
        }

        $params = [
            'action' => 'login'
        ];
        $options = [
            'name' => 'auth',
            'query' => [
                'prevurl' => (string)$event->getRequest()->getUri(),
            ]
        ];

        $url = $event->getRouter()->assemble($params, $options);

        $response = new \Laminas\Http\PhpEnvironment\Response();
        $response->getHeaders()->addHeaderLine('Location', $url);
        $response->setStatusCode(302);
        return $response;
    }
}
