<?php

namespace ControlPanel\Controller;

use Lam<PERSON>\Json\Json;
use Lam<PERSON>\Mvc\Controller\AbstractActionController;
use Laminas\View\Model\ViewModel;
use Wizatour\Model\CityTable;
use Wizatour\Model\CountryTable;
use Wizatour\Model\LangTable;
use Wizatour\Model\Region;
use Wizatour\Model\RegionI18n;
use Wizatour\Model\RegionTable;

class RegionController extends AbstractActionController
{
    const ACCESS_RESOURCE = 'support';
    public function IndexAction()
    {
        if(!$this->Access()->hasAccess(self::ACCESS_RESOURCE)) {
            return $this->redirect()->toRoute('auth', [], [], true);
        }
        return new ViewModel([]);
    }

    public function ListAction()
    {
        if(!$this->Access()->hasAccess(self::ACCESS_RESOURCE)) {
            return $this->redirect()->toRoute('auth', [], [], true);
        }
        $limit = 15;
        $lang_url = $this->params()->fromRoute('lang', 'bg_BG');
        $lang = substr($lang_url, 0, 2);
        $filter = $this->params()->fromQuery('filter', '');
        $country_code = $this->params()->fromQuery('country', '');
        $page_number = $this->params()->fromQuery('page', '1');

        /** @var RegionTable $regionTable */
        $regionTable = $this->Table(RegionTable::class);

        if($country_code != '') {
            /** @var CountryTable $countryTable */
            $countryTable = $this->Table(CountryTable::class);
            $country_name = $countryTable->get($country_code)->name;
        }

        $start = ($page_number-1)*$limit;
        $list = $regionTable->fetchOnePage($filter, $country_name??'', $start, $limit, $lang);
        $pages = ceil(($regionTable->getLastFoundRows())/$limit);
        if ($regionTable->getLastFoundRows()<1){
            $list = null;
            $pages = null;
        }
        $visible = 6;
        if($pages > $visible){
            if ($page_number <= ($visible/2) + 1) {
                $begin = 1;
            } elseif ($page_number >= $pages - ($visible/2)) {
                $begin = $pages - $visible;
            } else {
                $begin = $page_number - ($visible/2);
            }
            $end = $begin + $visible;
            if ($end > $pages) {
                $end = $pages;
            }
        } else {
            $begin = 1;
            $end = $pages;
        }
        $viewData = [
            'list' => $list,
            'filter' => $filter,
            'country_code' => $country_code,
            'country_name' => $country_name??'',
            "page_number" => $page_number,
            "begin" => $begin,
            "end" => $end,
            "pages" => $pages,
        ];
        return new ViewModel($viewData);
    }

    public function AddAction()
    {
        if(!$this->Access()->hasAccess(self::ACCESS_RESOURCE)) {
            return $this->redirect()->toRoute('auth', [], [], true);
        }
        /** @var LangTable $langTable */
        $langTable = $this->Table(LangTable::class);
        $langs = $langTable->getAll();
        $activeLangs = $langTable->getAllActive();

        $viewData = [
            'title' => 'Добавяне на регион',
            'langs' => $langs,
            'activeLangs' => $activeLangs
        ];
        $postedData = $this->params()->fromPost();
        if (empty($postedData)) {
            // Not posted
            $view = new ViewModel($viewData);
            $view->setTemplate('control-panel/region/add.phtml');
            return $view;
        }

        /** @var RegionTable $regionTable */
        $regionTable = $this->Table(RegionTable::class);

        // Data posted
        $region = new Region();
        $region->exchangeArray($postedData);
        try {
            $regionTable->addRegion($region);
            for ($i=0 ; $i<count($postedData['i18n_lang']); $i++) {
                $regionI18n = new RegionI18n();
                $regionI18n->initialize($postedData['id'],
                    $postedData['i18n_lang'][$i],
                    $postedData['i18n_name'][$i],
                    $postedData['i18n_del'][$i]);
                if($regionI18n->del) {
                    $regionTable->deleteRegionI18n($postedData['id'], $postedData['i18n_lang'][$i]);
                } else {
                    $regionTable->addRegionI18n($regionI18n);

                }
            }
            $this->flashMessenger()->addSuccessMessage("Успешно добавен регион {$region->name}");
            return $this->redirect()->toRoute('control-panel/main', ['controller'=>'region', 'action'=>'list']);
        } catch (\Exception $e) {
            $this->flashMessenger()->addErrorMessage('Възникна проблем при добавяне на регион');
            return $this->redirect()->toRoute('control-panel/main', ['controller' => 'region', 'action' => 'add']);
        }
    }

    public function EditAction()
    {
        if(!$this->Access()->hasAccess(self::ACCESS_RESOURCE)) {
            return $this->redirect()->toRoute('auth', [], [], true);
        }
        /** @var LangTable $langTable */
        $langTable = $this->Table(LangTable::class);
        $langs = $langTable->getAll();
        $activeLangs = $langTable->getAllActive();

        $viewData = [
            'title' => 'Редакция на регион',
            'disabledId' => true,
            'langs' => $langs,
            'activeLangs' => $activeLangs
        ];

        $id = $this->params()->fromQuery('id');
        /** @var RegionTable $regionTable */
        $regionTable = $this->Table(RegionTable::class);

        $regionsI18n_arr = $regionTable->getAllI18n($id);
        $viewData = array_merge($viewData, [
            'i18n_set' => $regionsI18n_arr
        ]);
        try {
            /** @var Region $region */
            $region = $regionTable->get($id);
            /** @var CountryTable $countryTable */
            $countryTable = $this->Table(CountryTable::class);
            $country = $countryTable->fetch($region->countryCode);
            $viewData = array_merge($viewData, [
                'id' => $region->id,
                'country_code' => $region->countryCode,
                'name'  => $region->name,
                'alt_name'  => $region->alt_name,
                'country_name' => $country->name
            ]);
        } catch(\Exception $e) {
            $this->flashMessenger()->addErrorMessage("Не е открит регион с идентификатор '{$id}'");
            return $this->redirect()->toRoute('control-panel/main', ['controller'=>'region', 'action'=>'list']);
        }

        $postedData = $this->params()->fromPost();
        if (empty($postedData)) {
            // Not posted
            $view = new ViewModel($viewData);
            $view->setTemplate('control-panel/region/add.phtml');
            return $view;
        }

        // Posted
        $region = new Region();
        $region->exchangeArray($postedData);
        $region->id = $id;

        try {
            $regionTable->saveRegion($region);
            for ($i=0 ; $i<count($postedData['i18n_lang']); $i++) {
                $regionI18n = new RegionI18n();
                $regionI18n->initialize($id,
                    $postedData['i18n_lang'][$i],
                    $postedData['i18n_name'][$i],
                    $postedData['i18n_del'][$i]);
                if($regionI18n->del) {
                    $regionTable->deleteRegionI18n($id, $postedData['i18n_lang'][$i]);
                } else {
                    $regionTable->saveRegionI18n($regionI18n);

                }
            }
            $this->flashMessenger()->addSuccessMessage("Успешно редактиран регион {$region->id}");
            return $this->redirect()->toRoute('control-panel/main', ['controller'=>'region', 'action'=>'list']);
        } catch (\Exception $e) {
            $this->flashMessenger()->addErrorMessage('Възникна проблем при редакция на регион');
            return $this->redirect()->toRoute('control-panel/main',
                ['controller'=>'region','action'=>'edit'],
                ['query'=>['id'=>$id]]);
        }
    }

    public function DeleteAction()
    {
        if(!$this->Access()->hasAccess(self::ACCESS_RESOURCE)) {
            return $this->redirect()->toRoute('auth', [], [], true);
        }
        $id = $this->params()->fromQuery('id');
        /** @var RegionTable $regionTable */
        $regionTable = $this->Table(RegionTable::class);

        try{
            $regionTable->deleteRegion($id);
            $this->flashMessenger()->addSuccessMessage("Успешно изтрит регион {$id}");
            return $this->redirect()->toRoute('control-panel/main', ['controller'=>'region', 'action'=>'list']);
        } catch (\Exception $e) {
            $this->flashMessenger()->addErrorMessage('Възникна проблем при изтриването на регион');
            return $this->redirect()->toRoute('control-panel/main', ['controller'=>'region', 'action'=>'list']);
        }
    }

    /**
     * @throws \Exception
     */
    public function FindAction()
    {
        $data = [
            'countries' => [
                'class' => CountryTable::class,
                'idCol' => 'code',
            ],
        ];
        $limit = 20;
        $viewData = [];

        $rawInput = $this->request->getContent();
        $input = Json::decode($rawInput);
        $term = isset($input->where) ? preg_replace('/[^\s\w&.,\-А-я]/u', '', trim($input->where[0]->value)) : '';

        if (strlen($term) < 2){
            $this->getResponse()->setContent(Json::encode($viewData));
            return $this->getResponse();
        }

        $lang_url = $this->params()->fromRoute('lang', 'bg_BG');
        $lang = substr($lang_url, 0, 2);

        $tbl = $this->params()->fromQuery('tbl');
        if(!($tbl==='countries')) {
            $viewData['error'] = "Missing required parameter";
            $this->getResponse()->setContent(Json::encode(array_values($viewData)));
            return $this->getResponse();
        }

        $table = $this->Table($data[$tbl]['class']);
        $idCol = $data[$tbl]['idCol'];

        $list = $table->search($term, $lang, $limit);

        foreach ($list as $v) {
            $viewData[] = ["value" => $v->$idCol, "label" => $v->name];
        }

        $this->getResponse()->setContent(Json::encode($viewData));
        return $this->getResponse();
    }
}
