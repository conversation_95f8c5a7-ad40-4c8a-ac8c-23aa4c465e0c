<?php
namespace ControlPanel\Model;

use Laminas\Db\Sql\Select;
use Laminas\Db\TableGateway\TableGatewayInterface;

class UserAccessTable
{
    CONST TABLE = 'user_access';
    private $tableGateway;


    public function __construct(TableGatewayInterface $tableGateway)
    {
        $this->tableGateway = $tableGateway;
    }

    public function fetchAll()
    {
        return $this->tableGateway->select();
    }

    public function getOne(int $id)
    {
        $rowset = $this->tableGateway->select(['id' => $id]);
        $row = $rowset->current();
        if (! $row) {
            throw new \RuntimeException(sprintf(
                'Could not find row with identifier %s',
                $id
            ));
        }

        return $row;
    }

    public function save(UserAccess $userAcess)
    {
        $data = $userAcess->asArray();
        unset($data['id']);

        $id = $userAcess->id;

        if ($id === '') {
            $this->tableGateway->insert($data);
            return;
        }

        try {
            $this->getOne($id);
        } catch (\RuntimeException $e) {
            throw new \RuntimeException(sprintf(
                'Cannot update UserAccess with id %d; does not exist',
                $id
            ));
        }

        $this->tableGateway->update($data, ['id' => $id]);
    }

    public function delete(int $id)
    {
        $this->tableGateway->delete(['id' => $id]);
    }
}
