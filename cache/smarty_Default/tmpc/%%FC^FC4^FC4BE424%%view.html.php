<?php /* Smarty version 2.6.33, created on 2025-02-14 12:22:41
         compiled from /var/www/Nzoom-Evolution/_libs/modules/dashlets/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/dashlets/templates/view.html', 19, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/dashlets/templates/view.html', 26, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/dashlets/templates/view.html', 31, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/dashlets/templates/view.html', 31, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/dashlets/templates/view.html', 31, false),)), $this); ?>
<?php if ($this->_tpl_vars['dashlet']): ?>
  <?php $this->assign('max_columns_full', @PH_DASHLETS_FULL_MAX_COLUMNS); ?>
  <?php if ($this->_tpl_vars['dashlet']->get('full_width') == '1'): ?>
    <?php $this->assign('max_columns', $this->_tpl_vars['max_columns_full']); ?>
  <?php else: ?>
    <?php $this->assign('max_columns', @PH_DASHLETS_MAX_COLUMNS); ?>
  <?php endif; ?>

<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
<div id="form_container">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'dashlet_for'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap"><?php echo $this->_tpl_vars['module_name_i18n']; ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'name'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['dashlet']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'description'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['dashlet']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp) : smarty_modifier_mb_wordwrap($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
        </tr>
        <?php if ($this->_tpl_vars['dashlet']->get('default') == '1'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'default'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td colspan="2"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" alt="<?php echo $this->_config[0]['vars']['yes']; ?>
" title="<?php echo $this->_config[0]['vars']['yes']; ?>
" /></td>
        </tr>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['params']['module'] != 'reports'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'fields_order'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <table cellspacing="1" cellpadding="3" border="0" class="no_padding_left">
              <?php if ($this->_tpl_vars['dashlet']->get('full_width') == '1'): ?>
              <tr>
                <td colspan="2"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" alt="<?php echo $this->_config[0]['vars']['yes']; ?>
" title="<?php echo $this->_config[0]['vars']['yes']; ?>
" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['dashlets_full_width'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              </tr>
              <?php endif; ?>
              <tr class="strong">
                <td>
                  <?php echo ((is_array($_tmp=$this->_config[0]['vars']['dashlets_column_position'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                </td>
                <td>
                  <?php echo ((is_array($_tmp=$this->_config[0]['vars']['dashlets_col_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                </td>
              </tr>
              <?php $this->assign('settings', $this->_tpl_vars['dashlet']->get('settings')); ?>
              <?php $this->assign('columns', $this->_tpl_vars['settings']['columns']); ?>
              <?php $this->assign('visible', $this->_tpl_vars['settings']['visible']); ?>
              <?php unset($this->_sections['cols']);
$this->_sections['cols']['name'] = 'cols';
$this->_sections['cols']['loop'] = is_array($_loop=$this->_tpl_vars['max_columns_full']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['cols']['show'] = true;
$this->_sections['cols']['max'] = $this->_sections['cols']['loop'];
$this->_sections['cols']['step'] = 1;
$this->_sections['cols']['start'] = $this->_sections['cols']['step'] > 0 ? 0 : $this->_sections['cols']['loop']-1;
if ($this->_sections['cols']['show']) {
    $this->_sections['cols']['total'] = $this->_sections['cols']['loop'];
    if ($this->_sections['cols']['total'] == 0)
        $this->_sections['cols']['show'] = false;
} else
    $this->_sections['cols']['total'] = 0;
if ($this->_sections['cols']['show']):

            for ($this->_sections['cols']['index'] = $this->_sections['cols']['start'], $this->_sections['cols']['iteration'] = 1;
                 $this->_sections['cols']['iteration'] <= $this->_sections['cols']['total'];
                 $this->_sections['cols']['index'] += $this->_sections['cols']['step'], $this->_sections['cols']['iteration']++):
$this->_sections['cols']['rownum'] = $this->_sections['cols']['iteration'];
$this->_sections['cols']['index_prev'] = $this->_sections['cols']['index'] - $this->_sections['cols']['step'];
$this->_sections['cols']['index_next'] = $this->_sections['cols']['index'] + $this->_sections['cols']['step'];
$this->_sections['cols']['first']      = ($this->_sections['cols']['iteration'] == 1);
$this->_sections['cols']['last']       = ($this->_sections['cols']['iteration'] == $this->_sections['cols']['total']);
?>
                  <tr <?php if ($this->_sections['cols']['index'] >= $this->_tpl_vars['max_columns'] && ! $this->_tpl_vars['dashlet']->get('full_width')): ?> style="display: none;"<?php endif; ?>>
                    <td><?php if (! $this->_tpl_vars['visible'] || $this->_tpl_vars['visible'][$this->_sections['cols']['index']] == 1): ?><?php echo $this->_config[0]['vars']['dashlets_visible']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['dashlets_invisible']; ?>
<?php endif; ?>
                    </td>
                    <td>
                      <?php $this->assign('col_index', $this->_sections['cols']['index']); ?>
                      <?php $this->assign('col_label', $this->_tpl_vars['columns'][$this->_tpl_vars['col_index']]); ?>
                      <?php echo $this->_tpl_vars['columns_options'][$this->_tpl_vars['col_label']]; ?>

                    </td>
                  </tr>
              <?php endfor; endif; ?>
            </table>
          </td>
        </tr>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'records_per_page','disabled' => true,'custom_id' => 'records_per_page','label' => $this->_config[0]['vars']['dashlets_records_per_page'],'value' => $this->_tpl_vars['dashlet']->get('records_per_page'),'options' => $this->_tpl_vars['system_fields']['display'],'custom_class' => 'small','skip_please_select' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php else: ?>
          <?php if ($this->_tpl_vars['dashlet']->get('full_width') == '1'): ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['dashlets_fields_order']), $this);?>
</td>
            <td class="unrequired">&nbsp;</td>
            <td><?php echo $this->_config[0]['vars']['dashlets_full_width']; ?>
</td>
          </tr>
          <?php endif; ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'report_display'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td><?php echo $this->_tpl_vars['report_display_value_label']; ?>
</td>
          </tr>
        <?php endif; ?>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td id="dashlets_search_options">
      <?php if ($this->_tpl_vars['params']['module'] == 'reports'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."reports_filters.html", 'smarty_include_vars' => array('view_mode' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php else: ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_action_search_options.html", 'smarty_include_vars' => array('view_mode' => true,'inner_search' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['dashlet'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>
<?php endif; ?>