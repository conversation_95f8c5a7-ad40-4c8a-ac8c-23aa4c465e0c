<?php /* Smarty version 2.6.33, created on 2025-03-06 16:01:51
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Default/templates/single_actions_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/single_actions_list.html', 6, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/single_actions_list.html', 6, false),array('function', 'popup', '/var/www/Nzoom-Evolution/_libs/themes/Default/templates/single_actions_list.html', 78, false),)), $this); ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['controller'] != $this->_tpl_vars['module']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('controller_string', ob_get_contents());ob_end_clean(); ?>
<?php if ($this->_tpl_vars['object']->isTranslated()): ?>
  <?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#edit#' , $this->_tpl_vars['exclude'] )): ?>
    <?php if ($this->_tpl_vars['object']->getTypeRowLinkAction() && preg_match ( '/\(.*\)/' , $this->_tpl_vars['object']->get('type_row_link_action') )): ?>      <a href="javascript: void(0);" onclick="<?php echo $this->_tpl_vars['object']->get('type_row_link_action'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" width="16" height="16" border="0" class="edit<?php if (! ( $this->_tpl_vars['object']->checkPermissions('edit') && ! $this->_tpl_vars['object']->isDeleted() )): ?> dimmed<?php endif; ?>" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onload="this.up('tr').addClassName('pointer row_link_action <?php echo ((is_array($_tmp=$this->_tpl_vars['object']->modelName)) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
_<?php echo $this->_tpl_vars['object']->get('id'); ?>
').observe('click', function(event) { if (isSubelementClicked()) return false; <?php echo $this->_tpl_vars['object']->get('type_row_link_action'); ?>
 });" /></a>
    <?php elseif ($this->_tpl_vars['object']->modelName == 'Contract' && $this->_tpl_vars['object']->get('include_section_about') && $this->_tpl_vars['object']->checkPermissions('edittopic') && ! $this->_tpl_vars['object']->isDeleted() && ! preg_match ( '#edittopic#' , $this->_tpl_vars['disabled'] )): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=edittopic&amp;edittopic=<?php echo $this->_tpl_vars['object']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php elseif ($this->_tpl_vars['object']->checkPermissions('edit') && ! $this->_tpl_vars['object']->isDeleted() && ! preg_match ( '#edit#' , $this->_tpl_vars['disabled'] )): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=edit&amp;edit=<?php echo $this->_tpl_vars['object']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>
  <?php endif; ?>
  <?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#view#' , $this->_tpl_vars['exclude'] )): ?>
    <?php if ($this->_tpl_vars['object']->modelName == 'Contract' && $this->_tpl_vars['object']->get('include_section_about') && $this->_tpl_vars['object']->checkPermissions('viewtopic') && ! $this->_tpl_vars['object']->isDeleted() && ! preg_match ( '#viewtopic#' , $this->_tpl_vars['disabled'] )): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewtopic&amp;viewtopic=<?php echo $this->_tpl_vars['object']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php elseif ($this->_tpl_vars['object']->checkPermissions('view') && ! $this->_tpl_vars['object']->isDeleted() && ! preg_match ( '#view#' , $this->_tpl_vars['disabled'] )): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['object']->get('id'); ?>
<?php if ($this->_tpl_vars['object']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_view_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_view_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['module'] == 'finance' && $this->_tpl_vars['controller'] == 'budgets'): ?>
    <?php if ($this->_tpl_vars['include'] && preg_match ( '#enter#' , $this->_tpl_vars['include'] )): ?>
    <?php if ($this->_tpl_vars['object']->checkAssignments('enter')): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=enter&amp;enter=<?php echo $this->_tpl_vars['object']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
enter.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['enter'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['enter'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
enter.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['enter'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_enter_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_enter_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['include'] && preg_match ( '#control#' , $this->_tpl_vars['include'] )): ?>
    <?php if ($this->_tpl_vars['object']->checkAssignments('control')): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=control&amp;control=<?php echo $this->_tpl_vars['object']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
control.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['control'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['control'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
control.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['control'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_control_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_control_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>
    <?php endif; ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['module'] == 'roles' && $this->_tpl_vars['include'] && preg_match ( '#menu#' , $this->_tpl_vars['include'] )): ?>
    <?php if ($this->_tpl_vars['object']->checkPermissions('menu') && ! $this->_tpl_vars['object']->isDeleted()): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=menu&amp;menu=<?php echo $this->_tpl_vars['object']->get('id'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
menu.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
menu.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_menu_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_menu_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>
  <?php endif; ?>
<?php elseif ($this->_tpl_vars['action'] != 'filter'): ?>
  <?php if ($this->_tpl_vars['object']->checkPermissions('translate') && ! $this->_tpl_vars['object']->isDeleted()): ?>
    <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=translate&amp;translate=<?php echo $this->_tpl_vars['object']->get('id'); ?>
&amp;model_lang=<?php echo $this->_tpl_vars['object']->get('model_lang'); ?>
"<?php if ($this->_tpl_vars['link_target']): ?> target="<?php echo $this->_tpl_vars['link_target']; ?>
"<?php endif; ?>><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
translate.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
  <?php else: ?>
    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
translate.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_translate_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_translate_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
  <?php endif; ?>
<?php endif; ?>
  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
info.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />