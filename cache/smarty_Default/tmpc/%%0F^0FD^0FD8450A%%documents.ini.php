<?php $_config_vars = array (
  'document' => 'Document',
  'documents' => 'Documents',
  'documents_active' => 'Activate',
  'documents_sg' => 'document',
  'documents_pl' => 'documents',
  'documents_all' => 'All documents',
  'documents_name' => 'About',
  'documents_attachment' => 'File',
  'documents_file_location' => 'Attached file',
  'documents_file_locations' => 'Attached files',
  'documents_file_generated' => 'Generated file',
  'documents_files_generated' => 'Generated files',
  'documents_file_not_exist' => 'File not existing!',
  'documents_direction' => 'Kind',
  'documents_type' => 'Type',
  'documents_type_section' => 'Section',
  'documents_prefix' => 'Prefix',
  'documents_relatives_children' => 'Source of',
  'documents_relatives_parent' => 'Created from',
  'documents_relative_document_name' => 'Related document',
  'documents_project' => 'Project',
  'documents_project_code' => 'Project code',
  'documents_project_name' => 'Project name',
  'documents_project_undefined' => 'undefined',
  'documents_customer' => 'Contractor',
  'documents_customer_num' => 'Contractor number',
  'documents_customer_code' => 'Contractor code',
  'documents_customer_name' => 'Contractor name',
  'documents_customer_sender' => 'Sent by',
  'documents_customer_recipient' => 'Recipient',
  'documents_contract' => 'Contract num',
  'documents_office' => 'Office',
  'documents_employee' => 'Employee',
  'documents_group' => 'Group',
  'documents_department' => 'Department',
  'documents_media' => 'Source',
  'documents_types' => 'Types of documents',
  'documents_description' => 'Description',
  'documents_notes' => 'Notes',
  'documents_status' => 'Status',
  'documents_added_by' => 'Added by',
  'documents_modified_by' => 'Modified by',
  'documents_added' => 'Added on',
  'documents_modified' => 'Modified on',
  'documents_deleted' => 'Deleted on',
  'documents_incoming_sg' => 'incoming',
  'documents_outgoing_sg' => 'outgoing',
  'documents_internal_sg' => 'internal',
  'documents_incoming' => 'Incoming',
  'documents_outgoing' => 'Outgoing',
  'documents_internal' => 'Internal',
  'documents_any_direction' => 'no importance',
  'documents_id' => 'Document No.',
  'documents_num' => 'Document No.',
  'documents_num_format' => '%06d',
  'documents_custom_num' => 'Contractor No.',
  'documents_full_num' => 'Document No.',
  'documents_full_num2' => 'Full number',
  'documents_media_used' => 'Used',
  'documents_type_used' => 'Used',
  'documents_view_url' => 'Link to document view',
  'documents_date' => 'Date',
  'documents_datetime' => 'Date & Time',
  'documents_deadline' => 'Deadline',
  'documents_validity_term' => 'Validity',
  'documents_no_deadline' => 'Not specified',
  'documents_no_validity_term' => 'Not specified',
  'documents_department_undefined' => 'undefined',
  'documents_initial_notify' => 'Notification letter',
  'documents_referers' => 'Linked documents',
  'documents_tasks_referers' => 'Connected tasks',
  'documents_events_referers' => 'Connected events',
  'documents_relatives_action' => 'Links to other documents',
  'documents_refid_undefined' => 'undefined',
  'documents_parents' => 'Parent documents',
  'documents_children' => 'Connected documents',
  'documents_parents2' => 'Derives from',
  'documents_children2' => 'Basis for',
  'documents_vars' => 'Data',
  'documents_edit_vars' => 'Edit data',
  'documents_expired' => 'Document expired',
  'documents_validity_term_expired' => 'Validity expired',
  'documents_origin' => 'Origin',
  'documents_multiadd' => 'Multiple adding of documents',
  'documents_multiedit' => 'Multiple editing of documents',
  'documents_create' => 'Create',
  'documents_task' => 'Task',
  'documents_task_type' => 'Task type',
  'documents_event' => 'Event',
  'documents_minitask' => 'Mini task',
  'documents_transform_type' => 'Transform to type',
  'documents_source' => 'Transform from',
  'documents_multitransform_type' => 'Transform to type',
  'documents_transform_method' => 'Method',
  'documents_transform_one2one' => 'one to one',
  'documents_transform_one2many' => 'one to many',
  'documents_transform_many2one' => 'many to one',
  'documents_format' => 'Format',
  'documents_pattern' => 'Pattern',
  'documents_pattern_header' => 'Header',
  'documents_pattern_footer' => 'Footer',
  'documents_export' => 'Export',
  'documents_export_title' => 'Export document',
  'documents_export_all' => 'Export all records found',
  'documents_export_label' => 'Export to',
  'documents_export_help' => 'Export current document into a file with selected extension',
  'documents_export_xls_label' => '.xls',
  'documents_export_csv_label' => '.csv',
  'documents_export_type_label' => 'Outgoing file',
  'documents_export_type_help' => 'Outgoing file type: .xls - standard file for EXCEL or CSV(universal data file)',
  'documents_multitransform' => 'Multiple transformation of documents',
  'documents_comment' => 'Comment',
  'documents_substatus' => 'Status',
  'documents_setstatus' => 'Change status',
  'documents_status_btn' => 'Confirm status',
  'documents_settags' => 'Edit tags',
  'document_transform_operations' => 'Operation',
  'documents_contact_person' => 'Contact person',
  'documents_customers_info' => 'Customer data',
  'documents_main_contact_person' => 'Main contact person',
  'documents_customers_contacts' => 'Contact data',
  'documents_status_change_comment' => 'Status change',
  'documents_remind' => 'Remind',
  'documents_last_customers_records' => 'Last %s for customer',
  'documents_tags' => 'Tags',
  'documents_email' => 'E-mail',
  'documents_all_documents' => 'All documents',
  'documents_company' => 'Company',
  'documents_action_email' => 'E-mails',
  'documents_name_full_num' => '[Num.] About',
  'documents_customer_name_code' => '[Code] Contractor',
  'documents_contract_name_num' => '[Num.] Contract',
  'documents_project_name_code' => '[Code] Project',
  'documents_model_id' => 'For record',
  'documents_assigned_to' => 'Assigned to',
  'documents_severity' => 'Priority',
  'documents_origin_linked' => 'Linked',
  'documents_origin_inherited' => 'Inherited',
  'documents_origin_transformed' => 'Transformed',
  'documents_origin_cloned' => 'Cloned',
  'documents_link_type' => 'Link type',
  'documents_generated_files' => 'Generated files',
  'documents_generated_revisions' => 'Add new or rewrite',
  'documents_generated_add_new' => 'Add new',
  'documents_pattern_variables' => 'Please, filled in the data used in pattern!',
  'documents_pattern_modify' => 'Last pattern editions',
  'documents_generated_get_revision' => 'Take data from Version',
  'documents_generated_save_revision' => 'Replace version',
  'documents_generate_revision_title' => 'Version name',
  'documents_generate_revision_description' => 'Version description',
  'document_generated_no_save_title' => 'Variables',
  'document_generated_no_save_text' => 'The variable will be saved only in file Version',
  'documents_added_attachments' => 'Added files',
  'documents_deleted_attachments' => 'Deleted files',
  'documents_generated_filename' => 'File',
  'documents_generated_revision' => 'Version',
  'documents_generated_pattern' => 'Pattern',
  'documents_generated_description' => 'Description',
  'documents_generated_added' => 'Added on',
  'documents_generated_added_by' => 'Added by',
  'documents_ownership' => 'Ownership',
  'documents_ownership_unforwarded' => 'Unforwarded',
  'documents_ownership_forwarded' => 'Forwarded',
  'documents_ownership_assigned' => 'Assigned',
  'documents_status_opened' => 'Open',
  'documents_status_locked' => 'Locked',
  'documents_status_closed' => 'Closed',
  'documents_has_child' => 'Has inheritors',
  'documents_has_parent' => 'Has parents',
  'documents_has_family' => 'Has inheritors & parents',
  'documents_search' => 'Documents search &raquo; Filters',
  'documents_insert_medial_document' => 'With medial number',
  'documents_insert_medial_document_date' => 'Date',
  'documents_medial_first_cyrilic_capital_letter' => 'А',
  'documents_medial_first_cyrilic_small_letter' => 'а',
  'documents_my' => 'My documents',
  'documents_my_tab' => 'Created by me',
  'documents_myassigned' => 'For execution',
  'documents_mydep' => 'Documents unassigned into department',
  'documents_send_initial_notification' => 'Send notification letter to department',
  'documents_resend_initial_notification' => 'Send <strong>again</strong> notification letter to department',
  'documents_initial_notify_sent' => 'Notification letter sent to',
  'documents_initial_notify_not_sent' => 'No notification letter has been sent yet',
  'documents_select_direction' => 'Please, select kind!',
  'documents_add_projects' => 'Add new project',
  'documents_search_projects' => 'Search/Select project',
  'documents_add_customers' => 'Add new contractor',
  'documents_search_customers' => 'Search/Select contractor',
  'documents_add_media' => 'Add new document source!',
  'documents_add_type' => 'Add new type!',
  'documents_add_employee' => 'Add new employee!',
  'documents_add_filter' => 'Add new filter',
  'documents_add_' => 'Add',
  'documents_view_' => 'Review',
  'documents_add_new' => 'Add %s',
  'documents_add_incoming' => 'Add incoming document',
  'documents_add_outgoing' => 'Add outgoing document',
  'documents_add_internal' => 'Add internal document',
  'documents_clone_incoming' => 'Clone incoming document',
  'documents_clone_outgoing' => 'Clone outgoing document',
  'documents_clone_internal' => 'Clone internal document',
  'documents_edit' => 'Edit %s',
  'documents_view' => 'View %s',
  'documents_translate' => 'Translate %s',
  'documents_transform' => 'Transformation of %s',
  'documents_history' => 'History of %s',
  'documents_history_activity' => 'Activity',
  'documents_relatives' => 'Relations of %s',
  'documents_timesheets' => 'Reports to %s',
  'documents_attachments' => 'Files to %s',
  'documents_generate' => 'Generate file by pattern from %s',
  'documents_communications' => 'Communications',
  'documents_comments' => 'Comments',
  'documents_emails' => 'E-mails',
  'documents_minitasks' => 'Mini tasks',
  'documents_add_legend' => 'Add documents',
  'documents_history_legend2' => 'View document history (click on a row of the history to track changes)',
  'documents_system_task_type' => 'Report on',
  'documents_no_system_task' => 'Cannot add reports for specified document',
  'documents_timesheet_time' => 'Time reported',
  'documents_add_timesheet' => 'Add report',
  'documents_types_transform_to' => 'Transformations',
  'documents_no_department' => 'Document not distributed',
  'documents_today' => 'Today',
  'documents_yesterday' => 'Yesterday',
  'documents_week' => 'One week ago',
  'documents_month' => 'One month ago',
  'documents_year' => 'One year ago',
  'documents_last_modified' => 'Last edited',
  'documents_expired_label' => 'Expired',
  'documents_added_label' => 'Added',
  'documents_group_by' => 'Grouping by',
  'documents_reminder_email' => 'e-mail',
  'documents_reminder_toaster' => 'online',
  'documents_reminder_both' => 'both',
  'documents_reminder_event_name' => 'Reminder about document %s',
  'documents_mail_to_name' => 'Recipient name',
  'documents_mail_content' => 'E-mail content',
  'documents_generate_from_pattern' => 'Generate/create a new file using a pattern',
  'documents_variables' => 'Variables',
  'documents_communication_comments_add' => 'Add comment',
  'documents_communication_emails_add' => 'Send e-mail',
  'message_documents_add_success' => '%s added successfully',
  'message_documents_edit_success' => '%s edited successfully',
  'message_documents_status_success' => 'Status of %s changed successfully',
  'message_documents_translate_success' => '%s translated successfully',
  'message_documents_pattern_selected1' => 'You have selected a pattern for document generation.',
  'message_documents_pattern_selected2' => 'After entering the data, You will be transfered to file generation.',
  'message_documents_type_add_success' => 'Document type successfully added!',
  'message_documents_type_edit_success' => 'Document type successfully edited!',
  'message_documents_media_add_success' => 'Source successfully added!',
  'message_documents_media_edit_success' => 'Source successfully edited!',
  'message_documents_generate_success' => 'File successfully generated!',
  'error_documents_generate_document' => 'File exported UNSUCCESSFULLY!',
  'error_documents_generate_invalid_pattern' => 'No pattern selected, or unexisting pattern selected!',
  'message_documents_export_success' => 'File successfully exported!',
  'error_documents_export_document' => 'File exported UNSUCCESSFULLY!',
  'error_print_no_default_pattern' => 'No pattern selected for print',
  'error_documents_print_document' => 'File cannot be printed due to an error in generating document',
  'error_documents_change_status_closed' => 'The status of the document is "CLOSED". You cannot change the status of closed documents!',
  'error_documents_uncompleted_minitasks' => 'Record cannot be finished, as long as there are uncompleted mini tasks for it.',
  'message_initial_notify' => 'Notification letter successfully sent!',
  'message_document_vars_text' => 'Field of type <strong>TEXT</strong>',
  'message_document_vars_dropdown' => 'Field of type <strong>DROPDOWN</strong>',
  'message_document_vars_textarea' => 'Field of type <strong>TEXTAREA</strong>',
  'message_document_vars_integer' => 'This field value must be <strong>a positive integer</strong>',
  'message_document_vars_float' => 'his field value must be a positive integer that may be with floating comma',
  'message_document_vars_date' => 'Th,is field value must be in format: <strong>[yyyy-mm-dd]</strong>',
  'message_document_vars_datetime' => 'This variable value must be in format: <strong>[yyyy-mm-dd hh:mm]</strong>',
  'message_documents_new_vals' => 'No data saved for the additional variables',
  'message_documents_multiadd_success' => '%s added successfully',
  'message_documents_multiedit_success' => '%s edited successfully',
  'message_documents_multistatus_success' => 'Status of %s changed successfully',
  'message_documents_comments_add_success' => 'The comment was successfully saved',
  'message_documents_transform_success' => '%s successfully transformed',
  'message_documents_clone_success' => '%s successfully cloned',
  'message_documents_reminder_edit_success' => 'Reminder successfully edited',
  'message_documents_reminder_add_success' => 'Reminder successfully added',
  'message_documents_file_deleted_success' => 'File successfully deleted!',
  'error_documents_file_deleted_failed' => 'Error in deleting file!',
  'error_documents_add_failed' => '%s added UNSUCCESSFULLY',
  'error_documents_edit_failed' => '%s edited UNSUCCESSFULLY',
  'error_documents_status_failed' => 'Status of %s changed UNSUCCESSFULLY',
  'error_documents_translate_failed' => '%s not translated',
  'error_invalid_date' => 'Invalid date!',
  'error_invalid_deadline' => 'Enter valid date for %s!',
  'error_invalid_validity_term' => 'Enter valid date for %s!',
  'error_invalid_status_change' => 'Invalid selection of status!',
  'error_invalid_substatus_change' => 'Invalid selection of substatus!',
  'error_no_name' => 'Enter "%s" for the document!',
  'error_invalid_email' => 'Invalid e-mail!',
  'error_no_such_document' => 'This record is not available for you, because of any the following reasons:<br/>- no permissions<br/>- deleted record<br/>- archived record',
  'error_no_media_direction' => 'Please, select the kind of document type (incoming, outgoing or both).',
  'error_no_type_direction' => 'Please, select the kind of document reason (incoming, outgoing or both)',
  'error_no_customer' => 'Select %s!',
  'error_invalid_trademark' => 'Please, enter of select valid %s!',
  'error_no_new_customer' => 'Please, enter new contractor or select from list of contractors',
  'error_no_type' => 'Select type!',
  'error_invalid_type' => 'Invalid or inactive type selected!',
  'error_invalid_project' => 'Select %s!',
  'error_invalid_office' => 'Select %s!',
  'error_no_custom_num' => 'Enter %s!',
  'error_no_trademark' => 'Select %s!',
  'error_no_contract' => 'Select %s!',
  'error_no_project' => 'Select %s!',
  'error_no_office' => 'Select %s!',
  'error_no_employee' => 'Select %s!',
  'error_no_media' => 'Select %s!',
  'error_no_deadline' => 'Invalid date for %s!',
  'error_no_validity_term' => 'Invalid date for %s!',
  'error_no_description' => 'Enter %s!',
  'error_no_notes' => 'Enter %s!',
  'error_no_department' => 'Select %s!',
  'error_no_date' => 'Select %s!',
  'error_no_new_type' => 'Please, enter new type or select from list of types',
  'error_no_new_media' => 'Enter new source or select from list of sources!',
  'error_invalid_project_customer' => 'Selected project is not intended for selected contractor. Select another project or contractor "%s"',
  'error_no_new_project' => 'Enter new project or select from list of projects.',
  'error_delete_media' => 'Selected sources already in use and cannot be deleted!',
  'error_delete_type' => 'Selected types already in use and cannot be deleted!',
  'error_no_department_manager' => 'No manager assigned for department, notification letter cannot be sent',
  'error_type_already_used' => 'Documents using selected type already existing. You cannot change its kind',
  'error_media_already_used' => 'Documents using selected media already existing. You cannot change its kind',
  'error_invalid_status' => 'Invalid status assignment',
  'error_comments_add_failed' => 'Comment\'s add failed!',
  'error_not_allowed_medial_document_add_as_inactive' => 'It\'s not allowed to add a medial document as inactive!',
  'error_media_not_unique' => 'Such document media already existing. Please enter another media',
  'error_no_such_document_counter' => 'This record is not available for you!',
  'error_no_such_document_media' => 'This record is not available for you!',
  'error_no_such_document_section' => 'This record is not available for you!',
  'error_no_such_document_type' => 'This record is not available for you!',
  'error_initial_notify' => 'Error in sending notification letter:',
  'error_view_notallowed' => 'You don\'t have enough permissions to view document',
  'error_documents_multiadd_failed' => '%s not added',
  'error_documents_multiedit_failed' => '%s not edited',
  'error_documents_multistatus_failed' => 'Status of %s was not changed',
  'error_documents_no_multi_operation_variables' => 'There are no variables for this multiple operation',
  'error_documents_multiprint_failed' => 'Error printing multiple %s',
  'error_documents_print_invalid_pattern' => 'Template not chosen or incorrect template was chosen',
  'expired_documents_attention' => 'Attention!',
  'expired_documents_attention_full' => 'Documents that require my attention',
  'expired_documents_full_legend' => 'Documents with expired terms for processing or terms of validity',
  'expired_documents_legend' => 'Terms for documents processing expired',
  'expired_documents_validity_terms_legend' => 'Terms of documents validity expired',
  'warning_documents_relative_child' => 'Cannot connect with inherited document',
  'warning_documents_change_status_not_all' => 'Status of some %s was not changed because status change is invalid, they have uncompleted mini tasks or you have not enough rights to change their status.',
  'error_documents_transform_type' => 'Document cannot be transformed to selected type.',
  'error_documents_transform' => 'UNSUCCESSFUL transformation of %s',
  'error_no_such_transformation' => 'Inactive or invalid transformation selected!',
  'error_documents_clone' => 'UNSUCCESSFUL cloning of %s',
  'error_different_types' => 'Selected documents are of different types',
  'error_no_documents_or_deleted' => 'No documents selected or some of selected documents are deleted.',
  'error_documents_reminder_failed' => 'Reminder was not saved',
  'error_no_date_for_medial_document_add' => 'No date entered for medial document!',
  'error_medial_document_date_after_current_date' => 'You cannot add a document with medial number for a future date!',
  'error_no_previous_document_of_this_type' => 'You cannot add a document with medial number without having previous documents from this type!',
  'error_no_previous_document_of_this_type_and_customer' => 'You cannot add a document with medial number without having previous documents from this type for customer!',
  'error_no_counter_for_this_document_type' => 'No valid counter for this document type!',
  'error_isValidNumber' => 'The field [var_label] must be numbers',
  'error_documents_invalid_mails' => 'The following e-mails are invalid: [invalid_mails]',
  'documents_email_sent_success' => 'Notification letter successfully sent %s',
  'error_documents_send_email' => 'Error in sending letter %s',
  'error_documents_no_contract' => '"Contract num" not entered',
  'error_transformation_already_made' => 'Transformation is not possible because document has already been transformed to %s!',
  'documents_document_forward_notify' => 'for a non-forwarded document',
  'documents_document_status_notify' => 'for status change of %s',
  'documents_document_edit_notify' => 'for modication of %s',
  'documents_log_add' => '%s adds document (status: %s, %s)',
  'documents_log_multiadd' => '%s adds documents (status: %s, %s)',
  'documents_log_edit' => '%s edits document (status: %s, %s)',
  'documents_log_multiedit' => '%s edits documents (status: %s, %s)',
  'documents_log_translate' => '%s translates document (status: %s, %s)',
  'documents_log_managevars' => '%s edits document data (status: %s, %s)',
  'documents_log_activate' => '%s activates document (status: %s, %s)',
  'documents_log_deactivate' => '%s deactivates document (status: %s, %s)',
  'documents_log_delete' => '%s deletes document (status: %s, %s)',
  'documents_log_restore' => '%s restores document (status: %s, %s)',
  'documents_log_forward' => '%s forwards document (status: %s, %s)',
  'documents_log_assign' => '%s assigns document (status: %s, %s)',
  'documents_log_remove_assignments' => '%s removes assignments of %s type from document (status: %s, %s)',
  'documents_log_create' => '%s creates %s using %s',
  'documents_log_transform' => '%s transforms document (status: %s, %s) from %s',
  'documents_log_clone' => '%s clones document from document No. %s ("%s")',
  'documents_log_generate' => '%s generates file for document using template "%s"%s',
  'documents_log_print' => '%s prints document using template "%s"%s',
  'documents_log_multiprint' => '%s prints document using template "%s"%s',
  'documents_log_export' => '%s exports file for document (status: %s, %s)',
  'documents_log_modified_attachments' => '%s modifies attached file for document (status: %s, %s)',
  'documents_log_modified_gen' => '%s modifies generated file for document (status: %s, %s)',
  'documents_log_add_attachments' => '%s adds attached file for document (status: %s, %s)',
  'documents_log_del_attachments' => '%s deletes attached file for document (status: %s, %s)',
  'documents_log_generate_delete' => '%s deletes generated file for document (status: %s, %s)',
  'documents_log_status' => '%s changes document status (status: %s, %s)',
  'documents_log_multistatus' => '%s changes document status (status: %s, %s)',
  'documents_log_tag' => '%s changes document tags (status: %s, %s)',
  'documents_log_multitag' => '%s changes document tags (status: %s, %s)',
  'documents_log_email' => '%s sends e-mail about document',
  'documents_log_receive_email' => '%s receives document e-mail',
  'documents_log_receive_email_detailed' => 'There is an email from %s sent',
  'documents_log_merge' => '%s merges %s with %s',
  'documents_log_add_comment' => '%s adds comment about document',
  'documents_log_edit_comment' => '%s edits comment about document',
  'documents_log_archive' => '%s adds the document to archive',
  'documents_log_extract' => '%s extracts the document from archive',
  'documents_log_add_minitask' => '%s adds mini task to document',
  'documents_log_edit_minitask' => '%s edits mini task to document',
  'documents_log_status_minitask' => '%s changes status of mini task to document',
  'documents_log_multistatus_minitask' => '%s changes status of mini task to document',
  'documents_log_add_timesheet' => '%s reported %d minutes',
  'documents_log_edit_timesheet' => '%s reported %d minutes',
  'documents_log_relatives' => '%s modifies relations %s',
  'documents_logtype_add' => 'Add',
  'documents_logtype_multiadd' => 'Multiple add',
  'documents_logtype_edit' => 'Edit',
  'documents_logtype_multiedit' => 'Multiple edit',
  'documents_logtype_translate' => 'Translate',
  'documents_logtype_managevars' => 'Edit data',
  'documents_logtype_activate' => 'Activate',
  'documents_logtype_deactivate' => 'Deactivate',
  'documents_logtype_delete' => 'Delete',
  'documents_logtype_restore' => 'Restore',
  'documents_logtype_forward' => 'Forward',
  'documents_logtype_assign' => 'Assign',
  'documents_logtype_remove_assignments' => 'Assignments removal',
  'documents_logtype_create' => 'Create',
  'documents_logtype_transform' => 'Transform',
  'documents_logtype_clone' => 'Clone',
  'documents_logtype_generate' => 'Generate file',
  'documents_logtype_generate_delete' => 'Delete file',
  'documents_logtype_print' => 'Print',
  'documents_logtype_multiprint' => 'Multiple print',
  'documents_logtype_modified_attachments' => 'Modify file',
  'documents_logtype_modified_gen' => 'Modify file',
  'documents_logtype_add_attachments' => 'Add file',
  'documents_logtype_del_attachments' => 'Delete file',
  'documents_logtype_status' => 'Modify status',
  'documents_logtype_multistatus' => 'Multiple status change',
  'documents_logtype_tag' => 'Modify tags',
  'documents_logtype_multitag' => 'Multiple modification of tags',
  'documents_logtype_email' => 'Send e-mail',
  'documents_logtype_receive_email' => 'Received e-mail',
  'documents_logtype_merge' => 'Merge',
  'documents_logtype_add_comment' => 'Add comment',
  'documents_logtype_edit_comment' => 'Edit comment',
  'documents_logtype_archive' => 'Archiving',
  'documents_logtype_extract' => 'Extracting',
  'documents_logtype_add_minitask' => 'Add mini task',
  'documents_logtype_edit_minitask' => 'Edit mini task',
  'documents_logtype_status_minitask' => 'Change status of mini task',
  'documents_logtype_multistatus_minitask' => 'Multiple status change of mini tasks',
  'documents_logtype_add_timesheet' => 'Add timesheet',
  'documents_logtype_edit_timesheet' => 'Edit timesheet',
  'documents_logtype_relatives' => 'Relations',
  'documents_hotlinks' => 'Hotlinks',
  'documents_hotlinks_legend' => 'Add new documents',
  'documents_types_type_legend' => 'Document type. Unique field specifying type of documents. Mandatory field.',
  'documents_types_direction_legend' => 'Document direction: incoming, outgoing, internal. Mandatory field.',
  'documents_types_default_department_legend' => 'The field specifies default department in regime for adding document.',
  'documents_types_default_media_legend' => 'The field specifies default media in regime for adding document.',
  'documents_types_transform_to_legend' => 'Documents transformation requires selection of documents types, in which the document set can be transformed',
  'documents_expired_legend' => 'Document expired! <strong>Term for processing</strong> of document expired on',
  'documents_expired_validity_legend' => '<strong>Validity</strong> of document expired on',
  'confirm_link_documents' => 'Are You sure You want to connect selected documents?\\nPress "OK" to confirm!',
  'alert_link_documents' => 'Please, select document for link!',
  'documents_department_not_forwarded' => 'Document is not forwarded to a department and cannot be <strong>assigned to</strong> specific user.',
  'documents_group_no_users' => 'No users in this department',
  'var_type' => 'Type',
  'var_name' => 'Field',
  'var_value' => 'New text',
  'old_value' => 'Previous text',
  'no_old_value' => 'no previous text',
  'audit_vars' => 'History of modifications done with document',
  'audit_legend' => 'Detailed information about modifications done by %s on %s',
  'documents_replace_all' => 'Replace<br />all',
  'documents_replace_col' => 'Replace column',
  'documents_assign_title' => 'Assignments as',
  'documents_assign_owner' => 'Assign To',
  'documents_assign_responsible' => 'Supervisor',
  'documents_assign_observer' => 'Follow',
  'documents_assign_decision' => 'Decision maker',
  'documents_owner' => 'Assign To',
  'documents_responsible' => 'Supervisor',
  'documents_observer' => 'Follow',
  'documents_decision' => 'Decision maker',
  'documents_assign_change' => 'Change assignments',
  'documents_show_full_assignments_list' => 'Show full assignments list',
  'documents_hide_full_assignments_list' => 'Hide full assignments list',
  'help_file_locations' => 'Path to selected files must start with <strong>%s</strong> or with <strong>\\\\</strong>',
  'help_documents_notes' => '',
  'help_documents_name' => '',
  'help_documents_file_location' => '',
  'help_documents_file_locations' => '',
  'help_documents_file_generated' => '',
  'help_documents_files_generated' => '',
  'help_documents_direction' => '',
  'help_documents_type' => '',
  'help_documents_validity_term' => '',
  'help_documents_prefix' => '',
  'help_documents_project' => 'Project to document. May be selected by simply searching by name or code of the project You are interested in. It is enough to write two letters and You will see the results from the search for these letters.',
  'help_documents_customer' => 'Contractor to document. May be selected by simply searching by name or code of the contractor You are interested in. It is enough to write two letters and You will see the results from the search for these letters.',
  'help_documents_customer_recipient' => '',
  'help_documents_office' => '',
  'help_documents_employee' => '',
  'help_documents_group' => '',
  'help_documents_department' => '',
  'help_documents_media' => '',
  'help_documents_media_2' => '',
  'help_documents_types' => '',
  'help_documents_description' => '',
  'help_documents_status' => 'Document status',
  'help_documents_substatus' => '<b>Status: </b>',
  'help_documents_status_opened' => 'Document status is <b>"OPEN"</b> - editing of all document parameters allowed.',
  'help_documents_status_closed' => 'Document status is <b>"CLOSED"</b> - document parameters cannot be edited.',
  'help_documents_status_locked' => 'Document status is <b>"LOCKED"</b> - only part of document parameters may be edited.',
  'help_documents_num' => 'Document Serial No.',
  'help_documents_custom_num' => 'Number set by contractor. Usually used for connecting to the number in the contractor document register.',
  'help_documents_media_used' => '',
  'help_documents_type_used' => '',
  'help_documents_view_url' => '',
  'help_documents_date' => '',
  'help_documents_datetime' => '',
  'help_documents_deadline' => '',
  'help_documents_initial_notify' => '',
  'help_documents_relatives' => '',
  'help_documents_parents' => '',
  'help_documents_children' => '',
  'help_documents_parents2' => '',
  'help_documents_children2' => '',
  'help_documents_event_text' => '',
  'help_documents_event_type' => '',
  'help_documents_pattern' => '',
  'help_documents_pattern_header' => '',
  'help_documents_pattern_footer' => '',
  'help_documents_select_direction' => '',
  'help_documents_add_name' => 'About for document You enter. It may include the following variables that will be replaced with their respective values after adding:<br /><strong>[document_full_num]</strong> - document Serial No.<br /><strong>[customer_name]</strong> - client name<br /><strong>[office_name]</strong> - Name of office, to which the document refers<br /><strong>[project_name]</strong> - the project, to which the document refers',
  'help_documents_add_notes' => 'Notes, specifications, etc. to the document You enter. Here the following variables may be added, which will be replaced with their respective values after adding:<br /><strong>[document_full_num]</strong> - document Serial No.<br /><strong>[customer_name]</strong> - client name<br /><strong>[office_name]</strong> - Name of office, to which the document refers<br /><strong>[project_name]</strong> - Project, to which the document refers',
  'help_documents_add_description' => 'Description of the document You enter. Description may include the following variables that will be replaced with their respective values after adding:<br /><strong>[document_full_num]</strong> - document Serial No.<br /><strong>[customer_name]</strong> - Client name<br /><strong>[office_name]</strong> - name of office, to which the document refers<br /><strong>[project_name]</strong> - project, to which the document refers',
  'help_documents_add_projects' => 'You may add a new project that will be directly filled in',
  'help_documents_add_customers' => 'You may add a new contractor that will be directly filled in',
  'help_documents_search_projects' => 'Search for project necessary and select it for this document',
  'help_documents_search_customers' => 'Search/Select contractor',
  'help_documents_add_media' => '',
  'help_documents_add_type' => '',
  'help_documents_add_employee' => '',
  'help_documents_add_filter' => '',
  'help_documents_check_all_caption' => 'Mark / Demark',
  'help_documents_check_all_text' => 'Mark / Demark all fields for replacing columns',
  'help_documents_separator' => 'Data separator for csv format',
  'help_documents_multiedit' => 'To finish simultaneous editing by columns, You must mark the tick before the text <strong>"Replace column"</strong> in the column where You wish to edit. On the row below the text "Replace column" You will the field where You may enter the edition, with which You wish to replace the whole column. By clicking on the button "Record", replacement will be automatically made. <br /><span class=\'red\'><strong>Attention!</strong></span> The function "Replace column" is used only in the cases when You wish the information in the entire column to be the same. Otherwise, use editing line by line.',
); ?>