<?php /* Smarty version 2.6.33, created on 2024-10-15 17:18:49
         compiled from _gt2_edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '_gt2_edit.html', 19, false),array('modifier', 'escape', '_gt2_edit.html', 23, false),array('modifier', 'count', '_gt2_edit.html', 25, false),array('function', 'math', '_gt2_edit.html', 28, false),array('function', 'help', '_gt2_edit.html', 36, false),array('function', 'counter', '_gt2_edit.html', 56, false),)), $this); ?>
<?php if (empty ( $this->_tpl_vars['table'] )): ?>
  <?php $this->assign('table', $this->_tpl_vars['model']->get('grouping_table_2')); ?>
<?php endif; ?>
<?php if (! $this->_tpl_vars['table']['no_container']): ?>
<div id="gt2_container">
<?php endif; ?>
<?php if (trim ( $this->_tpl_vars['table']['label'] ) != '' && ! $this->_tpl_vars['hide_label']): ?>
<div class="t_caption2_title" style="float: left"><?php echo $this->_tpl_vars['table']['label']; ?>
</div>
<br clear="all" />
<?php endif; ?>
<?php if (! empty ( $this->_tpl_vars['table']['configurator_group'] )): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_configs.html", 'smarty_include_vars' => array('configs' => $this->_tpl_vars['table']['configs'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
<?php if (trim ( $this->_tpl_vars['table']['javascript'] )): ?>
<script type="text/javascript">
    <?php echo $this->_tpl_vars['table']['javascript']; ?>

</script>
<?php endif; ?>
<?php $this->assign('allow_negative_price_explicitly', ((is_array($_tmp=@$this->_tpl_vars['table']['allow_negative_price'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))); ?>
<input type="hidden" name="calc_price" id="calc_price" value="<?php echo $this->_tpl_vars['table']['calculated_price']; ?>
"<?php if ($this->_tpl_vars['allow_negative_price_explicitly']): ?> class="allowNegativePrice"<?php endif; ?> />
<table id="var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
" class="t_grouping_table grouping_table2 gt2edit<?php if ($this->_tpl_vars['table']['custom_class']): ?> <?php echo $this->_tpl_vars['table']['custom_class']; ?>
<?php endif; ?>" style="<?php if ($this->_tpl_vars['table']['width']): ?>width:<?php echo $this->_tpl_vars['table']['width']; ?>
<?php if (preg_match ( '#^\d+$#' , $this->_tpl_vars['table']['width'] )): ?>px<?php endif; ?>;<?php endif; ?>">
<tr>
  <th width="20" style="text-align:right"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
  <?php $this->assign('table_values_count', 0); ?>
  <?php if (! empty ( $this->_tpl_vars['table']['values'] )): ?><?php $this->assign('table_values_count', count($this->_tpl_vars['table']['values'])); ?><?php endif; ?>
  <?php $this->assign('table_rows_readonly_count', 0); ?>
  <?php if (! empty ( $this->_tpl_vars['table']['rows_readonly'] )): ?><?php $this->assign('table_rows_readonly_count', count($this->_tpl_vars['table']['rows_readonly'])); ?><?php endif; ?>
  <?php echo smarty_function_math(array('assign' => 'values_count','equation' => 'a-b','a' => $this->_tpl_vars['table_values_count'],'b' => $this->_tpl_vars['table_rows_readonly_count']), $this);?>

  <?php $_from = $this->_tpl_vars['table']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo $this->_tpl_vars['var']['help']; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['label']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
    <th<?php if ($this->_tpl_vars['var']['hidden']): ?> style="display: none;"<?php endif; ?><?php if ($this->_tpl_vars['var']['cell_class']): ?> class="<?php echo $this->_tpl_vars['var']['cell_class']; ?>
"<?php endif; ?>>
      <?php if ($this->_tpl_vars['table']['last_visible_column'] == $this->_tpl_vars['key']): ?>
        <div<?php if ($this->_tpl_vars['var']['width']): ?> style=" width:<?php echo $this->_tpl_vars['var']['width']; ?>
px;"<?php endif; ?>>
          <div class="floatl">
            <a name="error_<?php echo $this->_tpl_vars['var']['name']; ?>
"></a>
            <label for="<?php echo $this->_tpl_vars['var']['name']; ?>
_1"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['var']['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['info'],'label_sufix' => ''), $this);?>
<?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></label>
          </div>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_table_buttons.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['table'],'values_count' => $this->_tpl_vars['values_count'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php if (! empty ( $this->_tpl_vars['table']['floating_buttons'] )): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_table_buttons.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['table'],'values_count' => $this->_tpl_vars['values_count'],'floating_buttons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; ?>
        </div>
      <?php else: ?>
        <div style="<?php if ($this->_tpl_vars['var']['hidden']): ?> display: none;<?php elseif ($this->_tpl_vars['var']['width']): ?> width:<?php echo $this->_tpl_vars['var']['width']; ?>
px;<?php endif; ?>">
          <a name="error_<?php echo $this->_tpl_vars['var']['name']; ?>
"></a><label for="<?php echo $this->_tpl_vars['var']['name']; ?>
_1"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['var']['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['info'],'label_sufix' => ''), $this);?>
<?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></label>
        </div>
      <?php endif; ?>
    </th>
  <?php endforeach; endif; unset($_from); ?>
</tr>
<?php echo smarty_function_counter(array('assign' => 'cols_count','print' => false,'name' => 'colcount','start' => 0), $this);?>

<?php $_from = $this->_tpl_vars['table']['values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['row'] => $this->_tpl_vars['val']):
        $this->_foreach['i']['iteration']++;
?>
<?php if (empty ( $this->_tpl_vars['table']['rows'] ) || ( ! empty ( $this->_tpl_vars['table']['rows'] ) && ! in_array ( $this->_tpl_vars['row'] , $this->_tpl_vars['table']['rows'] ) )): ?>
    <?php ob_start(); ?>-<?php echo $this->_foreach['i']['iteration']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_index', ob_get_contents());ob_end_clean(); ?>
<?php else: ?>
  <?php $this->assign('row_index', $this->_tpl_vars['row']); ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['table']['last_editable_row_index'] === $this->_foreach['i']['iteration']-1): ?>
<tr id="gt2_delimeter<?php if ($this->_tpl_vars['table']['delimeter_start']): ?>_start<?php endif; ?>" style="display:none"><td colspan="50" style="height: 0px;padding: 0"></td></tr>
<?php endif; ?>
<tr id="var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
_<?php echo $this->_foreach['i']['iteration']; ?>
"<?php if ($this->_tpl_vars['val']['deleted'] == 1): ?> style="display: none"<?php endif; ?> class="<?php if ($this->_tpl_vars['val']['deleted'] == 1): ?> input_inactive<?php endif; ?><?php if ($this->_tpl_vars['table']['rows_readonly'] && @ in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly'] )): ?> readonly<?php if ($this->_tpl_vars['table']['delimeter_start']): ?>_start<?php endif; ?><?php if ($this->_tpl_vars['table']['allow_readonly_delete']): ?> allow_delete<?php endif; ?><?php endif; ?>"<?php if ($this->_tpl_vars['table']['floating_buttons']): ?> onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"<?php endif; ?>>
  <td style="text-align:right" nowrap="nowrap">
  <?php if (! $this->_tpl_vars['table']['hide_delete'] && ( ! @ in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly'] ) || $this->_tpl_vars['table']['allow_readonly_delete'] ) && ! @ in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly_force'] )): ?>
    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if ($this->_tpl_vars['values_count'] <= 1 && ! @ in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly'] ) && ( ! $this->_tpl_vars['table']['allow_readonly_delete'] || $this->_foreach['i']['iteration'] == 1 || ( ( $this->_foreach['i']['iteration'] ) - ( $this->_tpl_vars['table_rows_readonly_count'] ) <= 1 ) )): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function() { hideField('var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
','<?php echo $this->_foreach['i']['iteration']; ?>
'); }, this);" />&nbsp;<a href="javascript: disableField('var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
','<?php echo $this->_foreach['i']['iteration']; ?>
')"><?php echo $this->_foreach['i']['iteration']; ?>
</a>
  <?php else: ?>
    <?php echo $this->_foreach['i']['iteration']; ?>

  <?php endif; ?>
  </td>
  <td style="display:none">
    <input type="hidden" id="deleted_<?php echo $this->_foreach['i']['iteration']; ?>
" name="deleted[<?php if ($this->_tpl_vars['empty_indexes'] != 1): ?><?php echo $this->_tpl_vars['row_index']; ?>
<?php endif; ?>]" class="deleted_flag" value="<?php echo $this->_tpl_vars['val']['deleted']; ?>
" />
  </td>
  <?php $_from = $this->_tpl_vars['table']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
      <?php if (($this->_foreach['i']['iteration'] <= 1) && ( ! $this->_tpl_vars['var']['hidden'] || $this->_tpl_vars['var']['hidden'] === '0' || $this->_tpl_vars['var']['hidden'] === 0 )): ?>
        <?php echo smarty_function_counter(array('assign' => 'cols_count','print' => false,'name' => 'colcount'), $this);?>

      <?php endif; ?>
      <?php if (($this->_foreach['i']['iteration'] <= 1) && ! empty ( $this->_tpl_vars['var']['agregate'] )): ?>
        <?php echo smarty_function_counter(array('assign' => 'agregates_count','print' => false,'name' => 'agregatescount'), $this);?>

      <?php endif; ?>
      <td style="<?php if ($this->_tpl_vars['var']['width'] && ! $this->_tpl_vars['var']['back_label']): ?>max-width:<?php echo $this->_tpl_vars['var']['width']; ?>
<?php if (preg_match ( '#^\d+$#' , $this->_tpl_vars['var']['width'] )): ?>px<?php endif; ?>;<?php endif; ?><?php if ($this->_tpl_vars['var']['hidden']): ?>display: none;<?php endif; ?>" class="<?php if ($this->_tpl_vars['var']['cell_class']): ?><?php echo $this->_tpl_vars['var']['cell_class']; ?>
<?php endif; ?>">
      <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && ( $this->_tpl_vars['var']['type'] == 'autocompleter' || $this->_tpl_vars['var']['type'] == 'formula' || $this->_tpl_vars['var']['type'] == 'index' || $this->_tpl_vars['var']['back_label'] )): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
      <?php if ($this->_tpl_vars['table']['rows_readonly'] && ( @ in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly'] ) && @ ! in_array ( $this->_tpl_vars['var']['name'] , $this->_tpl_vars['table']['fields_not_readonly'] ) || @ in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly_force'] ) ) || preg_match ( '#^(warehouse\d+_)?quantity$#' , $this->_tpl_vars['var']['name'] ) && $this->_tpl_vars['val']['has_batch']): ?>
        <?php $this->assign('var_readonly', 1); ?>
      <?php else: ?>
        <?php $this->assign('var_readonly', $this->_tpl_vars['var']['readonly']); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['var']['name'] == 'article_id'): ?>
        <?php if (is_array ( $this->_tpl_vars['table']['system_articles'] ) && array_key_exists ( $this->_tpl_vars['val']['article_id'] , $this->_tpl_vars['table']['system_articles'] )): ?>
          <?php $this->assign('article_id', $this->_tpl_vars['val']['article_id']); ?>
          <?php if ($this->_tpl_vars['table']['system_articles'][$this->_tpl_vars['article_id']] == 'advance'): ?>
            <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 allowNegativePrice<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
          <?php elseif ($this->_tpl_vars['table']['system_articles'][$this->_tpl_vars['article_id']] == 'discount'): ?>
            <?php if (! preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) || $this->_tpl_vars['table']['model_type'] != PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE): ?>
              <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 onlyNegativePrice<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
            <?php endif; ?>
          <?php elseif (preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) && $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE): ?>
            <?php if ($this->_tpl_vars['table']['system_articles'][$this->_tpl_vars['article_id']] == 'surplus'): ?>
              <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 onlyNegativePrice<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
            <?php else: ?>
              <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 onlyNegativePrice onlyNegativeQuantity<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
            <?php endif; ?>
          <?php else: ?>
            <?php $this->assign('var_custom_class', ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name']))); ?>
          <?php endif; ?>
        <?php elseif (preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) && ( $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $this->_tpl_vars['printform'] && $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_CREDIT_NOTICE )): ?>
          <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 onlyNegativePrice onlyNegativeQuantity<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
        <?php elseif ($this->_tpl_vars['allow_negative_price_explicitly']): ?>
          <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 allowNegativePrice<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
        <?php else: ?>
          <?php $this->assign('var_custom_class', ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name']))); ?>
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['var']['name'] == $this->_tpl_vars['table']['calculated_price']): ?>
        <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
<?php if (! empty ( $this->_tpl_vars['table']['rows_readonly_force'] ) && in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly_force'] )): ?> system_readonly<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
        <?php if (is_array ( $this->_tpl_vars['table']['system_articles'] ) && array_key_exists ( $this->_tpl_vars['val']['article_id'] , $this->_tpl_vars['table']['system_articles'] )): ?>
          <?php $this->assign('article_id', $this->_tpl_vars['val']['article_id']); ?>
          <?php if ($this->_tpl_vars['table']['system_articles'][$this->_tpl_vars['article_id']] == 'advance'): ?>
            <?php $this->assign('text_restrict', 'insertOnlyReals'); ?>
          <?php elseif ($this->_tpl_vars['table']['system_articles'][$this->_tpl_vars['article_id']] == 'discount'): ?>
            <?php if (! preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) || $this->_tpl_vars['table']['model_type'] != PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE): ?>
              <?php $this->assign('text_restrict', 'insertOnlyReals'); ?>
            <?php endif; ?>
          <?php elseif (preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) && $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE): ?>
            <?php $this->assign('text_restrict', 'insertOnlyReals'); ?>
          <?php else: ?>
            <?php $this->assign('text_restrict', 'insertOnlyFloats'); ?>
          <?php endif; ?>
        <?php elseif (preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) && $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE): ?>
          <?php $this->assign('text_restrict', 'insertOnlyReals'); ?>
        <?php elseif ($this->_tpl_vars['allow_negative_price_explicitly']): ?>
          <?php $this->assign('text_restrict', 'insertOnlyReals'); ?>
        <?php else: ?>
          <?php $this->assign('text_restrict', 'insertOnlyFloats'); ?>
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['var']['name'] == 'quantity'): ?>
        <?php if (preg_match ( '#^Finance_#' , $this->_tpl_vars['table']['model'] ) && ( $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $this->_tpl_vars['printform'] && $this->_tpl_vars['table']['model_type'] == PH_FINANCE_TYPE_CREDIT_NOTICE )): ?>
          <?php $this->assign('text_restrict', 'insertOnlyReals'); ?>
        <?php else: ?>
          <?php $this->assign('text_restrict', 'insertOnlyFloats'); ?>
        <?php endif; ?>
        <?php if (is_array ( $this->_tpl_vars['table']['system_articles'] ) && array_key_exists ( $this->_tpl_vars['val']['article_id'] , $this->_tpl_vars['table']['system_articles'] )): ?>
          <?php $this->assign('var_readonly', true); ?>
          <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
 system_readonly<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
        <?php else: ?>
          <?php $this->assign('var_custom_class', ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name']))); ?>
        <?php endif; ?>
      <?php else: ?>
        <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
<?php if (preg_match ( '/^(surplus|discount)/' , $this->_tpl_vars['var']['name'] ) && ! empty ( $this->_tpl_vars['table']['rows_readonly_force'] ) && in_array ( $this->_foreach['i']['iteration'] , $this->_tpl_vars['table']['rows_readonly_force'] )): ?> system_readonly<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_custom_class', ob_get_contents());ob_end_clean(); ?>
        <?php $this->assign('text_restrict', $this->_tpl_vars['var']['js_filter']); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['var']['type'] == 'text'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['text_restrict'],'text_align' => $this->_tpl_vars['var']['text_align'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'origin' => 'group')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'formula' && ( $this->_tpl_vars['var']['formula_type'] == 'text' || $this->_tpl_vars['var']['formula_type'] == 'index' )): ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['key']; ?>
_formula<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('key_formula', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_formula.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'formula_value' => $this->_tpl_vars['val'][$this->_tpl_vars['key_formula']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['text_restrict'],'text_align' => $this->_tpl_vars['var']['text_align'],'width' => $this->_tpl_vars['var_width'],'source' => $this->_tpl_vars['var']['formula_type'],'height' => $this->_tpl_vars['var']['height'],'origin' => 'group')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'index'): ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['key']; ?>
_formula<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('key_formula', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['key']; ?>
_date<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('key_date', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_index.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'formula_value' => $this->_tpl_vars['val'][$this->_tpl_vars['key_formula']],'date_value' => $this->_tpl_vars['val'][$this->_tpl_vars['key_date']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'indexes' => $this->_tpl_vars['indexes'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['text_restrict'],'text_align' => $this->_tpl_vars['var']['text_align'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'origin' => 'group')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'textarea'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_textarea.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'origin' => 'group')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'dropdown'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'text_align' => $this->_tpl_vars['var']['text_align'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'required' => $this->_tpl_vars['var']['required'],'origin' => 'group')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'radio'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_radio.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'on_change' => $this->_tpl_vars['var']['on_change'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'options' => $this->_tpl_vars['var']['options'],'options_align' => $this->_tpl_vars['var']['options_align'],'required' => $this->_tpl_vars['var']['required'],'origin' => 'group')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'autocompleter'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'value_id' => $this->_tpl_vars['val'][$this->_tpl_vars['var']['autocomplete']['id_var']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'autocomplete' => $this->_tpl_vars['var']['autocomplete'],'exclude_oldvalues' => $this->_tpl_vars['var']['exclude_oldvalues'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'date'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'origin' => 'group','js_methods' => $this->_tpl_vars['var']['js_methods'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'datetime'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_datetime.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'origin' => 'group','js_methods' => $this->_tpl_vars['var']['js_methods'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'time'): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_time.html', 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['var']['name'],'custom_class' => $this->_tpl_vars['var_custom_class'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'origin' => 'group','js_methods' => $this->_tpl_vars['var']['js_methods'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['var']['type'] == 'file_upload'): ?>
        <?php ob_start(); ?>deleteid_<?php echo $this->_tpl_vars['key']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('deleteid_key', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_file_upload.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => true,'source' => 'gt2','name' => $this->_tpl_vars['var']['name'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['row_index'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'readonly' => $this->_tpl_vars['var_readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['var']['disabled'],'width' => $this->_tpl_vars['var_width'],'height' => $this->_tpl_vars['var']['height'],'origin' => 'group','view_mode' => $this->_tpl_vars['var']['view_mode'],'thumb_width' => $this->_tpl_vars['var']['thumb_width'],'thumb_height' => $this->_tpl_vars['var']['thumb_height'],'deleteid' => $this->_tpl_vars['val'][$this->_tpl_vars['deleteid_key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
      </td>
  <?php endforeach; endif; unset($_from); ?>
</tr>
<?php if ($this->_tpl_vars['val']['inactive']): ?><script type="text/javascript">disableField('var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
','<?php echo $this->_foreach['i']['iteration']; ?>
');</script><?php endif; ?>
<?php if ($this->_tpl_vars['val']['has_batch'] && ! $this->_tpl_vars['val']['deleted']): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_batch_extention.html", 'smarty_include_vars' => array('idx' => $this->_foreach['i']['iteration'],'row_index' => $this->_tpl_vars['row_index'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
<?php if (! isset ( $this->_tpl_vars['table']['last_editable_row_index'] ) || $this->_tpl_vars['table']['delimeter_start']): ?>
<tr id="gt2_delimeter" style="display:none"><td></td></tr>
<?php endif; ?>
<?php if (! $this->_tpl_vars['table']['hide_agregates']): ?>
  <?php if ($this->_tpl_vars['agregates_count'] > 0): ?>
    <tr class="gt2_agregates">
      <td>&nbsp;</td>
      <td style="display:none"></td>
    <?php $_from = $this->_tpl_vars['table']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
      <td style="<?php if ($this->_tpl_vars['var']['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['var']['text_align']): ?> text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>"><?php echo ''; ?><?php if ($this->_tpl_vars['var']['agregate']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['name']; ?><?php echo '_agregate'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ag_name', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['agregate']): ?><?php echo 'agregate'; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ag_class', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('hidden' => 1,'custom_class' => 'agregate','name' => $this->_tpl_vars['ag_name'],'value' => $this->_tpl_vars['var']['agregate'],'standalone' => true,'width' => $this->_tpl_vars['var']['width'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'gt2_tagregates_'; ?><?php echo $this->_tpl_vars['var']['agregate']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ag_text', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo $this->_config[0]['vars'][$this->_tpl_vars['ag_text']]; ?><?php echo ': <span class="'; ?><?php echo $this->_tpl_vars['var']['name']; ?><?php echo '_ag_content"></span>'; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?>
</td>
    <?php endforeach; endif; unset($_from); ?>
    </tr>
  <?php endif; ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['table']['totals_texts_colspan']): ?>
<?php $this->assign('totals_texts_colspan', $this->_tpl_vars['table']['totals_texts_colspan']); ?>
<?php else: ?>
<?php $this->assign('totals_texts_colspan', 3); ?>
<?php endif; ?>
<?php $this->assign('totals_colspan', $this->_tpl_vars['cols_count']-$this->_tpl_vars['totals_texts_colspan']); ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_without_discount']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_without_discount']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_without_discount','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_without_discount'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'width' => $this->_tpl_vars['var_width'],'readonly' => 1,'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_discount_percentage']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_discount_percentage']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_discount_percentage','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_discount_percentage'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'readonly' => $this->_tpl_vars['var']['readonly'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'width' => $this->_tpl_vars['var_width'],'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_discount_value']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_discount_value']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_discount_value','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_discount_value'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'readonly' => $this->_tpl_vars['var']['readonly'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'width' => $this->_tpl_vars['var_width'],'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_surplus_percentage']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_surplus_percentage']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_surplus_percentage','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_surplus_percentage'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'readonly' => $this->_tpl_vars['var']['readonly'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'width' => $this->_tpl_vars['var_width'],'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_surplus_value']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_surplus_value']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_surplus_value','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_surplus_value'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'readonly' => $this->_tpl_vars['var']['readonly'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'width' => $this->_tpl_vars['var_width'],'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_discount_surplus_field']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_discount_surplus_field']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_discount_surplus_field','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_discount_surplus_field'])) ? $this->_run_mod_handler('default', true, $_tmp, 'none') : smarty_modifier_default($_tmp, 'none')),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'readonly' => $this->_tpl_vars['var']['readonly'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'width' => $this->_tpl_vars['var_width'],'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'width' => $this->_tpl_vars['var_width'],'readonly' => 1,'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_vat_rate']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_vat_rate']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">
  <?php if ($this->_tpl_vars['table']['plain_vars']['total_no_vat_reason'] && $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']): ?>
    <div<?php if ($this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['hidden']): ?> style="display: none;"<?php endif; ?>>
    <label for="total_no_vat_reason_text" class="vtop"><?php echo $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['label']; ?>
:</label>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'total_no_vat_reason_text','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['name'])),'index' => 0,'value' => $this->_tpl_vars['table']['plain_values']['total_no_vat_reason_text'],'value_id' => $this->_tpl_vars['table']['plain_values']['total_no_vat_reason'],'autocomplete' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['autocomplete'],'exclude_oldvalues' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['exclude_oldvalues'],'required' => 0,'disabled' => 0,'text_align' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['text_align'],'label' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['label'],'help' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['help'],'back_label' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['back_label'],'back_label_style' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['back_label_style'],'show_placeholder' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['show_placeholder'],'width' => 200,'readonly' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['readonly'],'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_no_vat_reason','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_vars']['total_no_vat_reason']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['table']['plain_vars']['total_no_vat_reason']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['table']['plain_vars']['total_no_vat_reason']['name'])),'index' => 0,'value' => $this->_tpl_vars['table']['plain_values']['total_no_vat_reason'],'required' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
  <?php endif; ?>
  </td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('standalone' => true,'name' => 'total_vat_rate','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_vat_rate'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['table']['default_VAT']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['table']['default_VAT'])),'required' => 1,'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'width' => $this->_tpl_vars['var_width'],'sequences' => 'gt2calc(this)','onkeyup' => 'gt2calc(this)','restrict' => 'insertOnlyFloats','readonly' => $this->_tpl_vars['var']['readonly'],'disabled' => 0,'hidden' => $this->_tpl_vars['var']['hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_vat']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_vat']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'total_vat','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_vat'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'width' => $this->_tpl_vars['var_width'],'readonly' => 1,'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['total_with_vat']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_with_vat']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none"></td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'total_with_vat','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_with_vat'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'width' => $this->_tpl_vars['var_width'],'readonly' => 1,'hidden' => 0)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
</tr>
<?php endif; ?>

<?php if ($this->_tpl_vars['table']['plain_vars']['currency']): ?>
<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['currency']); ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none"></td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['width'] && $this->_tpl_vars['var']['back_label']): ?><?php echo $this->_tpl_vars['var']['width']; ?>
<?php else: ?>100%<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'currency','custom_class' => ((is_array($_tmp=@$this->_tpl_vars['var']['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])),'index' => 0,'value' => ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['currency'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['options']['0']['option_value']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['options']['0']['option_value'])),'text_align' => $this->_tpl_vars['var']['text_align'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'width' => $this->_tpl_vars['var_width'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'required' => 1,'readonly' => $this->_tpl_vars['var']['readonly'],'onchange' => "changeGT2Currency(this)",'hidden' => $this->_tpl_vars['var']['hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <input type="hidden" id="old_currency" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['currency'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['options']['0']['option_value']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['options']['0']['option_value'])); ?>
" />
  </td>
</tr>
<?php endif; ?>
<?php if ($this->_tpl_vars['table']['extend']): ?>
  <?php ob_start(); ?><?php echo $this->_tpl_vars['templatesDir']; ?>
<?php echo $this->_tpl_vars['table']['extend']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('filename', ob_get_contents());ob_end_clean(); ?>
  <?php if (! file_exists ( $this->_tpl_vars['filename'] )): ?>
    <?php ob_start(); ?><?php echo $this->_tpl_vars['theme']->templatesDir; ?>
<?php echo $this->_tpl_vars['table']['extend']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('filename', ob_get_contents());ob_end_clean(); ?>
  <?php endif; ?>
  <?php if (file_exists ( $this->_tpl_vars['filename'] )): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['filename'], 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endif; ?>
<?php endif; ?>
</table>
<input type="hidden" name="gt2_requested" id="gt2_requested" value="1" />
<script type="text/javascript">
    gt2calc('total'<?php if ($this->_tpl_vars['table']['bb']): ?>, 'var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
'<?php endif; ?>);
    gt2calc('agregates'<?php if ($this->_tpl_vars['table']['bb']): ?>, 'var_group_<?php echo $this->_tpl_vars['table']['grouping']; ?>
'<?php endif; ?>);
    $$('.warehouse_quantity').each(function(field){
      if (field.onkeyup) field.onkeyup();
    });
</script>
<?php if (! $this->_tpl_vars['table']['no_container']): ?>
</div>
<?php endif; ?>