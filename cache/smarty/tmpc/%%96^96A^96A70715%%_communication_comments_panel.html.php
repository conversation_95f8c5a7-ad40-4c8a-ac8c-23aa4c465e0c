<?php /* Smarty version 2.6.33, created on 2023-07-25 12:08:39
         compiled from _communication_comments_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'mb_lower', '_communication_comments_panel.html', 1, false),array('modifier', 'default', '_communication_comments_panel.html', 12, false),array('modifier', 'escape', '_communication_comments_panel.html', 21, false),array('function', 'uniqid', '_communication_comments_panel.html', 54, false),)), $this); ?>
<?php ob_start(); ?><?php echo ((is_array($_tmp=$this->_tpl_vars['model_name'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
s<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('module_name', ob_get_contents());ob_end_clean(); ?>
<?php if ($_REQUEST['inline_add']): ?>
  <?php $this->assign('inline_add', 1); ?>
<?php endif; ?>
  <a name="comments_add_form"></a>
  <form name="comments_add" id="comments_add" action="" method="post" onsubmit="saveCommunicationRecord(this, 'comment', '<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
', '<?php echo $this->_tpl_vars['communication_type']; ?>
'); return false;">
    <?php if ($this->_tpl_vars['inline_add']): ?>
    <script type="text/javascript" defer="defer" src="<?php echo @PH_CKEDITOR_URL; ?>
ckeditor.js"></script>
    <script type="text/javascript" defer="defer" src="<?php echo @PH_MODULES_URL; ?>
communications/javascript/communications.js?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
    <?php endif; ?>
    <input type="hidden" name="model_id" id="model_id" value="<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
" />
    <input type="hidden" name="parent_id" id="parent_id" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['comment_parent_id'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
    <input type="hidden" name="comment_id" id="comment_id" value="<?php echo $this->_tpl_vars['comment_id']; ?>
" />
    <input type="hidden" name="model" id="model" value="<?php echo $this->_tpl_vars['current_model']->modelName; ?>
" />
    <?php if ($this->_tpl_vars['event_date']): ?>
      <input type="hidden" name="event_date" id="event_date" value="<?php echo $this->_tpl_vars['event_date']; ?>
" />
    <?php endif; ?>
    <div id="comment_container">
      <table border="0" cellpadding="3" cellspacing="0" class="t_table1 t_borderless" style="width:100%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="2"><div class="t_caption_title"><?php if ($this->_tpl_vars['comment_id']): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit_comment'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add_comment'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div></td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="2"><input type="text" style="width:100%;" class="txtbox" name="subject" id="subject" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['comment_subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="" onfocus="highlight(this)" onblur="unhighlight(this)" placeholder="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
          </td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="2">
            <a id="error_content"></a>
            <textarea class="areabox doubled higher" name="content" id="content" title="" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['comment_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
            <script type="text/javascript">
              initCommentsCKEditor('<?php echo $this->_tpl_vars['theme']->getEditorSkin(); ?>
');
            </script>
          </td>
        </tr>
        <?php if ($this->_tpl_vars['comment_id']): ?>
        <tr>
          <td style="width:190px;" nowrap="nowrap">
            <label class="labelbox" for="dont_notify_edit"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['dont_notify_edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</label>
          </td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_checkbox.html", 'smarty_include_vars' => array('name' => 'dont_notify_edit','standalone' => true,'option_value' => 'dont_notify_edit','value' => $this->_tpl_vars['dont_notify'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_portal_users_option'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>
          <tr>
            <td style="width:190px;" class="labelbox" nowrap="nowrap"><a name="error_is_portal"><label<?php if ($this->_tpl_vars['messages']->getErrors('is_portal')): ?> class="error"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</label></a></td>
            <td nowrap="nowrap">
              <?php ob_start(); ?>_<?php echo smarty_function_uniqid(array(), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal_suffix', ob_get_contents());ob_end_clean(); ?>
              <input type="radio" name="is_portal" id="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (( ! $this->_tpl_vars['comment_id'] && $this->_tpl_vars['default_portal_comment'] ) || $this->_tpl_vars['comment_is_portal']): ?> checked="checked"<?php endif; ?> /><label for="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
              <input type="radio" name="is_portal" id="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (( $this->_tpl_vars['comment_id'] && ! $this->_tpl_vars['comment_is_portal'] ) || ( ! $this->_tpl_vars['comment_id'] && ! $this->_tpl_vars['default_portal_comment'] )): ?> checked="checked"<?php endif; ?> /><label for="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
            </td>
          </tr>
        <?php endif; ?>
        <tr>
          <td colspan="2">
            <button type="submit" class="button" name="addComment" onclick="send2TexArea();" id="addComment"><?php if ($this->_tpl_vars['comment_id']): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></button><button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() { <?php if ($this->_tpl_vars['inline_add']): ?>lb.deactivate()<?php else: ?>manageCommunicationAddPanels('comment', 'add', <?php echo $this->_tpl_vars['current_model']->get('id'); ?>
)<?php endif; ?>; }, this)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
          </td>
        </tr>
      </table>
    </div>
  </form>