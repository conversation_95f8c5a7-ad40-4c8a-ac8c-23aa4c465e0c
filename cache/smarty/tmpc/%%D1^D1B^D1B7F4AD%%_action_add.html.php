<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:37
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/templates/_action_add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_action_add.html', 7, false),)), $this); ?>
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td colspan="3">
        <table cellpadding="6" cellspacing="0" class="adds_options_box">
          <tr>
            <td width="25%" nowrap="nowrap" class="labelbox">
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['document_transform_operations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:
            </td>
            <td nowrap="nowrap" id="adds_suboptions_row">
            <?php $_from = $this->_tpl_vars['available_actions']['adds']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['adds_operations'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['adds_operations']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['operation_name'] => $this->_tpl_vars['operation']):
        $this->_foreach['adds_operations']['iteration']++;
?>
                <input type="radio" name="operation" id="<?php echo $this->_tpl_vars['operation']['action']; ?>
" value="<?php echo $this->_tpl_vars['operation']['action']; ?>
" onclick="toggleCombinedActionOptions(this)"<?php if (($this->_foreach['adds_operations']['iteration'] <= 1)): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['operation']['action']; ?>
"><?php echo $this->_tpl_vars['operation']['label']; ?>
</label>&nbsp;
            <?php endforeach; else: ?>
                &nbsp;
            <?php endif; unset($_from); ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <?php $_from = $this->_tpl_vars['available_actions']['adds']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['add_options'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['add_options']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['opt_name'] => $this->_tpl_vars['current_option']):
        $this->_foreach['add_options']['iteration']++;
?>
          <table border="0" cellpadding="3" cellspacing="3" id="adds_<?php echo $this->_tpl_vars['current_option']['action']; ?>
_box"<?php if (! ($this->_foreach['add_options']['iteration'] <= 1)): ?> style="display: none;"<?php endif; ?>>
            <?php $_from = $this->_tpl_vars['current_option']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
              <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['option']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['option']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['option']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

              <?php if ($this->_tpl_vars['option']['type']): ?>
                                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['option']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['option'],'standalone' => false,'name' => $this->_tpl_vars['option']['name'],'custom_id' => $this->_tpl_vars['option']['custom_id'],'label' => $this->_tpl_vars['option']['label'],'help' => $this->_tpl_vars['option']['help'],'value' => $this->_tpl_vars['option']['value'],'options' => $this->_tpl_vars['option']['options'],'optgroups' => $this->_tpl_vars['option']['optgroups'],'option_value' => $this->_tpl_vars['option']['option_value'],'first_option_label' => $this->_tpl_vars['option']['first_option_label'],'onclick' => $this->_tpl_vars['option']['onclick'],'on_change' => $this->_tpl_vars['option']['on_change'],'onchange' => $this->_tpl_vars['option']['onchange'],'sequences' => $this->_tpl_vars['option']['sequences'],'check' => $this->_tpl_vars['option']['check'],'scrollable' => $this->_tpl_vars['option']['scrollable'],'readonly' => $this->_tpl_vars['option']['readonly'],'hidden' => $this->_tpl_vars['option']['hidden'],'disallow_date_after' => $this->_tpl_vars['option']['disallow_date_after'],'disallow_date_before' => $this->_tpl_vars['option']['disallow_date_before'],'required' => $this->_tpl_vars['option']['required'],'options_align' => $this->_tpl_vars['option']['options_align'],'disabled' => $this->_tpl_vars['option']['disabled'],'show_placeholder' => $this->_tpl_vars['option']['show_placeholder'],'text_align' => $this->_tpl_vars['option']['text_align'],'custom_class' => $this->_tpl_vars['option']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            <tr>
              <td colspan="3">
                <button type="submit" class="button" name="<?php echo $this->_tpl_vars['current_option']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['current_option']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['current_option']['label']; ?>
"<?php if ($this->_tpl_vars['current_option']['confirm']): ?> onclick="return confirmAction('<?php echo $this->_tpl_vars['current_option']['name']; ?>
', submitForm, this);"<?php endif; ?>><?php echo $this->_tpl_vars['current_option']['label']; ?>
</button>
              </td>
            </tr>
          </table>
        <?php endforeach; endif; unset($_from); ?>
      </td>
    </tr>
  </table>