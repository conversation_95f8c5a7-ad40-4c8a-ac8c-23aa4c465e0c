<?php /* Smarty version 2.6.33, created on 2023-07-13 16:42:48
         compiled from navbar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'navbar.html', 3, false),array('modifier', 'default', 'navbar.html', 10, false),array('modifier', 'regex_replace', 'navbar.html', 10, false),)), $this); ?>
<img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
nav_bar.png" style="vertical-align: middle; margin-bottom:1px" alt="" />
<?php if ($this->_tpl_vars['validLogin']): ?>
  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=index&amp;index=frontend"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu_index_frontend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
<?php else: ?>
  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=auth&amp;auth=login"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu_auth_login'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
<?php endif; ?>
<?php $_from = $this->_tpl_vars['navBar']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['nb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['nb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['item']):
        $this->_foreach['nb']['iteration']++;
?>
  <span class="red">&raquo;</span>
  <?php if (($this->_foreach['nb']['iteration'] == $this->_foreach['nb']['total'])): ?>
    <strong><a href="<?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['item']['url'])) ? $this->_run_mod_handler('default', true, $_tmp, '#') : smarty_modifier_default($_tmp, '#')))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, $this->_tpl_vars['amp_regex'], '&amp;') : smarty_modifier_regex_replace($_tmp, $this->_tpl_vars['amp_regex'], '&amp;')); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['item']['i18n_full'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['item']['i18n']; ?>
</a></strong>
  <?php else: ?>
    <a href="<?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['item']['url'])) ? $this->_run_mod_handler('default', true, $_tmp, '#') : smarty_modifier_default($_tmp, '#')))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, $this->_tpl_vars['amp_regex'], '&amp;') : smarty_modifier_regex_replace($_tmp, $this->_tpl_vars['amp_regex'], '&amp;')); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['item']['i18n_full'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['item']['i18n']; ?>
</a>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
