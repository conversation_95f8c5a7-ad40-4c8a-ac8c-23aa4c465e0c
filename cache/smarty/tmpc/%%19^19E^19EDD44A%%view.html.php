<?php /* Smarty version 2.6.33, created on 2023-07-25 12:37:23
         compiled from /var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 13, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 16, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 16, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 16, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 71, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 151, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/patterns/templates/view.html', 151, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'name'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['pattern']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

          </td>
        </tr>
        <tr>
          <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_patterns_prefix']; ?><?php echo ':'; ?><?php $_from = $this->_tpl_vars['pattern']->get('output_filename_placeholders'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pl'] => $this->_tpl_vars['pl_translation']):
?><?php echo '<br /><strong>['; ?><?php echo $this->_tpl_vars['pl']; ?><?php echo ']</strong> - '; ?><?php echo $this->_tpl_vars['pl_translation']; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('prefix_help', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'prefix','text_content' => $this->_tpl_vars['prefix_help']), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']->get('prefix'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'format'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']->get('format'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'model'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['pattern']->get('model_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'section_type'), $this);?>
</td>
          <td class="required"<?php if ($this->_tpl_vars['pattern']->get('model')): ?> style="visibility: hidden;"<?php endif; ?>><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['pattern']->get('model_type')): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']->get('section_type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 - <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['pattern']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?>-<?php endif; ?>
          </td>
        </tr>
        <?php if ($this->_tpl_vars['pattern']->get('model') == 'Finance_Warehouses_Document' && $this->_tpl_vars['pattern']->get('model_type') == @PH_FINANCE_TYPE_HANDOVER && ! $this->_tpl_vars['pattern']->get('section') && ! $this->_tpl_vars['pattern']->get('list')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'handover_direction'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap"><?php ob_start(); ?>patterns_handover_direction_<?php echo $this->_tpl_vars['pattern']->get('handover_direction'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('hd', ob_get_contents());ob_end_clean(); ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['hd']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <?php endif; ?>
        <tr<?php if (! $this->_tpl_vars['plugins']): ?> style="display: none;"<?php endif; ?> id="pattern_plugins">
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'plugin'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['pattern']->get('plugin')): ?>
              <?php $_from = $this->_tpl_vars['plugins']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['plugin']):
?>
                <?php if ($this->_tpl_vars['pattern']->get('plugin') == $this->_tpl_vars['plugin']['id']): ?>
                  <div class="floatl" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['plugin']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['plugin']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                  <div style="float: left" id="plugins_preview">
                  <?php if ($this->_tpl_vars['plugin']['images']): ?>
                    <?php $_from = $this->_tpl_vars['plugin']['images']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['image_idx'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['image_idx']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['image']):
        $this->_foreach['image_idx']['iteration']++;
?>
                      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" alt="" class="pointer plugin_<?php echo $this->_tpl_vars['plugin']['id']; ?>
_<?php echo $this->_foreach['image_idx']['iteration']; ?>
" title="<?php echo $this->_config[0]['vars']['preview']; ?>
 <?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_plugin'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['plugin']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" onclick="previewPlugin(this, 'pattern')" />
                    <?php endforeach; endif; unset($_from); ?>
                  <?php endif; ?>
                  </div>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'list'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['pattern']->get('list')): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_list2'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_list1'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
          </td>
        </tr>
        <?php if ($this->_tpl_vars['pattern']->get('model') == 'Finance_Incomes_Reason' && in_array ( $this->_tpl_vars['pattern']->get('model_type') , array ( @PH_FINANCE_TYPE_INVOICE , @PH_FINANCE_TYPE_PRO_INVOICE , @PH_FINANCE_TYPE_CREDIT_NOTICE , @PH_FINANCE_TYPE_DEBIT_NOTICE ) ) && ! $this->_tpl_vars['pattern']->get('section') && ! $this->_tpl_vars['pattern']->get('list')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'for_printform'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['pattern']->get('for_printform') == '1'): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?>
          </td>
        </tr>
        <?php endif; ?>
        <?php if (preg_match ( '#^Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Annulment|Payment)$#i' , $this->_tpl_vars['pattern']->get('model') )): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'company'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php endif; ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'position'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']->get('position'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'header'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['pattern']->get('header_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'footer'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['pattern']->get('footer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'landscape'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php if (( $this->_tpl_vars['pattern']->get('landscape') == 0 )): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_type_portrait'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_type_landscape'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'background_image_show'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <a href="<?php echo @PH_PATTERNS_URL; ?>
<?php echo $this->_tpl_vars['pattern']->get('background_image'); ?>
" target="_blank"><?php echo $this->_tpl_vars['pattern']->get('background_image'); ?>
</a>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'background_image_position'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['pattern']->get('background_image_position')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
align_<?php echo $this->_tpl_vars['pattern']->get('background_image_position'); ?>
.png" alt="" /><?php else: ?>&nbsp;<?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'description'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['pattern']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp) : smarty_modifier_mb_wordwrap($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
        <tr<?php if ($this->_tpl_vars['pattern']->get('list')): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'force_generate'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="checkbox" value="1" name="force_generate" id="force_generate" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_force_generate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled="disabled"<?php if ($this->_tpl_vars['pattern']->get('force_generate')): ?> checked="checked"<?php endif; ?> />
          </td>
        </tr>
        <tr<?php if (! $this->_tpl_vars['pattern']->get('model') || ( $this->_tpl_vars['pattern']->get('model') == 'Customer' ) || ( $this->_tpl_vars['pattern']->get('model') == 'Report' ) || $this->_tpl_vars['pattern']->get('list')): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'not_regenerate_finished_record'), $this);?>
</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="checkbox" value="1" name="not_regenerate_finished_record" id="not_regenerate_finished_record" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['patterns_force_generate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled="disabled" class="inactive_checkbox"<?php if ($this->_tpl_vars['pattern']->get('not_regenerate_finished_record')): ?> checked="checked"<?php endif; ?> />
          </td>
        </tr>
        <tr>
          <td class="labelbox" colspan="3"><?php echo smarty_function_help(array('label' => 'content'), $this);?>
</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="3">
            <?php echo $this->_tpl_vars['editor_content']; ?>

          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['pattern'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>

<br />
<br />
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_placeholders_list.html", 'smarty_include_vars' => array('title' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<br />
<?php if (! $this->_tpl_vars['pattern']->get('list')): ?>
<?php if ($this->_tpl_vars['model']->get('model') == 'Document' || $this->_tpl_vars['model']->get('model') == 'Contract' || $this->_tpl_vars['model']->get('model') == 'Customer' || $this->_tpl_vars['model']->get('model') == 'Project'): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_additionalvars_list.html", 'smarty_include_vars' => array('title' => $this->_config[0]['vars']['placeholders_additional_list'],'list' => $this->_tpl_vars['additional_vars']->get('vars'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php elseif (preg_match ( '#Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Annulment)#' , $this->_tpl_vars['pattern']->get('model') )): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_additionalvars_list.html", 'smarty_include_vars' => array('title' => $this->_config[0]['vars']['placeholders_additional_list'],'grouping_table_2' => $this->_tpl_vars['additional_vars']->get('grouping_table_2'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['plugin_vars']): ?>
  <br />
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_placeholders_list.html", 'smarty_include_vars' => array('basic_placeholders' => $this->_tpl_vars['plugin_vars'],'title' => $this->_tpl_vars['plugin_vars_title'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
<?php endif; ?>