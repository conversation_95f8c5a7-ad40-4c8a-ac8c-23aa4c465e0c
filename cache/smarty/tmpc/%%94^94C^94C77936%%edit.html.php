<?php /* Smarty version 2.6.33, created on 2023-07-13 16:50:28
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/edit.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/edit.html', 14, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/edit.html', 47, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/edit.html', 85, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/edit.html', 85, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <form name="nomenclatures_edit" id="nomenclatures_edit" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" enctype="multipart/form-data" onsubmit="return calculateBeforeSubmit(this);">
        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['nomenclature']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."popup_autocomplete_fields.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['nomenclature'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <?php if (empty ( $this->_tpl_vars['available_actions'] )): ?>
          <tr>
            <td class="t_footer"></td>
          </tr>
          <?php endif; ?>
          <tr>
            <td>
              <?php if (empty ( $this->_tpl_vars['available_actions'] )): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layouts_index.html', 'smarty_include_vars' => array('display' => 'abs_div')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php endif; ?>
              <?php $this->assign('layouts_vars', $this->_tpl_vars['nomenclature']->get('vars')); ?>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <?php $_from = $this->_tpl_vars['nomenclature']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

                <?php if ($this->_tpl_vars['layout']['system'] || $this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <tr<?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible'] || $this->_tpl_vars['lkey'] == 'batch_options' && $this->_tpl_vars['nomenclature']->get('subtype') != 'commodity'): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    </div>
                    <div class="layout_switch" <?php if ($this->_tpl_vars['layout']['system']): ?>onclick="toggleViewLayouts(this)" id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch"<?php else: ?>onclick="toggleLayouts(this)" id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_switch"<?php endif; ?>>
                      <a name="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>

                <?php if ($this->_tpl_vars['lkey'] == 'type'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature_type']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <input type="hidden" id="type" name="type" value="<?php echo $this->_tpl_vars['nomenclature_type']->get('id'); ?>
" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'code'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_code"><label for="code"<?php if ($this->_tpl_vars['messages']->getErrors('code')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="code" id="code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="code" id="code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'num'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_num"><label for="num"<?php if ($this->_tpl_vars['messages']->getErrors('num')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <input type="hidden" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" name="num" id="num" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
                      <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>
</span>
                      <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'last_delivery_price'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_last_delivery_price"><label for="last_delivery_price"<?php if ($this->_tpl_vars['messages']->getErrors('last_delivery_price')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox hright" name="last_delivery_price" id="last_delivery_price" style="width: 110px;" onkeypress="return changeKey(this, event, insertOnlyFloats);" value="<?php echo $this->_tpl_vars['nomenclature']->get('last_delivery_price'); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('required' => 1,'standalone' => true,'width' => '80','name' => 'last_delivery_price_currency','options' => $this->_tpl_vars['currencies'],'value' => $this->_tpl_vars['nomenclature']->get('last_delivery_price_currency'),'label' => $this->_config[0]['vars']['nomenclatures_last_delivery_price_currency'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('last_delivery_price'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('last_delivery_price_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="last_delivery_price" id="last_delivery_price" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('last_delivery_price'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                      <input type="hidden" name="last_delivery_price_currency" id="last_delivery_price_currency" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('last_delivery_price_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'average_weighted_delivery_price'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_average_weighted_delivery_price"><label for="average_weighted_delivery_price"<?php if ($this->_tpl_vars['messages']->getErrors('average_weighted_delivery_price')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox hright" name="average_weighted_delivery_price" id="average_weighted_delivery_price" style="width: 110px;" onkeypress="return changeKey(this, event, insertOnlyFloats);" value="<?php echo $this->_tpl_vars['nomenclature']->get('average_weighted_delivery_price'); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('required' => 1,'standalone' => true,'width' => '80','name' => 'average_weighted_delivery_price_currency','options' => $this->_tpl_vars['currencies'],'value' => $this->_tpl_vars['nomenclature']->get('average_weighted_delivery_price_currency'),'label' => $this->_config[0]['vars']['nomenclatures_average_weighted_delivery_price_currency'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('average_weighted_delivery_price'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('average_weighted_delivery_price_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="average_weighted_delivery_price" id="average_weighted_delivery_price" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('average_weighted_delivery_price'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                      <input type="hidden" name="average_weighted_delivery_price_currency" id="average_weighted_delivery_price_currency" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('average_weighted_delivery_price_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'sell_price'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_sell_price"><label for="sell_price"<?php if ($this->_tpl_vars['messages']->getErrors('sell_price')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox hright" name="sell_price" id="sell_price" style="width: 110px;" onkeypress="return changeKey(this, event, insertOnlyFloats);" value="<?php echo $this->_tpl_vars['nomenclature']->get('sell_price'); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('required' => 1,'standalone' => true,'width' => '80','name' => 'sell_price_currency','options' => $this->_tpl_vars['currencies'],'value' => $this->_tpl_vars['nomenclature']->get('sell_price_currency'),'label' => $this->_config[0]['vars']['nomenclatures_sell_price_currency'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('sell_price'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('sell_price_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="sell_price" id="sell_price" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('sell_price'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                      <input type="hidden" name="sell_price_currency" id="sell_price_currency" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['nomenclature']->get('sell_price_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'subtype'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_subtype"><label for="subtype"<?php if ($this->_tpl_vars['messages']->getErrors('subtype')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['nomenclature']->get('disable_batch_edit')): ?>
                      <?php if ($this->_tpl_vars['nomenclature_type']->get('keyword') == 'system'): ?>
                        <input type="radio" name="subtype" id="subtype_advance" value="advance"<?php if ($this->_tpl_vars['nomenclature']->get('subtype') == 'advance' || ! $this->_tpl_vars['nomenclature']->isDefined('subtype') && $this->_tpl_vars['nomenclature_type']->get('subtype') == 'advance'): ?> checked="checked"<?php endif; ?> /><label for="subtype_advance"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_subtype_advance'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                        <input type="radio" name="subtype" id="subtype_discount" value="discount"<?php if ($this->_tpl_vars['nomenclature']->get('subtype') == 'discount' || ! $this->_tpl_vars['nomenclature']->isDefined('subtype') && $this->_tpl_vars['nomenclature_type']->get('subtype') == 'discount'): ?> checked="checked"<?php endif; ?> /><label for="subtype_discount"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_subtype_discount'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                        <input type="radio" name="subtype" id="subtype_surplus" value="surplus"<?php if ($this->_tpl_vars['nomenclature']->get('subtype') == 'surplus' || ! $this->_tpl_vars['nomenclature']->isDefined('subtype') && $this->_tpl_vars['nomenclature_type']->get('subtype') == 'surplus'): ?> checked="checked"<?php endif; ?> /><label for="subtype_surplus"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_subtype_surplus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                      <?php else: ?>
                        <input type="radio" name="subtype" id="subtype_commodity" value="commodity"<?php if ($this->_tpl_vars['nomenclature']->get('subtype') == 'commodity' || ! $this->_tpl_vars['nomenclature']->isDefined('subtype') && $this->_tpl_vars['nomenclature_type']->get('subtype') == 'commodity'): ?> checked="checked"<?php endif; ?> onclick="$('nomenclature_batch_options').style.display = '';" /><label for="subtype_commodity"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_subtype_commodity'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                        <input type="radio" name="subtype" id="subtype_service" value="service"<?php if ($this->_tpl_vars['nomenclature']->get('subtype') == 'service' || ! $this->_tpl_vars['nomenclature']->isDefined('subtype') && $this->_tpl_vars['nomenclature_type']->get('subtype') == 'service'): ?> checked="checked"<?php endif; ?> onclick="$('nomenclature_batch_options').style.display = 'none';" /><label for="subtype_service"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_subtype_service'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                        <input type="radio" name="subtype" id="subtype_other" value="other"<?php if ($this->_tpl_vars['nomenclature']->get('subtype') == 'other' || ! $this->_tpl_vars['nomenclature']->isDefined('subtype') && $this->_tpl_vars['nomenclature_type']->get('subtype') == 'other'): ?> checked="checked"<?php endif; ?> onclick="$('nomenclature_batch_options').style.display = 'none';" /><label for="subtype_other"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_subtype_other'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                      <?php endif; ?>
                    <?php else: ?>
                      <?php ob_start(); ?>nomenclatures_subtype_<?php echo $this->_tpl_vars['nomenclature']->get('subtype'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('subtype_label', ob_get_contents());ob_end_clean(); ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['subtype_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="subtype" id="subtype" value="<?php echo $this->_tpl_vars['nomenclature']->get('subtype'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'batch_options'): ?>
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view'] || $this->_tpl_vars['nomenclature']->get('subtype') != 'commodity'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_batch_options"><label for="has_batch"<?php if ($this->_tpl_vars['messages']->getErrors('has_batch')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit'] && ! $this->_tpl_vars['nomenclature']->get('disable_batch_edit')): ?>
                      <?php $this->assign('bo_hidden', false); ?>
                    <?php else: ?>
                      <?php if ($this->_tpl_vars['nomenclature']->get('has_batch')): ?>
                        <?php echo $this->_config[0]['vars']['nomenclatures_has_batch']; ?>

                        <?php if ($this->_tpl_vars['nomenclature']->get('has_serial')): ?>/ <?php echo $this->_config[0]['vars']['nomenclatures_has_serial']; ?>
<?php endif; ?>
                        <?php if ($this->_tpl_vars['nomenclature']->get('has_expire')): ?>/ <?php echo $this->_config[0]['vars']['nomenclatures_has_expire']; ?>
<?php endif; ?>
                        <?php if ($this->_tpl_vars['nomenclature']->get('has_batch_code')): ?>/ <?php echo $this->_config[0]['vars']['nomenclatures_has_batch_code']; ?>
<?php endif; ?>
                      <?php else: ?>
                        <?php echo $this->_config[0]['vars']['no']; ?>

                      <?php endif; ?>
                      <?php $this->assign('bo_hidden', true); ?>
                    <?php endif; ?>
                    <?php ob_start(); ?>if (this.checked && !$('has_batch').checked) $('has_batch').checked = true;<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('clickf', ob_get_contents());ob_end_clean(); ?>
                    <?php ob_start(); ?>if (!this.checked) {$('has_serial').checked = false;$('has_expire').checked = false;$('has_batch_code').checked = false;}<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('clickf1', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_checkbox.html", 'smarty_include_vars' => array('option_value' => 1,'standalone' => true,'no_br' => true,'value' => $this->_tpl_vars['nomenclature']->get('has_batch'),'label' => $this->_config[0]['vars']['nomenclatures_has_batch'],'name' => 'has_batch','onclick' => $this->_tpl_vars['clickf1'],'hidden' => $this->_tpl_vars['bo_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_checkbox.html", 'smarty_include_vars' => array('option_value' => 1,'standalone' => true,'no_br' => true,'value' => $this->_tpl_vars['nomenclature']->get('has_serial'),'label' => $this->_config[0]['vars']['nomenclatures_has_serial'],'name' => 'has_serial','onclick' => $this->_tpl_vars['clickf'],'hidden' => $this->_tpl_vars['bo_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_checkbox.html", 'smarty_include_vars' => array('option_value' => 1,'standalone' => true,'no_br' => true,'value' => $this->_tpl_vars['nomenclature']->get('has_expire'),'label' => $this->_config[0]['vars']['nomenclatures_has_expire'],'name' => 'has_expire','onclick' => $this->_tpl_vars['clickf'],'hidden' => $this->_tpl_vars['bo_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_checkbox.html", 'smarty_include_vars' => array('option_value' => 1,'standalone' => true,'no_br' => true,'value' => $this->_tpl_vars['nomenclature']->get('has_batch_code'),'label' => $this->_config[0]['vars']['nomenclatures_has_batch_code'],'name' => 'has_batch_code','onclick' => $this->_tpl_vars['clickf'],'hidden' => $this->_tpl_vars['bo_hidden'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <!-- Nomenclature Additional Vars -->
                  <?php $this->assign('layout_id', $this->_tpl_vars['layout']['id']); ?>
                  <?php $this->assign('vars', $this->_tpl_vars['layouts_vars'][$this->_tpl_vars['layout_id']]); ?>
                  <?php if ($this->_tpl_vars['layout']['id']): ?>
                  <tr id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_box"<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                    <td class="nopadding" colspan="3">
                      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                  <?php endif; ?>
                  <?php $_from = $this->_tpl_vars['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
                    <?php if ($this->_tpl_vars['var']['type']): ?>
                      <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['var']['js_filter'] )): ?><?php echo ''; ?><?php $this->assign('restrict', $this->_tpl_vars['var']['js_filter']); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('restrict', ''); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

                      <?php if ($this->_tpl_vars['layout']['edit']): ?>
                                                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'value' => $this->_tpl_vars['var']['value'],'value_id' => $this->_tpl_vars['var']['value_id'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'option_value' => $this->_tpl_vars['var']['option_value'],'first_option_label' => $this->_tpl_vars['var']['first_option_label'],'onclick' => $this->_tpl_vars['var']['onclick'],'on_change' => $this->_tpl_vars['var']['on_change'],'sequences' => $this->_tpl_vars['var']['sequences'],'check' => $this->_tpl_vars['var']['check'],'scrollable' => $this->_tpl_vars['var']['scrollable'],'calculate' => $this->_tpl_vars['var']['calculate'],'readonly' => $this->_tpl_vars['var']['readonly'],'source' => $this->_tpl_vars['var']['source'],'onchange' => $this->_tpl_vars['var']['onchange'],'map_params' => $this->_tpl_vars['var']['map_params'],'width' => $this->_tpl_vars['var']['width'],'hidden' => $this->_tpl_vars['var']['hidden'],'really_required' => $this->_tpl_vars['var']['required'],'required' => $this->_tpl_vars['var']['required'],'disabled' => $this->_tpl_vars['var']['disabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'autocomplete' => $this->_tpl_vars['var']['autocomplete'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['restrict'],'deleteid' => $this->_tpl_vars['var']['deleteid'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php else: ?>
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "view_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php endif; ?>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                  <?php if ($this->_tpl_vars['layout']['id']): ?>
                      </table>
                    </td>
                  </tr>
                <?php endif; ?>
                <?php elseif ($this->_tpl_vars['lkey'] == 'categories'): ?>
                <!-- Nomenclature categories -->
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_categories.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['lkey'] == 'attachments'): ?>
                <!-- Nomenclature attachments -->
                <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="nopadding">
                    <a name="attachments"></a>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_attachments.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3">
                    <?php echo ''; ?><?php if ($this->_tpl_vars['nomenclature']->get('buttons')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['nomenclature']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo '<button type="submit" name="saveButton1" class="button">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>

                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['nomenclature'],'exclude' => 'is_portal')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </form>
      </div>
    </td>
    <?php if (isset ( $this->_tpl_vars['side_panels'] )): ?>
    <td class="side_panel_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_side_panel_options.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."side_panels_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <?php endif; ?>
  </tr>
</table>
<br />
<br />
<?php if (isset ( $this->_tpl_vars['side_panels'] ) && in_array ( 'related_records' , $this->_tpl_vars['side_panels'] ) && $this->_tpl_vars['related_records_modules']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="subpanel_container">
  <tr>
    <td>
      <?php if ($_COOKIE['nomenclatures_selected_related_tab'] && in_array ( $_COOKIE['nomenclatures_selected_related_tab'] , $this->_tpl_vars['related_records_modules'] )): ?>
        <?php $this->assign('rel_type', $_COOKIE['nomenclatures_selected_related_tab']); ?>
      <?php else: ?>
        <?php $this->assign('rel_type', $this->_tpl_vars['related_records_modules']['0']); ?>
      <?php endif; ?>
      <input type="hidden" id="rel_type" name="rel_type" value="<?php echo $this->_tpl_vars['rel_type']; ?>
" />
      <a name="related_subpanel_nomenclature<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
"></a>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."related_records_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <div class="m_header_m_menu scroll_box_container">
        <?php $_from = $this->_tpl_vars['related_records_modules']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['model'] => $this->_tpl_vars['module']):
?>
          <div id="<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
" class="rel_tab<?php if ($this->_tpl_vars['rel_type'] == $this->_tpl_vars['module']): ?> loaded<?php else: ?>" style="display: none;<?php endif; ?>">
            <?php if ($this->_tpl_vars['rel_type'] == $this->_tpl_vars['module']): ?>
              <script type="text/javascript">
                ajaxUpdater({
                  link: '<?php echo $this->_tpl_vars['related'][$this->_tpl_vars['module']]; ?>
',
                  target: '<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
',
                  execute_after: function() { removeClass($('related_records_action_tabs'), 'hidden'); } 
                });
              </script>
            <?php endif; ?>
          </div>
        <?php endforeach; endif; unset($_from); ?>
      </div>
    </td>
  </tr>
</table>
<?php endif; ?>