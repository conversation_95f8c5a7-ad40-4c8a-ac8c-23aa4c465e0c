<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:37
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_select_items.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_select_items.html', 1, false),array('modifier', 'replace', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_select_items.html', 49, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_select_items.html', 66, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_select_items.html', 72, false),)), $this); ?>
<?php echo smarty_function_counter(array('name' => 'checkall_sequence','assign' => 'checkall_number'), $this);?>

<?php if ($this->_tpl_vars['module'] == 'reports'): ?><?php $this->assign('controller', 'reports'); ?><?php endif; ?>
<?php $this->assign('checkall_id', ($this->_tpl_vars['module'])."_".($this->_tpl_vars['controller'])."_".($this->_tpl_vars['action'])."_checkall_".($this->_tpl_vars['checkall_number'])); ?>
<?php $this->assign('checkall_div', ($this->_tpl_vars['module'])."_".($this->_tpl_vars['controller'])."_".($this->_tpl_vars['action'])."_select_".($this->_tpl_vars['checkall_number'])); ?>
<script type="text/javascript">
var t;
</script>
<div id="<?php echo $this->_tpl_vars['checkall_div']; ?>
" 
     class="check_all_menu" 
     style="position: absolute; z-index: 1; display: none;" 
     onmouseout="t=setTimeout('hideSelectMenu(\'<?php echo $this->_tpl_vars['checkall_div']; ?>
\')', 200);"
     onmouseover="clearTimeout(t);">
  <div onclick="manage_checkboxes(params = {
                                            select_what: 'all',
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            total: <?php echo $this->_tpl_vars['total']; ?>
,
                                            button_id: '<?php echo $this->_tpl_vars['checkall_id']; ?>
',
                                            session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
'
                                           });<?php if ($this->_tpl_vars['onclick']): ?><?php echo $this->_tpl_vars['onclick']; ?>
<?php endif; ?>"
       <?php if ($this->_tpl_vars['pages'] < 2 || $this->_tpl_vars['action'] == 'filter' || $this->_tpl_vars['skip_session_ids']): ?>style="display: none;"<?php endif; ?>
       id="check_all_menu_all_items">
    <?php echo $this->_config[0]['vars']['select_all']; ?>

  </div>
  <div onclick="manage_checkboxes(params = {
                                            select_what: 'page',
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            button_id: '<?php echo $this->_tpl_vars['checkall_id']; ?>
',
                                            session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
'
                                           });<?php if ($this->_tpl_vars['onclick']): ?><?php echo $this->_tpl_vars['onclick']; ?>
<?php endif; ?>"
       id="check_all_menu_page_items"
  >
    <?php echo $this->_config[0]['vars']['select_page']; ?>

  </div>
<?php if ($this->_tpl_vars['first_x']): ?>
  <div onclick="manage_checkboxes(params = {
                                              select_what: 'first_<?php echo $this->_tpl_vars['first_x']; ?>
',
                                              module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                              controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                              action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                              button_id: '<?php echo $this->_tpl_vars['checkall_id']; ?>
',
                                              session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
'
                                             });<?php if ($this->_tpl_vars['onclick']): ?><?php echo $this->_tpl_vars['onclick']; ?>
<?php endif; ?>"
       id="check_first_x_items"
  >
    <?php echo ((is_array($_tmp=$this->_config[0]['vars']['select_first_x'])) ? $this->_run_mod_handler('replace', true, $_tmp, '[x]', $this->_tpl_vars['first_x']) : smarty_modifier_replace($_tmp, '[x]', $this->_tpl_vars['first_x'])); ?>

  </div>
<?php endif; ?>
  <div onclick="manage_checkboxes(params = {
                                            select_what: 'none',
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            button_id: '<?php echo $this->_tpl_vars['checkall_id']; ?>
',
                                            session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
'
                                           });<?php if ($this->_tpl_vars['onclick']): ?><?php echo $this->_tpl_vars['onclick']; ?>
<?php endif; ?>"
       id="check_all_menu_none_items"
  >
    <?php echo $this->_config[0]['vars']['select_none']; ?>

  </div>
</div>

<?php if (( $this->_tpl_vars['selected_items']['select_all'] && ! $this->_tpl_vars['selected_items']['ignore_ids'] ) || ( isset ( $this->_tpl_vars['selected_items']['ids'] ) && count($this->_tpl_vars['selected_items']['ids']) == $this->_tpl_vars['total'] && $this->_tpl_vars['total'] != 0 )): ?>
<button class="checkall" 
        id="<?php echo $this->_tpl_vars['checkall_id']; ?>
" 
        name="<?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['action']; ?>
_checkall[]"
        style="background-image: url('<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
checkbox_all.png');" 
        title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
        onclick="manage_checkboxes(params = {
                                              module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                              controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                              action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                              session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
',
                                              button_id: this.id
                                             }); return false;"></button>
<?php elseif ($this->_tpl_vars['selected_items']['ids'] || $this->_tpl_vars['selected_items']['ignore_ids']): ?>
<button class="checkall"
        id="<?php echo $this->_tpl_vars['checkall_id']; ?>
" 
        name="<?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['action']; ?>
_checkall[]"
        style="background-image: url('<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
checkbox_.png');"
        title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
        onclick="manage_checkboxes(params = {
                                              module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                              controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                              action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                              session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
',
                                              button_id: this.id
                                             }); return false;"></button>

<?php else: ?>
<button class="checkall"
        id="<?php echo $this->_tpl_vars['checkall_id']; ?>
"
        name="<?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['action']; ?>
_checkall[]" 
        style="background-image: url('<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
checkbox_none.png');"
        title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
        onclick="manage_checkboxes(params = {
                                              module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                              controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                              action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                              session_param: '<?php echo $this->_tpl_vars['session_param']; ?>
',
                                              button_id: this.id
                                             }); return false;"></button>

<?php endif; ?>