<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:49
         compiled from view_grouping.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'view_grouping.html', 3, false),array('modifier', 'url2href', 'view_grouping.html', 16, false),array('modifier', 'nl2br', 'view_grouping.html', 16, false),array('modifier', 'default', 'view_grouping.html', 16, false),array('modifier', 'date_format', 'view_grouping.html', 23, false),array('modifier', 'encrypt', 'view_grouping.html', 61, false),array('function', 'help', 'view_grouping.html', 6, false),array('function', 'json', 'view_grouping.html', 38, false),array('function', 'getimagesize', 'view_grouping.html', 60, false),)), $this); ?>
<tr<?php if ($this->_tpl_vars['var']['hidden_all']): ?> style="display: none;"<?php endif; ?>><td style="border-style: none;" colspan="3"><div class="t_caption2_title"><?php echo $this->_tpl_vars['var']['label']; ?>
</div>
<table class="t_grouping_table"<?php if ($this->_tpl_vars['var']['t_width']): ?> width="<?php echo $this->_tpl_vars['var']['t_width']; ?>
"<?php endif; ?>><tr>
<th width="29" style="text-align: right;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
<?php $_from = $this->_tpl_vars['var']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['label']):
        $this->_foreach['i']['iteration']++;
?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
<?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?><th <?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]; ?>
"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['info']), $this);?>
<?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></th><?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</tr>
<?php $_from = $this->_tpl_vars['var']['values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['kk'] => $this->_tpl_vars['val']):
        $this->_foreach['i']['iteration']++;
?>
<tr>
<td style="text-align: right;"><?php echo $this->_foreach['i']['iteration']; ?>
</td>
<?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
<?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?>
<td style="<?php if ($this->_tpl_vars['var']['text_align'][$this->_tpl_vars['key']]): ?>text-align: <?php echo $this->_tpl_vars['var']['text_align'][$this->_tpl_vars['key']]; ?>
;<?php endif; ?>">
  <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter'): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'value_id' => $this->_tpl_vars['var']['values_id'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']],'view_mode_url' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']]['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date' && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime' && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time'): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown'): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'map'): ?>
    <script type="text/javascript">
      params_map_<?php echo $this->_tpl_vars['name']; ?>
_<?php echo $this->_foreach['i']['iteration']; ?>
 = <?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))), $this);?>
;
    </script>
    <?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params']['type'] == 'inline'): ?>
      
    <?php else: ?>
      <input
        type="image"
        src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
map.png"
        width="16"
        height="16"
        alt=""
        class="image_map"
        border="0"
        <?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>
        onclick="showMap(this, params_map_<?php echo $this->_tpl_vars['name']; ?>
_<?php echo $this->_foreach['i']['iteration']; ?>
, '<?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
', '<?php echo $this->_tpl_vars['name']; ?>
[<?php echo $this->_foreach['i']['iteration']; ?>
]'); return false;"
      />
    <?php endif; ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?>
    <?php $this->assign('file_info', $this->_tpl_vars['val'][$this->_tpl_vars['key']]); ?>
    <?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?>
      <?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?>
        <?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
          <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

          <img src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']; ?>
<?php endif; ?>" onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '<?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')" alt="" />
        <?php else: ?>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
        <?php endif; ?>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
      <?php endif; ?>
    <?php else: ?>
      &nbsp;
    <?php endif; ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula'): ?>
    <?php if (( is_array ( $this->_tpl_vars['var']['source'] ) && isset ( $this->_tpl_vars['var']['source'][$this->_tpl_vars['name']] ) && preg_match ( '/datetime/' , $this->_tpl_vars['var']['source'][$this->_tpl_vars['name']] ) ) || ( ! is_array ( $this->_tpl_vars['var']['source'] ) && preg_match ( '/datetime/' , $this->_tpl_vars['var']['source'] ) ) && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?>
      <?php echo ((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

    <?php elseif (( is_array ( $this->_tpl_vars['var']['source'] ) && isset ( $this->_tpl_vars['var']['source'][$this->_tpl_vars['name']] ) && preg_match ( '/date/' , $this->_tpl_vars['var']['source'][$this->_tpl_vars['name']] ) ) || ( ! is_array ( $this->_tpl_vars['var']['source'] ) && preg_match ( '/date/' , $this->_tpl_vars['var']['source'] ) ) && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?>
      <?php echo ((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

    <?php else: ?>
      <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

    <?php endif; ?>
    <?php $this->assign('formula_value', $this->_tpl_vars['var']['formula'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']]['value']); ?>
    <?php if ($this->_tpl_vars['formula_value']): ?>
      <?php $_from = $this->_tpl_vars['formulas']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
        <?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['formula_value']): ?>(<?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
)<?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'index'): ?>
    <?php $_from = $this->_tpl_vars['indexes']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
      <?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['val'][$this->_tpl_vars['key']]): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
    <?php $this->assign('index_value', $this->_tpl_vars['var']['index'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']]); ?>
    <?php if ($this->_tpl_vars['index_value']['date'] && $this->_tpl_vars['index_value']['date'] != 0): ?>
      <?php echo ((is_array($_tmp=$this->_tpl_vars['index_value']['date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

    <?php elseif ($this->_tpl_vars['index_value']['datetime'] && $this->_tpl_vars['index_value']['datetime'] != 0): ?>
      <?php echo ((is_array($_tmp=$this->_tpl_vars['index_value']['datetime'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

    <?php elseif ($this->_tpl_vars['index_value']['text']): ?>
      <?php echo $this->_tpl_vars['index_value']['text']; ?>

    <?php endif; ?>
    <?php if ($this->_tpl_vars['index_value']['formula']): ?>
      <?php $_from = $this->_tpl_vars['formulas']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
        <?php if ($this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['index_value']['formula']): ?>(<?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
)<?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
  <?php endif; ?>
  <?php if (( $this->_tpl_vars['val'][$this->_tpl_vars['key']] || $this->_tpl_vars['val'][$this->_tpl_vars['key']] === '0' ) && $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?>
</td>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</tr>
<?php endforeach; endif; unset($_from); ?>
</table>
</td></tr>