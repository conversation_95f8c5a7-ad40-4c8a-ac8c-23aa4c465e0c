<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:37
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 2, false),array('function', 'array', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 66, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 101, false),array('function', 'uniqid', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 108, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 4, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 15, false),array('modifier', 'replace', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/multiple_actions_list.html', 67, false),)), $this); ?>
<?php echo ''; ?><?php echo smarty_function_counter(array('name' => 'stat_items_sequence1','assign' => 'stat_items_sequence'), $this);?><?php echo ''; ?><?php if (! $this->_tpl_vars['pagination']): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_displayed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':<strong><script type="text/javascript">document.write(\'<span id="totalItemsFound_'; ?><?php echo $this->_tpl_vars['stat_items_sequence']; ?><?php echo '">\');document.write(document.getElementsByName(\'items[]\').length);document.write(\'</span>\');</script></strong> /'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['count_selected_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':<span id="selectedItemsCount_'; ?><?php echo $this->_tpl_vars['stat_items_sequence']; ?><?php echo '">'; ?><?php if ($this->_tpl_vars['selected_items']['ids']): ?><?php echo ''; ?><?php echo count($this->_tpl_vars['selected_items']['ids']); ?><?php echo ''; ?><?php else: ?><?php echo '0'; ?><?php endif; ?><?php echo '</span><br /><br />'; ?><?php endif; ?><?php echo '<input type="hidden" name="session_param" value="'; ?><?php echo $this->_tpl_vars['session_param']; ?><?php echo '" /><input type="hidden" name="after_action" value="'; ?><?php echo $this->_tpl_vars['action']; ?><?php echo '" />'; ?><?php if ($this->_tpl_vars['exclude'] != 'all'): ?><?php echo '<div class="multi_action_tabs">'; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?><?php echo ', \''; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '\''; ?><?php else: ?><?php echo ', \'\''; ?><?php endif; ?><?php echo ', '; ?><?php echo $this->_tpl_vars['stat_items_sequence']; ?><?php echo ''; ?><?php if ($this->_tpl_vars['skip_session_ids']): ?><?php echo ', \'\', 1'; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('additional_str', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['controller'] && ( $this->_tpl_vars['module'] != $this->_tpl_vars['controller'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '_'; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('module_check', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#activate#' , $this->_tpl_vars['exclude'] )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'activate')): ?><?php echo '<button type="submit" name="activateButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'activate\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['activate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#deactivate#' , $this->_tpl_vars['exclude'] )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'deactivate')): ?><?php echo '<button type="submit" name="deactivateButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'deactivate\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#delete#' , $this->_tpl_vars['exclude'] )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'delete')): ?><?php echo '<button type="submit" name="deleteButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'delete\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['action'] == 'search' && ( ! $this->_tpl_vars['exclude'] || ! preg_match ( '#restore#' , $this->_tpl_vars['exclude'] ) ) ) || preg_match ( '#restore#' , $this->_tpl_vars['include'] )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'restore')): ?><?php echo '<button type="submit" name="restoreButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'restore\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['restore'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['action'] == 'search' && ( ! $this->_tpl_vars['exclude'] || ! preg_match ( '#purge#' , $this->_tpl_vars['exclude'] ) ) ) && preg_match ( '#purge#' , $this->_tpl_vars['include'] )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'purge')): ?><?php echo '<button type="submit" name="purgeButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'purge\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['purge'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#multiedit#' , $this->_tpl_vars['exclude'] )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'multiedit')): ?><?php echo '<button type="submit" name="editButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'multiedit\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['multiedit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (preg_match ( '#tags#' , $this->_tpl_vars['include'] ) && ( ! empty ( $this->_tpl_vars['tags_options'] ) || ! empty ( $this->_tpl_vars['tags_optgroups'] ) ) && $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'tags_view') && $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'tags_edit')): ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'if ($(\'tagsSelect\').value != \'\') '; ?>{<?php echo ' return confirmation($(\'tagsSelect\'), \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'multitag\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ') '; ?>}<?php echo ' else '; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_tags'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); $(\'tagsSelect\').focus(); return false;'; ?>}<?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('tags_onclick', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo smarty_function_array(array('assign' => 'multitag_options','name' => 'multitag','label' => $this->_config[0]['vars']['add'],'help' => $this->_config[0]['vars']['add'],'img' => 'multitag','url' => 'javascript: void(0);','onclick' => $this->_tpl_vars['tags_onclick']), $this);?><?php echo ''; ?><?php echo smarty_function_array(array('assign' => 'multiremovetag_options','name' => 'multiremovetag','label' => $this->_config[0]['vars']['remove'],'help' => $this->_config[0]['vars']['remove'],'img' => 'multiremovetag','url' => 'javascript: void(0);','onclick' => ((is_array($_tmp=$this->_tpl_vars['tags_onclick'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'multitag', 'multiremovetag') : smarty_modifier_replace($_tmp, 'multitag', 'multiremovetag'))), $this);?><?php echo ''; ?><?php echo smarty_function_array(array('assign' => 'multitag_action_options','1' => $this->_tpl_vars['multitag_options'],'2' => $this->_tpl_vars['multiremovetag_options']), $this);?><?php echo ''; ?><?php echo smarty_function_array(array('assign' => 'multitag_action_options','label' => ((is_array($_tmp=$this->_config[0]['vars']['tags'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'name' => 'multitag','no_tab' => true,'button' => true,'no_img' => true,'options' => $this->_tpl_vars['multitag_action_options']), $this);?><?php echo '<ul id="multitag_actions" class="zpHideOnLoad hidden">'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_drop_menu_item.html", 'smarty_include_vars' => array('action_options' => $this->_tpl_vars['multitag_action_options'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo '</ul><script type="text/javascript">new Zapatec.Menu('; ?>{<?php echo 'source: \'multitag_actions\',hideDelay: 100,theme: \'nzoom\''; ?>}<?php echo ');</script>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'tagsSelect','id' => 'tagsSelect','custom_class' => 'small','label' => $this->_config[0]['vars']['tags'],'first_option_label' => $this->_config[0]['vars']['select'],'options' => $this->_tpl_vars['tags_options'],'optgroups' => $this->_tpl_vars['tags_optgroups'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (preg_match ( '#multistatus#' , $this->_tpl_vars['include'] ) && $this->_tpl_vars['statuses']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'multistatus')): ?><?php echo '<button type="submit" name="multistatusButton" class="button" onclick="if ($(\'multistatusSelect\').value != \'\') '; ?>{<?php echo ' if (checkMultistatusRequiredComment(this)) '; ?>{<?php echo ' return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'multistatus\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ') '; ?>}<?php echo ' else '; ?>{<?php echo ' return false; '; ?>}<?php echo ' '; ?>}<?php echo ' else '; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_multistatus'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><select name="multistatusSelect" id="multistatusSelect" class="selbox small undefined" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); showHideMultistatusCommentField(this);" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '"><option class="undefined" value="">['; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ']</option>'; ?><?php $_from = $this->_tpl_vars['statuses']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['status']):
?><?php echo '<option value="'; ?><?php echo $this->_tpl_vars['status']['id']; ?><?php echo '" class="'; ?><?php echo $this->_tpl_vars['status']['requires_comment']; ?><?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['status']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</option>'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</select><table id="multistatus_available_comment_table" style="display: none;"><tr><td class="required" id="multistatus_required_comment" rowspan="2" style="visibility: hidden;">'; ?><?php echo $this->_config[0]['vars']['required']; ?><?php echo '<input type="hidden" name="multistatus_requires_comment" id="multistatus_requires_comment" value="0" /></td><td class="labelbox"><label for="multistatus_comment">'; ?><?php echo smarty_function_help(array('label' => 'comment'), $this);?><?php echo '</label></td></tr><tr><td><textarea class="areabox" name="multistatus_comment" id="multistatus_comment" onfocus="highlight(this)" onblur="unhighlight(this)" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['comment'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '"></textarea>'; ?><?php if ($this->_tpl_vars['include_portal_users_option'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?><?php echo '<br />'; ?><?php ob_start(); ?><?php echo '_'; ?><?php echo smarty_function_uniqid(array(), $this);?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal_suffix', ob_get_contents());ob_end_clean(); ?><?php echo '<input type="radio" name="is_portal" id="is_portal1'; ?><?php echo $this->_tpl_vars['is_portal_suffix']; ?><?php echo '" value="1" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onfocus="highlight(this)" onblur="unhighlight(this)"'; ?><?php if (! isset ( $this->_tpl_vars['default_portal_comment'] ) || $this->_tpl_vars['default_portal_comment']): ?><?php echo ' checked="checked"'; ?><?php endif; ?><?php echo ' /><label for="is_portal1'; ?><?php echo $this->_tpl_vars['is_portal_suffix']; ?><?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</label><input type="radio" name="is_portal" id="is_portal2'; ?><?php echo $this->_tpl_vars['is_portal_suffix']; ?><?php echo '" value="0" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onfocus="highlight(this)" onblur="unhighlight(this)"'; ?><?php if (isset ( $this->_tpl_vars['default_portal_comment'] ) && ! $this->_tpl_vars['default_portal_comment']): ?><?php echo ' checked="checked"'; ?><?php endif; ?><?php echo ' /><label for="is_portal2'; ?><?php echo $this->_tpl_vars['is_portal_suffix']; ?><?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</label>'; ?><?php endif; ?><?php echo '</td></tr></table>&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (preg_match ( '#multiprint#' , $this->_tpl_vars['include'] ) && $this->_tpl_vars['patterns_grouped']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'multiprint')): ?><?php echo '<button type="submit" name="multiprintButton" id="multiprintButton" class="button" value="multiprint" onclick="if ($(\'pattern\').value != \'\') '; ?>{<?php echo ' return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'multiprint\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ') '; ?>}<?php echo ' else '; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_multiprint'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['print'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $this->assign('one_pattern', ''); ?><?php echo ''; ?><?php if (count($this->_tpl_vars['patterns_grouped']) == 1): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['patterns_grouped']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pgi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pgi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['pgk'] => $this->_tpl_vars['patterns']):
        $this->_foreach['pgi']['iteration']++;
?><?php echo ''; ?><?php if (count($this->_tpl_vars['patterns']) == 1): ?><?php echo ''; ?><?php $this->assign('one_pattern', $this->_tpl_vars['patterns'][0]['id']); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['one_pattern']): ?><?php echo '<input type="hidden" name="pattern" id="pattern" value="'; ?><?php echo $this->_tpl_vars['one_pattern']; ?><?php echo '" />'; ?><?php else: ?><?php echo '<select name="pattern" id="pattern" class="selbox small undefined" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_pattern'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '"><option class="undefined" value="">['; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ']</option>'; ?><?php $_from = $this->_tpl_vars['patterns_grouped']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pgi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pgi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['pgk'] => $this->_tpl_vars['patterns']):
        $this->_foreach['pgi']['iteration']++;
?><?php echo ''; ?><?php if ($this->_tpl_vars['pgk']): ?><?php echo '<optgroup label="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['pgk'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '">'; ?><?php endif; ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['patterns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pattern']):
?><?php echo '<option value="'; ?><?php echo $this->_tpl_vars['pattern']['id']; ?><?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['pattern']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</option>'; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php if ($this->_tpl_vars['pgk']): ?><?php echo '</optgroup>'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo '</select>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['module_check'] == 'finance_expenses_reasons' && preg_match ( '#multiaddinvoice#' , $this->_tpl_vars['include'] ) && $this->_tpl_vars['expense_proformas']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['module_check']; ?><?php echo ''; ?><?php echo @PH_FINANCE_TYPE_EXPENSES_INVOICE; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('module_type_check', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_type_check'],'add')): ?><?php echo '<button type="submit" name="multiaddinvoiceButton" id="multiaddinvoiceButton" class="button" value="multiaddinvoice" onclick="addMergedExpensesInvoice(this); return false;">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_expenses_reasons_addinvoice'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['module_check'] == 'contracts' && preg_match ( '#change_templates_observer#' , $this->_tpl_vars['include'] ) && $this->_tpl_vars['currentUser']->checkRights('contracts','editfinance')): ?><?php echo '<button type="submit" name="changeTemplatesObserverButton" id="changeTemplatesObserverButton" class="button" value="change_templates_observer" onclick="changeTemplatesObserver(this, $(\'templates_observer\')); return false;">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_change_templates_observer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'templates_observer','standalone' => true,'options' => $this->_tpl_vars['users_options'],'label' => $this->_config[0]['vars']['contracts_invoices_templates_observer'],'custom_class' => 'short')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php echo ''; ?><?php if ($this->_tpl_vars['locking_records'] && $this->_tpl_vars['module'] == 'users' && ( ! $this->_tpl_vars['exclude'] || ! preg_match ( '#unlock#' , $this->_tpl_vars['exclude'] ) )): ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_check'],'unlock')): ?><?php echo '<button type="submit" name="unlockButton" class="button" onclick="return confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'unlock\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['unlock'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['module'] == 'layouts' && $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module_type_check'],'edit')): ?><?php echo ''; ?><?php if (preg_match ( '#multiassignpermissions#' , $this->_tpl_vars['include'] )): ?><?php echo '<button type="submit" name="multiAssignPermissionsButton" id="multiAssignPermissionsButton" class="button" value="multiassignpermissions" onclick="launchMultiAssignPermissions(this); return false;">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['layouts_multi_assign_permissions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['allow_layouts_order']): ?><?php echo '<button type="submit" name="saveOrderButton" id="saveOrderButton" class="button" value="saveorder" onclick="return layoutsOrderSave() && confirmation(this, \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \'saveorder\''; ?><?php echo $this->_tpl_vars['additional_str']; ?><?php echo ', 1);">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['layouts_saveorder'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?>
