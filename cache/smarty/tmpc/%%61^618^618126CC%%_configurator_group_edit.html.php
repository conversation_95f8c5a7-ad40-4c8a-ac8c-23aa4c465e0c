<?php /* Smarty version 2.6.33, created on 2023-07-13 16:44:05
         compiled from _configurator_group_edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_configurator_group_edit.html', 6, false),array('modifier', 'default', '_configurator_group_edit.html', 8, false),array('modifier', 'count', '_configurator_group_edit.html', 18, false),array('modifier', 'replace', '_configurator_group_edit.html', 43, false),array('function', 'help', '_configurator_group_edit.html', 16, false),)), $this); ?>
<?php if (trim ( $this->_tpl_vars['var']['label'] )): ?><div class="t_caption2_title"><?php echo $this->_tpl_vars['var']['label']; ?>
</div><?php endif; ?>

<table id="var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
" class="t_grouping_table<?php if ($this->_tpl_vars['var']['dont_copy_values']): ?> dont_copy_values<?php endif; ?><?php if ($this->_tpl_vars['var']['t_custom_class']): ?> <?php echo $this->_tpl_vars['var']['t_custom_class']; ?>
<?php endif; ?>"<?php if ($this->_tpl_vars['var']['t_width']): ?> width="<?php echo $this->_tpl_vars['var']['t_width']; ?>
"<?php endif; ?>>
<tr>
  <th style="text-align: right;">
    <div style="width: 29px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
        <input type="hidden" name="max_num_<?php echo $this->_tpl_vars['var']['grouping']; ?>
" id="max_num_<?php echo $this->_tpl_vars['var']['grouping']; ?>
" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['max_num'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" disabled="disabled" />
  </th>
<?php $_from = $this->_tpl_vars['var']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['label']):
?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
  <th<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> style="display: none;"<?php endif; ?>>
    <?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?>
      <?php if ($this->_tpl_vars['var']['last_visible_column'] == $this->_tpl_vars['key']): ?>
        <div class="floatl">
          <a name="error_<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
"></a><label for="<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
_1"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['var']['names'][$this->_tpl_vars['key']])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['info']), $this);?>
<?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></label>
        </div>
        <?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['var']['values'] ) && is_array ( $this->_tpl_vars['var']['values'] )): ?><?php echo count($this->_tpl_vars['var']['values']); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('values_count', ob_get_contents());ob_end_clean(); ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_table_buttons.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'values_count' => $this->_tpl_vars['values_count'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php if (! empty ( $this->_tpl_vars['var']['floating_buttons'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_table_buttons.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'values_count' => $this->_tpl_vars['values_count'],'floating_buttons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      <?php else: ?>
        <div style="<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]): ?>width: <?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]; ?>
px;<?php endif; ?>">
          <a name="error_<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
"></a><label for="<?php echo $this->_tpl_vars['var']['names'][$this->_tpl_vars['key']]; ?>
_1"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['var']['names'][$this->_tpl_vars['key']])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['info']), $this);?>
<?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></label>
        </div>
      <?php endif; ?>
    <?php endif; ?>
  </th>
<?php endforeach; endif; unset($_from); ?>
</tr>
<?php $_from = $this->_tpl_vars['var']['values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['kk'] => $this->_tpl_vars['val']):
        $this->_foreach['i']['iteration']++;
?>
<tr id="var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
_<?php echo $this->_foreach['i']['iteration']; ?>
"<?php if ($this->_tpl_vars['var']['floating_buttons']): ?> onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"<?php endif; ?>>
  <td align="right" nowrap="nowrap">
    <?php if ($this->_tpl_vars['var']['readonly_all'] && $this->_tpl_vars['var']['hide_multiple_rows_buttons']): ?>
    <span><?php echo $this->_foreach['i']['iteration']; ?>
</span>
    <?php else: ?>
    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (empty ( $this->_tpl_vars['var']['values'] ) || count($this->_tpl_vars['var']['values']) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function(el) { hideField('var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
','<?php echo $this->_foreach['i']['iteration']; ?>
')<?php if ($this->_tpl_vars['var']['upon_table_rows_update']): ?>;<?php echo ((is_array($_tmp=$this->_tpl_vars['var']['upon_table_rows_update'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'this', 'el') : smarty_modifier_replace($_tmp, 'this', 'el')); ?>
<?php endif; ?>; }, this);" />&nbsp;<span class="group_table_tow_num" onclick="disableField('var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
','<?php echo $this->_foreach['i']['iteration']; ?>
')<?php if ($this->_tpl_vars['var']['upon_table_rows_update']): ?>;<?php echo $this->_tpl_vars['var']['upon_table_rows_update']; ?>
<?php endif; ?>"><?php echo $this->_foreach['i']['iteration']; ?>
</span>
    <?php endif; ?>
  </td>
  <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
        $this->_foreach['j']['iteration']++;
?>
  <td<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> style="display: none;"<?php endif; ?><?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'index'): ?> nowrap="nowrap"<?php endif; ?>>
  <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula'): ?>
    <?php $this->assign('formula_value', $this->_tpl_vars['var']['formula'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']]['value']); ?>
  <?php else: ?>
    <?php $this->assign('formula_value', $this->_tpl_vars['var']['index'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']]['formula']); ?>
  <?php endif; ?>
  <?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']][$this->_tpl_vars['kk']]['options'] )): ?>
    <?php $this->assign('options', $this->_tpl_vars['var'][$this->_tpl_vars['name']][$this->_tpl_vars['kk']]['options']); ?>
  <?php else: ?>
    <?php $this->assign('options', $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']); ?>
  <?php endif; ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']]).".html", 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'index' => $this->_foreach['i']['iteration'],'name_index' => $this->_tpl_vars['kk'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'value_id' => $this->_tpl_vars['var']['values_id'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']],'label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'description' => $this->_tpl_vars['var']['descriptions'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var']['back_label_styles'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'calculate' => $this->_tpl_vars['var']['calculate'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'autocomplete' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'origin' => 'group','options' => $this->_tpl_vars['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'onchange' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['onchange'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'really_required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'map_params' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params'],'view_mode' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'],'thumb_width' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width'],'thumb_height' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height'],'width' => $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'formulas' => $this->_tpl_vars['formulas'],'formula_value' => $this->_tpl_vars['formula_value'],'source' => $this->_tpl_vars['var']['formula'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']]['source'],'indexes' => $this->_tpl_vars['indexes'],'date_value' => $this->_tpl_vars['var']['index'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']]['date'],'do_not_show_check_all_button' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['do_not_show_check_all_button'],'do_not_show_check_none_button' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['do_not_show_check_none_button'],'first_option_label' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['first_option_label'],'options_align' => $this->_tpl_vars['var']['options_align'][$this->_tpl_vars['key']],'deleteid' => $this->_tpl_vars['var']['deleteids'][$this->_tpl_vars['kk']][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var']['custom_class'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
  <?php endforeach; endif; unset($_from); ?>
</tr>
<?php endforeach; else: ?>
<tr id="var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
_1"<?php if ($this->_tpl_vars['var']['floating_buttons']): ?> onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"<?php endif; ?>>
  <td align="right" nowrap="nowrap">
    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function(el) { hideField('var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
','1')<?php if ($this->_tpl_vars['var']['upon_table_rows_update']): ?>;<?php echo ((is_array($_tmp=$this->_tpl_vars['var']['upon_table_rows_update'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'this', 'el') : smarty_modifier_replace($_tmp, 'this', 'el')); ?>
<?php endif; ?>; }, this);" />&nbsp;<span class="group_table_tow_num" onclick="disableField('var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
','1')<?php if ($this->_tpl_vars['var']['upon_table_rows_update']): ?>;<?php echo $this->_tpl_vars['var']['upon_table_rows_update']; ?>
<?php endif; ?>">1</span>
  </td>
  <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
  <td<?php if ($this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?> style="display: none;"<?php endif; ?><?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'index'): ?> nowrap="nowrap"<?php endif; ?>>
  <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula'): ?>
    <?php $this->assign('formula_value', $this->_tpl_vars['var']['formula']['1'][$this->_tpl_vars['key']]['value']); ?>
  <?php else: ?>
    <?php $this->assign('formula_value', $this->_tpl_vars['var']['index']['1'][$this->_tpl_vars['key']]['formula']); ?>
  <?php endif; ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']]).".html", 'smarty_include_vars' => array('standalone' => true,'name' => $this->_tpl_vars['name'],'index' => 1,'name_index' => 1,'value' => '','value_id' => '','label' => $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']],'help' => $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']],'description' => $this->_tpl_vars['var']['descriptions'][$this->_tpl_vars['key']],'back_label' => $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']],'back_label_style' => $this->_tpl_vars['var']['back_label_styles'][$this->_tpl_vars['key']],'readonly' => $this->_tpl_vars['var']['readonly'][$this->_tpl_vars['key']],'calculate' => $this->_tpl_vars['var']['calculate'][$this->_tpl_vars['key']],'hidden' => $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']],'autocomplete' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']],'js_methods' => $this->_tpl_vars['var']['js_methods'][$this->_tpl_vars['key']],'restrict' => $this->_tpl_vars['var']['js_filters'][$this->_tpl_vars['key']],'origin' => 'group','options' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options'],'optgroups' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups'],'on_change' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['on_change'],'onchange' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['onchange'],'sequences' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['sequences'],'really_required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'required' => $this->_tpl_vars['var']['required'][$this->_tpl_vars['key']],'map_params' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['map_params'],'view_mode' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'],'thumb_width' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width'],'thumb_height' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height'],'width' => $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']],'height' => $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']],'formulas' => $this->_tpl_vars['formulas'],'formula_value' => $this->_tpl_vars['formula_value'],'source' => $this->_tpl_vars['var']['formula']['1'][$this->_tpl_vars['key']]['source'],'indexes' => $this->_tpl_vars['indexes'],'date_value' => $this->_tpl_vars['var']['index']['1'][$this->_tpl_vars['key']]['date'],'do_not_show_check_all_button' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['do_not_show_check_all_button'],'do_not_show_check_none_button' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['do_not_show_check_none_button'],'first_option_label' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['first_option_label'],'options_align' => $this->_tpl_vars['var']['options_align'][$this->_tpl_vars['key']],'show_placeholder' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'][$this->_tpl_vars['key']],'custom_class' => $this->_tpl_vars['var']['custom_class'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
  <?php endforeach; endif; unset($_from); ?>
</tr>
<?php endif; unset($_from); ?>
</table>