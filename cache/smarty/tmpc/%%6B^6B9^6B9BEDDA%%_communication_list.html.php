<?php /* Smarty version 2.6.33, created on 2023-07-25 12:08:39
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 40, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 79, false),array('modifier', 'strip_tags', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 117, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 117, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 12, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 14, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 86, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_communication_list.html', 121, false),)), $this); ?>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border" style="width: 100%;">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap" style="width: 20px"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <?php if (! $this->_tpl_vars['custom_filters'] || $this->_tpl_vars['custom_filters']['view_mails'] == 'all'): ?>
        <td class="t_caption t_border <?php echo $this->_tpl_vars['communications_sort']['from']['class']; ?>
" nowrap="nowrap" style="width: 100px"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['communications_sort']['from']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <?php endif; ?>
      <td class="t_caption t_border <?php echo $this->_tpl_vars['communications_sort']['to']['class']; ?>
" nowrap="nowrap" style="width: 140px"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['communications_sort']['to']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['to'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_caption t_border <?php echo $this->_tpl_vars['communications_sort']['content']['class']; ?>
"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['communications_sort']['content']['link']; ?>
" style="width: 300px"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['content_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_caption t_border <?php echo $this->_tpl_vars['communications_sort']['added']['class']; ?>
" nowrap="nowrap" style="width: 100px"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['communications_sort']['added']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <td class="t_caption" style="width: 14px">&nbsp;</td>
    </tr>
    <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

    <?php $_from = $this->_tpl_vars['communications']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['communication']):
        $this->_foreach['i']['iteration']++;
?>
      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop" onclick="expandCommunication($(this).select('td.content')[0]);">
        <td class="t_border hright" nowrap="nowrap">
          <?php if (( $this->_tpl_vars['communication']['attachments'] )): ?>
            <?php if ($this->_tpl_vars['communication']['attachments_link']): ?>
              <a href="<?php echo $this->_tpl_vars['communication']['attachments_link']; ?>
">
                <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                     onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['communication']['module']; ?>
', '<?php echo $this->_tpl_vars['communication']['controller']; ?>
', <?php echo $this->_tpl_vars['communication']['model_id']; ?>
, '<?php echo $this->_tpl_vars['communication']['attachments']; ?>
')"
                     onmouseout="mclosetime()" />
              </a>
            <?php else: ?>
              <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" width="16" height="16" alt="" style="vertical-align: middle;" />
            <?php endif; ?>
          <?php endif; ?>
          <?php if (( $this->_tpl_vars['communication']['type'] == 'comment' )): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
comments.png" width="16" height="16" alt="<?php echo $this->_config[0]['vars']['communications_comment']; ?>
" title="<?php echo $this->_config[0]['vars']['communications_comment']; ?>
" border="0" style="vertical-align: middle;" />
          <?php else: ?>
            <?php if ($this->_tpl_vars['communication']['status'] == 'received'): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
receive_email.png" width="16" height="16" alt="<?php echo $this->_config[0]['vars']['communications_email_received']; ?>
" title="<?php echo $this->_config[0]['vars']['communications_email_received']; ?>
" border="0" style="vertical-align: middle;" />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
send_email.png" width="16" height="16" alt="<?php echo $this->_config[0]['vars']['communications_email_sent']; ?>
" title="<?php echo $this->_config[0]['vars']['communications_email_sent']; ?>
" border="0" style="vertical-align: middle;" />
            <?php endif; ?>
          <?php endif; ?>
          &nbsp;<?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

        </td>
        <?php if (! $this->_tpl_vars['custom_filters'] || $this->_tpl_vars['custom_filters']['view_mails'] == 'all'): ?>
          <td class="t_border  <?php echo $this->_tpl_vars['communications_sort']['from']['isSorted']; ?>
">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        <?php endif; ?>
        <td class="t_border <?php echo $this->_tpl_vars['communications_sort']['to']['isSorted']; ?>
 communicationdatefield" >
          <?php $_from = $this->_tpl_vars['communication']['to']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['c'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['c']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['to_user']):
        $this->_foreach['c']['iteration']++;
?>
            <?php if ($this->_tpl_vars['to_user']['mail_status'] == 'sent' && $this->_tpl_vars['to_user']['receive_status'] == 'received'): ?>
              <?php $this->assign('name_color', '000000'); ?>
              <?php $this->assign('name_message', 'email_sent_received_comment_reason'); ?>
            <?php elseif ($this->_tpl_vars['to_user']['mail_status'] == 'received'): ?>
              <?php $this->assign('name_color', '006600'); ?>
              <?php $this->assign('name_message', 'email_received_comment_reason'); ?>
            <?php else: ?>
              <?php $this->assign('name_color', 'FF0000'); ?>
              <?php if ($this->_tpl_vars['to_user']['receive_status'] == 'not_received'): ?>
                <?php $this->assign('name_message', 'email_sent_not_received_comment_reason'); ?>
              <?php elseif ($this->_tpl_vars['to_user']['mail_status'] != 'sent'): ?>
                <?php ob_start(); ?>email_not_sent_<?php echo $this->_tpl_vars['to_user']['mail_status']; ?>
_reason<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('name_message', ob_get_contents());ob_end_clean(); ?>
              <?php endif; ?>
            <?php endif; ?>

            <?php ob_start(); ?>
              <table border="0" cellpadding="3" cellspacing="0" class="t_table t_table_border" width="100%;">
                <?php $_from = $this->_tpl_vars['to_user']['copies_info']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ci'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ci']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['copy_info']):
        $this->_foreach['ci']['iteration']++;
?>
                  <?php if ($this->_tpl_vars['copy_info']['mail_status'] == 'sent' && $this->_tpl_vars['copy_info']['receive_status'] == 'received'): ?>
                    <?php $this->assign('current_mail_icon', 'small/check_yes.png'); ?>
                    <?php $this->assign('current_mail_message', 'email_sent_received_comment_reason'); ?>
                  <?php elseif ($this->_tpl_vars['copy_info']['mail_status'] == 'received'): ?>
                    <?php $this->assign('current_mail_icon', 'warning.png'); ?>
                    <?php $this->assign('current_mail_message', 'email_received_comment_reason'); ?>
                  <?php else: ?>
                    <?php $this->assign('current_mail_icon', 'small/delete.png'); ?>
                    <?php if ($this->_tpl_vars['copy_info']['receive_status'] == 'not_received'): ?>
                      <?php $this->assign('current_mail_message', 'email_sent_not_received_comment_reason'); ?>
                    <?php elseif ($this->_tpl_vars['copy_info']['mail_status'] != 'sent'): ?>
                      <?php ob_start(); ?>email_not_sent_<?php echo $this->_tpl_vars['copy_info']['mail_status']; ?>
_reason<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_mail_message', ob_get_contents());ob_end_clean(); ?>
                    <?php endif; ?>
                  <?php endif; ?>
                  <?php ob_start(); ?>info_box_cycle_<?php echo ($this->_foreach['i']['iteration']-1); ?>
_<?php echo ($this->_foreach['c']['iteration']-1); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('cycle_name', ob_get_contents());ob_end_clean(); ?>
                  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even','name' => $this->_tpl_vars['cycle_name']), $this);?>
">
                    <td class="t_border" style="width: 60px;"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['copy_info']['sent'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                    <td><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['current_mail_icon']; ?>
" width="12" height="12" border="0" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['current_mail_message']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                  </tr>
                <?php endforeach; endif; unset($_from); ?>
              </table>
            <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info_sent_mails', ob_get_contents());ob_end_clean(); ?>

            <span style="color: #<?php echo $this->_tpl_vars['name_color']; ?>
; cursor: pointer;" <?php echo smarty_function_popup(array('caption' => ((is_array($_tmp=$this->_config[0]['vars']['mail_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text' => ((is_array($_tmp=$this->_tpl_vars['info_sent_mails'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
>
              <?php if ($this->_tpl_vars['communication']['type'] == 'comment'): ?>
                <?php if ($this->_tpl_vars['to_user']['name']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['to_user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php else: ?>&#60;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['to_user']['email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#62;<?php endif; ?>
              <?php else: ?>
                <?php if ($this->_tpl_vars['to_user']['name']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['to_user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
 <?php endif; ?><?php if ($this->_tpl_vars['to_user']['email']): ?>&#60;<?php echo ((is_array($_tmp=$this->_tpl_vars['to_user']['email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&#62;<?php endif; ?>
              <?php endif; ?>
            </span><?php if (! ($this->_foreach['c']['iteration'] == $this->_foreach['c']['total'])): ?><br /><?php endif; ?>
          <?php endforeach; else: ?>
            &nbsp;
          <?php endif; unset($_from); ?>
        </td>
        <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['communication']['type'] == 'comment'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['full_comment']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['full_email']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('full_communication', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['communication']['type'] == 'comment'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['part_comment']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['part_email']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('part_communication', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/full_comment.png" width="12" height="12" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['full_communication'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['full_communication'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('communication_full', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/full_comment.png" width="12" height="12" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['part_communication'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['part_communication'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('communication_part', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

        <td class="t_border <?php echo $this->_tpl_vars['communications_sort']['content']['isSorted']; ?>
 content" id="communication_<?php echo $this->_tpl_vars['communication']['id']; ?>
">
          <?php if (( $this->_tpl_vars['communication']['subject'] )): ?><strong><?php echo ((is_array($_tmp=$this->_tpl_vars['communication']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><br /><?php endif; ?>
          <div id="communication_full_<?php echo $this->_foreach['i']['iteration']; ?>
" class="comment_parent communication_parent">
            <div class="comment communication"><?php echo $this->_tpl_vars['communication']['content']; ?>
</div>
          </div>
        </td>
        <td class="t_border <?php echo $this->_tpl_vars['communications_sort']['added']['isSorted']; ?>
" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        <td class="hleft" nowrap="nowrap">
          <?php if ($this->_tpl_vars['communication']['type'] == 'comment' && $this->_tpl_vars['current_model']): ?>
            <?php if ($this->_tpl_vars['current_model']->checkPermissions('comments_add')): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/reply.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="page_menu_link" onclick="manageCommunicationAddPanels('comment', 'add',  '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
','','Re: <?php if ($this->_tpl_vars['communication']['subject']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['content'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp) : smarty_modifier_strip_tags($_tmp)))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, "...") : smarty_modifier_mb_truncate($_tmp, 20, "...")))) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>',<?php echo $this->_tpl_vars['communication']['id']; ?>
)" />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/reply.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_add_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" />
            <?php endif; ?>
            <?php echo smarty_function_math(array('assign' => 'last_date','equation' => "x-60*y",'x' => time(),'y' => @PH_COMMENTS_EDIT_INTERVAL), $this);?>

            <?php ob_start(); ?><?php echo ((is_array($_tmp=$this->_tpl_vars['last_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d %H:%M:%S') : smarty_modifier_date_format($_tmp, '%Y-%m-%d %H:%M:%S')); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('last_date_formated', ob_get_contents());ob_end_clean(); ?>
            <?php if ($this->_tpl_vars['communication']['added_by'] == $this->_tpl_vars['currentUser']->get('id') && $this->_tpl_vars['last_date_formated'] < $this->_tpl_vars['communication']['modified'] && $this->_tpl_vars['current_model']->checkPermissions('comments_add')): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/edit.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="page_menu_link" onclick="manageCommunicationAddPanels('comment', 'edit',  '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
','<?php echo $this->_tpl_vars['communication']['id']; ?>
')" />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/edit.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" />
            <?php endif; ?>
          <?php endif; ?>
          <?php if ($this->_tpl_vars['dashlet']): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/view.png" width="12" height="12" border="0" class="pointer<?php if (! $this->_tpl_vars['communication']['view_link']): ?> dimmed<?php endif; ?>" onclick="<?php if ($this->_tpl_vars['communication']['view_link']): ?>window.open('<?php echo $this->_tpl_vars['communication']['view_link']; ?>
', '_blank')<?php else: ?>alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['plugin_record_unable_view'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')<?php endif; ?>" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_view_record'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_view_record'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
          <?php endif; ?>
          <?php if ($this->_tpl_vars['communication']['resent_available']): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
resend.png" class="pointer" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['resend_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['resend_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="openResendMenu(this, '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
', '<?php echo $this->_tpl_vars['communication']['model']; ?>
', '<?php echo $this->_tpl_vars['communication']['type']; ?>
', '<?php echo $this->_tpl_vars['communication']['id']; ?>
', '<?php if ($this->_tpl_vars['dashlet']): ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
<?php endif; ?>')" />
          <?php elseif ($this->_tpl_vars['communication']['type'] == 'email'): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
reply.png" class="pointer" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="manageCommunicationAddPanels('<?php echo $this->_tpl_vars['communication']['type']; ?>
', 'reply',  '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
','','',<?php echo $this->_tpl_vars['communication']['id']; ?>
)" />
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
reply_all.png" class="pointer" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="manageCommunicationAddPanels('<?php echo $this->_tpl_vars['communication']['type']; ?>
', 'replyAll',  '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
','','',<?php echo $this->_tpl_vars['communication']['id']; ?>
)" />
          <?php endif; ?>
        </td>
      </tr>
    <?php endforeach; else: ?>
      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
        <td class="error" colspan="<?php echo $this->_tpl_vars['colspan_total']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endif; unset($_from); ?>
    <tr>
      <td class="t_footer" colspan="<?php echo $this->_tpl_vars['colspan_total']; ?>
"></td>
    </tr>
  </table>
  <script type="text/javascript">collapseCommunications();</script>