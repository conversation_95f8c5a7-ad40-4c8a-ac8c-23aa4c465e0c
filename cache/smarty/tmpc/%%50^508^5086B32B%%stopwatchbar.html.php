<?php /* Smarty version 2.6.33, created on 2023-07-13 16:42:48
         compiled from stopwatchbar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'mb_lower', 'stopwatchbar.html', 7, false),array('modifier', 'escape', 'stopwatchbar.html', 7, false),array('modifier', 'date_format', 'stopwatchbar.html', 7, false),array('function', 'help', 'stopwatchbar.html', 9, false),)), $this); ?>
<span id="m_started_timers">
<?php if ($this->_tpl_vars['currentUser'] && $this->_tpl_vars['currentUser']->get('started_timers') && count ( $this->_tpl_vars['currentUser']->get('started_timers') ) > 0): ?>
    <?php $_from = $this->_tpl_vars['currentUser']->get('started_timers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['started_model'] => $this->_tpl_vars['started_timers']):
?>
      <?php $_from = $this->_tpl_vars['started_timers']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['started_model_id'] => $this->_tpl_vars['started_model_details']):
?>
        <?php ob_start(); ?>lr_model_<?php echo $this->_tpl_vars['started_model_details']['model']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('model_label', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>
          <?php echo $this->_config[0]['vars']['stopwatch_started_prefix']; ?>
 <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['model_label']])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
 <strong><?php if ($this->_tpl_vars['started_model_details']['full_num']): ?>[<?php echo $this->_tpl_vars['started_model_details']['full_num']; ?>
] <?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['started_model_details']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong> <?php echo $this->_config[0]['vars']['stopwatch_started_suffix']; ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['started_model_details']['start_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('started_model_info', ob_get_contents());ob_end_clean(); ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
stopwatch.png" class="icon_button pointer <?php echo $this->_tpl_vars['started_model']; ?>
" id="startedwatch_<?php echo $this->_tpl_vars['started_model']; ?>
_<?php echo $this->_tpl_vars['started_model_id']; ?>
" width="16" height="16" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['stopwatch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['stopwatch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" style="vertical-align: middle;" onclick="confirmAction('stop_watch', function(el) { stopWatch(el, '<?php echo $this->_tpl_vars['started_model']; ?>
', <?php echo $this->_tpl_vars['started_model_id']; ?>
); }, this)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['stopwatch'],'text_content' => ((is_array($_tmp=$this->_tpl_vars['started_model_info'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')),'popup_only' => 1), $this);?>
 />
      <?php endforeach; endif; unset($_from); ?>
    <?php endforeach; endif; unset($_from); ?>
<?php else: ?>
  &nbsp;
<?php endif; ?>
</span>