<?php /* Smarty version 2.6.33, created on 2025-03-10 14:03:00
         compiled from /var/www/Nzoom-Evolution/_libs/modules/auth/view/templates/lost_password.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/auth/view/templates/lost_password.html', 4, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/auth/view/templates/lost_password.html', 16, false),)), $this); ?>
<link rel="stylesheet" href="<?php echo $this->_tpl_vars['view_url']; ?>
css/login.css?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" />
<div class="sec-lost_pass-wrapper">
  <section class="sec-lost_pass nz-rounded nz-surface nz-elevation nz-elevation--z20">
<h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
password.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['login'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <?php echo $this->_tpl_vars['title']; ?>
</h1>
<p><?php echo $this->_config[0]['vars']['auth_lost_password_legend']; ?>
</p>
<form name="loginform" class="form_container" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
  <table border="0" cellpadding="0" cellspacing="0" class="t_table">
    <tr>
      <td class="t_caption"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_lost_password_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    </tr>
    <tr>
      <td>
        <table border="0" cellspacing="0" cellpadding="5">
          <?php if ($this->_tpl_vars['users']): ?>
          <tr>
            <td class="labelbox" nowrap="nowrap"><a name="error_username"><label for="username"<?php if ($this->_tpl_vars['messages']->getErrors('username')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'username'), $this);?>
</label></a></td>
            <td><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <select class="selbox" name="username" id="username">
                <?php $_from = $this->_tpl_vars['users']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['usr']):
?>
                  <?php if ($this->_tpl_vars['usr']['active_option']): ?>
                    <option value="<?php echo $this->_tpl_vars['usr']['option_value']; ?>
"><?php echo $this->_tpl_vars['usr']['label']; ?>
</option>
                  <?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
              </select>
            </td>
          </tr>
          <?php endif; ?>
          <tr>
            <td class="labelbox" nowrap="nowrap"><a name="error_email"><label for="email"<?php if ($this->_tpl_vars['messages']->getErrors('email')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => $this->_config[0]['vars']['email'],'text_content' => $this->_config[0]['vars']['help_auth_email']), $this);?>
</label></a></td>
            <td><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <?php if ($this->_tpl_vars['users']): ?>
                <?php echo $_POST['email']; ?>

                <input type="hidden" name="email" id="email" value="<?php echo $_POST['email']; ?>
" />
              <?php else: ?>
                <input type="text" class="txtbox" name="email" id="email" value="<?php echo $_POST['email']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox" nowrap="nowrap" valign="top"><a name="error_captcha"><label for="captcha"<?php if ($this->_tpl_vars['messages']->getErrors('captcha')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'captcha'), $this);?>
</label></a></td>
            <td valign="top"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <img src="<?php echo $this->_tpl_vars['captcha']; ?>
" id="captcha" alt="" /><br />
              <input type="text" class="txtbox" name="captcha" id="captcha" value="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_captcha'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="float: left; width: <?php echo $this->_tpl_vars['width']; ?>
px; padding-left: 3px;" /><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
refresh.png" alt="" onclick="$('captcha').src=$('captcha').src+'#'" align="absmiddle"  />
            </td>
          </tr>
        </table>
        <div class="lost_pass-controls">
          <div>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=login"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_login'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
          </div>
          <div>
            <button type="submit" class="nz-button" name="Send"   onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_lost_password_button'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
          </div>
        </div>
      </td>
    </tr>
    <tr>
      <td class="t_footer"></td>
    </tr>
  </table>
</form>
<?php echo '
<script type="text/javascript">
  nz_ready().then(()=> {
    focusLostPass();
  });
</script>
'; ?>


  </section>
</div>