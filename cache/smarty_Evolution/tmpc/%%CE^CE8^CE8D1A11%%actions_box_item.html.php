<?php /* Smarty version 2.6.33, created on 2025-05-21 13:23:50
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/actions_box_item.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/actions_box_item.html', 30, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/actions_box_item.html', 32, false),)), $this); ?>
<li class="nz-actions-list-item<?php if (! empty ( $this->_tpl_vars['action']['options'] )): ?> nz-actions-list-item-has_options<?php endif; ?><?php if (! empty ( $this->_tpl_vars['action']['drop_menu'] ) && $this->_tpl_vars['action']['drop_menu'] && ! empty ( $this->_tpl_vars['action']['options'] ) && ! isset ( $this->_tpl_vars['action']['template'] )): ?> nz-actions-list-item-has_dropdown nz-openable<?php endif; ?><?php if ($this->_tpl_vars['action']['selected']): ?> nz--selected<?php endif; ?><?php if (! $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?> tab_no_label<?php endif; ?><?php if (! empty ( $this->_tpl_vars['action']['annulled'] )): ?> nz--strike<?php endif; ?>" title="<?php echo $this->_tpl_vars['action']['label']; ?>
">
    <a  draggable="false" <?php if ($this->_tpl_vars['action']['options']): ?>href="javascript:void(0)"<?php else: ?>href="<?php echo $this->_tpl_vars['action']['url']; ?>
"<?php if ($this->_tpl_vars['action']['target']): ?> target="<?php echo $this->_tpl_vars['action']['target']; ?>
"<?php endif; ?><?php endif; ?>
    class="nz-action__<?php echo $this->_tpl_vars['action']['name']; ?>
<?php if (isset ( $this->_tpl_vars['action']['template'] ) || ( $this->_tpl_vars['action']['options'] && empty ( $this->_tpl_vars['action']['drop_menu'] ) )): ?> nz-popout-trigger nz-popout-autoinit<?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['tabs'] ) && empty ( $this->_tpl_vars['action']['onclick'] )): ?> nz-tab-button<?php endif; ?>"
      <?php if (! empty ( $this->_tpl_vars['action']['onclick'] ) && ! ( ( $this->_tpl_vars['action']['name'] == 'referent_records' || $this->_tpl_vars['action']['name'] == 'finance' ) && preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['action']['url'] ) )): ?>
        onclick="document.querySelector('#<?php echo $this->_tpl_vars['action']['name']; ?>
_action')?.click(); event.preventDefault();"<?php endif; ?>
    <?php if ($this->_tpl_vars['action']['options'] && empty ( $this->_tpl_vars['action']['drop_menu'] )): ?>
       data-popout-on-load="prep_compatibility_<?php echo $this->_tpl_vars['action']['name']; ?>
"
    <?php endif; ?>
    <?php if (! empty ( $this->_tpl_vars['action']['options'] ) && empty ( $this->_tpl_vars['action']['drop_menu'] ) && $this->_tpl_vars['action']['name'] != 'search' && $this->_tpl_vars['action']['name'] != 'filter'): ?>
        data-popout-element="#temp_<?php echo $this->_tpl_vars['action']['controller']; ?>
_<?php echo $this->_tpl_vars['action']['action']; ?>
"
        <?php if (in_array ( $this->_tpl_vars['action']['name'] , array ( 'attachments' , 'tag' , 'status' , 'remind' ) )): ?>
          data-popout-position="panel: top right at: bottom center"
        <?php else: ?>
          data-popout-position="panel: top left at: bottom left"
        <?php endif; ?>
        data-container-attr="{&quot;class&quot;: &quot;nz-actions-box-popout nz-popout-panel <?php if (in_array ( $this->_tpl_vars['action']['name'] , array ( 'attachments' , 'tag' , 'status' , 'remind' ) )): ?>nz-pointer-top-right<?php else: ?>nz-pointer-top-left<?php endif; ?> nz-modal&quot;}"
    <?php elseif (isset ( $this->_tpl_vars['action']['template'] ) || ( $this->_tpl_vars['action']['options'] && empty ( $this->_tpl_vars['action']['drop_menu'] ) )): ?>data-popout-template="#xtemp_<?php echo $this->_tpl_vars['action']['controller']; ?>
_<?php echo $this->_tpl_vars['action']['action']; ?>
"
        data-popout-position="panel: top left at: bottom left"
    <?php endif; ?>
        title="<?php echo $this->_tpl_vars['action']['label']; ?>
"
    <?php if (isset ( $this->_tpl_vars['tabs'] ) && empty ( $this->_tpl_vars['action']['onclick'] )): ?>data-tabref="<?php echo $this->_tpl_vars['action']['name']; ?>
"<?php endif; ?>>
    <?php if (! empty ( $this->_tpl_vars['action']['icon'] )): ?>
        <i class="material-icons nz-glyph"><?php echo $this->_tpl_vars['action']['icon']; ?>
</i>
    <?php elseif (! empty ( $this->_tpl_vars['action']['img'] )): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['action']['img']; ?>
.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['action']['label']; ?>
" border="0" />
    <?php elseif (empty ( $this->_tpl_vars['action']['icon'] ) && empty ( $this->_tpl_vars['action']['img'] )): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['action']['name']; ?>
.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['action']['label']; ?>
" border="0" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?><span class="nz-actions-list-label"><?php echo ((is_array($_tmp=$this->_tpl_vars['action']['label'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, '...', true) : smarty_modifier_mb_truncate($_tmp, 20, '...', true)); ?>
</span><?php endif; ?></a>
        <?php if (((is_array($_tmp=@$this->_tpl_vars['action']['url_preview'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))): ?>
        <span style="vertical-align: middle;"><img onclick="window.open('<?php echo $this->_tpl_vars['action']['url_preview']; ?>
', '<?php echo $this->_tpl_vars['action']['target_preview']; ?>
');" class="menu_additional_option_button_img" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['action']['img_preview']; ?>
.png" alt="" width="14" height="14" title="<?php echo $this->_tpl_vars['action']['label_preview']; ?>
" border="0" /></span>
    <?php endif; ?>
    <span style="display: none;"
          <?php if (isset ( $this->_tpl_vars['tabs'] )): ?>
              <?php if (( $this->_tpl_vars['action']['name'] == 'referent_records' || $this->_tpl_vars['action']['name'] == 'finance' ) && preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['action']['url'] )): ?>
                  onclick="toggleRelatedTabs(this); $('rel_type').value='<?php echo $this->_tpl_vars['action']['name']; ?>
';" id="tab_<?php echo $this->_tpl_vars['action']['name']; ?>
"
              <?php elseif ($this->_tpl_vars['action']['onclick']): ?>onclick="<?php echo $this->_tpl_vars['action']['onclick']; ?>
"<?php endif; ?>
              id="tab_<?php echo $this->_tpl_vars['action']['name']; ?>
"
          <?php else: ?>
              <?php if ($this->_tpl_vars['action']['options']): ?> onclick="if (null === $('<?php echo $this->_tpl_vars['action']['name']; ?>
Go')) if ('<?php echo $this->_tpl_vars['action']['ajax_no']; ?>
' != '1') {getActionOptions('td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', '<?php echo $this->_tpl_vars['action']['name']; ?>
', <?php if ($this->_tpl_vars['model'] && $this->_tpl_vars['model']->get('id')): ?>'<?php echo $this->_tpl_vars['model']->get('id'); ?>
'<?php else: ?>0<?php endif; ?>, {});}toggleActionOptions(this); return false;"
              <?php elseif ($this->_tpl_vars['action']['confirm']): ?> onclick="return confirmAction('<?php echo $this->_tpl_vars['action']['name']; ?>
', function(el) { window.open('<?php echo $this->_tpl_vars['action']['url']; ?>
', '<?php echo ((is_array($_tmp=@$this->_tpl_vars['action']['target'])) ? $this->_run_mod_handler('default', true, $_tmp, '_self') : smarty_modifier_default($_tmp, '_self')); ?>
'); }, this, i18n['messages']['confirm_<?php echo ((is_array($_tmp=@$this->_tpl_vars['action']['confirm_label'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['action']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['action']['name'])); ?>
']);"
              <?php elseif ($this->_tpl_vars['action']['onclick']): ?> onclick="<?php echo $this->_tpl_vars['action']['onclick']; ?>
"<?php endif; ?>
              id="<?php echo $this->_tpl_vars['action']['name']; ?>
_action"
          <?php endif; ?>
    ></span>
    <?php if (((is_array($_tmp=@$this->_tpl_vars['action']['drop_menu'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))): ?>
        <?php if ($this->_tpl_vars['action']['options']): ?>
        <ul class="nz-actions-list-dropdown">
            <?php $_from = $this->_tpl_vars['action']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
            <?php if (isset ( $this->_tpl_vars['option']['hr'] )): ?>
                <li><hr /></li>
            <?php else: ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box_item.html", 'smarty_include_vars' => array('action' => $this->_tpl_vars['option'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
        </ul>
        <?php endif; ?>
    <?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['action']['template'] ) && ! empty ( $this->_tpl_vars['action']['ajax_nol'] )): ?>
        <?php ob_start(); ?><div id="td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options"><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['action']['template']), 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?></div><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('template_box', ob_get_contents());ob_end_clean(); ?>
    <?php elseif ($this->_tpl_vars['action']['options'] && empty ( $this->_tpl_vars['action']['drop_menu'] )): ?>
      <?php if ($this->_tpl_vars['action']['name'] == 'filter' || $this->_tpl_vars['action']['name'] == 'search'): ?>
        <?php ob_start(); ?>
          <table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%" style="min-width: 50px; min-height: 40px;">
            <tr>
              <td id="td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options">
                <?php if ($this->_tpl_vars['action']['show_notice']): ?>
                  <span style="color: #0000FF"><?php echo $this->_tpl_vars['action']['show_notice']; ?>
</span>
                <?php endif; ?>
                <?php if ($this->_tpl_vars['action']['ajax_no'] == '1'): ?>
                  <?php if ($this->_tpl_vars['action']['template']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['action']['template'], 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => '_action_common_options.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                <?php endif; ?>
              </td>
            </tr>
          </table>
        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('template_box', ob_get_contents());ob_end_clean(); ?>
      <?php else: ?>
        <?php ob_start(); ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."/_action_common.html", 'smarty_include_vars' => array('action' => $this->_tpl_vars['action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('scriptTemp', ob_get_contents());ob_end_clean(); ?>
      <?php endif; ?>
    <?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['template_box'] ) || isset ( $this->_tpl_vars['scriptTemp'] )): ?>
        <?php if ($this->_tpl_vars['action']['name'] == 'filter'): ?>
          <script type="text/javascript">
              window.addEventListener('load', getSearchOptionsExclusively);
              function getSearchOptionsExclusively(e) {
                getActionOptions('td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', '<?php echo $this->_tpl_vars['action']['name']; ?>
', <?php if ($this->_tpl_vars['model'] && $this->_tpl_vars['model']->get('id')): ?>'<?php echo $this->_tpl_vars['model']->get('id'); ?>
'<?php else: ?>0<?php endif; ?>, {<?php if ($_GET['autocomplete_filter']): ?>autocomplete_filter: '<?php echo $_GET['autocomplete_filter']; ?>
'<?php endif; ?>});
                scalePopup();
              }
          </script>
        <?php endif; ?>
        <?php if (isset ( $this->_tpl_vars['template_box'] )): ?>
          <?php ob_start(); ?><aside id="actionBox_<?php echo $this->_tpl_vars['action']['controller']; ?>
_<?php echo $this->_tpl_vars['action']['action']; ?>
" class="nz-actions-box-popout nz-popout-panel nz-pointer-top-left nz-modal"
            ><div class="nz-actions-box-popout__body nz-popout-surface nz-surface nz-elevation--z6">
              <form method="<?php echo ((is_array($_tmp=@$this->_tpl_vars['action']['options']['form_method'])) ? $this->_run_mod_handler('default', true, $_tmp, 'get') : smarty_modifier_default($_tmp, 'get')); ?>
" action="<?php echo $_SERVER['PHP_SELF']; ?>
" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')); ?>
_form" enctype="multipart/form-data">
              <input type="hidden" name="<?php echo $this->_tpl_vars['action']['module_param']; ?>
" value="<?php echo $this->_tpl_vars['action']['module']; ?>
" />
              <?php if ($this->_tpl_vars['action']['controller']): ?>
                  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['controller_param']; ?>
" value="<?php echo $this->_tpl_vars['action']['controller']; ?>
" />
              <?php endif; ?>
              <input type="hidden" name="<?php echo $this->_tpl_vars['action']['action_param']; ?>
" value="<?php echo $this->_tpl_vars['action']['action']; ?>
" />
              <?php if ($this->_tpl_vars['action']['model_id']): ?>
                  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['action']; ?>
" value="<?php echo $this->_tpl_vars['action']['model_id']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['action']['model_lang']): ?>
                  <input type="hidden" name="model_lang" value="<?php echo $this->_tpl_vars['aaction']['model_lang']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['action']['name'] == 'search' || $this->_tpl_vars['action']['name'] == 'filter'): ?>
                  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['session_param']; ?>
" value="1" />
                  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['name']; ?>
_module" value="<?php echo $this->_tpl_vars['action']['module']; ?>
" />
                  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['name']; ?>
_controller" value="<?php echo $this->_tpl_vars['action']['controller']; ?>
" />
                  <?php if ($this->_tpl_vars['event'] && ! is_object ( $this->_tpl_vars['event'] )): ?>
                      <input type="hidden" name="event" value="<?php echo $this->_tpl_vars['event']; ?>
" />
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['relation']): ?>
                      <input type="hidden" name="relation" value="<?php echo $this->_tpl_vars['relation']; ?>
" />
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['group_table']): ?>
                      <input type="hidden" name="group_table" value="<?php echo $this->_tpl_vars['group_table']; ?>
" />
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['mynzoom_settings_table']): ?>
                      <input type="hidden" name="mynzoom_settings_table" value="<?php echo $this->_tpl_vars['mynzoom_settings_table']; ?>
" />
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['form_name']): ?>
                      <input type="hidden" name="form_name" value="<?php echo $this->_tpl_vars['form_name']; ?>
" />
                  <?php endif; ?>
                  <?php if ($_REQUEST['autocomplete_filter']): ?>
                      <input type="hidden" name="autocomplete_filter" id="autocomplete_filter" value="session" />
                  <?php endif; ?>
                  <?php if ($_REQUEST['uniqid']): ?>
                      <input type="hidden" name="uniqid" id="uniqid" value="<?php echo $_REQUEST['uniqid']; ?>
" />
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['session_param']): ?>
                      <input type="hidden" name="session_param" value="<?php echo $this->_tpl_vars['session_param']; ?>
" />
                  <?php endif; ?>
              <?php endif; ?>
              <?php if ($this->_tpl_vars['hidden_fields']): ?><?php echo $this->_tpl_vars['hidden_fields']; ?>
<?php endif; ?>
              <?php if ($this->_tpl_vars['template_box']): ?>
                <?php echo $this->_tpl_vars['template_box']; ?>

              <?php else: ?>
              <?php endif; ?>
              </form>
          </div></aside><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('scriptTemp', ob_get_contents());ob_end_clean(); ?>
          <script type="text/x-template" id="xtemp_<?php echo $this->_tpl_vars['action']['controller']; ?>
_<?php echo $this->_tpl_vars['action']['action']; ?>
"><?php echo $this->_tpl_vars['scriptTemp']; ?>

          </script>
        <?php else: ?>
          <div id="temp_<?php echo $this->_tpl_vars['action']['controller']; ?>
_<?php echo $this->_tpl_vars['action']['action']; ?>
" class="nz-actions-box-popout__body"><?php echo $this->_tpl_vars['scriptTemp']; ?>
</div>
        <?php endif; ?>
    <script>
      <?php if ($this->_tpl_vars['action']['name'] == 'search' || $this->_tpl_vars['action']['name'] == 'filter'): ?>
      window.prep_compatibility_<?php echo $this->_tpl_vars['action']['name']; ?>
 = function() {
        prep_compatibility('<?php echo $this->_tpl_vars['action']['name']; ?>
', 'td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options', '<?php echo $this->_tpl_vars['action']['ajax_no']; ?>
' !== '1');
        }
      <?php endif; ?>
        // <?php echo $this->_tpl_vars['action']['name']; ?>
;
      <?php if ($this->_tpl_vars['action']['name'] == 'attachments' || $this->_tpl_vars['action']['name'] == 'setstatus' || $this->_tpl_vars['action']['name'] == 'remind' || $this->_tpl_vars['action']['name'] == 'add' || $this->_tpl_vars['action']['name'] == 'multiadd' || $this->_tpl_vars['action']['name'] == 'adds' || $this->_tpl_vars['action']['name'] == 'create' || $this->_tpl_vars['action']['name'] == 'tag' || $this->_tpl_vars['action']['name'] == 'export' || $this->_tpl_vars['action']['name'] == 'clone' || $this->_tpl_vars['action']['name'] == 'generate' || $this->_tpl_vars['action']['name'] == 'receive_date' || $this->_tpl_vars['action']['name'] == 'annul' || $this->_tpl_vars['action']['name'] == 'timesheets' || $this->_tpl_vars['action']['name'] == 'transfer' || $this->_tpl_vars['action']['name'] == 'annulmentsubtype' || 'import'): ?>
      window.prep_compatibility_<?php echo $this->_tpl_vars['action']['name']; ?>
 = function() {
        prep_compatibility('<?php echo $this->_tpl_vars['action']['name']; ?>
', 'td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options', '<?php echo $this->_tpl_vars['action']['ajax_no']; ?>
' !== '1', <?php echo $this->_tpl_vars['action']['model_id']; ?>
);
        const actionName = '<?php echo $this->_tpl_vars['action']['name']; ?>
';
        <?php echo '
        if ([\'annulmentsubtype\'].includes(actionName)) {
          this.clickOutsideClose = false;
        }
        if ((actionName === \'adds\')) {
          var options_row = document.querySelector(`#${actionName}_suboptions_row`);
          if (!options_row) {
            return;
          }
          var inside_inputs = options_row.getElementsByTagName(\'input\');
          for (let j = 0; j < inside_inputs.length; j++) {
            if (inside_inputs[j].type === \'radio\') {
              const options_box_id = actionName + \'_\' + inside_inputs[j].id + \'_box\';
              const options_box = document.querySelector(\'#\'+options_box_id);
              if (inside_inputs[j].checked === true) {
                options_box.style.display = \'block\';
                toggleFields(options_box, true);
              } else {
                options_box.style.display = \'none\';
                toggleFields(options_box, false);
              }
            }
          }
        }
        '; ?>

      }
      <?php endif; ?>

      <?php if ($this->_tpl_vars['action']['name'] == 'transformations'): ?>
      window.prep_compatibility_<?php echo $this->_tpl_vars['action']['name']; ?>
 = function() {}
      <?php endif; ?>
    </script>
    <?php endif; ?>
</li>