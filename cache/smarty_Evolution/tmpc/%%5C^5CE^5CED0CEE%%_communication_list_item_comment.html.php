<?php /* Smarty version 2.6.33, created on 2025-05-19 16:26:50
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'd', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 1, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 11, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 11, false),array('modifier', 'strip_tags', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 18, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 18, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 27, false),array('function', 'math', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_communication_list_item_comment.html', 26, false),)), $this); ?>
<?php echo d($this->_tpl_vars['communication']['notifications']); ?>

<li class="nz-communication-list-item nz-communication--comment">
  <div class="nz-communication-item__timeline">
    <span class="nz-communication__type">
      <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForRecord('communications_comments'); ?>
</i>
    </span>
  </div>
  <div class="nz-communication-item__card nz-elevation--z4">
    <div class="nz-communication__title">
      <div class="nz-communication__title--line">
        <span class="nz-communication__sender-name"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</span>
        <div class="nz-communication__controls">
          <ul class="nz-communication__controls-list">
            <?php if ($this->_tpl_vars['current_model']): ?>
              <?php if ($this->_tpl_vars['current_model']->checkPermissions('comments_add')): ?>
                <li><span class="nz-icon-button"
                          title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['reply'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                          onclick="manageCommunicationAddPanels('comment', 'add',  '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
','','Re: <?php if ($this->_tpl_vars['communication']['subject']): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['content'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp) : smarty_modifier_strip_tags($_tmp)))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, "...") : smarty_modifier_mb_truncate($_tmp, 20, "...")))) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>',<?php echo $this->_tpl_vars['communication']['id']; ?>
)"
                  ><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_comment_reply'); ?>
</span></li>
              <?php else: ?>
                <li><span class="material-icons nz--disabled"
                          title="<?php echo ((is_array($_tmp=$this->_tpl_vars['full_communication'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                          onclick="Nz.alert('<?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
', '<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_add_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')"
                  ><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_comment_reply'); ?>
</span></li>
              <?php endif; ?>
              <?php echo smarty_function_math(array('assign' => 'last_date','equation' => "x-60*y",'x' => time(),'y' => @PH_COMMENTS_EDIT_INTERVAL), $this);?>

              <?php ob_start(); ?><?php echo ((is_array($_tmp=$this->_tpl_vars['last_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d %H:%M:%S') : smarty_modifier_date_format($_tmp, '%Y-%m-%d %H:%M:%S')); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('last_date_formated', ob_get_contents());ob_end_clean(); ?>
              <?php if ($this->_tpl_vars['communication']['added_by'] == $this->_tpl_vars['currentUser']->get('id') && $this->_tpl_vars['last_date_formated'] < $this->_tpl_vars['communication']['modified'] && $this->_tpl_vars['current_model']->checkPermissions('comments_add')): ?>
                <li><span class="nz-icon-button "
                          title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                          onclick="manageCommunicationAddPanels('comment', 'edit',  '<?php echo $this->_tpl_vars['communication']['model_id']; ?>
','<?php echo $this->_tpl_vars['communication']['id']; ?>
')"
                  ><?php echo $this->_tpl_vars['theme']->getIconForAction('edit'); ?>
</span></li>
              <?php else: ?>
                <li><span class="material-icons nz--disabled"
                          title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                          onclick="Nz.alert('<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
','<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')"
                  ><?php echo $this->_tpl_vars['theme']->getIconForAction('edit'); ?>
</span></li>
              <?php endif; ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['dashlet']): ?>
              <li><span class="nz-icon-button"
                        title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_view_record'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                        onclick="<?php if ($this->_tpl_vars['communication']['view_link']): ?>window.open('<?php echo $this->_tpl_vars['communication']['view_link']; ?>
', '_blank')<?php else: ?>alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['plugin_record_unable_view'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')<?php endif; ?>"
                ><?php echo $this->_tpl_vars['theme']->getIconForAction('view'); ?>
</span></li>
            <?php endif; ?>
          </ul>
        </div>
      </div>

      <div class="nz-communication__title--line">
        <span class="nz-communication__timestamp"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        <?php if (count ( $this->_tpl_vars['communication']['to'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_list_item_recepients.html", 'smarty_include_vars' => array('recipients' => $this->_tpl_vars['communication']['notifications'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
    </div>

    <div class="nz-communication__content" id="communication_<?php echo $this->_tpl_vars['communication']['id']; ?>
">
      <div class="nz-less-container">
        <div class="nz-less-body">
          <div class="nz-communication__content-body">
            <?php if (( $this->_tpl_vars['communication']['subject'] )): ?><h3 class="nz-communication__content-subject"><?php echo ((is_array($_tmp=$this->_tpl_vars['communication']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h3><?php endif; ?>
              <div class="nz-communication__content-text"><?php echo $this->_tpl_vars['communication']['content']; ?>
</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</li>
