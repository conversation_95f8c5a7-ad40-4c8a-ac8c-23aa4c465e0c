<?php /* Smarty version 2.6.33, created on 2024-09-13 13:33:33
         compiled from /var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 26, false),array('modifier', 'mb_upper', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 37, false),array('modifier', 'string_format', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 55, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 57, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 59, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 81, false),array('function', 'popup', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 97, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/attachments.html', 238, false),)), $this); ?>
<div class="nz-page-wrapper<?php if (! empty ( $this->_tpl_vars['side_panels'] ) && count ( $this->_tpl_vars['side_panels'] )): ?> nz--has-side-panel<?php endif; ?>">
  <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">
    <div class="nz-page-title"><h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
      <div class="nz-page-title-tools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['general'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['general'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
      <div class="nz-page-title-sidetools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['quick'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['quick'],'onlyIcons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
    </div>
    <div class="nz-page-actions">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['context'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_add_popout_xtemplate.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_translations_menu.html", 'smarty_include_vars' => array('translations' => ($this->_tpl_vars['translations']))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>


    <div id="form_container" class="nz-page-content main_panel_container">
      <form name="documents_attachments" id="documents_attachments" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" enctype="multipart/form-data">
        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['document']->get('id'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />

        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info_header.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <tr>
                  <td colspan="3" nowrap="nowrap" class="nopadding">
                    <table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
                      <tr>
                        <td class="t_caption2" colspan="7"><div class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_genfiles_title'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>
</div></td>
                      </tr>
                      <tr>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo $this->_config[0]['vars']['required']; ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3">&nbsp;</td>
                      </tr>
          <?php $_from = $this->_tpl_vars['document']->get('genfiles'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['file']):
        $this->_foreach['i']['iteration']++;
?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['modified_genfiles'] && array_key_exists ( $this->_tpl_vars['file']->get('id') , $this->_tpl_vars['modified_genfiles'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('modified', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['modified']): ?><?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('gidx', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?>
              <?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('filename'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_pattern'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('pattern_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp) : smarty_modifier_mb_truncate($_tmp)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['file']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['file']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['file']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?>

            <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
            <?php if (! $this->_tpl_vars['file']->get('not_exist')): ?>
              <?php ob_start(); ?><?php echo ''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['document']->get('id'); ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?>
                <?php echo 'onclick="window.open(\''; ?><?php echo $this->_tpl_vars['row_link']; ?><?php echo '\', \'_blank\')" style="cursor:pointer;"'; ?>

              <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_clauses', ob_get_contents());ob_end_clean(); ?>
            <?php else: ?>
              <?php ob_start(); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_clauses', ob_get_contents());ob_end_clean(); ?>
            <?php endif; ?>
                      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['erred_modified_genfiles'] && in_array ( $this->_tpl_vars['file']->get('id') , ((is_array($_tmp=@$this->_tpl_vars['erred_modified_genfiles'])) ? $this->_run_mod_handler('default', true, $_tmp, 'array') : smarty_modifier_default($_tmp, 'array')) ( ) )): ?> t_deleted<?php endif; ?>">
                        <td<?php if ($this->_tpl_vars['erred_modified_genfiles'] && in_array ( $this->_tpl_vars['file']->get('id') , ((is_array($_tmp=@$this->_tpl_vars['erred_modified_genfiles'])) ? $this->_run_mod_handler('default', true, $_tmp, 'array') : smarty_modifier_default($_tmp, 'array')) ( ) )): ?> class="error"<?php endif; ?> align="right">
                    <?php if ($this->_tpl_vars['file']->get('not_exist')): ?>
                      <span class="material-icons nz-file-not-exist nz-tooltip nz-tooltip-dir-top"
                            data-tooltip-content="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">warning</span>
                    <?php endif; ?>
                        <?php echo $this->_foreach['i']['iteration']; ?>
.</td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <input type="hidden" name="g_file_filenames[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" value="<?php echo $this->_tpl_vars['file']->get('filename'); ?>
" />
                          <input type="hidden" name="g_file_indices[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" value="<?php echo $this->_foreach['i']['iteration']; ?>
" />
                          <div id="g_file_paths_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="" /> <?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('filename'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                        </td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php ob_start(); ?><?php if ($this->_tpl_vars['file']->get('name')): ?><?php echo $this->_tpl_vars['file']->get('name'); ?>
<?php else: ?><?php echo $this->_tpl_vars['file']->get('filename'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('file_name', ob_get_contents());ob_end_clean(); ?>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="g_file_names_value_<?php echo $this->_tpl_vars['gidx']; ?>
" style="display: none"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" width="11" height="11" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['file_name'],'caption' => ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 /> <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <input type="text" name="g_file_names[<?php echo $this->_tpl_vars['gidx']; ?>
]" id="g_file_names_<?php echo $this->_tpl_vars['gidx']; ?>
" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['modified_genfiles'][$this->_tpl_vars['gidx']]['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px;" /></td>
                          <?php else: ?>
                          <div id="g_file_names_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" width="11" height="11" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['file_name'],'caption' => ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 /> <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <input type="text" name="g_file_names[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="g_file_names_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" value="<?php echo $this->_tpl_vars['file']->get('name'); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px; display: none" disabled="disabled" /></td>
                          <?php endif; ?>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="g_file_descriptions_value_<?php echo $this->_tpl_vars['gidx']; ?>
"<?php if ($this->_tpl_vars['modified']): ?> style="display: none"<?php endif; ?>><?php if ($this->_tpl_vars['file']->get('description')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" width="11" height="11" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['file']->get('description'),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 /> <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>&nbsp;<?php endif; ?></div>
                          <input type="text" name="g_file_descriptions[<?php echo $this->_tpl_vars['gidx']; ?>
]" id="g_file_descriptions_<?php echo $this->_tpl_vars['gidx']; ?>
" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['modified_genfiles'][$this->_tpl_vars['gidx']]['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px;" />
                          <?php else: ?>
                          <div id="g_file_descriptions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><?php if ($this->_tpl_vars['file']->get('description')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" width="11" height="11" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['file']->get('description'),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 /> <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>&nbsp;<?php endif; ?></div>
                          <input type="text" name="g_file_descriptions[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="g_file_descriptions_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" value="<?php echo $this->_tpl_vars['file']->get('description'); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px; display: none" disabled="disabled" />
                          <?php endif; ?>
                        </td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <div id="g_file_revisions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?>
</div>
                        </td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="g_file_permissions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" style="display: none">
                            <?php if ($this->_tpl_vars['file']->get('permission') == 'mine'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'group'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'all'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php endif; ?>
                          </div>
                          <select class="selbox" name="g_file_permissions[<?php echo $this->_tpl_vars['gidx']; ?>
]" id="g_file_permissions_<?php echo $this->_tpl_vars['gidx']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px;">
                            <option value="all"<?php if ($this->_tpl_vars['modified_genfiles'][$this->_tpl_vars['gidx']]['permission'] == 'all'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="group"<?php if ($this->_tpl_vars['modified_genfiles'][$this->_tpl_vars['gidx']]['permission'] == 'group'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="mine"<?php if ($this->_tpl_vars['modified_genfiles'][$this->_tpl_vars['gidx']]['permission'] == 'mine'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                          </select>
                          <?php else: ?>
                          <div id="g_file_permissions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
">
                            <?php if ($this->_tpl_vars['file']->get('permission') == 'mine'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'group'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'all'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php endif; ?>
                          </div>
                          <select class="selbox" name="g_file_permissions[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="g_file_permissions_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px; display: none;" disabled="disabled">
                            <option value="all"<?php if ($this->_tpl_vars['file']->get('permission') == 'all'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="group"<?php if ($this->_tpl_vars['file']->get('permission') == 'group'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="mine"<?php if ($this->_tpl_vars['file']->get('permission') == 'mine'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                          </select>
                          <?php endif; ?>
                        </td>
                        <td nowrap="nowrap">
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php if ($this->_tpl_vars['currentUser']->checkRights('documents','edit_file') && ( $this->_tpl_vars['session']->get('currentUserId') == $this->_tpl_vars['file']->get('added_by') ) && ! $this->_tpl_vars['file']->get('not_exist') && ! $this->_tpl_vars['document']->get('archived_by')): ?>onclick="editFileBrowse(this, <?php echo $this->_tpl_vars['file']->get('id'); ?>
, 'generated', '<?php echo $this->_tpl_vars['row_link']; ?>
')" class="pointer<?php if ($this->_tpl_vars['modified']): ?> dimmed<?php endif; ?>"<?php else: ?> onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" class="pointer dimmed"<?php endif; ?> />
                        <?php if (! $this->_tpl_vars['file']->get('not_exist')): ?>
                          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['document']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="" /></a>
                          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['document']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>" download><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="" /></a>
                        <?php else: ?>
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
                        <?php endif; ?>
                        <?php if ($this->_tpl_vars['currentUser']->checkRights('documents','delete_file') && ( $this->_tpl_vars['session']->get('currentUserId') == $this->_tpl_vars['file']->get('added_by') ) && ! $this->_tpl_vars['file']->get('not_exist') && ! $this->_tpl_vars['document']->get('archived_by')): ?>
                          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=delfile&amp;delfile=<?php echo $this->_tpl_vars['document']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
" onclick="return confirmAction('delete_file', function(el) { window.location.href = el.href; }, this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                        <?php else: ?>
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" class="pointer dimmed"/>
                        <?php endif; ?>
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
info.png" width="16" height="16" border="0" alt="" class="help" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
                        </td>
                      </tr>
        <?php endforeach; else: ?>
                      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
                        <td class="error" colspan="7"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                      </tr>
        <?php endif; unset($_from); ?>

        <!-- ATTACHMENTS -->
                      <tr>
                        <td class="t_caption2" colspan="7"><div class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_files_title'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>
</div></td>
                      </tr>
                      <tr>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo $this->_config[0]['vars']['required']; ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3">&nbsp;</td>
                      </tr>
          <?php $_from = $this->_tpl_vars['document']->get('attachments'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['file']):
        $this->_foreach['i']['iteration']++;
?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['modified_files'] && array_key_exists ( $this->_tpl_vars['file']->get('id') , $this->_tpl_vars['modified_files'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('modified', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['modified']): ?><?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('midx', ob_get_contents());ob_end_clean(); ?>
            <?php ob_start(); ?>
              <?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('filename'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp) : smarty_modifier_mb_truncate($_tmp)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['file']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['file']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['file']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?>

            <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
            <?php if (! $this->_tpl_vars['file']->get('not_exist')): ?>
              <?php ob_start(); ?><?php echo ''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['document']->get('id'); ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?>
                <?php echo 'onclick="window.open(\''; ?><?php echo $this->_tpl_vars['row_link']; ?><?php echo '\', \'_blank\')" style="cursor: pointer;"'; ?>

              <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_clauses', ob_get_contents());ob_end_clean(); ?>
            <?php else: ?>
              <?php ob_start(); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_clauses', ob_get_contents());ob_end_clean(); ?>
            <?php endif; ?>
                      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['erred_modified_files'] && in_array ( $this->_tpl_vars['file']->get('id') , ((is_array($_tmp=@$this->_tpl_vars['erred_modified_files'])) ? $this->_run_mod_handler('default', true, $_tmp, 'array') : smarty_modifier_default($_tmp, 'array')) ( ) )): ?> t_deleted<?php endif; ?>">
                        <td<?php if ($this->_tpl_vars['erred_modified_files'] && in_array ( $this->_tpl_vars['file']->get('id') , ((is_array($_tmp=@$this->_tpl_vars['erred_modified_files'])) ? $this->_run_mod_handler('default', true, $_tmp, 'array') : smarty_modifier_default($_tmp, 'array')) ( ) )): ?> class="error"<?php endif; ?> align="right">
                    <?php if ($this->_tpl_vars['file']->get('not_exist')): ?>
                      <span class="material-icons nz-file-not-exist nz-tooltip nz-tooltip-dir-top"
                            data-tooltip-content="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">warning</span>
                    <?php endif; ?>
        <?php echo $this->_foreach['i']['iteration']; ?>
.</td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <input type="hidden" name="file_filenames[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" value="<?php echo $this->_tpl_vars['file']->get('filename'); ?>
" />
                          <input type="hidden" name="file_indices[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" value="<?php echo $this->_foreach['i']['iteration']; ?>
" />
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="file_paths_value_<?php echo $this->_tpl_vars['midx']; ?>
" style="display: none"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="" /> <?php echo ((is_array($_tmp=$this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['filename'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <input type="file" class="filebox" name="file_paths[<?php echo $this->_tpl_vars['midx']; ?>
]" id="file_paths_<?php echo $this->_tpl_vars['midx']; ?>
" value="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                          <?php else: ?>
                          <div id="file_paths_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="" /> <?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('filename'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <input type="file" class="filebox" name="file_paths[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="file_paths_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" value="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="display: none" disabled="disabled" />
                          <?php endif; ?>
                        </td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php ob_start(); ?><?php if ($this->_tpl_vars['file']->get('name')): ?><?php echo $this->_tpl_vars['file']->get('name'); ?>
<?php else: ?><?php echo $this->_tpl_vars['file']->get('filename'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('file_name', ob_get_contents());ob_end_clean(); ?>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="file_names_value_<?php echo $this->_tpl_vars['midx']; ?>
" style="display: none"><?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['file_name']), $this);?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <input type="text" name="file_names[<?php echo $this->_tpl_vars['midx']; ?>
]" id="file_names_<?php echo $this->_tpl_vars['midx']; ?>
" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px;" /></td>
                          <?php else: ?>
                          <div id="file_names_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['file_name']), $this);?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <input type="text" name="file_names[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="file_names_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" value="<?php echo $this->_tpl_vars['file']->get('name'); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px; display: none" disabled="disabled" /></td>
                          <?php endif; ?>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="file_descriptions_value_<?php echo $this->_tpl_vars['midx']; ?>
" style="display: none"><?php if ($this->_tpl_vars['file']->get('description')): ?><?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['file']->get('description')), $this);?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>&nbsp;<?php endif; ?></div>
                          <input type="text" name="file_descriptions[<?php echo $this->_tpl_vars['midx']; ?>
]" id="file_descriptions_<?php echo $this->_tpl_vars['midx']; ?>
" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px;" />
                          <?php else: ?>
                          <div id="file_descriptions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><?php if ($this->_tpl_vars['file']->get('description')): ?><?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['file']->get('description')), $this);?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>&nbsp;<?php endif; ?></div>
                          <input type="text" name="file_descriptions[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="file_descriptions_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" value="<?php echo $this->_tpl_vars['file']->get('description'); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px; display: none" disabled="disabled" />
                          <?php endif; ?>
                        </td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="file_revisions_value_<?php echo $this->_tpl_vars['midx']; ?>
" style="display: none"><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?>
</div>
                          <select class="selbox" name="file_revisions[<?php echo $this->_tpl_vars['midx']; ?>
]" id="file_revisions_<?php echo $this->_tpl_vars['midx']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revisions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 50px;">
                            <option value=""<?php if ($this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['revision'] == ''): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_new_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="<?php echo $this->_tpl_vars['file']->get('revision'); ?>
"<?php if ($this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['revision'] == $this->_tpl_vars['file']->get('revision')): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?>
</option>
                          </select>
                          <?php else: ?>
                          <div id="file_revisions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?>
</div>
                          <select class="selbox" name="file_revisions[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="file_revisions_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revisions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 50px; display: none" disabled="disabled">
                            <option value=""><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_new_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="<?php echo $this->_tpl_vars['file']->get('revision'); ?>
" selected="selected"><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?>
</option>
                          </select>
                          <?php endif; ?>
                        </td>
                        <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
                          <?php if ($this->_tpl_vars['modified']): ?>
                          <div id="file_permissions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" style="display: none">
                            <?php if ($this->_tpl_vars['file']->get('permission') == 'mine'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'group'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'all'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php endif; ?>
                          </div>
                          <select class="selbox" name="file_permissions[<?php echo $this->_tpl_vars['midx']; ?>
]" id="file_permissions_<?php echo $this->_tpl_vars['midx']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px;">
                            <option value="all"<?php if ($this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['permission'] == 'all'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="group"<?php if ($this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['permission'] == 'group'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="mine"<?php if ($this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['permission'] == 'mine'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                          </select>
                          <?php else: ?>
                          <div id="file_permissions_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
">
                            <?php if ($this->_tpl_vars['file']->get('permission') == 'mine'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'group'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php elseif ($this->_tpl_vars['file']->get('permission') == 'all'): ?>
                              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                            <?php endif; ?>
                          </div>
                          <select class="selbox" name="file_permissions[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="file_permissions_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px; display: none;" disabled="disabled">
                            <option value="all"<?php if ($this->_tpl_vars['file']->get('permission') == 'all'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="group"<?php if ($this->_tpl_vars['file']->get('permission') == 'group'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="mine"<?php if ($this->_tpl_vars['file']->get('permission') == 'mine'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                          </select>
                          <?php endif; ?>
                        </td>
                        <td nowrap="nowrap">
                          <?php if ($this->_tpl_vars['currentUser']->checkRights('documents','edit_file') && $this->_tpl_vars['session']->get('currentUserId') == $this->_tpl_vars['file']->get('added_by') && ! $this->_tpl_vars['file']->get('not_exist') && ! $this->_tpl_vars['document']->get('archived_by')): ?>
                            <a class="material-icons<?php if ($this->_tpl_vars['modified']): ?> nz--disabled<?php endif; ?>"
                              href="javascript:void(0);"
                              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                              onclick="editFileBrowse(this, <?php echo $this->_tpl_vars['file']->get('id'); ?>
, '', '<?php echo $this->_tpl_vars['row_link']; ?>
')">
                              <?php echo $this->_tpl_vars['theme']->getIconForAction('edit'); ?>

                            </a>
                          <?php else: ?>
                            <span
                              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                              class="material-icons pointer nz--disabled">
                            <?php echo $this->_tpl_vars['theme']->getIconForAction('edit'); ?>

                          </span>
                          <?php endif; ?>
                        <?php if (! $this->_tpl_vars['file']->get('not_exist')): ?>
                          <a class="material-icons"
                            href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['document']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"
                            target="_blank"
                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                            <?php echo $this->_tpl_vars['theme']->getIconForAction('view'); ?>

                          </a>
                          <a class="material-icons"
                            href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['document']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"
                            download
                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                            <?php echo $this->_tpl_vars['theme']->getIconForAction('download'); ?>

                          </a>
                        <?php else: ?>
                          <span
                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                            class="material-icons pointer nz--disabled">
                            <?php echo $this->_tpl_vars['theme']->getIconForAction('view'); ?>

                          </span>
                          <span
                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                            class="material-icons pointer nz--disabled">
                            <?php echo $this->_tpl_vars['theme']->getIconForAction('download'); ?>

                          </span>
                        <?php endif; ?>
                          <?php if ($this->_tpl_vars['currentUser']->checkRights('documents','delete_file') && ( $this->_tpl_vars['session']->get('currentUserId') == $this->_tpl_vars['file']->get('added_by') ) && ! $this->_tpl_vars['file']->get('not_exist') && ! $this->_tpl_vars['document']->get('archived_by')): ?>
                            <a class="material-icons"
                              href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=delfile&amp;delfile=<?php echo $this->_tpl_vars['document']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
"
                              onclick="return confirmAction('delete_file', function(el) { window.location.href = el.href; }, this);"
                              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                              <?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>

                            </a>
                          <?php else: ?>
                            <span
                              title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                              class="material-icons pointer nz--disabled"
                              onclick="Nz.alert('<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
', '<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');">
                              <?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>

                            </span>
                          <?php endif; ?>
                          <?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['info']), $this);?>

                        </td>
                      </tr>
        <?php endforeach; else: ?>
                      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
                        <td class="error" colspan="7"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                      </tr>
        <?php endif; unset($_from); ?>
                      <tr>
                        <td class="error" colspan="7">&nbsp;</td>
                      </tr>
        <?php if (! $this->_tpl_vars['document']->get('archived_by')): ?>
                      <tr>
                        <td class="t_caption2" colspan="7"><div class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_add_new'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>
</div></td>
                      </tr>
                      <tr>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo $this->_config[0]['vars']['required']; ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                        <td class="t_caption3" colspan="2">
                          <div class="t_caption3_title floatl"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                          <div class="t_buttons">
                            <div id="plusButton" onclick="addFileBrowse('a_file_paths', 'a_file_names', 'a_file_descriptions', 'a_file_revisions', 'a_file_permissions', this)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                            <div id="minusButton"<?php if (count ( $this->_tpl_vars['added_files'] ) == 1 || count ( $this->_tpl_vars['erred_added_files'] ) == 1): ?> class="disabled"<?php endif; ?> onclick="removeFileBrowse(this)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
                          </div>
                        </td>
                      </tr>
        <?php $_from = $this->_tpl_vars['added_files']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['file']):
        $this->_foreach['j']['iteration']++;
?>
          <?php if (empty ( $this->_tpl_vars['file'] ) || $this->_tpl_vars['erred_added_files'] && in_array ( $this->_tpl_vars['idx'] , ((is_array($_tmp=@$this->_tpl_vars['erred_added_files'])) ? $this->_run_mod_handler('default', true, $_tmp, 'array') : smarty_modifier_default($_tmp, 'array')) ( ) )): ?>
            <?php $this->assign('aidx', $this->_tpl_vars['idx']+1); ?>
                      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! empty ( $this->_tpl_vars['file'] )): ?> t_deleted<?php endif; ?>">
                        <td class="t_border hright<?php if (! empty ( $this->_tpl_vars['file'] )): ?> error<?php endif; ?>" align="right"><?php echo $this->_tpl_vars['aidx']; ?>
.</td>
                        <td class="t_border">
                          <input type="file" class="filebox" name="a_file_paths[]" id="a_file_paths_<?php echo $this->_tpl_vars['aidx']; ?>
" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                        </td>
                        <td class="t_border">
                          <input type="text" name="a_file_names[]" id="a_file_names_<?php echo $this->_tpl_vars['aidx']; ?>
" value="<?php echo $this->_tpl_vars['file']['name']; ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px;" />
                        </td>
                        <td class="t_border">
                          <input type="text" name="a_file_descriptions[]" id="a_file_descriptions_<?php echo $this->_tpl_vars['aidx']; ?>
" value="<?php echo $this->_tpl_vars['file']['description']; ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 120px;" />
                        </td>
                        <td class="t_border">
                          <select class="selbox" name="a_file_revisions[]" id="a_file_revisions_<?php echo $this->_tpl_vars['aidx']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revisions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 50px;">
                            <option value=""><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_new_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                          </select>
                        </td>
                        <td colspan="2">
                          <select class="selbox" name="a_file_permissions[]" id="a_file_permissions_<?php echo $this->_tpl_vars['aidx']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px;">
                            <option value="all"<?php if ($this->_tpl_vars['file']['permission'] == 'all'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="group"<?php if ($this->_tpl_vars['file']['permission'] == 'group'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                            <option value="mine"<?php if ($this->_tpl_vars['file']['permission'] == 'mine'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                          </select>
                        </td>
                      </tr>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
        <?php endif; ?>
                    </table>
                    <div style="height:10px; border-top: 1px solid #CCCCCC"></div>
                  </td>
                </tr>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <?php if (! $this->_tpl_vars['document']->get('archived_by')): ?>
                <tr>
                  <td colspan="3">
                    <div class="nz-quick-buttons ">
                    <?php if ($this->_tpl_vars['action'] == 'ajax_edit'): ?>
                      <button type="button" name="saveButton1" class="nz-form-button nz-button-primary" onclick="ajaxForm('<?php echo $this->_tpl_vars['action']; ?>
', 'documents', this.form)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                      <button type="button" name="cancel" class="nz-form-button nz-button-cancel" onclick="confirmAction('cancel', function() { <?php if (! empty ( $this->_tpl_vars['document']->lockedByMe )): ?> unlockRecord({module: '<?php echo $this->_tpl_vars['module']; ?>
', controller: '<?php echo $this->_tpl_vars['controller']; ?>
', action: '<?php echo $this->_tpl_vars['action']; ?>
', unlock: '<?php echo $this->_tpl_vars['document']->lockedInfo['id']; ?>
', model_id: '<?php echo $this->_tpl_vars['document']->get('id'); ?>
', skip_message: true}); <?php endif; ?> lb.deactivate(); }, this);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                    <?php else: ?>
                      <button type="submit" name="saveButton1" class="nz-form-button nz-button-primary"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>
    </div>
  </div>
</div>