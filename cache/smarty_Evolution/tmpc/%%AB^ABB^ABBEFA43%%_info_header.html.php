<?php /* Smarty version 2.6.33, created on 2024-07-22 15:39:39
         compiled from /var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 5, false),array('function', 'popup', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 9, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 11, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 9, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 11, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 100, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 165, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_info_header.html', 165, false),)), $this); ?>
  <?php $_from = $this->_tpl_vars['project']->getLayoutsDetails(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
    <?php if ($this->_tpl_vars['layout']['info_header_visibility'] && $this->_tpl_vars['layout']['view']): ?>
      <?php if ($this->_tpl_vars['lkey'] == 'name'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
            <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
projects.png" class="t_info_image" alt="" title="" />
              <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

            </span>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['project']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['project']->get('trademark')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo $this->_tpl_vars['project']->get('trademark'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'code'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'num'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'manager'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('manager_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'status'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php ob_start(); ?>projects_status_<?php echo $this->_tpl_vars['project']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_name', ob_get_contents());ob_end_clean(); ?>
            <div style="height: 100%;" class="projects_status <?php echo $this->_tpl_vars['project']->get('status'); ?>
<?php if ($this->_tpl_vars['project']->get('status') == 'finished'): ?><?php if ($this->_tpl_vars['project']->get('finished') === '1'): ?>_success<?php elseif ($this->_tpl_vars['project']->get('finished') === '0'): ?>_failed<?php endif; ?><?php endif; ?>"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>

              <?php if ($this->_tpl_vars['project']->get('stage')): ?>
                - <?php echo smarty_function_mb_truncate_overlib(array('text' => $this->_tpl_vars['project']->get('stage_name'),'length' => 50), $this);?>

              <?php endif; ?>
              <?php if ($this->_tpl_vars['project']->get('status') != 'finished' && $this->_tpl_vars['layout']['edit']): ?>
                <?php if ($this->_tpl_vars['project']->checkStages()): ?>
                  <a style="white-space: nowrap;" href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=projects&amp;projects=phases&amp;phases=<?php echo $this->_tpl_vars['project']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['project_setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
                <?php elseif ($this->_tpl_vars['project']->checkPermissions('setstatus')): ?>
                  <a style="white-space: nowrap;" href="#" onclick="toggleActionOptions($('setstatus_action'));return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['project_setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
                <?php endif; ?>
              <?php endif; ?>
            </div>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'finished_project'): ?>
      <tr>
        <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
        <td class="unrequired">&nbsp;</td>
        <td>
          <?php if ($this->_tpl_vars['project']->get('finished') === '0'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_substatus_finished_failed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php elseif ($this->_tpl_vars['project']->get('finished') === '1'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_substatus_finished_success'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>&nbsp;<?php endif; ?>
        </td>
      </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_start'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('date_start'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_end'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['project']->get('date_end') != '0000-00-00 00:00:00'): ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('date_end'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'priority'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['project']->get('priority'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('priority_name', ob_get_contents());ob_end_clean(); ?>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['priority_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'parent_project'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['project']->get('parent_project')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['project']->get('parent_project'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('parent_project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('parent_project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'finished_part'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
              <?php echo $this->_tpl_vars['project']->get('finished_part'); ?>
 %
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'budget' && $this->_tpl_vars['project']->get('available_planned_budget')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('budget'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'work_period' && $this->_tpl_vars['project']->get('available_working_hours')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['project']->get('work_period'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['project']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php endif; ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>