<?php /* Smarty version 2.6.33, created on 2024-08-13 16:58:50
         compiled from /var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_events_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'math', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_events_panel.html', 11, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_events_panel.html', 11, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_events_panel.html', 15, false),array('modifier', 'trim', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_events_panel.html', 15, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_events_panel.html', 40, false),)), $this); ?>
<?php echo ''; ?><?php if ($this->_tpl_vars['events_for'] == 'tomorrow'): ?><?php echo ''; ?><?php $this->assign('events_label_var', 'calendars_events_tomorrow'); ?><?php echo ''; ?><?php $this->assign('day', $this->_tpl_vars['calendar']->getTomorrow()); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('events_label_var', 'calendars_events_today'); ?><?php echo ''; ?><?php $this->assign('day', $this->_tpl_vars['calendar']->getToday()); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->assign('max_visible_events', 3); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['events_for']; ?><?php echo '_events_box'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('events_box_var', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => 'x-y','x' => count($this->_tpl_vars['events']),'y' => $this->_tpl_vars['max_visible_events'],'assign' => 'remaining_events'), $this);?><?php echo ''; ?>


<div id="<?php echo $this->_tpl_vars['events_box_var']; ?>
" class="nz-events-panel<?php if (true || count ( $this->_tpl_vars['events'] ) > $this->_tpl_vars['max_visible_events']): ?> nz-events-panel__has-more<?php endif; ?>">
  <div class="nz-events-panel__head"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo ((is_array($_tmp=$this->_tpl_vars['day']->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short'])); ?>
"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['events_label_var']]; ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['day']->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)); ?>
)</a></div>
  <ul class="nz-events-panel--list">
    <?php $_from = $this->_tpl_vars['events']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['events_list'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['events_list']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['event']):
        $this->_foreach['events_list']['iteration']++;
?>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=$this->_tpl_vars['day']->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_short'])); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('event_label', ob_get_contents());ob_end_clean(); ?>
    <li class="nz-events-panel--list__item<?php if ($this->_foreach['events_list']['iteration'] > $this->_tpl_vars['max_visible_events']): ?> nz-events-panel--list__item--more<?php endif; ?>"><?php if ($this->_tpl_vars['private_event']): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_event_item_private.html", 'smarty_include_vars' => array('event' => $this->_tpl_vars['event'],'event_label' => $this->_tpl_vars['event_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php else: ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_event_item_side.html", 'smarty_include_vars' => array('event' => $this->_tpl_vars['event'],'event_label' => $this->_tpl_vars['event_label'],'calendar_settings' => $this->_tpl_vars['calendar_settings'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?></li>
    <?php endforeach; else: ?>
    <li><?php echo $this->_config[0]['vars']['calendars_no_events']; ?>
</li>
    <?php endif; unset($_from); ?>
  </ul>
  <?php if ($this->_foreach['events_list']['iteration'] > $this->_tpl_vars['max_visible_events']): ?>
  <div class="nz-events-panel--list__show-more nz-toggle nz-toggle-autoinit"
       data-toggle-target="#<?php echo $this->_tpl_vars['events_box_var']; ?>
"
       data-toggle-toggleClass="nz--opened"
       data-toggle-personalsettings-section="switch"
       data-toggle-personalsettings-name="<?php echo $this->_tpl_vars['events_box_var']; ?>
"
       data-toggle-personalsettings-value="1">
    <button type="button" class="nz-button nz-toggle__inactive"  title="<?php if ($this->_tpl_vars['remaining_events'] == 1): ?><?php echo $this->_config[0]['vars']['calendars_remaining_event']; ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars_remaining_events'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/\%s/', $this->_tpl_vars['remaining_events']) : smarty_modifier_regex_replace($_tmp, '/\%s/', $this->_tpl_vars['remaining_events'])); ?>
<?php endif; ?>"><i class="material-icons">expand_more</i><?php if ($this->_tpl_vars['remaining_events'] == 1): ?><?php echo $this->_config[0]['vars']['calendars_remaining_event']; ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars_remaining_events'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/\%s/', $this->_tpl_vars['remaining_events']) : smarty_modifier_regex_replace($_tmp, '/\%s/', $this->_tpl_vars['remaining_events'])); ?>
<?php endif; ?></button>
    <button type="button" class="nz-button nz-toggle__active" title="<?php if ($this->_tpl_vars['remaining_events'] == 1): ?><?php echo $this->_config[0]['vars']['calendars_remaining_event']; ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars_remaining_events'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/\%s/', $this->_tpl_vars['remaining_events']) : smarty_modifier_regex_replace($_tmp, '/\%s/', $this->_tpl_vars['remaining_events'])); ?>
<?php endif; ?>"><i class="material-icons">expand_less</i><?php if ($this->_tpl_vars['remaining_events'] == 1): ?><?php echo $this->_config[0]['vars']['calendars_remaining_event']; ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars_remaining_events'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/\%s/', $this->_tpl_vars['remaining_events']) : smarty_modifier_regex_replace($_tmp, '/\%s/', $this->_tpl_vars['remaining_events'])); ?>
<?php endif; ?></button>
  </div>
  <?php endif; ?>
</div>