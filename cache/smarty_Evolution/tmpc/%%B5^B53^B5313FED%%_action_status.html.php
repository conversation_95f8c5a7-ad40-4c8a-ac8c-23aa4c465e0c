<?php /* Smarty version 2.6.33, created on 2024-07-22 15:38:48
         compiled from /var/www/Nzoom-Evolution/_libs/modules/projects/templates/_action_status.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_action_status.html', 2, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_action_status.html', 34, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_action_status.html', 28, false),array('function', 'uniqid', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_action_status.html', 58, false),)), $this); ?>
<?php if ($this->_tpl_vars['available_action']['show_form']): ?>
  <form method="<?php echo ((is_array($_tmp=@$this->_tpl_vars['available_action']['options']['form_method'])) ? $this->_run_mod_handler('default', true, $_tmp, 'get') : smarty_modifier_default($_tmp, 'get')); ?>
" action="<?php echo $_SERVER['PHP_SELF']; ?>
" id="<?php echo $this->_tpl_vars['available_action']['action']; ?>
_form">
    <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
    <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" />
    <?php if ($this->_tpl_vars['available_action']['model_id']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['model_id']; ?>
" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['model_lang']): ?>
      <input type="hidden" name="model_lang" value="<?php echo $this->_tpl_vars['available_action']['model_lang']; ?>
" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['name'] == 'search' || $this->_tpl_vars['available_action']['name'] == 'filter'): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['session_param']; ?>
" value="1" />
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_module" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_controller" value="<?php echo $this->_tpl_vars['available_action']['controller']; ?>
" />
      <?php if ($this->_tpl_vars['event']): ?>
      <input type="hidden" name="event" value="<?php echo $this->_tpl_vars['event']; ?>
" />
      <?php endif; ?>
      <?php if ($this->_tpl_vars['form_name']): ?>
      <input type="hidden" name="form_name" value="<?php echo $this->_tpl_vars['form_name']; ?>
" />
      <?php endif; ?>
    <?php endif; ?>
    <?php $this->assign('lb_suffix', '_'); ?>
<?php endif; ?>

  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <?php if (! $this->_tpl_vars['hide_status_label']): ?>
        <td class="labelbox"><a name="error_status"><label for="status"<?php if ($this->_tpl_vars['messages']->getErrors('status')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'status'), $this);?>
</label></a></td>
        <td>&nbsp;</td>
      <?php endif; ?>
      <td class="databox" nowrap="nowrap" style="vertical-align: top;">
        <?php ob_start(); ?><?php echo $this->_tpl_vars['project']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_status', ob_get_contents());ob_end_clean(); ?>
        <input type="hidden" name="current_status_base" value="<?php echo $this->_tpl_vars['current_status']; ?>
" id="current_status_base" class="current_status_base" />
        <input type="radio" name="status" id="status_planning<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status_planning" onclick="if (! validateProjectStatusChange(this)) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="planning"<?php if ($this->_tpl_vars['project']->get('status') == 'planning' || ! $this->_tpl_vars['project']->get('status')): ?> checked="checked"<?php endif; ?> />
        <label for="status_planning<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="projects_status planning"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_status_planning'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
        <input type="radio" name="status" id="status_progress<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status_progress" onclick="if (! validateProjectStatusChange(this)) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="progress"<?php if ($this->_tpl_vars['project']->get('status') == 'progress'): ?> checked="checked"<?php endif; ?> />
        <label for="status_progress<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="projects_status progress"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_status_progress'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
        <input type="radio" name="status" id="status_control<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status_control" onclick="if (! validateProjectStatusChange(this)) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="control"<?php if ($this->_tpl_vars['project']->get('status') == 'control'): ?> checked="checked"<?php endif; ?> />
        <label for="status_control<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="projects_status control"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_status_control'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
        <input type="radio" name="status" id="status_finished<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status_finished" onclick="if (! validateProjectStatusChange(this)) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="finished"<?php if ($this->_tpl_vars['project']->get('status') == 'finished'): ?> checked="checked"<?php endif; ?> />
        <label for="status_finished<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="projects_status finished"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_status_finished'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
        <blockquote id="substatus_finished" class="block_quote_status substatus_finished">
          <input id="checked_finished_substatus" name="checked_finished_substatus" class="checked_finished_substatus" value="<?php echo $this->_tpl_vars['project']->get('finished'); ?>
" type="hidden" />
          <input type="radio" name="substatus" id="substatus_1<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="substatus_1" value="1" onclick="validateProjectsSubstatusChange(this)"<?php if ($this->_tpl_vars['project']->get('finished') === '1'): ?> checked="checked"<?php endif; ?> /><label for="substatus_1<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="projects_status finished_success"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_substatus_finished_success'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
          <input type="radio" name="substatus" id="substatus_0<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="substatus_0" value="0" onclick="validateProjectsSubstatusChange(this)"<?php if ($this->_tpl_vars['project']->get('finished') === '0'): ?> checked="checked"<?php endif; ?> /><label for="substatus_0<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="projects_status finished_failed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_substatus_finished_failed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
        </blockquote>
      </td>
      <td align="right" style="vertical-align: top;">
        <table>
          <tr>
            <td class="labelbox"><a name="error_comment"><label for="comment<?php echo $this->_tpl_vars['lb_suffix']; ?>
"<?php if ($this->_tpl_vars['messages']->getErrors('comment')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'comment'), $this);?>
</label></a></td>
          </tr>
          <tr>
            <td>
              <textarea class="areabox comment" name="comment" id="comment<?php echo $this->_tpl_vars['lb_suffix']; ?>
" style="height: 80px;"></textarea>
              <?php if ($this->_tpl_vars['include_portal_users_option'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>
                <br />
                <?php ob_start(); ?>_<?php echo smarty_function_uniqid(array(), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal_suffix', ob_get_contents());ob_end_clean(); ?>
                <input type="radio" name="is_portal" id="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! isset ( $this->_tpl_vars['available_action']['default_portal_comment'] ) || $this->_tpl_vars['available_action']['default_portal_comment']): ?> checked="checked"<?php endif; ?> /><label for="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                <input type="radio" name="is_portal" id="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (isset ( $this->_tpl_vars['available_action']['default_portal_comment'] ) && ! $this->_tpl_vars['available_action']['default_portal_comment']): ?> checked="checked"<?php endif; ?> /><label for="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
              <?php endif; ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="<?php if (! $this->_tpl_vars['hide_status_label']): ?>4<?php else: ?>2<?php endif; ?>">
        <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
"<?php if ($this->_tpl_vars['available_action']['confirm']): ?> onclick="return confirmAction('<?php echo $this->_tpl_vars['available_action']['name']; ?>
', submitForm, this);"<?php endif; ?>><?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
</button>
      </td>
    </tr>
  </table>

<?php if ($this->_tpl_vars['available_action']['show_form']): ?>
  </form>
<?php endif; ?>