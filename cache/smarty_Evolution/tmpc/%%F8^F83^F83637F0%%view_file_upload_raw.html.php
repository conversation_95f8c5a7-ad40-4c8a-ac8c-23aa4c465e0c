<?php /* Smarty version 2.6.33, created on 2024-09-25 15:42:33
         compiled from view_file_upload_raw.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'view_file_upload_raw.html', 5, false),array('modifier', 'encrypt', 'view_file_upload_raw.html', 14, false),array('function', 'getimagesize', 'view_file_upload_raw.html', 12, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?>
  <?php $this->assign('iconName', $this->_tpl_vars['file_info']->getIconName()); ?>
  <?php if ($this->_tpl_vars['file_info']->get('not_exist')): ?>
    <?php if ($this->_tpl_vars['iconName'] === 'attachments'): ?>
      <span class="material-icons nz-input-icon nz--disabled" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" ><?php echo $this->_tpl_vars['theme']->getIconForAction('attachments'); ?>
</span>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['iconName']; ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="nz-input-icon nz--disabled" />
    <?php endif; ?>
    <span class="material-icons nz-input-icon nz--disabled" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" ><?php echo $this->_tpl_vars['theme']->getIconForAction('download'); ?>
</span>
  <?php else: ?>
    <?php if ($this->_tpl_vars['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
      <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

      <img class="nz--clickable"
        src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?><?php if ($this->_tpl_vars['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['thumb_height']; ?>
<?php endif; ?>"
        onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['file_info']->get('model_id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '<?php echo $this->_tpl_vars['label']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')" alt="" />
    <?php else: ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['file_info']->get('model_id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"
         title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
         target="_blank"
         class="nz-input-icon <?php echo ''; ?><?php if ($this->_tpl_vars['iconName'] === 'attachments'): ?><?php echo 'nz-icon-button">'; ?><?php echo $this->_tpl_vars['theme']->getIconForAction('attachments'); ?><?php echo ''; ?><?php else: ?><?php echo '"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['iconName']; ?><?php echo '.png" class="nz-input-icon" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php endif; ?><?php echo ''; ?>
</a>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['file_info']->get('model_id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"
         download
         title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
         class="nz-input-icon nz-icon-button">
        <?php echo $this->_tpl_vars['theme']->getIconForAction('download'); ?>

      </a>
    <?php endif; ?>
  <?php endif; ?>
<?php else: ?>
  &nbsp;
<?php endif; ?>