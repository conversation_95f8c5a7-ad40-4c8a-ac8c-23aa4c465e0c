<?php /* Smarty version 2.6.33, created on 2024-07-04 18:39:16
         compiled from _gt2_view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_gt2_view.html', 14, false),array('modifier', 'date_format', '_gt2_view.html', 57, false),array('modifier', 'default', '_gt2_view.html', 66, false),array('modifier', 'encrypt', '_gt2_view.html', 77, false),array('modifier', 'nl2br', '_gt2_view.html', 95, false),array('modifier', 'inwords', '_gt2_view.html', 232, false),array('function', 'help', '_gt2_view.html', 21, false),array('function', 'counter', '_gt2_view.html', 26, false),array('function', 'getimagesize', '_gt2_view.html', 76, false),)), $this); ?>
  <?php if (empty ( $this->_tpl_vars['table'] )): ?>
    <?php $this->assign('table', $this->_tpl_vars['model']->get('grouping_table_2')); ?>
  <?php endif; ?>

  <?php if (trim ( $this->_tpl_vars['table']['label'] ) != '' && ! $this->_tpl_vars['hide_label']): ?>
    <div class="t_caption2_title" style="float: left"><?php echo $this->_tpl_vars['table']['label']; ?>
</div>
    <div class="clear"></div>
  <?php endif; ?>
  <div class="nz-view-wrapper">
<input type="hidden" name="calc_price" id="calc_price" value="<?php echo $this->_tpl_vars['table']['calculated_price']; ?>
" />
<table class="t_grouping_table grouping_table2 gt2view" style="<?php if ($this->_tpl_vars['table']['width']): ?>width:<?php echo $this->_tpl_vars['table']['width']; ?>
<?php if (preg_match ( '#^\d+$#' , $this->_tpl_vars['table']['width'] )): ?>px<?php endif; ?>;<?php endif; ?>"<?php if ($this->_tpl_vars['table']['bb']): ?> id="var_gt2_<?php echo $this->_tpl_vars['table']['id']; ?>
"<?php endif; ?>>
<tr>
  <?php if ($this->_tpl_vars['transform']): ?>
    <th width="15"><input type="checkbox" name="checkall_<?php echo $this->_tpl_vars['table']['type']; ?>
_<?php echo $this->_tpl_vars['table']['new_type']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" checked="checked" onclick="toggleCheckboxes(this,'items_<?php echo $this->_tpl_vars['table']['type']; ?>
_<?php echo $this->_tpl_vars['table']['new_type']; ?>
', this.checked)" /></th>
  <?php endif; ?>
  <th width="20" style="text-align:right"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
  <?php $_from = $this->_tpl_vars['table']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo $this->_tpl_vars['var']['help']; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['label']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
    <th style="<?php if ($this->_tpl_vars['var']['hidden']): ?>display: none;<?php endif; ?><?php if ($this->_tpl_vars['var']['width']): ?>width: <?php echo $this->_tpl_vars['var']['width']; ?>
px;<?php endif; ?>">
      <?php if (! $this->_tpl_vars['var']['hidden'] && trim ( $this->_tpl_vars['var']['label'] ) != ''): ?>
        <?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['info'],'label_sufix' => ''), $this);?>
<?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?>
      <?php endif; ?>
    </th>
  <?php endforeach; endif; unset($_from); ?>
</tr>
<?php echo smarty_function_counter(array('assign' => 'cols_count','print' => false,'name' => 'colcount','start' => 0), $this);?>

<?php $_from = $this->_tpl_vars['table']['values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['row'] => $this->_tpl_vars['val']):
        $this->_foreach['i']['iteration']++;
?>
<tr>
  <?php if ($this->_tpl_vars['transform']): ?>
    <td width="15"><input onclick="" type="checkbox" name="items_<?php echo $this->_tpl_vars['table']['type']; ?>
_<?php echo $this->_tpl_vars['table']['new_type']; ?>
[]" value="<?php echo $this->_tpl_vars['row']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" checked="checked" /></td>
  <?php endif; ?>
  <td style="text-align:right" nowrap="nowrap"><?php echo $this->_foreach['i']['iteration']; ?>
</td>
  <?php $_from = $this->_tpl_vars['table']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
      <?php if (($this->_foreach['i']['iteration'] <= 1) && ( ! $this->_tpl_vars['var']['hidden'] || $this->_tpl_vars['var']['hidden'] === '0' || $this->_tpl_vars['var']['hidden'] === 0 )): ?>
        <?php echo smarty_function_counter(array('assign' => 'cols_count','print' => false,'name' => 'colcount'), $this);?>

      <?php endif; ?>
      <?php if (($this->_foreach['i']['iteration'] <= 1) && ! empty ( $this->_tpl_vars['var']['agregate'] )): ?>
        <?php echo smarty_function_counter(array('assign' => 'agregates_count','print' => false,'name' => 'agregatescount'), $this);?>

      <?php endif; ?>
      <td class="<?php echo $this->_tpl_vars['var']['name']; ?>
" style="<?php if ($this->_tpl_vars['var']['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['var']['text_align']): ?> text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>" title="<?php echo $this->_tpl_vars['var']['label']; ?>
">
        <?php ob_start(); ?><?php echo $this->_tpl_vars['key']; ?>
_formula<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('formula_key', ob_get_contents());ob_end_clean(); ?>
        <?php echo ''; ?><?php if ($this->_tpl_vars['var']['type'] == 'formula' && $this->_tpl_vars['val'][$this->_tpl_vars['key']] == 0 && $this->_tpl_vars['val'][$this->_tpl_vars['formula_key']]): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['formulas']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['val'][$this->_tpl_vars['formula_key']]): ?><?php echo ''; ?><?php echo $this->_tpl_vars['formula']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] == 'index'): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['key']; ?><?php echo '_date'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('date_key', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['indexes']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['index']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['index']['option_value'] == $this->_tpl_vars['val'][$this->_tpl_vars['key']]): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']['label']; ?><?php echo '<br />'; ?><?php if (( ! $this->_tpl_vars['val'][$this->_tpl_vars['date_key']] || $this->_tpl_vars['val'][$this->_tpl_vars['date_key']] == '0000-00-00' ) && $this->_tpl_vars['val'][$this->_tpl_vars['formula_key']]): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['formulas']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['val'][$this->_tpl_vars['formula_key']] == $this->_tpl_vars['formula']['option_value']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['formula']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['date_key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php elseif (in_array ( $this->_tpl_vars['var']['type'] , array ( 'dropdown' , 'radio' ) )): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] == 'date' && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] == 'datetime' && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] == 'time' && $this->_tpl_vars['val'][$this->_tpl_vars['key']] != 0): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] == 'file_upload'): ?><?php echo ''; ?><?php $this->assign('file_info', $this->_tpl_vars['val'][$this->_tpl_vars['key']]); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?><?php echo ''; ?><?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?><?php echo ''; ?><?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?><?php echo '<img src="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '=files&amp;files=viewfile&amp;viewfile='; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['thumb_width']): ?><?php echo '&amp;maxwidth='; ?><?php echo $this->_tpl_vars['var']['thumb_width']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['thumb_height']): ?><?php echo '&amp;maxheight='; ?><?php echo $this->_tpl_vars['var']['thumb_height']; ?><?php echo ''; ?><?php endif; ?><?php echo '" onclick="showFullLBImage(\''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['width']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['image_dimensions']['height']; ?><?php echo '\')" alt="" />'; ?><?php else: ?><?php echo '<a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '" target="_blank"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a><a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=getfile&amp;getfile='; ?><?php echo $this->_tpl_vars['var']['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file_info']->get('id'); ?><?php echo '"><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" /></a>'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['file_info']->getIconName(); ?><?php echo '.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" /><img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'download.png" width="16" height="16" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" class="pointer dimmed" />'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['var']['type'] == 'autocompleter'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['val'][$this->_tpl_vars['key']],'value_id' => $this->_tpl_vars['val'][$this->_tpl_vars['var']['autocomplete']['id_var']],'view_mode_url' => $this->_tpl_vars['var']['autocomplete']['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['val'][$this->_tpl_vars['key']] || $this->_tpl_vars['val'][$this->_tpl_vars['key']] === '0' || $this->_tpl_vars['val'][$this->_tpl_vars['formula_key']] ) && $this->_tpl_vars['var']['back_label']): ?><?php echo '&nbsp;'; ?><?php echo $this->_tpl_vars['var']['back_label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

      </td>
  <?php endforeach; endif; unset($_from); ?>
</tr>
<?php if ($this->_tpl_vars['val']['has_batch']): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_batch_view.html", 'smarty_include_vars' => array('idx' => $this->_foreach['i']['iteration'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
<tr style="display:none"><td></td></tr>
<?php if (! $this->_tpl_vars['table']['hide_agregates']): ?>
  <?php if ($this->_tpl_vars['agregates_count'] > 0): ?>
    <tr class="gt2_agregates">
      <?php if ($this->_tpl_vars['transform']): ?><td>&nbsp;</td><?php endif; ?>
      <td>&nbsp;</td>
    <?php $_from = $this->_tpl_vars['table']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
      <td style="white-space:nowrap!important;<?php if ($this->_tpl_vars['var']['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['var']['text_align']): ?> text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>"><?php echo ''; ?><?php if ($this->_tpl_vars['var']['agregate']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['name']; ?><?php echo '_agregate'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ag_name', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['agregate']): ?><?php echo 'agregate'; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ag_class', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('hidden' => 1,'custom_class' => 'agregate','name' => $this->_tpl_vars['ag_name'],'value' => $this->_tpl_vars['var']['agregate'],'standalone' => true,'width' => $this->_tpl_vars['var']['width'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'gt2_tagregates_'; ?><?php echo $this->_tpl_vars['var']['agregate']; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ag_text', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo $this->_config[0]['vars'][$this->_tpl_vars['ag_text']]; ?><?php echo ': <span class="'; ?><?php echo $this->_tpl_vars['var']['name']; ?><?php echo '_ag_content"></span>'; ?><?php else: ?><?php echo '&nbsp;'; ?><?php endif; ?><?php echo ''; ?>
</td>
    <?php endforeach; endif; unset($_from); ?>
    </tr>
  <?php endif; ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['table']['totals_texts_colspan']): ?>
<?php $this->assign('totals_texts_colspan', $this->_tpl_vars['table']['totals_texts_colspan']); ?>
<?php else: ?>
<?php $this->assign('totals_texts_colspan', 3); ?>
<?php endif; ?>
<?php $this->assign('totals_colspan', $this->_tpl_vars['cols_count']-$this->_tpl_vars['totals_texts_colspan']); ?>
<?php if ($this->_tpl_vars['transform']): ?><?php $this->assign('totals_colspan', $this->_tpl_vars['totals_colspan']+1); ?><?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_without_discount']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_without_discount'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_discount_percentage']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_discount_percentage'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_discount_value']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_discount_value'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_surplus_percentage']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_surplus_percentage'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_surplus_value']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_surplus_value'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_vat_rate']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">
  <?php if ($this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text'] && ! $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['hidden']): ?>
    <?php echo $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['label']; ?>
:
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['table']['plain_values']['total_no_vat_reason_text'],'value_id' => $this->_tpl_vars['table']['plain_values']['total_no_vat_reason'],'view_mode_url' => $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['autocomplete']['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php if ($this->_tpl_vars['table']['plain_values']['total_no_vat_reason_text'] && $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['back_label']): ?> <?php echo $this->_tpl_vars['table']['plain_vars']['total_no_vat_reason_text']['back_label']; ?>
<?php endif; ?>
  <?php else: ?>
    &nbsp;
  <?php endif; ?>
  </td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo $this->_tpl_vars['table']['plain_values']['total_vat_rate']; ?>
 %<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_vat']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">
    <?php if ($this->_tpl_vars['table']['show_totals_inwords']): ?><?php echo $this->_tpl_vars['var']['label']; ?>
 (<?php echo $this->_config[0]['vars']['in_words']; ?>
): <?php echo ((is_array($_tmp=$this->_tpl_vars['table']['plain_values']['total_vat'])) ? $this->_run_mod_handler('inwords', true, $_tmp, $this->_tpl_vars['table']['plain_values']['currency'], $this->_tpl_vars['lang']) : smarty_modifier_inwords($_tmp, $this->_tpl_vars['table']['plain_values']['currency'], $this->_tpl_vars['lang'])); ?>
<?php endif; ?>
  </td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_vat'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['total_with_vat']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">
    <?php if ($this->_tpl_vars['table']['show_totals_inwords']): ?><?php echo $this->_tpl_vars['var']['label']; ?>
 (<?php echo $this->_config[0]['vars']['in_words']; ?>
): <?php echo ((is_array($_tmp=$this->_tpl_vars['table']['plain_values']['total_with_vat'])) ? $this->_run_mod_handler('inwords', true, $_tmp, $this->_tpl_vars['table']['plain_values']['currency'], $this->_tpl_vars['lang']) : smarty_modifier_inwords($_tmp, $this->_tpl_vars['table']['plain_values']['currency'], $this->_tpl_vars['lang'])); ?>
<?php endif; ?>
  </td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['table']['plain_values']['total_with_vat'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>

<?php $this->assign('var', $this->_tpl_vars['table']['plain_vars']['currency']); ?>
<?php if ($this->_tpl_vars['var']): ?>
<tr<?php if ($this->_tpl_vars['table']['hide_totals'] || $this->_tpl_vars['var']['hidden']): ?> style="display:none"<?php endif; ?>>
  <td <?php if ($this->_tpl_vars['totals_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_colspan']; ?>
"<?php endif; ?> style="border:none">&nbsp;</td>
  <td<?php if ($this->_tpl_vars['totals_texts_colspan']): ?> colspan="<?php echo $this->_tpl_vars['totals_texts_colspan']; ?>
"<?php endif; ?> style="text-align:right"><?php echo $this->_tpl_vars['var']['label']; ?>
</td>
  <td style="<?php if ($this->_tpl_vars['var']['text_align']): ?>text-align:<?php echo $this->_tpl_vars['var']['text_align']; ?>
<?php endif; ?>">
    <?php echo $this->_tpl_vars['table']['plain_values']['currency']; ?>
<?php if ($this->_tpl_vars['var']['back_label']): ?> <?php echo $this->_tpl_vars['var']['back_label']; ?>
<?php endif; ?>
  </td>
</tr>
<?php endif; ?>
</table>
  </div>
<?php if (! $this->_tpl_vars['hide_script']): ?>
<script type="text/javascript">
    if (typeof(gt2calc) == 'function') {
        gt2calc('agregates'<?php if ($this->_tpl_vars['table']['bb']): ?>, 'var_gt2_<?php echo $this->_tpl_vars['table']['id']; ?>
'<?php endif; ?>);
    }
</script>
<?php endif; ?>