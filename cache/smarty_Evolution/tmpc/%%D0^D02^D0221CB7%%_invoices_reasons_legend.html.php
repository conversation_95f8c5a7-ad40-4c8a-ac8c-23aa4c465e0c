<?php /* Smarty version 2.6.33, created on 2024-07-04 18:35:13
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_invoices_reasons_legend.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_invoices_reasons_legend.html', 6, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_invoices_reasons_legend.html', 13, false),)), $this); ?>
  <tr>
    <td>
      <table border="0" cellspacing="5">
        <tr>
          <td colspan="2">
            <strong><u><?php echo ((is_array($_tmp=$this->_config[0]['vars']['legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</u></strong>
          </td>
        </tr>
        <?php $_from = $this->_tpl_vars['background_colors']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['bc'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['bc']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['bg_color_k'] => $this->_tpl_vars['bg_color']):
        $this->_foreach['bc']['iteration']++;
?>
          <?php ob_start(); ?>finance_incomes_reasons_<?php echo $this->_tpl_vars['bg_color_k']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_lang_legend', ob_get_contents());ob_end_clean(); ?>
          <tr>
            <td style="width: 20px; background-color: #<?php echo $this->_tpl_vars['bg_color']; ?>
;"></td>
            <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['current_lang_legend']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          </tr>
        <?php endforeach; endif; unset($_from); ?>
      </table>
    </td>
  </tr>