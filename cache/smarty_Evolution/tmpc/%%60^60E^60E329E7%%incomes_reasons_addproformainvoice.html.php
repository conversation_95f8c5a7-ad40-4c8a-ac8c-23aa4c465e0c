<?php /* Smarty version 2.6.33, created on 2024-10-08 18:27:22
         compiled from /var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 13, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 160, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 206, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 206, false),array('modifier', 'indent', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 221, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 36, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/incomes_reasons_addproformainvoice.html', 42, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

      <form name="finance" enctype="multipart/form-data" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" onsubmit="return gt2calc('submit_gt2');">
      <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['finance_incomes_reason']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
      <input type="hidden" name="link_to" id="link_to" value="<?php echo $this->_tpl_vars['finance_incomes_reason']->get('id'); ?>
" />
      <input type="hidden" name="link_to_model_name" id="link_to_model_name" value="<?php echo $this->_tpl_vars['finance_incomes_reason']->modelName; ?>
" />
      <input type="hidden" name="type" id="type" value="<?php echo @PH_FINANCE_TYPE_PRO_INVOICE; ?>
" />
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td>
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <?php $_from = $this->_tpl_vars['finance_incomes_reason']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

              <tr<?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                <td colspan="3" class="t_caption3 pointer">
                  <div class="floatr index_arrow_anchor">
                    <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                  </div>
                  <div class="layout_switch" onclick="toggleViewLayouts(this)" id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch">
                    <a name="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                  </div>
                </td>
              </tr>

              <?php if ($this->_tpl_vars['lkey'] == 'name'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                  <?php else: ?>
                    <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

                    <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['finance_incomes_reason']->get('customer'),'value_code' => $this->_tpl_vars['finance_incomes_reason']->get('customer_code'),'value_name' => $this->_tpl_vars['finance_incomes_reason']->get('customer_name'),'readonly' => 1,'width' => 200,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['finance_incomes_reason']->get('trademark'),'value_name' => $this->_tpl_vars['finance_incomes_reason']->get('trademark_name'),'readonly' => 1,'width' => 200,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['finance_incomes_reason']->get('project'),'value_code' => $this->_tpl_vars['finance_incomes_reason']->get('project_code'),'value_name' => $this->_tpl_vars['finance_incomes_reason']->get('project_name'),'readonly' => 1,'width' => 200,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php if ($this->_tpl_vars['finance_incomes_reason']->get('phase')): ?> <span class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['finance_incomes_reasons_phase']), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('phase_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                  <input type="hidden" name="phase" id="phase" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['finance_incomes_reason']->get('phase'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'company_data'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_company_data"><label for="company_data"<?php if ($this->_tpl_vars['messages']->getErrors('company_data')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['finance_incomes_reason']->get('company_data') && $this->_tpl_vars['finance_incomes_reason']->get('company_data') != '0_0_0_0'): ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('company_data'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('company_data_value', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'company_data','index' => 0,'optgroups' => $this->_tpl_vars['companies_data'],'value' => $this->_tpl_vars['company_data_value'],'required' => 1,'really_required' => 1,'readonly' => 0,'hidden' => 0,'width' => 200,'label' => $this->_tpl_vars['layout']['name'],'onchange' => "updateAvailableQuantities(this.value);",'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php if ($this->_tpl_vars['finance_incomes_reason']->get('company_data')): ?>
                      <?php $_from = $this->_tpl_vars['companies_data']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['company_name'] => $this->_tpl_vars['company_data']):
?>
                        <?php $_from = $this->_tpl_vars['companies_data'][$this->_tpl_vars['company_name']]; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['data']):
?>
                          <?php if ($this->_tpl_vars['data']['option_value'] == $this->_tpl_vars['finance_incomes_reason']->get('company_data')): ?><span class="strong"><?php echo ((is_array($_tmp=$this->_tpl_vars['company_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['data']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                    <input type="hidden" name="company_data" id="company_data" value="<?php echo $this->_tpl_vars['finance_incomes_reason']->get('company_data'); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'issue_date'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_issue_date"><label for="issue_date"<?php if ($this->_tpl_vars['messages']->getErrors('issue_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                <?php ob_start(); ?>finance_incomes_reasons<?php echo @PH_FINANCE_TYPE_PRO_INVOICE; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('right_invoice', ob_get_contents());ob_end_clean(); ?>
                <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'issue_date') || $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'future_issue_date')): ?>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'issue_date')): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('disallow_date_before', ob_get_contents());ob_end_clean(); ?>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'future_issue_date')): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('disallow_date_after', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'issue_date','value' => $this->_tpl_vars['finance_incomes_reason']->get('issue_date'),'width' => 200,'show_calendar_icon' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('issue_date'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <input type="hidden" name="issue_date" id="issue_date" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('issue_date'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                <?php else: ?>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_incomes_reason']->get('issue_date'))) ? $this->_run_mod_handler('default', true, $_tmp, time()) : smarty_modifier_default($_tmp, time())))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'date_of_payment'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_date_of_payment"><label for="date_of_payment"<?php if ($this->_tpl_vars['messages']->getErrors('date_of_payment')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'date_of_payment_count','required' => 1,'restrict' => 'insertOnlyDigits','value' => $this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_count'),'width' => 80)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <select name="date_of_payment_period_type" id="date_of_payment_period_type" class="selbox short configurable" onfocus="highlight(this)" onblur="unhighlight(this)">
                      <option value="working"<?php if ($this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_period_type') == 'working'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['working_days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                      <option value="calendar"<?php if ($this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_period_type') == 'calendar'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendar_days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                    </select>
                    <?php echo ((is_array($_tmp=$this->_config[0]['vars']['days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <select name="date_of_payment_point" id="date_of_payment_point" class="selbox short configurable" onfocus="highlight(this)" onblur="unhighlight(this)">
                      <option value="issue"<?php if ($this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_point') == 'issue'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['after_issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                      <option value="receive"<?php if ($this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_point') == 'receive'): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['after_receive'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
                    </select>
                  <?php else: ?>
                    <?php echo $this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_count'); ?>
 <?php if ($this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_period_type') == 'working'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['working_days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendar_days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['days'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php if ($this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_point') == 'issue'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['after_issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['after_receive'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                    <input type="hidden" name="date_of_payment_count" id="date_of_payment_count" value="<?php echo $this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_count'); ?>
" />
                    <input type="hidden" name="date_of_payment_period_type" id="date_of_payment_period_type" value="<?php echo $this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_period_type'); ?>
" />
                    <input type="hidden" name="date_of_payment_point" id="date_of_payment_point" value="<?php echo $this->_tpl_vars['finance_incomes_reason']->get('date_of_payment_point'); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="description" id="description" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_department"><label for="department"<?php if ($this->_tpl_vars['messages']->getErrors('department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <select class="selbox<?php if (! $this->_tpl_vars['finance_incomes_reason']->get('department')): ?> undefined<?php endif; ?>" name="department" id="department" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                      <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                      <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                        <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['finance_incomes_reason']->get('department')): ?>
                        <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['finance_incomes_reason']->get('department')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?>*&nbsp;<?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    </select>
                  <?php else: ?>
                    <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                      <?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['finance_incomes_reason']->get('department')): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>

                        <input type="hidden" name="department" id="department" value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
" />
                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_employee"><label for="employee"<?php if ($this->_tpl_vars['messages']->getErrors('employee')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employee','autocomplete_type' => 'customers','stop_customer_details' => 1,'autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['finance_incomes_reason']->get('employee'),'value_name' => $this->_tpl_vars['finance_incomes_reason']->get('employee_name'),'filters_array' => $this->_tpl_vars['autocomplete_employee_filters'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('employee_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <input type="hidden" name="employee" id="employee" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['finance_incomes_reason']->get('employee'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'fin_field_1'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_fin_field_1"><label for="fin_field_1"<?php if ($this->_tpl_vars['messages']->getErrors('fin_field_1')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'fin_field_1','value' => $this->_tpl_vars['finance_incomes_reason']->get('fin_field_1'),'standalone' => true,'width' => 200,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('fin_field_1'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="fin_field_1" id="fin_field_1" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('fin_field_1'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'fin_field_2'): ?>
              <tr id="finance_incomes_reason_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_fin_field_2"><label for="fin_field_2"<?php if ($this->_tpl_vars['messages']->getErrors('fin_field_2')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_textarea.html", 'smarty_include_vars' => array('name' => 'fin_field_2','value' => $this->_tpl_vars['finance_incomes_reason']->get('fin_field_2'),'standalone' => true,'width' => 200,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('fin_field_2'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="fin_field_2" id="fin_field_2" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_incomes_reason']->get('fin_field_2'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
              <tr>
                <td colspan="3" style="padding: 5px;">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_edit.html", 'smarty_include_vars' => array('model' => $this->_tpl_vars['finance_incomes_reason'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <script type="text/javascript">
                    onReady().then(() => {preventCorrectionColisions(null, true);});
                  </script>
                </td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 15px;">
                  <?php echo '<input type="hidden" name="finance_after_action" id="finance_after_action" value="" /><input type="hidden" name="submit_gt2" id="submit_gt2" /><input type="hidden" name="payment_date" id="payment_date" value="'; ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('payment_date'); ?><?php echo '" /><input type="hidden" name="payment_container" id="payment_container" value="'; ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('payment_container'); ?><?php echo '" /><input type="hidden" name="payment_container_rate" id="payment_container_rate" value="'; ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('container_rate'); ?><?php echo '" /><input type="hidden" name="payment_container_amount" id="payment_container_amount" value="'; ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('container_amount'); ?><?php echo '" /><input type="hidden" name="payment_container_currency" id="payment_container_currency" value="'; ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('container_currency'); ?><?php echo '" /><input type="hidden" name="payment_reason" id="payment_reason" value="'; ?><?php echo $this->_tpl_vars['finance_incomes_reason']->get('payment_reason'); ?><?php echo '" />'; ?><?php $_from = $this->_tpl_vars['after_action_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ai'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ai']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ak'] => $this->_tpl_vars['fin_action']):
        $this->_foreach['ai']['iteration']++;
?><?php echo '<button type="submit" name="saveButton1" id="submit_gt2_'; ?><?php echo $this->_tpl_vars['ak']; ?><?php echo '" class="button" onclick="'; ?><?php if ($this->_tpl_vars['fin_action']['option_value'] == 'payment'): ?><?php echo 'completeReasonsPaymentData(this, \'\'); return false;'; ?><?php else: ?><?php echo '$(\'finance_after_action\').value=\''; ?><?php echo $this->_tpl_vars['fin_action']['option_value']; ?><?php echo '\';'; ?><?php endif; ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['fin_action']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['fin_action']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>

                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['finance_incomes_reason'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>
      </div>
    </td>
  </tr>
</table>