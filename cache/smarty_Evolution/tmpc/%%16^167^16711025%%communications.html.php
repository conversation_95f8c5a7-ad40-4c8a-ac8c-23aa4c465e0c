<?php /* Smarty version 2.6.33, created on 2024-11-14 17:47:55
         compiled from /var/www/Nzoom-Evolution/_libs/modules/finance/templates/communications.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'array', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/communications.html', 16, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/communications.html', 30, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/communications.html', 33, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/communications.html', 43, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
<div id="form_container">

  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
    <tr>
      <td class="nopadding">
        <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
          <?php if (in_array ( $this->_tpl_vars['controller'] , array ( 'expenses_reasons' , 'incomes_reasons' , 'annulments' , 'payments' , 'transfers' , 'warehouses_documents' ) )): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_".($this->_tpl_vars['controller'])."_info_header.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php else: ?>
            <?php if ($this->_tpl_vars['model']->modelName == 'Finance_Budget'): ?>
              <?php echo smarty_function_array(array('assign' => 'model_vars','company' => 'company_name','year' => 'year'), $this);?>

            <?php else: ?>
              <?php echo smarty_function_array(array('assign' => 'model_vars','num' => 'num','code' => 'code','type' => 'type_name','name' => 'name'), $this);?>

            <?php endif; ?>
            <?php $_from = $this->_tpl_vars['model_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['property']):
?>
            <?php if ($this->_tpl_vars['model']->get($this->_tpl_vars['property'])): ?>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['key']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('label_param', ob_get_contents());ob_end_clean(); ?>
            <tr>
              <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars'][$this->_tpl_vars['label_param']]), $this);?>
</td>
              <td class="unrequired">&nbsp;</td>
              <td nowrap="nowrap">
                <?php echo ((is_array($_tmp=$this->_tpl_vars['model']->get($this->_tpl_vars['property']))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

              </td>
            </tr>
            <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php endif; ?>
        </table>
      </td>
    </tr>

    <?php $this->assign('current_model_name', ((is_array($_tmp=$this->_tpl_vars['current_model']->modelName)) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp))); ?>
    <?php ob_start(); ?><?php echo $this->_tpl_vars['current_model_name']; ?>
s_communications_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('cookie_var', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?><?php echo $this->_tpl_vars['current_model_name']; ?>
s_<?php if ($this->_tpl_vars['communication_type']): ?><?php echo $this->_tpl_vars['communication_type']; ?>
<?php else: ?>communications<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_communication_list', ob_get_contents());ob_end_clean(); ?>
    <tr>
      <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="<?php echo $this->_tpl_vars['current_model_name']; ?>
s_communications_switch"><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['cookie_var']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title" id="title_section_communications"><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['current_communication_list']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    </tr>
    <tr id="<?php echo $this->_tpl_vars['current_model_name']; ?>
s_communications"<?php if ($_COOKIE[$this->_tpl_vars['cookie_var']] == 'off'): ?> style="display: none;"<?php endif; ?>>
      <td class="nopadding">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  </table>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>