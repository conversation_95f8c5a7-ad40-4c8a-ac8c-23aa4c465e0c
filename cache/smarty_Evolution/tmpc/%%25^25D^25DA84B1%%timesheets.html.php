<?php /* Smarty version 2.6.33, created on 2024-07-23 18:52:04
         compiled from /var/www/Nzoom-Evolution/_libs/modules/tasks/templates/timesheets.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/timesheets.html', 1, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
  <tr>
    <td class="nopadding">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info_header.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </table>
    </td>
  </tr>
  <tr>
    <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="tasks_timesheets_switch"><div class="switch_<?php if ($_COOKIE['tasks_timesheets_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <tr id="tasks_timesheets"<?php if ($_COOKIE['tasks_timesheets_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
    <td class="nopadding">
      <div id="panel_<?php echo $this->_tpl_vars['timesheets_session_param']; ?>
">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_timesheets_panel.html", 'smarty_include_vars' => array('model_id' => $this->_tpl_vars['task']->get('id'),'model_name' => 'Task')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>