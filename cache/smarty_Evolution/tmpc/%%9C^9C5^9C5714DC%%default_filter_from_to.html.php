<?php /* Smarty version 2.6.33, created on 2024-07-04 18:41:36
         compiled from /var/www/Nzoom-Evolution/_libs/modules/reports/templates/default_filter_from_to.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/reports/templates/default_filter_from_to.html', 51, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/reports/templates/default_filter_from_to.html', 51, false),)), $this); ?>
    <?php if (isset ( $this->_tpl_vars['filter_settings']['label'] )): ?>
    <?php $this->assign('filter_main_label', $this->_tpl_vars['filter_settings']['label']); ?>
  <?php else: ?>
    <?php $this->assign('filter_main_label', $this->_config[0]['vars']['from']); ?>
  <?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['filter_settings']['help'] )): ?>
    <?php $this->assign('filter_main_help', $this->_tpl_vars['filter_settings']['help']); ?>
  <?php else: ?>
    <?php $this->assign('filter_main_help', $this->_config[0]['vars']['from']); ?>
  <?php endif; ?>

    <?php if (isset ( $this->_tpl_vars['filter_settings']['first_filter_label'] )): ?>
    <?php $this->assign('filter_from_label', $this->_tpl_vars['filter_settings']['first_filter_label']); ?>
    <?php $this->assign('filter_from_help', $this->_tpl_vars['filter_settings']['first_filter_label']); ?>
  <?php else: ?>
    <?php $this->assign('filter_from_label', $this->_tpl_vars['filter_main_label']); ?>
    <?php $this->assign('filter_from_help', $this->_tpl_vars['filter_main_help']); ?>
  <?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['filter_settings']['width'] )): ?>
    <?php $this->assign('filter_from_width', $this->_tpl_vars['filter_settings']['width']); ?>
  <?php else: ?>
    <?php $this->assign('filter_from_width', '75'); ?>
  <?php endif; ?>

    <?php if (isset ( $this->_tpl_vars['filter_settings']['additional_filter']['label'] )): ?>
    <?php $this->assign('filter_to_label', $this->_tpl_vars['filter_settings']['additional_filter']['label']); ?>
  <?php else: ?>
    <?php $this->assign('filter_to_label', $this->_config[0]['vars']['to']); ?>
  <?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['filter_settings']['additional_filter']['help'] )): ?>
    <?php $this->assign('filter_to_help', $this->_tpl_vars['filter_settings']['additional_filter']['help']); ?>
  <?php else: ?>
    <?php $this->assign('filter_to_help', $this->_config[0]['vars']['to']); ?>
  <?php endif; ?>
    <?php if (isset ( $this->_tpl_vars['filter_settings']['additional_filter']['width'] )): ?>
    <?php $this->assign('filter_to_width', $this->_tpl_vars['filter_settings']['additional_filter']['width']); ?>
  <?php else: ?>
    <?php $this->assign('filter_to_width', '74'); ?>
  <?php endif; ?>
<tr>
  <td class="labelbox">
    <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['filter_settings']['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['filter_settings']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['filter_settings']['name'])); ?>
" style="white-space: nowrap;"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['filter_main_label'],'text_content' => $this->_tpl_vars['filter_main_help']), $this);?>
</label>
  </td>
  <td<?php if ($this->_tpl_vars['filter_settings']['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>
  <td nowrap="nowrap" style="vertical-align: top;">
    <div style="float: left; padding-right: 10px;">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'width' => $this->_tpl_vars['filter_from_width'],'name' => $this->_tpl_vars['filter_settings']['name'],'custom_id' => $this->_tpl_vars['filter_settings']['custom_id'],'label' => $this->_tpl_vars['filter_from_label'],'help' => $this->_tpl_vars['filter_from_help'],'restrict' => $this->_tpl_vars['filter_settings']['restrict'],'value' => $this->_tpl_vars['filter_settings']['value'],'disabled' => $this->_tpl_vars['filter_settings']['disabled'],'readonly' => $this->_tpl_vars['filter_settings']['readonly'],'onchange' => $this->_tpl_vars['filter_settings']['onchange'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div style="float: left; padding-right: 8px; font-size: 11px; color: #666666;"><?php echo $this->_tpl_vars['filter_to_label']; ?>
</div>
    <div style="float: left;">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'width' => $this->_tpl_vars['filter_to_width'],'name' => $this->_tpl_vars['filter_settings']['additional_filter']['name'],'custom_id' => $this->_tpl_vars['filter_settings']['additional_filter']['custom_id'],'label' => $this->_tpl_vars['filter_to_label'],'help' => $this->_tpl_vars['filter_to_help'],'restrict' => $this->_tpl_vars['filter_settings']['additional_filter']['restrict'],'value' => $this->_tpl_vars['filter_settings']['additional_filter']['value'],'disabled' => $this->_tpl_vars['filter_settings']['additional_filter']['disabled'],'readonly' => $this->_tpl_vars['filter_settings']['additional_filter']['readonly'],'onchange' => $this->_tpl_vars['filter_settings']['additional_filter']['onchange'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
  </td>
</tr>