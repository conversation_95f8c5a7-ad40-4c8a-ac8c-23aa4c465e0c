<?php /* Smarty version 2.6.33, created on 2024-07-12 12:21:17
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_history.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_history.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_history.html', 12, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_history.html', 13, false),array('function', 'math', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_history.html', 10, false),)), $this); ?>
    <table border="0" cellpadding="0" cellspacing="0" class="nz-table nz-history-table">
      <tr>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['history_event_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['history_event_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
      </tr>
    <?php $_from = $this->_tpl_vars['history']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['event']):
        $this->_foreach['i']['iteration']++;
?>
      <tr id="history_<?php echo $this->_tpl_vars['event']['h_id']; ?>
" class="<?php if ($this->_tpl_vars['event']['audits']): ?> strong<?php else: ?><?php endif; ?><?php if (! $this->_tpl_vars['no_audit']): ?> pointer<?php endif; ?><?php if (($this->_foreach['i']['iteration'] <= 1) && ! $_REQUEST['audit'] || $_REQUEST['audit'] == $this->_tpl_vars['event']['h_id']): ?> attention<?php endif; ?>"<?php if (! $this->_tpl_vars['no_audit']): ?> onclick="this.up('table').select('tr').each(function(t) { t.removeClassName('attention'); }.bind(t)); this.addClassName('attention'); ajaxUpdater({link: '<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?>&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?>&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=audit&amp;audit=<?php echo $this->_tpl_vars['event']['h_id']; ?>
<?php if ($this->_tpl_vars['model']->get('type')): ?>&amp;model_type=<?php echo $this->_tpl_vars['model']->get('type'); ?>
<?php endif; ?><?php if ($this->_tpl_vars['model']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', target: 'model_audit', highlight: 'model_audit'})"<?php endif; ?>>
        <td class="hright"><?php echo smarty_function_math(array('equation' => 'x-(y*(z-1))-(c-1)','x' => $this->_tpl_vars['pagination']['total'],'y' => $this->_tpl_vars['pagination']['rpp'],'z' => $this->_tpl_vars['pagination']['page'],'c' => $this->_foreach['i']['iteration']), $this);?>
</td>
        <td class=""><?php echo $this->_tpl_vars['event']['action_type_name']; ?>
</td>
        <td class=""><?php echo ((is_array($_tmp=@$this->_tpl_vars['event']['data'])) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
        <td class="td4"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['event']['h_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endforeach; else: ?>
      <tr>
        <td class="error" colspan="4"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endif; unset($_from); ?>
    </table>
    <div class="pagemenu">
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'target' => 'model_history','link' => ($this->_tpl_vars['submitLink'])."&amp;page=",'use_ajax' => 1,'hide_rpp' => 1,'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

  <br />
  <?php if ($this->_tpl_vars['audit_subpanel_template']): ?>
  <div id="model_audit">
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['audit_subpanel_template'], 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </div>
  <?php endif; ?>
  <?php if (! $this->_tpl_vars['no_audit'] && ! ( $_REQUEST['source'] == 'ajax' )): ?>
  <script type="text/javascript">
    if (document.location.href.toQueryParams().audit) $$('#history_' + document.location.href.toQueryParams().audit + '[onclick]').each(function(el){ document.location.hash = '#' + el.id; });
  </script>
  <?php endif; ?>