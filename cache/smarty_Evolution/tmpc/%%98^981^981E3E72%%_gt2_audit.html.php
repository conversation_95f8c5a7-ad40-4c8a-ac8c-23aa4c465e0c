<?php /* Smarty version 2.6.33, created on 2024-10-18 16:15:27
         compiled from /var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_gt2_audit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_gt2_audit.html', 3, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_gt2_audit.html', 24, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_gt2_audit.html', 36, false),array('modifier', 'nl2br', '/var/www/Nzoom-Evolution/_libs/themes/Evolution/templates/_gt2_audit.html', 42, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['gt2_audit']['new_values'] ) || ! empty ( $this->_tpl_vars['gt2_audit']['old_values'] )): ?>
  <aside class="gt2_audit_legend">
    <h4><?php echo ((is_array($_tmp=$this->_config[0]['vars']['legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h4>
    <ul class="nz-legend nz-legend--vertical">
      <li class="nz-legend__item finance_audit_added">
        <span class="legend_color">&nbsp;</span>
        <span class="legend_text"><?php echo $this->_config[0]['vars']['legend_added_row']; ?>
</span>
      </li>
      <li class="nz-legend__item finance_audit_deleted">
        <span class="legend_color">&nbsp;</span>
        <span class="legend_text"><?php echo $this->_config[0]['vars']['legend_deleted_row']; ?>
</span>
      </li>
      <li class="nz-legend__item finance_audit_updated">
        <span class="legend_color old_value">&nbsp;</span>
        <span class="legend_text"><?php echo $this->_config[0]['vars']['legend_old_value']; ?>
</span>
      </li>
      <li class="nz-legend__item finance_audit_updated">
        <span class="legend_color new_value">&nbsp;</span>
        <span class="legend_text"><?php echo $this->_config[0]['vars']['legend_new_value']; ?>
</span>
      </li>
    </ul>
  </aside>
  <span class="red"><?php echo $this->_config[0]['vars']['gt2_audit_only_changed_rows']; ?>
</span>
  <?php ob_start(); ?><?php echo count($this->_tpl_vars['gt2_audit']['labels']); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('colspan', ob_get_contents());ob_end_clean(); ?>
  <?php $this->assign('colspan', $this->_tpl_vars['colspan']*2); ?>
  <div class="nz-view-wrapper">
    <table border="0" cellpadding="0" cellspacing="0" class="nz-table">
      <tr>
        <?php $_from = $this->_tpl_vars['gt2_audit']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['label']):
        $this->_foreach['i']['iteration']++;
?>
          <th class="" colspan="2" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <?php endforeach; endif; unset($_from); ?>
      </tr>
      <?php $_from = $this->_tpl_vars['gt2_audit']['new_values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['row'] => $this->_tpl_vars['values']):
        $this->_foreach['i']['iteration']++;
?>
      <?php if (! empty ( $this->_tpl_vars['values']['field_name'] ) && $this->_tpl_vars['values']['field_name'] == 'bb_delimiter'): ?>
      <tr>
        <td colspan="<?php echo $this->_tpl_vars['colspan']; ?>
" class="nz-table__divider"><?php if (empty ( $this->_tpl_vars['values']['deleted'] )): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['values']['model_id']; ?>
<?php if ($_REQUEST['archive']): ?>&amp;archive=1<?php endif; ?>#bb_row_<?php echo $this->_tpl_vars['values']['bb_id']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</a><?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></td>
      </tr>
      <?php elseif ($this->_tpl_vars['values']['article_id'] || $this->_tpl_vars['values']['price'] || $this->_tpl_vars['values']['average_weighted_delivery_price'] || $this->_tpl_vars['values']['last_delivery_price'] || $this->_tpl_vars['values']['quantity'] || $this->_tpl_vars['values']['subtotal'] || $this->_tpl_vars['values']['action'] == 'deleted'): ?>
      <tr class="finance_audit_<?php if ($this->_tpl_vars['values']['action'] == 'deleted' || empty ( $this->_tpl_vars['values'] )): ?>deleted<?php elseif ($this->_tpl_vars['values']['action'] == 'added' || empty ( $this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']] )): ?>added<?php else: ?>updated<?php endif; ?>">
        <?php $_from = $this->_tpl_vars['gt2_audit']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var'] => $this->_tpl_vars['bla']):
        $this->_foreach['j']['iteration']++;
?>
          <?php if ($this->_tpl_vars['values']['action'] == 'deleted' || empty ( $this->_tpl_vars['values'] )): ?>
            <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?>" colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <?php elseif ($this->_tpl_vars['values']['action'] == 'added' || empty ( $this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']] )): ?>
            <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?>" colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <?php elseif ($this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']] == $this->_tpl_vars['values'][$this->_tpl_vars['var']]): ?>
            <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?>" colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <?php else: ?>
            <td class="t_border<?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?> old_value"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
            <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?> new_value"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </tr>
      <?php endif; ?>
      <?php endforeach; else: ?>
      <tr>
        <td class="error" colspan="<?php echo $this->_tpl_vars['colspan']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_changes_made'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
      <?php endif; unset($_from); ?>
    </table>
  </div>
<?php endif; ?>