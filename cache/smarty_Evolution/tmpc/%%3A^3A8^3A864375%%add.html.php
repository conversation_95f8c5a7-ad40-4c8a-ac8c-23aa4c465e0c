<?php /* Smarty version 2.6.33, created on 2024-12-17 10:02:05
         compiled from /var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 24, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 113, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 439, false),array('modifier', 'numerate', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 445, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 467, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 467, false),array('modifier', 'indent', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 495, false),array('modifier', 'replace', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 512, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 60, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/documents/view/templates/add.html', 126, false),)), $this); ?>
<div class="nz-page-wrapper<?php if (! empty ( $this->_tpl_vars['side_panels'] ) && count ( $this->_tpl_vars['side_panels'] )): ?> nz--has-side-panel<?php endif; ?>">
  <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">
    <div class="nz-page-title"><h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
      <div class="nz-page-title-tools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['general'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['general'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
      <?php if (! empty ( $this->_tpl_vars['available_page_actions']['quick'] )): ?>
      <div class="nz-page-title-sidetools">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['quick'],'onlyIcons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
      <?php endif; ?>
    </div>
    <div class="nz-page-actions">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['context'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

    <form name="documents_add" id="documents_add"
          action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" enctype="multipart/form-data"
          onsubmit="return calculateBeforeSubmit(this);">
        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['document']->get('id'); ?>
" />
        <input type="hidden" name="model_id" id="model_id" value="<?php echo $this->_tpl_vars['model_id']; ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <?php if ($this->_tpl_vars['document']->get('medial_document_insert')): ?>
          <input type="hidden" name="medial_document_insert" id="medial_document_insert" value="<?php echo $this->_tpl_vars['document']->get('medial_document_insert'); ?>
" />
          <input type="hidden" name="medial_document_date_add" id="medial_document_date_add" value="<?php echo $this->_tpl_vars['document']->get('medial_document_date_add'); ?>
" />
        <?php endif; ?>
        <?php if ($this->_tpl_vars['document']->get('event_id')): ?>
          <input type="hidden" name="event_id" id="event_id" value="<?php echo $this->_tpl_vars['document']->get('event_id'); ?>
" />
        <?php endif; ?>
        <?php if ($this->_tpl_vars['document']->get('task_id')): ?>
          <input type="hidden" name="task_id" id="task_id" value="<?php echo $this->_tpl_vars['document']->get('task_id'); ?>
" />
        <?php endif; ?>
        <?php if ($this->_tpl_vars['document']->get('create_from_customer')): ?>
          <input type="hidden" name="create_from_customer" id="create_from_customer" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('create_from_customer'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
        <?php endif; ?>
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              <?php $this->assign('layouts_vars', $this->_tpl_vars['document']->get('vars')); ?>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <?php $_from = $this->_tpl_vars['document']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

                <?php if ($this->_tpl_vars['layout']['system'] || $this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <tr<?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    </div>
                    <div class="layout_switch" <?php if ($this->_tpl_vars['layout']['system']): ?>onclick="toggleViewLayouts(this)" id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch"<?php else: ?>onclick="toggleLayouts(this)" id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_switch"<?php endif; ?>>
                      <a name="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>

                <?php if ($this->_tpl_vars['lkey'] == 'status'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <div class="documents_status opened">
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status_opened'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    </div>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="type" id="type" value="<?php echo $this->_tpl_vars['document']->get('type'); ?>
" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'full_num'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_full_num"><label for="full_num"<?php if ($this->_tpl_vars['messages']->getErrors('full_num')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    &nbsp;
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'custom_num'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_custom_num"><label for="custom_num"<?php if ($this->_tpl_vars['messages']->getErrors('custom_num')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="custom_num" id="custom_num" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" maxlength="30" />
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="custom_num" id="custom_num" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date'): ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_date"><label for="date"<?php if ($this->_tpl_vars['messages']->getErrors('date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'date','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['document']->get('date'),'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                      <input type="hidden" name="date" id="date" value="<?php echo $this->_tpl_vars['document']->get('date'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
                    <?php else: ?>
                      <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

                      <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['document']->get('customer'),'value_code' => $this->_tpl_vars['document']->get('customer_code'),'value_name' => $this->_tpl_vars['document']->get('customer_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <span id="branch_container" style="display: <?php if ($this->_tpl_vars['document']->get('customer_is_company')): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                        <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('documents_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('help_documents_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                        <select name="branch" id="branch" onchange="changeContactPersonsOptions(this, 'contact_person');" class="selbox<?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?> missing_records<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('documents_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
                          <?php if (empty ( $this->_tpl_vars['customer_branches'] )): ?>
                            <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('empty_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php else: ?>
                            <?php $_from = $this->_tpl_vars['customer_branches']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['customer_branch']):
?>
                              <?php if (( ! $this->_tpl_vars['customer_branch']->isDeleted() && $this->_tpl_vars['customer_branch']->isActivated() ) || $this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['document']->get('branch')): ?>
                              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['customer_branch']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['customer_branch']->get('id') === $this->_tpl_vars['document']->get('branch')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['customer_branch']->isDeleted() || ! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['customer_branch']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</option>
                              <?php endif; ?>
                            <?php endforeach; endif; unset($_from); ?>
                          <?php endif; ?>
                        </select>
                      <?php else: ?>
                        <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('documents_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                        <span class="branch">
                        <?php if ($this->_tpl_vars['document']->get('branch')): ?>
                          <span<?php if (! $this->_tpl_vars['document']->get('branch_active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                        <?php endif; ?>
                        </span>
                        <input type="hidden" name="branch" id="branch" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('branch'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                      <?php endif; ?>
                    </span>
                    <span id="contact_person_container" style="display: <?php if ($this->_tpl_vars['document']->get('customer_is_company') && ( $this->_tpl_vars['layout']['edit'] || $this->_tpl_vars['document']->get('contact_person') )): ?>inline<?php else: ?>none<?php endif; ?>;">
                      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                        <span class="help" <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['documents_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('help_documents_contact_person'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => '1'), $this);?>
>&nbsp;</span>
                        <select name="contact_person" id="contact_person" class="selbox<?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?> missing_records<?php elseif (! $this->_tpl_vars['document']->get('contact_person')): ?> undefined<?php endif; ?>" style="width: 100px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
                          <?php if (empty ( $this->_tpl_vars['contact_persons'] )): ?>
                            <option value="" class="missing_records" selected="selected">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['empty_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                          <?php else: ?>
                            <option value="" class="undefined"<?php if (! $this->_tpl_vars['document']->get('contact_person')): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                            <?php $_from = $this->_tpl_vars['contact_persons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact_person']):
?>
                              <?php if (( ! $this->_tpl_vars['contact_person']->isDeleted() && $this->_tpl_vars['contact_person']->isActivated() ) || $this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['document']->get('contact_person')): ?>
                              <option value="<?php echo ((is_array($_tmp=$this->_tpl_vars['contact_person']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if ($this->_tpl_vars['contact_person']->get('id') === $this->_tpl_vars['document']->get('contact_person')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['contact_person']->isDeleted() || ! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*&nbsp;<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('name'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if ($this->_tpl_vars['contact_person']->get('lastname')): ?> <?php echo ((is_array($_tmp=@$this->_tpl_vars['contact_person']->get('lastname'))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></option>
                              <?php endif; ?>
                            <?php endforeach; endif; unset($_from); ?>
                          <?php endif; ?>
                        </select>
                      <?php else: ?>
                        <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['documents_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                        <span class="contact_person">
                        <?php if ($this->_tpl_vars['document']->get('contact_person')): ?>
                          <span<?php if (! $this->_tpl_vars['document']->get('contact_person_active')): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                        <?php endif; ?>
                        </span>
                        <input type="hidden" name="contact_person" id="contact_person" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('contact_person'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                      <?php endif; ?>
                    </span>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['document']->get('trademark'),'value_name' => $this->_tpl_vars['document']->get('trademark_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'contract'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_contract"><label for="contract"<?php if ($this->_tpl_vars['messages']->getErrors('contract')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['contracts'],'standalone' => true,'name' => $this->_tpl_vars['contracts']['name'],'custom_id' => $this->_tpl_vars['contracts']['custom_id'],'value' => $this->_tpl_vars['contracts']['value'],'options' => $this->_tpl_vars['contracts']['options'],'optgroups' => $this->_tpl_vars['contracts']['optgroups'],'width' => 200,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <span id="contract_container">
                        <?php if ($this->_tpl_vars['document']->get('contract')): ?>
                          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['document']->get('contract'); ?>
" target="_blank"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('contract_custom_label'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                        <?php else: ?>
                          &nbsp;
                        <?php endif; ?>
                      </span>
                      <input type="hidden" name="contract" id="contract" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('contract'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if ($this->_tpl_vars['project_required'] || in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['document']->get('project'),'value_code' => $this->_tpl_vars['document']->get('project_code'),'value_name' => $this->_tpl_vars['document']->get('project_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'office'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_office"><label for="office"<?php if ($this->_tpl_vars['messages']->getErrors('office')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if ($this->_tpl_vars['office_required'] || in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php if ($this->_tpl_vars['currentUser']->checkRights('offices','add')): ?>
                        <?php $this->assign('control_type', 'input_combobox.html'); ?>
                      <?php else: ?>
                        <?php $this->assign('control_type', 'input_dropdown.html'); ?>
                      <?php endif; ?>
                      <div style="width: 200px;">
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['control_type'], 'smarty_include_vars' => array('var' => $this->_tpl_vars['offices'],'standalone' => true,'var_id' => $this->_tpl_vars['offices']['id'],'name' => $this->_tpl_vars['offices']['name'],'custom_id' => $this->_tpl_vars['offices']['custom_id'],'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['offices']['value'],'options' => $this->_tpl_vars['offices']['options'],'optgroups' => $this->_tpl_vars['offices']['optgroups'],'option_value' => $this->_tpl_vars['offices']['option_value'],'onclick' => $this->_tpl_vars['offices']['onclick'],'check' => $this->_tpl_vars['offices']['check'],'scrollable' => $this->_tpl_vars['offices']['scrollable'],'calculate' => $this->_tpl_vars['offices']['calculate'],'readonly' => $this->_tpl_vars['offices']['readonly'],'required' => $this->_tpl_vars['offices']['required'],'hidden' => $this->_tpl_vars['offices']['hidden'],'width' => 200,'disabled' => $this->_tpl_vars['offices']['disabled'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      </div>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('office_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="office" id="office" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('office'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_employee"><label for="employee"<?php if ($this->_tpl_vars['messages']->getErrors('employee')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employee','autocomplete_type' => 'customers','stop_customer_details' => 1,'autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['document']->get('employee'),'value_name' => $this->_tpl_vars['document']->get('employee_name'),'filters_array' => $this->_tpl_vars['autocomplete_employee_filters'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('employee_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="employee" id="employee" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('employee'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'media'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_media"><label for="media"<?php if ($this->_tpl_vars['messages']->getErrors('media')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php if ($this->_tpl_vars['currentUser']->checkRights('documents_medias','add')): ?>
                        <?php $this->assign('control_type', 'input_combobox.html'); ?>
                      <?php else: ?>
                        <?php $this->assign('control_type', 'input_dropdown.html'); ?>
                      <?php endif; ?>
                      <div style="width: 200px;">
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['control_type'], 'smarty_include_vars' => array('var' => $this->_tpl_vars['medias'],'standalone' => true,'var_id' => $this->_tpl_vars['medias']['id'],'name' => $this->_tpl_vars['medias']['name'],'custom_id' => $this->_tpl_vars['medias']['custom_id'],'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['medias']['value'],'options' => $this->_tpl_vars['medias']['options'],'optgroups' => $this->_tpl_vars['medias']['optgroups'],'option_value' => $this->_tpl_vars['medias']['option_value'],'onclick' => $this->_tpl_vars['medias']['onclick'],'check' => $this->_tpl_vars['medias']['check'],'scrollable' => $this->_tpl_vars['medias']['scrollable'],'calculate' => $this->_tpl_vars['medias']['calculate'],'readonly' => $this->_tpl_vars['medias']['readonly'],'hidden' => $this->_tpl_vars['medias']['hidden'],'width' => 200,'disabled' => $this->_tpl_vars['medias']['disabled'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                        </div>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('media_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <input type="hidden" name="media" id="media" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('media'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'deadline'): ?>
              <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                <?php ob_start(); ?><?php echo $this->_config[0]['vars']['documents_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired', ob_get_contents());ob_end_clean(); ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3">
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['document_expired'],'label_content' => ((is_array($_tmp=$this->_config[0]['vars']['documents_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => 1), $this);?>
/> <?php echo $this->_tpl_vars['document_expired']; ?>

                  </td>
                </tr>
              <?php endif; ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_deadline"><label for="deadline"<?php if ($this->_tpl_vars['messages']->getErrors('deadline')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'deadline','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['document']->get('deadline'),'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                      <input type="hidden" name="deadline" id="deadline" value="<?php echo $this->_tpl_vars['document']->get('deadline'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'validity_term'): ?>
              <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                <?php ob_start(); ?><?php echo $this->_config[0]['vars']['documents_expired_validity_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired_validity_term', ob_get_contents());ob_end_clean(); ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3">
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_help(array('text_content' => $this->_tpl_vars['document_expired_validity_term'],'label_content' => ((is_array($_tmp=$this->_config[0]['vars']['documents_validity_term_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => 1), $this);?>
/> <?php echo $this->_tpl_vars['document_expired_validity_term']; ?>

                  </td>
                </tr>
              <?php endif; ?>
                <tr class="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_validity_term"><label for="validity_term"<?php if ($this->_tpl_vars['messages']->getErrors('validity_term')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'validity_term','label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'],'value' => $this->_tpl_vars['document']->get('validity_term'),'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                      <input type="hidden" name="validity_term" id="validity_term" value="<?php echo $this->_tpl_vars['document']->get('validity_term'); ?>
" />
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'referers'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_referers"><label for="referers"<?php if ($this->_tpl_vars['messages']->getErrors('referers')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <input type="hidden" name="update_relatives" id="update_relatives" value="1" />
                    <div id="referers">
                      <div id="toggleCheckboxes" style="width: 300px; display: <?php if (is_array ( $this->_tpl_vars['document']->get('referers') ) && count($this->_tpl_vars['document']->get('referers')) > 4): ?>block<?php else: ?>none<?php endif; ?>;">
                        <span onclick="toggleCheckboxes(this, 'referers', true)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
                        <span onclick="toggleCheckboxes(this, 'referers', false)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      </div>
                      <?php if ($this->_tpl_vars['document']->get('referers')): ?>
                        <?php $_from = $this->_tpl_vars['document']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                          <input type="checkbox" name="referers[]" id="ref<?php echo $this->_tpl_vars['ref']['id']; ?>
" value="<?php echo $this->_tpl_vars['ref']['id']; ?>
" checked="checked" /><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
<?php if ($this->_tpl_vars['ref']['archived_by']): ?>&amp;archive=1<?php endif; ?>" target="_blank"><?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['full_num'])) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['ref']['direction']) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['ref']['direction'])); ?>
&nbsp;&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                    </div>
                    <button type="button" name="filterButton" class="button" onclick="var popUrl='<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['module']; ?>
=filter&amp;open_from=<?php echo $this->_tpl_vars['module']; ?>
&amp;customer=' + $('customer').value; pop(popUrl, 822, 580);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['link'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
...</button>
                    <?php else: ?>
                      <?php if ($this->_tpl_vars['document']->get('referers')): ?>
                        <?php $_from = $this->_tpl_vars['document']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                          <?php echo $this->_foreach['i']['iteration']; ?>
. <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
<?php if ($this->_tpl_vars['ref']['archived_by']): ?>&amp;archive=1<?php endif; ?>" target="_blank"><?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['full_num'])) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['ref']['direction']) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['ref']['direction'])); ?>
&nbsp;&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox doubled" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <textarea class="areabox doubled" name="description" id="description" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="display: none;"><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_notes"><label for="notes"<?php if ($this->_tpl_vars['messages']->getErrors('notes')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <textarea class="areabox doubled" name="notes" id="notes" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <textarea class="areabox doubled" name="notes" id="notes" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="display: none;"><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
                  <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><a name="error_department"><label for="department"<?php if ($this->_tpl_vars['messages']->getErrors('department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['layout']['edit']): ?>
                      <select class="selbox<?php if (! $this->_tpl_vars['document']->get('department')): ?> undefined<?php endif; ?>" name="department" id="department" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                        <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                        <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                          <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['document']->get('department')): ?>
                          <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['document']->get('department')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?>*&nbsp;<?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      </select>
                    <?php else: ?>
                      <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                        <?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['document']->get('department')): ?>
                          <?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>

                          <input type="hidden" name="department" id="department" value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
" />
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif (strpos ( $this->_tpl_vars['lkey'] , 'assignments_' ) === 0): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox">
                  <?php $this->assign('at', ((is_array($_tmp=$this->_tpl_vars['layout']['keyword'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'assignments_', '') : smarty_modifier_replace($_tmp, 'assignments_', ''))); ?>
                  <a name="error_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"><label for="<?php echo $this->_tpl_vars['at']; ?>
_ac"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['layout']['keyword'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/assignments/templates/_assignments_group.html", 'smarty_include_vars' => array('type' => $this->_tpl_vars['at'],'users' => $this->_tpl_vars['assignments_settings'][$this->_tpl_vars['at']]['users'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php if ($this->_tpl_vars['at'] == 'owner'): ?>
                  <script type="text/javascript" defer="defer">
                    Event.observe('department', 'change', updateAssignmentsParams);
                  </script>
                  <?php endif; ?>
                </td>
                <td class="unrequired">&nbsp;</td>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/assignments/templates/_assign.html", 'smarty_include_vars' => array('type' => $this->_tpl_vars['at'],'data' => $this->_tpl_vars['assignments_settings'][$this->_tpl_vars['at']],'borderless' => true,'model' => $this->_tpl_vars['document'],'width' => 200,'display_always' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </tr>
                <?php elseif ($this->_tpl_vars['layout']['view'] && array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
                <!-- Document Additional Vars -->
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_manage_vars.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php elseif ($this->_tpl_vars['lkey'] == 'attachments' && $this->_tpl_vars['currentUser']->get('is_portal')): ?>
                <tr id="document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="nopadding">
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_attachments.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3">
                    <div class="nz-quick-buttons ">
                    <?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('buttons')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['document']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['action'] == 'ajax_edit'): ?><?php echo '<button type="button" name="saveButton1" class="nz-form-button nz-button-primary" onclick="ajaxForm(\''; ?><?php echo $this->_tpl_vars['action']; ?><?php echo '\', \'documents\', this.form)">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="button" name="cancel" class="nz-form-button nz-button-cancel" onclick="confirmAction(\'cancel\', function() '; ?>{<?php echo ' '; ?><?php if (! empty ( $this->_tpl_vars['document']->lockedByMe )): ?><?php echo ' unlockRecord('; ?>{<?php echo 'module: \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', controller: \''; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '\', action: \''; ?><?php echo $this->_tpl_vars['action']; ?><?php echo '\', unlock: \''; ?><?php echo $this->_tpl_vars['document']->lockedInfo['id']; ?><?php echo '\', model_id: \''; ?><?php echo $this->_tpl_vars['document']->get('id'); ?><?php echo '\', skip_message: true'; ?>}<?php echo '); '; ?><?php endif; ?><?php echo ' lb.deactivate(); '; ?>}<?php echo ', this);">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php else: ?><?php echo '<button type="submit" name="saveButton1" class="nz-form-button nz-button-primary">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

                    </div>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php if ($this->_tpl_vars['action'] != 'ajax_add'): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
    </form>
  </div>
</div>