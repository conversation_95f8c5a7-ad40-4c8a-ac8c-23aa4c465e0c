<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:39
         compiled from confirm.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'confirm.html', 5, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['currentUser'] )): ?>
<div style="display: none;" id="tmp_confirm_container">
  <div id="tmp_confirm_message"></div>
  <div class="hright">
    <button type="button" class="button confirm" onclick="confirmLB(1);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['ok'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
    <button type="button" class="button cancel" onclick="confirmLB(0);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
    <div id="tmp_confirm_settings">
      <input type="checkbox" id="tmp_skip_confirm" name="skip_confirm" value="1" />
      <label for="tmp_skip_confirm"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['skip_confirm_action'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
      <?php if ($this->_tpl_vars['currentUser']->checkRights('users','mynzoom')): ?> <a target="_blank" href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=users&amp;users=mynzoom&amp;layout=interface#settings_confirm"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><?php endif; ?>
    </div>
  </div>
</div>
<?php endif; ?>