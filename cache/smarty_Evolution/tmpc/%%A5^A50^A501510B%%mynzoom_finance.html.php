<?php /* Smarty version 2.6.33, created on 2024-10-09 13:19:00
         compiled from /var/www/Nzoom-Evolution/_libs/modules/users/templates/mynzoom_finance.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/mynzoom_finance.html', 6, false),array('function', 'popup', '/var/www/Nzoom-Evolution/_libs/modules/users/templates/mynzoom_finance.html', 6, false),)), $this); ?>
    <input type="hidden" name="layout" id="layout" value="<?php echo $this->_tpl_vars['layout']; ?>
" />
    <table cellspacing="0" cellpadding="0" border="0" class="t_table">
      <tr>
        <td colspan="5" class="t_caption3">
          <div class="t_caption2_title">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['layouts'][$this->_tpl_vars['layout']]['description'],'caption' => $this->_config[0]['vars']['system_info']), $this);?>
 />
            <?php echo ((is_array($_tmp=$this->_tpl_vars['layouts'][$this->_tpl_vars['layout']]['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </div>
        </td>
      </tr>
      <tr>
        <td colspan="5" style="padding: 5px;">
          <input type="checkbox" name="set_observer" id="set_observer" value="1"<?php if ($this->_tpl_vars['settings']['finance']['set_observer'] || ! isset ( $this->_tpl_vars['settings']['finance']['set_observer'] )): ?> checked="checked"<?php endif; ?> /><label for="set_observer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_finance_set_observer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."mynzoom_assignments.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </table>