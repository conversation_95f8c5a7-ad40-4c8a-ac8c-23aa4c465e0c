<?php /* Smarty version 2.6.33, created on 2024-07-05 17:07:26
         compiled from /var/www/Nzoom-Evolution/_libs/modules/analyses/view/index_modern/../index/_display_objects.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/analyses/view/index_modern/../index/_display_objects.html', 10, false),)), $this); ?>
<div class="nz-page-content-wrapper">
  <?php $_from = $this->_tpl_vars['display_objects']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['display_object_name'] => $this->_tpl_vars['display_object']):
?>
    <div class="display_object_container display_object_<?php echo $this->_tpl_vars['display_object_name']; ?>
" data-analysis-id="<?php echo $this->_tpl_vars['analysis_id']; ?>
" data-display-object-mode="<?php echo $this->_tpl_vars['display_object']['mode']; ?>
" data-display-object-name="<?php echo $this->_tpl_vars['display_object_name']; ?>
"></div>
  <?php endforeach; endif; unset($_from); ?>
</div>

<script>
  onReady().then(()=> {
    <?php $_from = $this->_tpl_vars['display_objects']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['display_object_name'] => $this->_tpl_vars['display_object']):
?>
        window['analysisDisplayObjectSettings_<?php echo $this->_tpl_vars['display_object_name']; ?>
'] = <?php echo ((is_array($_tmp=@$this->_tpl_vars['display_object']['settings'])) ? $this->_run_mod_handler('default', true, $_tmp, 'null') : smarty_modifier_default($_tmp, 'null')); ?>
;
    <?php endforeach; endif; unset($_from); ?>
    prepareAnalyses();
  });
</script>