<?php /* Smarty version 2.6.33, created on 2025-02-14 11:23:36
         compiled from /var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 23, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 45, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 48, false),array('modifier', 'nl2br', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 48, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 50, false),array('function', 'counter', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 32, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/imports/templates/log.html', 34, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=imports&amp;imports=log&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'hide_rpp' => true,'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border t_sortable <?php echo $this->_tpl_vars['sort']['type']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['type']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['imports_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border t_sortable <?php echo $this->_tpl_vars['sort']['file']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['file']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['imports_upload_file'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border t_sortable <?php echo $this->_tpl_vars['sort']['status']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['status']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_log_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border t_sortable <?php echo $this->_tpl_vars['sort']['log_message']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['log_message']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_log_messages'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border t_sortable <?php echo $this->_tpl_vars['sort']['user']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['user']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_log_user'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_sortable <?php echo $this->_tpl_vars['sort']['date']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['date']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_log_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>

      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php $_from = $this->_tpl_vars['log']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['l']):
        $this->_foreach['i']['iteration']++;
?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 <?php if ($this->_tpl_vars['l']->get('success')): ?>green<?php else: ?>red<?php endif; ?>">
          <td class="t_border hright">
            <?php if ($this->_tpl_vars['l']->get('files_count')): ?>
              <a href="#">
                <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                  onmouseover="showFiles(this, 'imports', 'imports', <?php echo $this->_tpl_vars['l']->get('id'); ?>
)"
                  onmouseout="mclosetime()" />
              </a>
            <?php endif; ?>
            <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['type']['isSorted']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['l']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['file']['isSorted']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['l']->get('file_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['status']['isSorted']; ?>
"><?php if ($this->_tpl_vars['l']->get('success')): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_log_ok'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_log_failed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></td>
          <td class="t_border t_word_wrap <?php echo $this->_tpl_vars['sort']['log_message']['isSorted']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['l']->get('log'))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '#(?<=\S),(?=[^\s\d])#', "\n") : smarty_modifier_regex_replace($_tmp, '#(?<=\S),(?=[^\s\d])#', "\n")))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['user']['isSorted']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['l']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td class="<?php echo $this->_tpl_vars['sort']['date']['isSorted']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['l']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</td>
        </tr>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="8"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="8"></td>
        </tr>
      </table>
      <br />
      <br />
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."multiple_actions_list.html", 'smarty_include_vars' => array('session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])),'exclude' => 'multiedit')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'hide_rpp' => true,'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>