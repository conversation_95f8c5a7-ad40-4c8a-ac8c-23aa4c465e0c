<?php /* Smarty version 2.6.33, created on 2024-07-25 17:50:01
         compiled from /var/www/Nzoom-Evolution/_libs/modules/projects/templates/_assignments_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_assignments_side_panel.html', 4, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/projects/templates/_assignments_side_panel.html', 7, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 300px;"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_manager'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 300px;"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['projects_assigned'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop">
    <td class="t_border">
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['normal_user']; ?>
" /> <?php echo ((is_array($_tmp=$this->_tpl_vars['model']->get('manager_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    </td>
    <td>
    <?php if ($this->_tpl_vars['model']->get('departments_assignments')): ?>
      <?php $_from = $this->_tpl_vars['model']->get('departments_assignments'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['department']):
        $this->_foreach['i']['iteration']++;
?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/departments.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['department']; ?>
" />
        <?php echo ((is_array($_tmp=$this->_tpl_vars['department']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
      <?php endforeach; endif; unset($_from); ?>
    <?php elseif ($this->_tpl_vars['model']->get('users_assignments')): ?>
      <?php $_from = $this->_tpl_vars['model']->get('users_assignments'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['users']):
        $this->_foreach['j']['iteration']++;
?>
        <?php if ($this->_tpl_vars['users']['is_portal']): ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user_portal.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['portal_user']; ?>
" />
        <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/user.png" width="12" height="12" border="0" alt="" title="<?php echo $this->_config[0]['vars']['normal_user']; ?>
" />
        <?php endif; ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['users']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
    </td>
  </tr>
</table>
<?php endif; ?>