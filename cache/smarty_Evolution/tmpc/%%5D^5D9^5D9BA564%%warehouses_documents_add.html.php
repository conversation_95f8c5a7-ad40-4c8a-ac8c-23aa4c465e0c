<?php /* Smarty version 2.6.33, created on 2025-01-08 12:09:15
         compiled from /var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 11, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 218, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 250, false),array('modifier', 'indent', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 307, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 334, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 334, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 28, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 64, false),array('function', 'array', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/warehouses_documents_add.html', 245, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

      <form name="finance" enctype="multipart/form-data" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post" onsubmit="return gt2calc('submit_gt2');">
      <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
      <input type="hidden" name="type" id="type" value="<?php echo $this->_tpl_vars['finance_warehouses_document']->get('type'); ?>
" />
      <?php if ($this->_tpl_vars['finance_warehouses_document']->get('transform_params')): ?>
        <input type="hidden" name="transform_params" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('transform_params'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
        <input type="hidden" name="after_action" value="view" />
      <?php endif; ?>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td class="t_footer"></td>
        </tr>
        <tr>
          <td>
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <?php $_from = $this->_tpl_vars['finance_warehouses_document']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>

            <?php if ($this->_tpl_vars['lkey'] == 'to_warehouse_data' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr><td colspan="3"></td></tr>
              <tr><td colspan="3" class="t_caption2_title"><?php echo smarty_function_help(array('label' => 'warehouses_documents_transfer_to'), $this);?>
</td></tr>
            <?php endif; ?>

              <tr<?php if (! $this->_tpl_vars['layout']['view'] || ! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                <td colspan="3" class="t_caption3 pointer">
                  <div class="floatr index_arrow_anchor">
                    <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                  </div>
                  <div class="layout_switch" onclick="toggleViewLayouts(this)" id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch">
                    <a name="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                  </div>
                </td>
              </tr>

              <?php if ($this->_tpl_vars['lkey'] == 'type'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_type"><label for="type"<?php if ($this->_tpl_vars['messages']->getErrors('type')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_name"><label for="name"<?php if ($this->_tpl_vars['messages']->getErrors('name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'name','custom_id' => 'name','standalone' => true,'value' => $this->_tpl_vars['finance_warehouses_document']->get('name'),'width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

                    <input type="hidden" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'customer' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_RESERVATION): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_customer"><label for="customer"<?php if ($this->_tpl_vars['messages']->getErrors('customer')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'customer','autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['finance_warehouses_document']->get('customer'),'value_code' => $this->_tpl_vars['finance_warehouses_document']->get('customer_code'),'value_name' => $this->_tpl_vars['finance_warehouses_document']->get('customer_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'trademark' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_RESERVATION): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_trademark"><label for="trademark"<?php if ($this->_tpl_vars['messages']->getErrors('trademark')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'trademark','autocomplete_type' => 'nomenclatures','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'search clear','value' => $this->_tpl_vars['finance_warehouses_document']->get('trademark'),'value_name' => $this->_tpl_vars['finance_warehouses_document']->get('trademark_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 244,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'],'help' => $this->_tpl_vars['layout']['description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'project' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_RESERVATION): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_project"><label for="project"<?php if ($this->_tpl_vars['messages']->getErrors('project')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php ob_start(); ?><?php if ($this->_tpl_vars['layout']['edit']): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_readonly', ob_get_contents());ob_end_clean(); ?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'project','autocomplete_type' => 'projects','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'add search clear','value' => $this->_tpl_vars['finance_warehouses_document']->get('project'),'value_code' => $this->_tpl_vars['finance_warehouses_document']->get('project_code'),'value_name' => $this->_tpl_vars['finance_warehouses_document']->get('project_name'),'readonly' => $this->_tpl_vars['ac_readonly'],'width' => 266,'standalone' => true,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php if (! $this->_tpl_vars['ac_readonly']): ?>
                    <span class="help" <?php echo smarty_function_help(array('label' => 'warehouses_documents_phase','popup_only' => '1'), $this);?>
>&nbsp;</span>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'phase','options' => $this->_tpl_vars['phases'],'no_select_records_label' => $this->_config[0]['vars']['project_phase'],'first_option_label' => $this->_config[0]['vars']['project_phase'],'width' => 100,'value' => $this->_tpl_vars['finance_warehouses_document']->get('phase'),'label' => $this->_config[0]['vars']['finance_warehouses_documents_phase'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php if ($this->_tpl_vars['finance_warehouses_document']->get('phase')): ?> <span class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['finance_warehouses_documents_phase']), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('phase_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                    <input type="hidden" name="phase" id="phase" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('phase'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'warehouse_data'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_warehouse_data"><label for="warehouse_data"<?php if ($this->_tpl_vars['messages']->getErrors('warehouse_data')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <span class="strong"><?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('office_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 [<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('warehouse_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]
                  <input type="hidden" name="warehouse_data" id="warehouse_data" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('warehouse_data'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'from' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_from"><label for="from"<?php if ($this->_tpl_vars['messages']->getErrors('from')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_combobox.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'from','options' => $this->_tpl_vars['employees_options'],'value' => $this->_tpl_vars['finance_warehouses_document']->get('from'),'hidden' => 0,'width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php $_from = $this->_tpl_vars['employees_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['eo']):
?>
                      <?php if ($this->_tpl_vars['eo']['option_value'] == $this->_tpl_vars['finance_warehouses_document']->get('from')): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['eo']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'from','value' => $this->_tpl_vars['finance_warehouses_document']->get('from'),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'to' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_to"><label for="to"<?php if ($this->_tpl_vars['messages']->getErrors('to')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_combobox.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'to','custom_class' => 'do_not_change','options' => $this->_tpl_vars['employees'],'value' => $this->_tpl_vars['finance_warehouses_document']->get('to'),'hidden' => 0,'width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php $_from = $this->_tpl_vars['employees']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['eo']):
?>
                      <?php if ($this->_tpl_vars['eo']['option_value'] == $this->_tpl_vars['finance_warehouses_document']->get('to')): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['eo']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'to','value' => $this->_tpl_vars['finance_warehouses_document']->get('to'),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'date'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_date"><label for="date"<?php if ($this->_tpl_vars['messages']->getErrors('date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php ob_start(); ?><?php if ($this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_RESERVATION): ?><?php echo $this->_tpl_vars['default_release_date']; ?>
<?php else: ?><?php echo time(); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_date', ob_get_contents());ob_end_clean(); ?>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'date','width' => 200,'value' => ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('date'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_date']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_date'])))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short'])),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('date'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_date']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_date'])))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'date','value' => ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('date'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_date']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_date'])))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short'])),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'employees' && in_array ( $this->_tpl_vars['finance_warehouses_document']->get('type') , array ( @PH_FINANCE_TYPE_WASTE , @PH_FINANCE_TYPE_INSPECTION ) )): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_employees"><label for="employees"<?php if ($this->_tpl_vars['messages']->getErrors('employees')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <?php if ($this->_tpl_vars['layout']['edit']): ?>
                <td colspan="2" class="nopadding">
                  <table id="employees_container" cellspacing="0" cellpadding="0" border="0" class="floatl">
                    <tr style="display: none;">
                      <td colspan="2"></td>
                    </tr>
                    <?php $this->assign('employees', $this->_tpl_vars['finance_warehouses_document']->get('employees')); ?>
                    <?php if (! $this->_tpl_vars['employees']): ?>
                      <?php echo smarty_function_array(array('assign' => 'employees','eval' => 'array(array(\'id\' => \'\', \'code\' => \'\', \'name\' => \'\'))'), $this);?>

                    <?php endif; ?>
                    <?php $_from = $this->_tpl_vars['employees']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['j'] => $this->_tpl_vars['employee']):
        $this->_foreach['i']['iteration']++;
?>
                    <tr id="employees_container_<?php echo $this->_foreach['i']['iteration']; ?>
">
                      <td style="text-align: center; padding-right: 3px;">
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (count($this->_tpl_vars['employees']) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function() { hideField('employees_container','<?php echo $this->_foreach['i']['iteration']; ?>
'); }, this);" />
                        <a href="javascript: disableField('employees_container','<?php echo $this->_foreach['i']['iteration']; ?>
');" style="display: none;"><?php echo $this->_foreach['i']['iteration']; ?>
</a>
                      </td>
                      <td>
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => 'employees','index' => $this->_foreach['i']['iteration'],'autocomplete' => $this->_tpl_vars['autocomplete_employee'],'autocomplete_var_type' => 'basic','value' => $this->_tpl_vars['employee']['id'],'value_code' => $this->_tpl_vars['employee']['code'],'value_name' => $this->_tpl_vars['employee']['name'],'width' => 244,'standalone' => true,'label' => $this->_config[0]['vars']['employee'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      </td>
                    </tr>
                    <?php endforeach; endif; unset($_from); ?>
                  </table>
                  <div class="floatl" style="padding: 5px; width: 30px;">
                    <div class="t_buttons">
                      <div id="employees_container_plusButton" onclick="addField('employees_container', false, false, true);" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                      <div id="employees_container_minusButton"<?php if (count($this->_tpl_vars['employees']) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeField('employees_container');" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
                    </div>
                  </div>
                </td>
                <?php else: ?>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php $this->assign('employees', $this->_tpl_vars['finance_warehouses_document']->get('employees')); ?>
                  <?php if (! $this->_tpl_vars['employees']): ?>
                    <?php echo smarty_function_array(array('assign' => 'employees','eval' => 'array()'), $this);?>

                  <?php endif; ?>
                  <?php $_from = $this->_tpl_vars['employees']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['j'] => $this->_tpl_vars['employee']):
        $this->_foreach['i']['iteration']++;
?>
                    <?php if ($this->_tpl_vars['employee']['id']): ?>
                      <?php echo $this->_foreach['i']['iteration']; ?>
. <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?><br /><?php endif; ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'employees','index' => $this->_foreach['i']['iteration'],'label' => $this->_tpl_vars['layout']['name'],'value' => $this->_tpl_vars['employee']['id'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                </td>
                <?php endif; ?>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_department"><label for="department"<?php if ($this->_tpl_vars['messages']->getErrors('department')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <select class="selbox<?php if (! $this->_tpl_vars['finance_warehouses_document']->get('department')): ?> undefined<?php endif; ?>" name="department" id="department" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                      <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
                      <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                        <?php if (( ! $this->_tpl_vars['item']->isDeleted() && $this->_tpl_vars['item']->isActivated() ) || $this->_tpl_vars['item']->get('id') == $this->_tpl_vars['finance_warehouses_document']->get('department')): ?>
                        <option value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
"<?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['finance_warehouses_document']->get('department')): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if ($this->_tpl_vars['item']->isDeleted() || ! $this->_tpl_vars['item']->isActivated()): ?>*&nbsp;<?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>
</option>
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    </select>
                  <?php else: ?>
                    <?php $_from = $this->_tpl_vars['departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['item']):
?>
                      <?php if ($this->_tpl_vars['item']->get('id') == $this->_tpl_vars['finance_warehouses_document']->get('department')): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['item']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['item']->get('level'), "-") : smarty_modifier_indent($_tmp, $this->_tpl_vars['item']->get('level'), "-")); ?>

                        <input type="hidden" name="department" id="department" value="<?php echo $this->_tpl_vars['item']->get('id'); ?>
" />
                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_description"><label for="description"<?php if ($this->_tpl_vars['messages']->getErrors('description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_textarea.html", 'smarty_include_vars' => array('standalone' => true,'value' => $this->_tpl_vars['finance_warehouses_document']->get('description'),'name' => 'description','width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="description" id="description" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_notes"><label for="notes"<?php if ($this->_tpl_vars['messages']->getErrors('notes')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_textarea.html", 'smarty_include_vars' => array('standalone' => true,'value' => $this->_tpl_vars['finance_warehouses_document']->get('notes'),'name' => 'notes','width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="notes" id="notes" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'to_warehouse_data' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_to_warehouse_data"><label for="to_warehouse_data"<?php if ($this->_tpl_vars['messages']->getErrors('to_warehouse_data')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'to_warehouse_data','optgroups' => $this->_tpl_vars['to_warehouses_data'],'value' => $this->_tpl_vars['finance_warehouses_document']->get('to_warehouse_data'),'required' => 1,'really_required' => 1,'width' => 200,'standalone' => true,'onchange' => 'getEmployees(this.value, \'to_to\');','label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php if ($this->_tpl_vars['finance_warehouses_document']->get('to_warehouse_data')): ?>
                      <?php $_from = $this->_tpl_vars['to_warehouses_data']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['company_name'] => $this->_tpl_vars['wh_data']):
?>
                        <?php $_from = $this->_tpl_vars['wh_data']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['data']):
?>
                          <?php if ($this->_tpl_vars['data']['option_value'] == $this->_tpl_vars['finance_warehouses_document']->get('to_warehouse_data')): ?><span class="strong"><?php echo ((is_array($_tmp=$this->_tpl_vars['company_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['data']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                    <input type="hidden" name="to_warehouse_data" id="to_warehouse_data" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('to_warehouse_data'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'to_from' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_to_from"><label for="to_from"<?php if ($this->_tpl_vars['messages']->getErrors('to_from')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_combobox.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'to_from','custom_class' => 'do_not_change','options' => $this->_tpl_vars['employees'],'value' => $this->_tpl_vars['finance_warehouses_document']->get('to_from'),'hidden' => 0,'width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php $_from = $this->_tpl_vars['employees']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['eo']):
?>
                      <?php if ($this->_tpl_vars['eo']['option_value'] == $this->_tpl_vars['finance_warehouses_document']->get('to_from')): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['eo']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'to_from','custom_class' => 'do_not_change','value' => $this->_tpl_vars['finance_warehouses_document']->get('to_from'),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'to_to' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_to_to"><label for="to_to"<?php if ($this->_tpl_vars['messages']->getErrors('to_to')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_combobox.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'to_to','options' => $this->_tpl_vars['to_employees_options'],'value' => $this->_tpl_vars['finance_warehouses_document']->get('to_to'),'hidden' => 0,'width' => 200,'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <span id="to_to_name">
                    <?php $_from = $this->_tpl_vars['to_employees_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['eo']):
?>
                      <?php if ($this->_tpl_vars['eo']['option_value'] == $this->_tpl_vars['finance_warehouses_document']->get('to_to')): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['eo']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                    </span>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'to_to','value' => $this->_tpl_vars['finance_warehouses_document']->get('to_to'),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'to_date' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_to_date"><label for="to_date"<?php if ($this->_tpl_vars['messages']->getErrors('to_date')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'to_date','width' => 200,'value' => ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('to_date'))) ? $this->_run_mod_handler('default', true, $_tmp, time()) : smarty_modifier_default($_tmp, time())))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short'])),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('to_date'))) ? $this->_run_mod_handler('default', true, $_tmp, time()) : smarty_modifier_default($_tmp, time())))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'to_date','value' => ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['finance_warehouses_document']->get('to_date'))) ? $this->_run_mod_handler('default', true, $_tmp, time()) : smarty_modifier_default($_tmp, time())))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short'])),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'to_description' && $this->_tpl_vars['finance_warehouses_document']->get('type') == @PH_FINANCE_TYPE_COMMODITIES_TRANSFER): ?>
              <tr id="finance_warehouses_document_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><a name="error_to_description"><label for="to_description"<?php if ($this->_tpl_vars['messages']->getErrors('to_description')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php if ($this->_tpl_vars['layout']['edit']): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_textarea.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'to_description','width' => 200,'value' => $this->_tpl_vars['finance_warehouses_document']->get('to_description'),'label' => $this->_tpl_vars['layout']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('to_description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                    <input type="hidden" name="to_description" id="to_description" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['finance_warehouses_document']->get('to_description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
              <tr>
                <td colspan="3" style="padding: 5px;">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_edit.html", 'smarty_include_vars' => array('model' => $this->_tpl_vars['finance_warehouses_document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 15px;">
                  <?php echo '<input type="hidden" name="finance_after_action" id="finance_after_action" value="" /><input type="hidden" name="submit_gt2" id="submit_gt2" />'; ?><?php $_from = $this->_tpl_vars['after_action_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ai'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ai']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ak'] => $this->_tpl_vars['fin_action']):
        $this->_foreach['ai']['iteration']++;
?><?php echo '<button type="submit" name="saveButton1" id="submit_gt2_'; ?><?php echo $this->_tpl_vars['ak']; ?><?php echo '" class="button" onclick="$(\'finance_after_action\').value=\''; ?><?php echo $this->_tpl_vars['fin_action']['option_value']; ?><?php echo '\';" title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['fin_action']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['fin_action']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?>

                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['finance_warehouses_document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>
      </div>
    </td>
  </tr>
</table>