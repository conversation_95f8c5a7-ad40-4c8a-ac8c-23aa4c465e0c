<?php /* Smarty version 2.6.33, created on 2024-10-23 18:27:23
         compiled from /var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html', 1, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html', 5, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html', 32, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html', 43, false),array('modifier', 'nl2br', '/var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html', 43, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/events/templates/_remind_info.html', 43, false),)), $this); ?>
<?php $this->assign('allday_event_start_date', ((is_array($_tmp=$this->_tpl_vars['event']['event_start'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short']))); ?>
<?php $this->assign('allday_event_end_date', ((is_array($_tmp=$this->_tpl_vars['event']['event_end'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short']))); ?>
<?php if ($this->_tpl_vars['event']['visibility'] == 'private' && $this->_tpl_vars['event']['access'] != 'edit'): ?>
  <strong><?php echo $this->_config[0]['vars']['events_name']; ?>
:</strong> <strong><?php echo $this->_config[0]['vars']['events_private_event2']; ?>
</strong><?php if ($this->_tpl_vars['event']['event_start'] < $this->_tpl_vars['event']['selected_date'] && ! $this->_tpl_vars['event']['allday_event']): ?> <em>(<?php echo $this->_config[0]['vars']['events_continues_from_prev_day']; ?>
)</em><?php endif; ?><br />
  <strong><?php echo $this->_config[0]['vars']['added_by']; ?>
:</strong> <?php $_from = $this->_tpl_vars['event']['users_participants']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['up'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['up']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['participant']):
        $this->_foreach['up']['iteration']++;
?><?php if ($this->_tpl_vars['event']['added_by'] == $this->_tpl_vars['participant']['participant_id']): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['participant']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?><?php endforeach; endif; unset($_from); ?><br />
  <?php if ($this->_tpl_vars['event']['allday_event']): ?>
    <strong><?php if ($this->_tpl_vars['event']['allday_event'] == -1): ?><?php echo $this->_config[0]['vars']['events_date']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['events_allday_event']; ?>
<?php endif; ?>:</strong> <?php echo $this->_tpl_vars['allday_event_start_date']; ?>
<?php if ($this->_tpl_vars['event']['allday_event'] == -1): ?> (<?php echo $this->_tpl_vars['event']['duration']; ?>
 <?php if (abs ( $this->_tpl_vars['event']['duration'] ) != 1): ?><?php echo $this->_config[0]['vars']['minutes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['minute']; ?>
<?php endif; ?>)<?php endif; ?><?php if ($this->_tpl_vars['allday_event_start_date'] != $this->_tpl_vars['allday_event_end_date']): ?>&nbsp;&nbsp;<?php echo $this->_config[0]['vars']['to']; ?>
&nbsp;&nbsp;<?php echo $this->_tpl_vars['allday_event_end_date']; ?>
<?php endif; ?><br />
  <?php else: ?>
    <strong><?php echo $this->_config[0]['vars']['events_start_date']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['event_start'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
<br />
    <strong><?php echo $this->_config[0]['vars']['events_end_date']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['event_end'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
<br />
  <?php endif; ?>
<?php else: ?>
  <?php if ($this->_tpl_vars['event']['parent_info']): ?>
  <strong><?php echo $this->_config[0]['vars']['date']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['event_start'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
<br />
  <strong><?php echo $this->_config[0]['vars']['events_name']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['parent_info']['type_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['event']['parent_info']['module']; ?>
&amp;<?php echo $this->_tpl_vars['event']['parent_info']['module']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['event']['parent_info']['id']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['event']['parent_info']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if ($this->_tpl_vars['event']['event_start'] < $this->_tpl_vars['event']['selected_date'] && ! $this->_tpl_vars['event']['allday_event']): ?> <em>(<?php echo $this->_config[0]['vars']['events_continues_from_prev_day']; ?>
)</em><?php endif; ?></a><br />
  <?php else: ?>
  <strong><?php echo $this->_config[0]['vars']['events_name']; ?>
:</strong> <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=events&amp;events=view&amp;view=<?php echo $this->_tpl_vars['event']['id']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['event']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if ($this->_tpl_vars['event']['event_start'] < $this->_tpl_vars['event']['selected_date'] && ! $this->_tpl_vars['event']['allday_event']): ?> <em>(<?php echo $this->_config[0]['vars']['events_continues_from_prev_day']; ?>
)</em><?php endif; ?></a><br />
  <strong><?php echo $this->_config[0]['vars']['events_type']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['type_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
  <?php if ($this->_tpl_vars['event']['allday_event']): ?>
    <strong><?php if ($this->_tpl_vars['event']['allday_event'] == -1): ?><?php echo $this->_config[0]['vars']['events_date']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['events_allday_event']; ?>
<?php endif; ?>:</strong> <?php echo $this->_tpl_vars['allday_event_start_date']; ?>
<?php if ($this->_tpl_vars['event']['allday_event'] == -1): ?> (<?php echo $this->_tpl_vars['event']['duration']; ?>
 <?php if (abs ( $this->_tpl_vars['event']['duration'] ) != 1): ?><?php echo $this->_config[0]['vars']['minutes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['minute']; ?>
<?php endif; ?>)<?php endif; ?><?php if ($this->_tpl_vars['allday_event_start_date'] != $this->_tpl_vars['allday_event_end_date']): ?>&nbsp;&nbsp;<?php echo $this->_config[0]['vars']['to']; ?>
&nbsp;&nbsp;<?php echo $this->_tpl_vars['allday_event_end_date']; ?>
<?php endif; ?><br />
  <?php else: ?>
    <strong><?php echo $this->_config[0]['vars']['events_start_date']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['event_start'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
<br />
    <strong><?php echo $this->_config[0]['vars']['events_end_date']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['event_end'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
<br />
  <?php endif; ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['event']['customer']): ?>
    <strong><?php echo $this->_config[0]['vars']['events_customer']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['customer_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['event']['location']): ?>
    <strong><?php echo $this->_config[0]['vars']['events_location']; ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['event']['location'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
  <?php endif; ?>
  <?php if (! $this->_tpl_vars['event']['parent_info'] || count($this->_tpl_vars['event']['users_participants']) > 1): ?>
  <strong><?php if ($this->_tpl_vars['event']['parent_info']): ?><?php echo $this->_config[0]['vars']['events_reminder_participants']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['events_participants']; ?>
<?php endif; ?>:</strong> <?php $_from = $this->_tpl_vars['event']['customers_participants']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cp'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cp']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['participant']):
        $this->_foreach['cp']['iteration']++;
?>
    <?php ob_start(); ?>events_participant_status_<?php echo $this->_tpl_vars['participant']['user_status']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_name', ob_get_contents());ob_end_clean(); ?>
    <?php if ($this->_tpl_vars['participant']['user_status'] == 'pending'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pending.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php elseif ($this->_tpl_vars['participant']['user_status'] == 'confirmed'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
message.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php elseif ($this->_tpl_vars['participant']['user_status'] == 'not_sure'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
not_sure.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php elseif ($this->_tpl_vars['participant']['user_status'] == 'denied'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
error.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['participant']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! ($this->_foreach['cp']['iteration'] == $this->_foreach['cp']['total'])): ?>,<?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
  <?php $_from = $this->_tpl_vars['event']['users_participants']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['up'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['up']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['participant']):
        $this->_foreach['up']['iteration']++;
?>
    <?php ob_start(); ?>events_participant_status_<?php echo $this->_tpl_vars['participant']['user_status']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_name', ob_get_contents());ob_end_clean(); ?>
    <?php if ($this->_tpl_vars['participant']['user_status'] == 'pending'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pending.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php elseif ($this->_tpl_vars['participant']['user_status'] == 'confirmed'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
message.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php elseif ($this->_tpl_vars['participant']['user_status'] == 'not_sure'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
not_sure.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php elseif ($this->_tpl_vars['participant']['user_status'] == 'denied'): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
error.png" width="10" height="10" alt="" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_name']]; ?>
" border="0" /><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['participant']['assigned_to_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! ($this->_foreach['up']['iteration'] == $this->_foreach['up']['total'])): ?>,<?php endif; ?>
  <?php endforeach; endif; unset($_from); ?><br />
  <?php endif; ?>
  <?php if (! $this->_tpl_vars['short_info'] && $this->_tpl_vars['event']['event_type'] != @PH_REMINDER_EVENT_TYPE): ?>
    <strong><?php echo $this->_config[0]['vars']['events_description']; ?>
:</strong> <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['event']['description'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 150) : smarty_modifier_mb_truncate($_tmp, 150)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

  <?php endif; ?>
<?php endif; ?>