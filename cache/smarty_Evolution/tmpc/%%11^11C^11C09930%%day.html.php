<?php /* Smarty version 2.6.33, created on 2025-01-17 17:24:55
         compiled from /var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/day.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'math', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/day.html', 2, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/day.html', 5, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/day.html', 24, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/day.html', 38, false),array('modifier', 'trim', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/day.html', 38, false),)), $this); ?>
<?php echo ''; ?><?php echo smarty_function_math(array('equation' => "x+2",'x' => $this->_tpl_vars['week_days_number'],'assign' => 'table_cols'), $this);?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['monday_start'] && $this->_tpl_vars['calendar']->day_of_week == 0): ?><?php echo '7'; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['calendar']->day_of_week; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_day_of_week', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php $this->assign('week_num', $this->_tpl_vars['calendar']->day->week); ?><?php echo ''; ?><?php $this->assign('all_days_count', count($this->_tpl_vars['all_day_events'])); ?><?php echo ''; ?><?php $this->assign('day', $this->_tpl_vars['calendar']->day); ?><?php echo ''; ?><?php echo ''; ?><?php $this->assign('event_cell_height', 25); ?><?php echo ''; ?><?php $this->assign('event_allday_cell_height', 26); ?><?php echo ''; ?><?php $this->assign('event_cell_width', 850); ?><?php echo ''; ?><?php $this->assign('event_overlapping', 8); ?><?php echo ''; ?><?php $this->assign('event_area_offset_left', 16); ?><?php echo ''; ?><?php $this->assign('event_area_border_width', 1); ?><?php echo ''; ?><?php $this->assign('number_of_parts_in_hour', 4); ?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => "60/x",'x' => $this->_tpl_vars['number_of_parts_in_hour'],'assign' => 'minutes_interval'), $this);?><?php echo ''; ?>

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_event_colors_style.html", 'smarty_include_vars' => array('calendar_settings' => $this->_tpl_vars['calendar_settings'],'event' => $this->_tpl_vars['event'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<div class="nz-page-wrapper nz-calendar-page-wrapper">
  <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">
    <div class="nz-page-title"><h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1></div>
    <div class="nz-page-actions">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_actions'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

    <div id="form_container" class="nz-page-content main_panel_container">
      <div class="nz-calendar-wrapper nz-calendar__dayview">
        <?php ob_start(); ?><?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('navUrl', ob_get_contents());ob_end_clean(); ?>
        <div class="nz-calendar-navigation">
          <a href="<?php echo $this->_tpl_vars['navUrl']; ?>
&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay(-1); ?>
" class="nz-icon-button" title="<?php echo $this->_config[0]['vars']['calendars_previous_day']; ?>
">chevron_left</a>
          <a href="<?php echo $this->_tpl_vars['navUrl']; ?>
&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay(1); ?>
" class="nz-icon-button" title="<?php echo $this->_config[0]['vars']['calendars_next_day']; ?>
">chevron_right</a>
          <div class="nz-calendar-day__week-menu">
            <?php if (! $this->_tpl_vars['monday_start'] && $this->_tpl_vars['week_days_number'] > 6): ?>
            <?php echo smarty_function_math(array('equation' => "-x",'x' => $this->_tpl_vars['calendar']->day_of_week,'assign' => 'offset_date'), $this);?>

            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']); ?>
"><?php echo $this->_config[0]['vars']['weekday_0']; ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_mini']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_mini'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)); ?>
)</a>
            <?php endif; ?>
            <?php unset($this->_sections['weekdays']);
$this->_sections['weekdays']['name'] = 'weekdays';
$this->_sections['weekdays']['loop'] = is_array($_loop=6) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['weekdays']['start'] = (int)1;
$this->_sections['weekdays']['show'] = true;
$this->_sections['weekdays']['max'] = $this->_sections['weekdays']['loop'];
$this->_sections['weekdays']['step'] = 1;
if ($this->_sections['weekdays']['start'] < 0)
    $this->_sections['weekdays']['start'] = max($this->_sections['weekdays']['step'] > 0 ? 0 : -1, $this->_sections['weekdays']['loop'] + $this->_sections['weekdays']['start']);
else
    $this->_sections['weekdays']['start'] = min($this->_sections['weekdays']['start'], $this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] : $this->_sections['weekdays']['loop']-1);
if ($this->_sections['weekdays']['show']) {
    $this->_sections['weekdays']['total'] = min(ceil(($this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] - $this->_sections['weekdays']['start'] : $this->_sections['weekdays']['start']+1)/abs($this->_sections['weekdays']['step'])), $this->_sections['weekdays']['max']);
    if ($this->_sections['weekdays']['total'] == 0)
        $this->_sections['weekdays']['show'] = false;
} else
    $this->_sections['weekdays']['total'] = 0;
if ($this->_sections['weekdays']['show']):

            for ($this->_sections['weekdays']['index'] = $this->_sections['weekdays']['start'], $this->_sections['weekdays']['iteration'] = 1;
                 $this->_sections['weekdays']['iteration'] <= $this->_sections['weekdays']['total'];
                 $this->_sections['weekdays']['index'] += $this->_sections['weekdays']['step'], $this->_sections['weekdays']['iteration']++):
$this->_sections['weekdays']['rownum'] = $this->_sections['weekdays']['iteration'];
$this->_sections['weekdays']['index_prev'] = $this->_sections['weekdays']['index'] - $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['index_next'] = $this->_sections['weekdays']['index'] + $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['first']      = ($this->_sections['weekdays']['iteration'] == 1);
$this->_sections['weekdays']['last']       = ($this->_sections['weekdays']['iteration'] == $this->_sections['weekdays']['total']);
?>
              <?php ob_start(); ?>weekday_<?php echo $this->_sections['weekdays']['index']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_label', ob_get_contents());ob_end_clean(); ?>
              <?php echo smarty_function_math(array('equation' => "y-x",'x' => $this->_tpl_vars['current_day_of_week'],'y' => $this->_sections['weekdays']['index'],'assign' => 'offset_date'), $this);?>

              <?php $this->assign('week_day_index', $this->_sections['weekdays']['index']-1); ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']); ?>
"<?php if ($this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']) == $this->_tpl_vars['calendar']->dateISO): ?> class="nz--active"<?php endif; ?>><?php echo $this->_config[0]['vars'][$this->_tpl_vars['week_label']]; ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_mini']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_mini'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)); ?>
)</a>
            <?php endfor; endif; ?>
            <?php if ($this->_tpl_vars['week_days_number'] > 5): ?>
              <?php echo smarty_function_math(array('equation' => "6-x",'x' => $this->_tpl_vars['current_day_of_week'],'assign' => 'offset_date'), $this);?>

              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']); ?>
"><?php echo $this->_config[0]['vars']['weekday_6']; ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_mini']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_mini'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)); ?>
)</a>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['monday_start'] && $this->_tpl_vars['week_days_number'] > 6): ?>
              <?php echo smarty_function_math(array('equation' => "7-x",'x' => $this->_tpl_vars['current_day_of_week'],'assign' => 'offset_date'), $this);?>

              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo $this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']); ?>
"><?php echo $this->_config[0]['vars']['weekday_0']; ?>
 (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['calendar']->offsetDay($this->_tpl_vars['offset_date']))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_mini']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_mini'])))) ? $this->_run_mod_handler('trim', true, $_tmp) : trim($_tmp)); ?>
)</a>
            <?php endif; ?>
          </div>
        </div>

        <table border="0" cellpadding="0" cellspacing="0" class="nz-calendar-tbl">
          <tr class="nz-calendar-tbl-head">
            <td class="nz-calendar-tbl__hour">&nbsp;</td>
            <td><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->dateISO)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
</td>
          </tr>
          <tr class="nz-calendar-tbl-body">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events_list_time_column.html", 'smarty_include_vars' => array('is_last' => $this->_sections['all_day']['last'],'event_allday_cell_height' => $this->_tpl_vars['event_allday_cell_height'],'all_days_count' => $this->_tpl_vars['all_days_count'],'day_hours' => $this->_tpl_vars['day_hours'],'number_of_parts_in_hour' => $this->_tpl_vars['number_of_parts_in_hour'],'day_start' => $this->_tpl_vars['day_start'],'calendar' => $this->_tpl_vars['calendar'],'event_cell_height' => $this->_tpl_vars['event_cell_height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>


            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events_list_day_events.html", 'smarty_include_vars' => array('calendar' => $this->_tpl_vars['calendar'],'events' => $this->_tpl_vars['events'],'day' => $this->_tpl_vars['calendar'],'event_allday_cell_height' => $this->_tpl_vars['event_allday_cell_height'],'all_days_count' => $this->_tpl_vars['all_days_count'],'day_start' => $this->_tpl_vars['day_start'],'event_cell_width' => $this->_tpl_vars['event_cell_width'],'event_overlapping' => $this->_tpl_vars['event_overlapping'],'event_area_offset_left' => $this->_tpl_vars['event_area_offset_left'],'event_area_border_width' => $this->_tpl_vars['event_area_border_width'],'all_days_events' => $this->_tpl_vars['all_day_events'],'day_hours' => $this->_tpl_vars['day_hours'],'number_of_parts_in_hour' => $this->_tpl_vars['number_of_parts_in_hour'],'minutes_interval' => $this->_tpl_vars['minutes_interval'],'event_cell_height' => $this->_tpl_vars['event_cell_height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </tr>
        </table>
      </div>
    </div>
  </div>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_side_panel.html", 'smarty_include_vars' => array('panel_width' => '300')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>