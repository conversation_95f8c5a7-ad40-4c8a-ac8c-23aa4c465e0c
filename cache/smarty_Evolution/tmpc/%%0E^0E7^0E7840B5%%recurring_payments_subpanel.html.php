<?php /* Smarty version 2.6.33, created on 2024-07-10 14:36:56
         compiled from /var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_subpanel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_subpanel.html', 5, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_subpanel.html', 18, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_subpanel.html', 34, false),array('function', 'counter', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_subpanel.html', 13, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/finance/templates/recurring_payments_subpanel.html', 30, false),)), $this); ?>

<form name="finance_recurring_payment" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=recurring_payments" method="post" enctype="multipart/form-data">
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['gt2_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['gt2_total_with_vat'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption" width="80">&nbsp;</td>
  </tr>
<?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

<?php $_from = $this->_tpl_vars['finance_recurring_payments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['recurring_payment']):
        $this->_foreach['i']['iteration']++;
?>
<?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_recurring_payments_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['recurring_payment']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['recurring_payment']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['recurring_payment']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['recurring_payment']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="t_border hright"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
    <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
" nowrap="nowrap"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;controller=recurring_payments&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['recurring_payment']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></td>
    <td class="t_border" nowrap="nowrap">
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['recurring_payment']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
    </td>
    <td class="t_border" nowrap="nowrap">
      <?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    </td>
    <td class="t_border" nowrap="nowrap" align="right">
      <?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('total'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    </td>
    <td class="t_border" nowrap="nowrap" align="right">
      <?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('total_with_vat'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['recurring_payment']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    </td>
    <td class="hcenter" nowrap="nowrap">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['recurring_payment'],'exclude' => 'delete')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
<?php endforeach; else: ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="error" colspan="9"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
<?php endif; unset($_from); ?>
  <tr>
    <td class="t_footer" colspan="9"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['search_link'],'use_ajax' => $this->_tpl_vars['use_ajax'],'hide_selection_stats' => true,'session_param' => $this->_tpl_vars['session_param'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>
</form>