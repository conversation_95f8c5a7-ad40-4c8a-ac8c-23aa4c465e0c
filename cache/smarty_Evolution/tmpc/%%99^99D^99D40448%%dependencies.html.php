<?php /* Smarty version 2.6.33, created on 2025-01-31 15:50:27
         compiled from /var/www/Nzoom-Evolution/_libs/modules/tasks/templates/dependencies.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/dependencies.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/dependencies.html', 11, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/dependencies.html', 38, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<form name="dependencies" id="dependencies" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
<input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['task']->get('id'); ?>
" />
<input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
  <tr>
    <td class="nopadding">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info_header.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </table>
    </td>
  </tr>

  <!-- Task dependencies -->
  <tr>
    <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="tasks_dependencies_switch"><div class="switch_<?php if ($_COOKIE['tasks_dependencies_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <tr id="tasks_dependencies"<?php if ($_COOKIE['tasks_dependencies_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
    <td class="relatives_cell_style">
      <?php if ($this->_tpl_vars['parents_tree']): ?>
        <h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
dependencies.png" width="16" height="16" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_tree'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_tree'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_tree_parents_dependencies'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_tree.html", 'smarty_include_vars' => array('pc_tree' => $this->_tpl_vars['parents_tree'],'sfx' => 'parents','root' => $this->_tpl_vars['task'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>

      <?php if ($this->_tpl_vars['children_tree']): ?>
        <h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
dependencies.png" width="16" height="16" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_tree'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_tree'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_tree_children_dependencies'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_tree.html", 'smarty_include_vars' => array('pc_tree' => $this->_tpl_vars['children_tree'],'sfx' => 'children','root' => $this->_tpl_vars['task'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>

      <br />
      <a name="error_referer"></a><h3><?php echo smarty_function_help(array('label' => 'dependencies_action'), $this);?>
</h3>
      <div id="tasks_referers">
        <div id="tasks_toggleCheckboxes" style="width: 300px; display: <?php if (is_array ( $this->_tpl_vars['task']->get('tasks_dependencies') ) && @ count ( $this->_tpl_vars['task']->get('tasks_dependencies') ) > 4): ?>block<?php else: ?>none<?php endif; ?>;">
          <span onclick="toggleCheckboxes(this, 'tasks_referers', true)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
          <span onclick="toggleCheckboxes(this, 'tasks_referers', false)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        </div>
<?php if ($this->_tpl_vars['task']->get('tasks_dependencies')): ?>
<?php $_from = $this->_tpl_vars['task']->get('tasks_dependencies'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
        <input type="checkbox" name="tasks_referers[]" id="tasks_ref<?php echo $this->_tpl_vars['ref']['id']; ?>
" value="<?php echo $this->_tpl_vars['ref']['id']; ?>
" checked="checked" />
        <select class="selbox" style="width: 400px;" name="origin[<?php echo $this->_tpl_vars['ref']['id']; ?>
]" id="origin_<?php echo $this->_tpl_vars['ref']['id']; ?>
">
          <option value="F2S"<?php if ($this->_tpl_vars['ref']['origin'] == 'F2S'): ?> selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['tasks_F2S']; ?>
</option>
          <option value="F2F"<?php if ($this->_tpl_vars['ref']['origin'] == 'F2F'): ?> selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['tasks_F2F']; ?>
</option>
          <option value="S2S"<?php if ($this->_tpl_vars['ref']['origin'] == 'S2S'): ?> selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['tasks_S2S']; ?>
</option>
          <option value="S2F"<?php if ($this->_tpl_vars['ref']['origin'] == 'S2F'): ?> selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['tasks_S2F']; ?>
</option>
        </select>
        <?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_chosen_task'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
" target="_blank"><?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
        <br />
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
      </div>
      <br />
      <button type="button" name="filterButton" class="button" onclick="var popUrl='<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=tasks&amp;tasks=filter&amp;open_from=tasks&amp;model_id=<?php echo $this->_tpl_vars['task']->get('id'); ?>
'; pop(popUrl, 820, 580);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
...</button>
    </td>
  </tr>

  <tr>
    <td class="relatives_button_cell">
      <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>
</div>