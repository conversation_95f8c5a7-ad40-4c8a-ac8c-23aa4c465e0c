<?php /* Smarty version 2.6.33, created on 2024-08-13 16:58:50
         compiled from /var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_filter_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_filter_panel.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/calendars/view/templates/_filter_panel.html', 24, false),)), $this); ?>
<form method="post" action="<?php echo ((is_array($_tmp=$_SERVER['REQUEST_URI'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
    <div>
        <div class="nz-calendar-filter">
            <label for="assignments_autocomplete"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars_show_events'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
        </div>
        <div>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('name' => 'assignments','autocomplete' => $this->_tpl_vars['assignments_autocomplete'],'autocomplete_type' => 'autocompleters','autocomplete_var_type' => 'basic','standalone' => true,'width' => 288,'label' => $this->_config[0]['vars']['calendars_search_departments_and_users'],'show_placeholder' => 'label')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </div>
    </div>
    <div>
        <ul id="filter_assignments_list">
        <?php if ($this->_tpl_vars['selected_departments'] || $this->_tpl_vars['selected_users']): ?>
            <?php $_from = $this->_tpl_vars['selected_departments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['sd'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['sd']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['department']):
        $this->_foreach['sd']['iteration']++;
?>
                <li id="assignments_department_<?php echo $this->_tpl_vars['department']['id']; ?>
"
                     class="nz-calendar-filter-item"><i class="nz-icon-button action-remove" title="<?php echo $this->_config[0]['vars']['delete']; ?>
"><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</i><input type="hidden" name="assignments_filters[]" value="department_<?php echo $this->_tpl_vars['department']['id']; ?>
" />
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['department']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</li>
            <?php endforeach; endif; unset($_from); ?>
            <?php $_from = $this->_tpl_vars['selected_users']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['su'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['su']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['user']):
        $this->_foreach['su']['iteration']++;
?>
                <li id="assignments_user_<?php echo $this->_tpl_vars['user']['id']; ?>
"
                     class="nz-calendar-filter-item"><i class="nz-icon-button action-remove" title="<?php echo $this->_config[0]['vars']['delete']; ?>
"><?php echo $this->_tpl_vars['theme']->getIconForAction('delete'); ?>
</i><input type="hidden" name="assignments_filters[]" value="user_<?php echo $this->_tpl_vars['user']['id']; ?>
" />
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php if ($this->_tpl_vars['user']['default_department']): ?> (<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['user']['default_department'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
)<?php endif; ?></li>
            <?php endforeach; endif; unset($_from); ?>
        <?php endif; ?>
        </ul>
    </div>
    <div class="nz-calendar-filter nz-calendar-filter-event-types">
        <label><?php echo $this->_config[0]['vars']['calendars_events_types']; ?>
</label>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_checkbox_group.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'events_types','label' => $this->_tpl_vars['events_types']['label'],'help' => $this->_tpl_vars['events_types']['help'],'options' => $this->_tpl_vars['events_types']['options'],'value' => $this->_tpl_vars['events_types']['value'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="nz-form--controls nz-calendar-filter-controls">
        <label>
            <input type="checkbox" name="getAll" id="getAll" value="1"<?php if ($this->_tpl_vars['getAll']): ?> checked="checked"<?php endif; ?>/>
            <?php echo $this->_config[0]['vars']['calendars_events_all']; ?>

        </label>
        <input type="submit" name="filter" value="<?php echo $this->_config[0]['vars']['filter']; ?>
" class="nz-form-button nz-button-primary" />
    </div>
</form>