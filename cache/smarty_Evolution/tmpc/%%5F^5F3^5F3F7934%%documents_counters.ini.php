<?php $_config_vars = array (
  'documents_counters' => 'Documents counters',
  'documents_counters_name' => 'Name',
  'documents_counters_formula' => 'Formula',
  'documents_counters_description' => 'Description',
  'documents_counters_next_number' => 'Next number',
  'documents_counters_count_documents' => 'Number of documents',
  'documents_counters_types_used' => 'Used in types of documents',
  'documents_counters_status' => 'Status',
  'documents_counters_status_active' => 'Active',
  'documents_counters_status_inactive' => 'Inactive',
  'documents_counters_added_by' => 'Added by',
  'documents_counters_modified_by' => 'Modified by',
  'documents_counters_added' => 'Added on',
  'documents_counters_modified' => 'Modified on',
  'documents_counters_add' => 'Add documents counter',
  'documents_counters_edit' => 'Edit documents counter',
  'documents_counters_view' => 'View documents counter',
  'documents_counters_translate' => 'Translate documents counter',
  'message_documents_counters_add_success' => 'Data for counter successfully added!',
  'message_documents_counters_edit_success' => 'Data for counter successfully edited!',
  'message_documents_counters_translate_success' => 'Data for counter successfully translated!',
  'error_documents_counters_edit_failed' => 'Data for counter not edited successfully:',
  'error_documents_counters_add_failed' => 'Data for counter not added successfully:',
  'error_documents_counters_translate_failed' => 'Data for counter not translated successfully:',
  'error_no_counter_name_specified' => 'No name entered!',
  'error_no_counter_formula_specified' => 'No formula entered!',
  'error_invalid_next_number' => 'Please, enter next counter number containing only digits and with value greater than 0!',
  'error_documents_counter_mutex_num' => 'Counter formula can contain only one of [document_num] and [customer_num] components!',
  'error_no_types_used' => 'not used in any document type',
  'error_no_medial_number_index_specified' => 'Not entered index for medial documents!',
  'error_missing_index_delimiter' => 'Not entered separator for medial document index!',
  'error_no_selected_medial_number_index_position' => 'No position selected for the medial number index!',
  'documents_counters_medial_number_index' => 'Medial number index',
  'documents_counters_medial_number_delimiter' => 'Separator for medial number',
  'documents_counters_medial_number_index_position' => 'Position of medial number index',
  'documents_counters_medial_number_index_number' => 'numbers (0-9)',
  'documents_counters_medial_number_index_latin_capital_letters' => 'latin capital letters (A-Z)',
  'documents_counters_medial_number_index_latin_small_letters' => 'latin small letters (a-z)',
  'documents_counters_medial_number_index_cyrilic_capital_letters' => 'cyrilic capital letters (А-Я)',
  'documents_counters_medial_number_index_cyrilic_small_letters' => 'cyrilic small letters (а-я)',
  'documents_counters_medial_number_index_position_prefix' => 'prefix',
  'documents_counters_medial_number_index_position_suffix' => 'suffix',
  'documents_counters_formula_delimiter' => 'Delimiter',
  'documents_counters_empty_delimiter' => 'no delimiter',
  'documents_counters_formula_leading_zeroes' => 'Number of leading zeroes',
  'documents_counters_formula_date_format' => 'format',
  'documents_counters_formula_date_delimiter' => 'using delimiter',
  'documents_counters_formula_date_format_year' => 'yyyy',
  'documents_counters_formula_date_format_year_short' => 'yy',
  'documents_counters_formula_date_format_month' => 'mm',
  'documents_counters_formula_date_format_day' => 'dd',
  'documents_counters_formula_date_format1' => 'yyyy',
  'documents_counters_formula_date_format2' => 'mm/yyyy',
  'documents_counters_formula_date_format3' => 'mm/yy',
  'documents_counters_formula_date_format4' => 'yyyy/mm',
  'documents_counters_formula_date_format5' => 'yy/mm',
  'documents_counters_formula_date_format6' => 'dd/mm/yyyy',
  'documents_counters_formula_date_format7' => 'dd/mm/yy',
  'documents_counters_formula_date_format8' => 'mm/dd/yyyy',
  'documents_counters_formula_date_format9' => 'mm/dd/yy',
  'documents_counters_formula_date_format10' => 'yyyy/dd/mm',
  'documents_counters_formula_date_format11' => 'yy/dd/mm',
  'documents_counters_formula_date_format12' => 'yyyy/mm/dd',
  'documents_counters_formula_date_format13' => 'yy/mm/dd',
  'documents_counters_formula_date_format14' => 'yy',
  'documents_counters_formula_date_format15' => 'yyy/mm',
  'documents_counters_formula_legend' => 'Legend for filling in the counter formula.',
  'documents_counters_formula_prefix' => 'Prefix',
  'documents_counters_formula_document_num' => 'Document number',
  'documents_counters_formula_customer_code' => 'Contractor code',
  'documents_counters_formula_project_code' => 'Project code',
  'documents_counters_formula_office_code' => 'Office code',
  'documents_counters_formula_user_code' => 'User code',
  'documents_counters_formula_document_type_code' => 'Document type code',
  'documents_counters_formula_transform_subnum' => 'Transformation Serial No.',
  'documents_counters_formula_parent_doc_num' => 'Parent document number (for transformed documents)',
  'documents_counters_formula_customer_num' => 'Document number per customer',
  'documents_counters_formula_customer_year' => 'For current year only',
  'documents_counters_formula_document_added' => 'Document adding date',
  'documents_counters_formula_office_num' => 'Document number per office',
  'documents_counters_formula_office_year' => 'For current year only',
  'documents_counters_formula_prefix_descr' => 'directly fills in with 2-3 letters. e.g. OFF for offers.',
  'documents_counters_formula_document_num_descr' => 'fills in document Serial No.',
  'documents_counters_formula_customer_code_descr' => 'fills in code of contractor selected for document.',
  'documents_counters_formula_project_code_descr' => 'fills in code of the project selected for document. If no project is selected, code is not to be filled in.',
  'documents_counters_formula_office_code_descr' => 'fills in code of the office selected for document. If no office is selected, code is not to be filled in.',
  'documents_counters_formula_user_code_descr' => 'fills in the code of the user that created the document.',
  'documents_counters_formula_document_type_code_descr' => 'fills in code of the document type.',
  'documents_counters_formula_transform_subnum_descr' => '.01.01, etc. upon transformation from a primary/secondary document to a secondary document.',
  'documents_counters_formula_parent_doc_num_descr' => 'fills in the parent document number (for transformed documents).',
  'documents_counters_formula_customer_num_descr' => 'fills in next number of document for customer. Number can be specified according to documents for current year only or all.',
  'documents_counters_formula_document_added_descr' => 'date of document adding.',
  'documents_counters_formula_note' => '<strong>NOTE:</strong> To the counter formula can be added <strong>only 5 elements</strong>',
  'help_documents_counters_name' => '',
  'help_documents_counters_formula' => '',
  'help_documents_counters_count_documents' => '',
  'help_documents_counters_types_used' => '',
  'help_documents_counters_status' => '',
  'help_documents_counters_description' => '',
  'help_documents_counters_next_number' => 'Next number. You can use this field to set the starting counter number.',
  'help_documents_counters_medial_number_index' => 'Specifies how the index number for this document type will be formed',
  'help_documents_counters_medial_number_delimiter' => 'Separator between the number of the document and the medial index',
  'help_documents_counters_medial_number_index_position' => 'Position of the index from the document number',
); ?>