<?php /* Smarty version 2.6.33, created on 2024-10-24 18:33:22
         compiled from /var/www/Nzoom-Evolution/_libs/modules/customers/view/templates/_branches_add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/customers/view/templates/_branches_add.html', 3, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/customers/view/templates/_branches_add.html', 7, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/customers/view/templates/_branches_add.html', 10, false),)), $this); ?>
    <form name="branches_add" action="" method="post" onsubmit="saveBranch(this,'<?php echo $this->_tpl_vars['customers_branches_session_param']; ?>
', 'add', '<?php echo $this->_tpl_vars['branch']->get('parent_customer_id'); ?>
'); return false;">
      <input type="hidden" name="parent_customer_id" id="parent_customer_id" value="<?php echo $this->_tpl_vars['branch']->get('parent_customer_id'); ?>
" />
      <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['branch']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />

      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border t_layout_table" style="width: 75%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="3"><div class="t_caption_title"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['branch']->get('model_lang'); ?>
.png" alt="" <?php ob_start(); ?>lang_<?php echo $this->_tpl_vars['branch']->get('model_lang'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lang_label', ob_get_contents());ob_end_clean(); ?> title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['lang_label']]; ?>
" class="t_flag" /><?php echo ((is_array($_tmp=$this->_tpl_vars['branch']->getBranchLabels('customers_branches_add_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
        <tr>
          <td class="labelbox"><label for="name"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['branch']->getBranchLabels('customers_branches_name')), $this);?>
</label></td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="name" id="name" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['branch']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['branch']->getBranchLabels('customers_branches_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a id="error_country"><label for="country"<?php if ($this->_tpl_vars['messages']->getErrors('country')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'branches_country'), $this);?>
</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            <select class="selbox<?php if (! $this->_tpl_vars['branch']->get('country')): ?> undefined<?php endif; ?>" name="country" id="country" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_branches_country'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);">
              <option value="" class="undefined"<?php if (! $this->_tpl_vars['branch']->get('country')): ?> selected="selected"<?php endif; ?>>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
              <?php $_from = $this->_tpl_vars['countries']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['country']):
?>
                <option value="<?php echo $this->_tpl_vars['country']['option_value']; ?>
"<?php if ($this->_tpl_vars['branch']->get('country') == $this->_tpl_vars['country']['option_value']): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['country']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="city"><?php echo smarty_function_help(array('label' => 'branches_city'), $this);?>
</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="city" id="city" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['branch']->get('city'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_branches_city'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="postal_code"><?php echo smarty_function_help(array('label' => 'branches_postal_code'), $this);?>
</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="postal_code" id="postal_code" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['branch']->get('postal_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_branches_postal_code'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="address"><?php echo smarty_function_help(array('label' => 'branches_address'), $this);?>
</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="address" id="address" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_branches_address'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['branch']->get('address'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox" colspan="3"><?php echo smarty_function_help(array('label' => 'branches_contacts'), $this);?>
</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="3" class="nopadding">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_contact_data.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['branch'],'predefined_contact_params' => $this->_tpl_vars['branch']->predefinedBranchContactParameters)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr><td colspan="3"></td></tr>
        <tr>
          <td colspan="3" class="nz-form--controls">
            <button type="submit" class="nz-form-button nz-button-primary" name="addBranch" id="addBranch"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><button type="button" name="cancel" class="nz-form-button" onclick="confirmAction('cancel', function() { $('branch_custom_panel').innerHTML = ''; }, this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['help_cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="3"></td>
        </tr>
      </table>
    </form>