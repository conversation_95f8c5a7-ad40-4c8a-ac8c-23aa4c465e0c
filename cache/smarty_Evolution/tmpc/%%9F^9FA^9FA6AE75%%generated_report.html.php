<?php /* Smarty version 2.6.33, created on 2025-02-14 18:46:16
         compiled from /var/www/Nzoom-Evolution/_libs/modules/reports/plugins/hr_who_is_resting/generated_report.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/hr_who_is_resting/generated_report.html', 23, false),array('modifier', 'count', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/hr_who_is_resting/generated_report.html', 32, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/hr_who_is_resting/generated_report.html', 39, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/hr_who_is_resting/generated_report.html', 51, false),array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/reports/plugins/hr_who_is_resting/generated_report.html', 46, false),)), $this); ?>
<?php echo '
  <style type="text/css">
    .reports_day_off_approved {
        background-color: #BAFF8C!important;
    }
    .reports_day_off_disapproved {
        background-color: #F4878D!important;
    }
    .reports_day_off_requested {
        background-color: #FFFDC0!important;
    }
    .reports_day_off_sickness {
        background-color: #98BCFF!important;
    }
    .reports_day_off_home_office {
        background-color: #66CC99!important;
    }
  </style>
'; ?>


<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row hcenter">
    <td class="t_border"<?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?> rowspan="2"<?php endif; ?> style="vertical-align: middle;"><div style="width: 140px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_department'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_border"<?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?> rowspan="2"<?php endif; ?> style="vertical-align: middle;"><div style="width: 250px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_employee'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td<?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?> rowspan="2"<?php endif; ?> class="t_border" style="vertical-align: middle;"><div style="width: 300px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_deputy'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td<?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?> rowspan="2"<?php endif; ?> class="t_border" style="vertical-align: middle;"><div style="width: 60px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_total_days_off'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td<?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?> rowspan="2"<?php endif; ?> <?php if (@HOME_OFFICE_DOCUMENT_ID || $this->_tpl_vars['reports_additional_options']['periods']): ?> class="t_border"<?php endif; ?> style="vertical-align: middle;"><div style="width: 60px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_total_sickness'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php if (@HOME_OFFICE_DOCUMENT_ID): ?>
      <td<?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?> class="t_border" rowspan="2"<?php endif; ?> style="vertical-align: middle;"><div style="width: 60px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['reports_total_home_office'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php endif; ?>
    <?php $_from = $this->_tpl_vars['reports_additional_options']['periods']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pw'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pw']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['period_week']):
        $this->_foreach['pw']['iteration']++;
?>
      <td<?php if (! ($this->_foreach['pw']['iteration'] == $this->_foreach['pw']['total'])): ?> class="t_border"<?php endif; ?> colspan="<?php if (is_array ( $this->_tpl_vars['period_week']['days'] )): ?><?php echo count($this->_tpl_vars['period_week']['days']); ?>
<?php else: ?>0<?php endif; ?>" style="vertical-align: middle;"><div style=""><?php echo ((is_array($_tmp=$this->_tpl_vars['period_week']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php endforeach; endif; unset($_from); ?>
  </tr>
  <?php if ($this->_tpl_vars['reports_additional_options']['periods']): ?>
    <tr class="reports_title_row hcenter">
      <?php $_from = $this->_tpl_vars['reports_additional_options']['periods']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pwd'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pwd']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['period_week']):
        $this->_foreach['pwd']['iteration']++;
?>
        <?php $_from = $this->_tpl_vars['period_week']['days']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['pd'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['pd']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['day']):
        $this->_foreach['pd']['iteration']++;
?>
          <td<?php if (! ( ($this->_foreach['pwd']['iteration'] == $this->_foreach['pwd']['total']) && ($this->_foreach['pd']['iteration'] == $this->_foreach['pd']['total']) )): ?> class="t_border"<?php endif; ?> style="vertical-align: middle;"><div style="width: 33px;"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, "%d.%m") : smarty_modifier_date_format($_tmp, "%d.%m")))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        <?php endforeach; endif; unset($_from); ?>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
  <?php endif; ?>

  <?php $_from = $this->_tpl_vars['reports_results']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['dep'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['dep']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['department']):
        $this->_foreach['dep']['iteration']++;
?>
    <?php ob_start(); ?><?php echo smarty_function_cycle(array('values' => 't_odd1 t_odd2,t_even1 t_even2'), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_row_class', ob_get_contents());ob_end_clean(); ?>
    <?php $_from = $this->_tpl_vars['department']['employees']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['emp'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['emp']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['employee']):
        $this->_foreach['emp']['iteration']++;
?>
      <tr class="<?php echo $this->_tpl_vars['current_row_class']; ?>
">
        <?php if (($this->_foreach['emp']['iteration'] <= 1)): ?>
          <td class="t_border vmiddle" rowspan="<?php echo $this->_tpl_vars['department']['rowspan']; ?>
">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['department']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        <?php endif; ?>
        <td class="t_border vmiddle">
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['employee']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        </td>
        <td class="t_border">
          <?php $_from = $this->_tpl_vars['employee']['replacement']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['repl'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['repl']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['deputy']):
        $this->_foreach['repl']['iteration']++;
?>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['deputy'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! ($this->_foreach['repl']['iteration'] == $this->_foreach['repl']['total'])): ?><br /><?php endif; ?>
          <?php endforeach; else: ?>
            &nbsp;
          <?php endif; unset($_from); ?>
        </td>
        <td class="t_border vmiddle hright">
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['employee']['total_days_off'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        </td>
        <td class="t_border vmiddle hright">
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['employee']['total_sickness'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        </td>
        <?php if (@HOME_OFFICE_DOCUMENT_ID): ?>
          <td class="t_border vmiddle hright">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['employee']['total_home_office'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        <?php endif; ?>
        <?php $_from = $this->_tpl_vars['employee']['days']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['dw'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['dw']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['day']):
        $this->_foreach['dw']['iteration']++;
?>
          <td class="<?php if (! ($this->_foreach['dw']['iteration'] == $this->_foreach['dw']['total'])): ?>t_border<?php endif; ?><?php if ($this->_tpl_vars['day']['id']): ?> reports_day_off_<?php echo $this->_tpl_vars['day']['status']; ?>
<?php endif; ?>"<?php if ($this->_tpl_vars['day']['id']): ?> style="cursor: pointer;" onclick="window.open('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=view&amp;view=<?php echo $this->_tpl_vars['day']['id']; ?>
', '_blank');"<?php endif; ?>>
            <?php if ($this->_tpl_vars['day']['additional_info']): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['day']['additional_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>&nbsp;<?php endif; ?>
          </td>
        <?php endforeach; endif; unset($_from); ?>
      </tr>
    <?php endforeach; endif; unset($_from); ?>
  <?php endforeach; else: ?>
    <tr>
      <td class="error" colspan="<?php echo $this->_tpl_vars['reports_additional_options']['total_rowspan']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
  <?php endif; unset($_from); ?>
  <tr>
    <td class="t_footer" colspan="<?php echo $this->_tpl_vars['reports_additional_options']['total_rowspan']; ?>
"></td>
  </tr>
</table>

<table border="0" cellspacing="5">
  <tr>
    <td colspan="8">
      <strong><u><?php echo ((is_array($_tmp=$this->_config[0]['vars']['legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</u></strong>
    </td>
  </tr>
  <tr>
    <td style="width: 20px;" class="reports_day_off_approved"></td>
    <td style="padding-right: 10px;"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['reports_status_approved'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
    <td style="width: 20px;"  class="reports_day_off_requested"></td>
    <td style="padding-right: 10px;"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['reports_status_requested'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
    <?php if (@HOME_OFFICE_DOCUMENT_ID): ?>
      <td style="width: 20px;"  class="reports_day_off_home_office"></td>
      <td style="padding-right: 10px;"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['reports_status_home_office'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
    <?php endif; ?>
    <?php if (! @HIDE_NOT_APPROVED_DAYS_OFF): ?>
      <td style="width: 20px;" class="reports_day_off_disapproved"></td>
      <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['reports_status_disapproved'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
    <?php endif; ?>
    <?php if (@SHOW_SICKNESS_DAYS): ?>
      <td style="width: 20px;" class="reports_day_off_sickness"></td>
      <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['reports_status_sickness'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
    <?php endif; ?>
  </tr>
</table>