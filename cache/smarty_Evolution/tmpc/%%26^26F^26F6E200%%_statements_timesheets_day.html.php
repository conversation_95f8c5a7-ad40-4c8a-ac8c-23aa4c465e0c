<?php /* Smarty version 2.6.33, created on 2024-08-06 18:28:49
         compiled from /var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html', 3, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html', 18, false),array('modifier', 'replace', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html', 21, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html', 21, false),array('function', 'math', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html', 6, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/tasks/templates/_statements_timesheets_day.html', 21, false),)), $this); ?>
<?php $_from = $this->_tpl_vars['timesheets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['timesheet']):
        $this->_foreach['i']['iteration']++;
?>
    <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['day']->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day_short'])); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('timesheet_label', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => '((es-ds-eb)*((4*ch)/60))','ds' => $this->_tpl_vars['day_start'],'es' => $this->_tpl_vars['timesheet']['start'],'ch' => $this->_tpl_vars['timesheet_cell_height'],'assign' => 'timesheet_top','eb' => $this->_tpl_vars['timesheet_area_border_width']), $this);?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => '(ew+(ec-1)*eol)/ec-4*eb-eos','ec' => 1,'ew' => $this->_tpl_vars['timesheet_cell_width'],'eol' => $this->_tpl_vars['timesheet_overlapping'],'eos' => $this->_tpl_vars['timesheet_area_offset_left'],'eb' => $this->_tpl_vars['timesheet_area_border_width'],'assign' => 'timesheet_width'), $this);?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => '(te-ts-(2*eb))*((4*ch)/60)','te' => $this->_tpl_vars['timesheet']['end'],'ts' => $this->_tpl_vars['timesheet']['start'],'ch' => $this->_tpl_vars['timesheet_cell_height'],'eb' => $this->_tpl_vars['timesheet_area_border_width'],'assign' => 'timesheet_height'), $this);?><?php echo ''; ?><?php $this->assign('timesheet_name_truncate', 80); ?><?php echo ''; ?><?php $this->assign('timesheet_min_duration', 22); ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['tasks_customer']; ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['timesheet']['customer']; ?><?php echo '<br /><strong>'; ?><?php echo $this->_config[0]['vars']['tasks_timesheets_startperiod']; ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']['startperiod'])) ? $this->_run_mod_handler('date_format', true, $_tmp, "%H:%M") : smarty_modifier_date_format($_tmp, "%H:%M")); ?><?php echo '<br /><strong>'; ?><?php echo $this->_config[0]['vars']['tasks_timesheets_endperiod']; ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']['endperiod'])) ? $this->_run_mod_handler('date_format', true, $_tmp, "%H:%M") : smarty_modifier_date_format($_tmp, "%H:%M")); ?><?php echo '<br /><strong>'; ?><?php echo $this->_config[0]['vars']['tasks_timesheets_activity']; ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['timesheet']['activity_name']; ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['timesheet']['subject']): ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['tasks_timesheets_subject']; ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['timesheet']['subject']; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['tasks_timesheets_content']; ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']['content'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, $this->_tpl_vars['timesheet_name_truncate'], '...', true) : smarty_modifier_mb_truncate($_tmp, $this->_tpl_vars['timesheet_name_truncate'], '...', true)); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('timesheet_info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

    <div class="cal_event pointer" style="overflow:hidden; top:<?php echo $this->_tpl_vars['timesheet_top']; ?>
px; left:<?php echo $this->_tpl_vars['timesheet_left']; ?>
px; width:<?php echo $this->_tpl_vars['timesheet_width']; ?>
px; height:<?php if ($this->_tpl_vars['timesheet_height'] > 1): ?><?php echo $this->_tpl_vars['timesheet_height']; ?>
<?php else: ?>1<?php endif; ?>px;" <?php echo smarty_function_help(array('text_content' => ((is_array($_tmp=$this->_tpl_vars['timesheet_info'])) ? $this->_run_mod_handler('replace', true, $_tmp, "'", "&#39;") : smarty_modifier_replace($_tmp, "'", "&#39;")),'label_content' => ((is_array($_tmp=$this->_tpl_vars['timesheet_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'popup_only' => 1), $this);?>
 onclick="window.location.href='<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['timesheet']['parent_model']; ?>
&amp;<?php echo $this->_tpl_vars['timesheet']['parent_model']; ?>
=timesheets&amp;timesheets=<?php echo $this->_tpl_vars['timesheet']['parent_id']; ?>
'">
      <?php if ($this->_tpl_vars['timesheet_height'] >= $this->_tpl_vars['timesheet_min_duration']): ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']['startperiod'])) ? $this->_run_mod_handler('date_format', true, $_tmp, "%H:%M") : smarty_modifier_date_format($_tmp, "%H:%M")); ?>
 - <?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']['endperiod'])) ? $this->_run_mod_handler('date_format', true, $_tmp, "%H:%M") : smarty_modifier_date_format($_tmp, "%H:%M")); ?>
<br />
        <?php if ($this->_tpl_vars['timesheet']['subject']): ?><strong><?php echo $this->_tpl_vars['timesheet']['subject']; ?>
</strong><br /><?php endif; ?>
        <br />
        <?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']['content'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, $this->_tpl_vars['timesheet_name_truncate'], '...', true) : smarty_modifier_mb_truncate($_tmp, $this->_tpl_vars['timesheet_name_truncate'], '...', true)); ?>

      <?php else: ?>
        &nbsp;
      <?php endif; ?>
    </div>
<?php endforeach; endif; unset($_from); ?>