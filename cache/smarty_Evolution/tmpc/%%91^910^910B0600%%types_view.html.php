<?php /* Smarty version 2.6.33, created on 2024-08-14 10:45:55
         compiled from /var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 16, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 24, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 45, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 73, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 73, false),array('modifier', 'mb_ucfirst', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 244, false),array('modifier', 'lower', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 249, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 21, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/documents/templates/types_view.html', 24, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_basic_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_name'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['documents_type']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_name_plural'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['documents_type']->get('name_plural'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_code'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['documents_type']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_inheritance'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('inheritance') == 0): ?> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_inheritance_primary'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
<?php endif; ?>
            <?php if ($this->_tpl_vars['documents_type']->get('inheritance') == 1): ?> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_inheritance_secondary'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
<?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_direction'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('direction') == @PH_DOCUMENTS_INCOMING): ?>
              <span class="incoming"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_incoming_sg'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            <?php elseif ($this->_tpl_vars['documents_type']->get('direction') == @PH_DOCUMENTS_OUTGOING): ?>
              <span class="outgoing"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_outgoing_sg'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            <?php elseif ($this->_tpl_vars['documents_type']->get('direction') == @PH_DOCUMENTS_INTERNAL): ?>
              <span class="internal"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_internal_sg'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            <?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'type_section'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['documents_type']->get('section_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_description'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['documents_type']->get('description'))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp) : smarty_modifier_mb_wordwrap($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_assignment_types'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php $_from = $this->_tpl_vars['documents_type']->get('assignment_types'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['a'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['a']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['a_type']):
        $this->_foreach['a']['iteration']++;
?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['a_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['a']['iteration'] == $this->_foreach['a']['total'])): ?><br /><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>

                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_counter_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_counter'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('counter')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;controller=counters&amp;counters=view&amp;view=<?php echo $this->_tpl_vars['documents_type']->get('counter'); ?>
" target="_blank"><?php echo ((is_array($_tmp=$this->_tpl_vars['counter_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a> - <?php echo ((is_array($_tmp=$this->_tpl_vars['counter_formula'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>

                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_additional_settings_of_fields'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_validate'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php $_from = $this->_tpl_vars['documents_type']->get('validate'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['f'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['f']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['field']):
        $this->_foreach['f']['iteration']++;
?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['field'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['f']['iteration'] == $this->_foreach['f']['total'])): ?><br /><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_validate_unique'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('validate_unique_current_year')): ?>
              <span style="position: absolute; left: 450px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_validate_unique_current_year'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            <?php endif; ?>
            <?php $_from = $this->_tpl_vars['documents_type']->get('validate_unique'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['f'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['f']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['field']):
        $this->_foreach['f']['iteration']++;
?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['field'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! ($this->_foreach['f']['iteration'] == $this->_foreach['f']['total'])): ?><br /><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <?php if (! empty ( $this->_tpl_vars['customers_types'] )): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_related_customers_types'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'view_checkbox_group.html', 'smarty_include_vars' => array('standalone' => true,'name' => 'related_customers_types','options' => $this->_tpl_vars['customers_types'],'value' => $this->_tpl_vars['documents_type']->get('related_customers_types'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <?php endif; ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_gt2'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><?php if ($this->_tpl_vars['gt2_layout']): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?></td>
        </tr>
        <?php if ($this->_tpl_vars['gt2_layout']): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_gt2_layout'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=layouts&amp;layouts=view&amp;view=<?php echo $this->_tpl_vars['documents_type']->getGT2Layout(); ?>
" target="_blank"><?php echo $this->_tpl_vars['gt2_layout']; ?>
</a></td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_VAT'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('include_VAT')): ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_include_VAT'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
, <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_default_VAT'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo $this->_tpl_vars['documents_type']->get('default_VAT'); ?>
 %
            <?php else: ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_no_VAT'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <tr id="calc_price_row"<?php if (1 || ! $this->_tpl_vars['documents_type']->get('gt2') && ! $this->_tpl_vars['gt2_layout']): ?> style="display:none"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_calculated_price'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('calculated_price') == 'price' || ! $this->_tpl_vars['documents_type']->get('calculated_price')): ?>
              <?php echo $this->_config[0]['vars']['documents_types_gt2_price']; ?>

            <?php endif; ?>
            <?php if ($this->_tpl_vars['documents_type']->get('calculated_price') == 'last_delivery_price'): ?>
              <?php echo $this->_config[0]['vars']['documents_types_gt2_last_delivery_price']; ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['layouts_search_url']): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['menu_layouts']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="<?php echo $this->_tpl_vars['layouts_search_url']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['help_menu_layouts'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
          </td>
        </tr>
        <?php endif; ?>

                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_tasks_and_comments'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_opened_requires_comment'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td><?php ob_start(); ?>required_statuses_option_<?php echo $this->_tpl_vars['documents_type']->get('opened_requires_comment'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_opened_requires_comment_label', ob_get_contents());ob_end_clean(); ?>
            <?php echo $this->_config[0]['vars'][$this->_tpl_vars['current_opened_requires_comment_label']]; ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_locked_requires_comment'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td><?php ob_start(); ?>required_statuses_option_<?php echo $this->_tpl_vars['documents_type']->get('locked_requires_comment'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_locked_requires_comment_label', ob_get_contents());ob_end_clean(); ?>
            <?php echo $this->_config[0]['vars'][$this->_tpl_vars['current_locked_requires_comment_label']]; ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_closed_requires_comment'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td><?php ob_start(); ?>required_statuses_option_<?php echo $this->_tpl_vars['documents_type']->get('closed_requires_comment'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_closed_requires_comment_label', ob_get_contents());ob_end_clean(); ?>
            <?php echo $this->_config[0]['vars'][$this->_tpl_vars['current_closed_requires_comment_label']]; ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_generate_system_task'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('generate_system_task')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_requires_completed_minitasks'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('requires_completed_minitasks')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?>
          </td>
        </tr>

                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['archive'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['after'])) ? $this->_run_mod_handler('mb_ucfirst', true, $_tmp) : smarty_modifier_mb_ucfirst($_tmp))), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('archive_interval_count') && $this->_tpl_vars['documents_type']->get('archive_interval_type')): ?>
              <?php echo $this->_tpl_vars['documents_type']->get('archive_interval_count'); ?>
 
              <?php ob_start(); ?>date_<?php echo ((is_array($_tmp=$this->_tpl_vars['documents_type']->get('archive_interval_type'))) ? $this->_run_mod_handler('lower', true, $_tmp) : smarty_modifier_lower($_tmp)); ?>
s<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('interval_type_label', ob_get_contents());ob_end_clean(); ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['interval_type_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['field']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('archive_field')): ?>
              <?php $_from = $this->_tpl_vars['archive_field_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['og']):
?>
                <?php $_from = $this->_tpl_vars['og']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt']):
?>
                  <?php if ($this->_tpl_vars['documents_type']->get('archive_field') == $this->_tpl_vars['opt']['option_value']): ?>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['opt']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
          </td>
        </tr>


                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['purge'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['after'])) ? $this->_run_mod_handler('mb_ucfirst', true, $_tmp) : smarty_modifier_mb_ucfirst($_tmp))), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('purge_interval_count') && $this->_tpl_vars['documents_type']->get('purge_interval_type')): ?>
              <?php echo $this->_tpl_vars['documents_type']->get('purge_interval_count'); ?>

              <?php ob_start(); ?>date_<?php echo ((is_array($_tmp=$this->_tpl_vars['documents_type']->get('purge_interval_type'))) ? $this->_run_mod_handler('lower', true, $_tmp) : smarty_modifier_lower($_tmp)); ?>
s<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('interval_type_label', ob_get_contents());ob_end_clean(); ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['interval_type_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['field']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['documents_type']->get('purge_field')): ?>
              <?php $_from = $this->_tpl_vars['purge_field_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['og']):
?>
                <?php $_from = $this->_tpl_vars['og']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt']):
?>
                  <?php if ($this->_tpl_vars['documents_type']->get('purge_field') == $this->_tpl_vars['opt']['option_value']): ?>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['opt']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
          </td>
        </tr>

                <tr>
          <td colspan="3" class="t_section_title">
            <div class="t_section_title">
              <span class="title_text"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_types_default_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_default_name'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['documents_type']->get('default_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_default_customer'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['default_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['trademark']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['default_trademark'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_media'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['default_media'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_pattern'), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['default_pattern'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_department'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['default_department'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'types_group'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['default_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['documents_type'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>

<br /><br />
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_types_transform_list.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<br />
<br />
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_types_vars_list.html", 'smarty_include_vars' => array('title' => $this->_config[0]['vars']['patternvars_additional_list'],'list' => $this->_tpl_vars['document']->get('vars'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>