<?php /* Smarty version 2.6.33, created on 2024-10-21 16:24:08
         compiled from /var/www/Nzoom-Evolution/_libs/modules/contracts/templates/_preview_new_agreement_cd.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'cycle', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/_preview_new_agreement_cd.html', 60, false),array('function', 'array', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/_preview_new_agreement_cd.html', 75, false),array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/_preview_new_agreement_cd.html', 63, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/contracts/templates/_preview_new_agreement_cd.html', 99, false),)), $this); ?>
<div class="floatl labelbox" style="white-space: nowrap; padding-left: 5px;"><?php echo $this->_config[0]['vars']['contracts_diff_options']; ?>
:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px;">
  <?php echo $this->_config[0]['vars']['required']; ?>

  <?php ob_start(); ?>checkAgreementsDifferences(this.form, <?php echo $this->_tpl_vars['model_id']; ?>
<?php if ($this->_tpl_vars['annul']): ?>, 'annul'<?php endif; ?>)<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('diff_onchange', ob_get_contents());ob_end_clean(); ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'diff_options','options' => $this->_tpl_vars['diff_options'],'standalone' => true,'required' => 1,'onchange' => $this->_tpl_vars['diff_onchange'],'value' => $_GET['diff_options'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>
<div class="clear"></div>
<?php if (! empty ( $this->_tpl_vars['result'] )): ?>
    <?php ob_start(); ?>finance_incomes_reasons<?php echo @PH_FINANCE_TYPE_INVOICE; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('right_invoice', ob_get_contents());ob_end_clean(); ?>
    <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'issue_date') || $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'future_issue_date')): ?>
      <?php $this->assign('readonly_dates', 0); ?>
      <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'issue_date')): ?>
        <?php $this->assign('disallow_date_before', 0); ?>
      <?php else: ?>
        <?php $this->assign('disallow_date_before', 1); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'future_issue_date')): ?>
        <?php $this->assign('disallow_date_after', 0); ?>
      <?php else: ?>
        <?php $this->assign('disallow_date_after', 1); ?>
      <?php endif; ?>
    <?php else: ?>
      <?php $this->assign('readonly_dates', 1); ?>
    <?php endif; ?>
    
    <?php $this->assign('headers_show', true); ?>
    <?php $this->assign('has_previous', false); ?>
    <?php $_from = $this->_tpl_vars['result']['old_invoices']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['invoices'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['invoices']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['invoice']):
        $this->_foreach['invoices']['iteration']++;
?>
      <?php if ($this->_tpl_vars['invoice']->get('debit')): ?>
        <?php $this->assign('debit', $this->_tpl_vars['invoice']->get('debit')); ?>
        <?php $this->assign('has_debit', true); ?>
        <?php if ($this->_tpl_vars['headers_show']): ?>
          <?php $this->assign('headers_show', false); ?>
          <?php $this->assign('has_previous', true); ?>
          <br />
          <div class="red"><?php echo $this->_config[0]['vars']['message_contracts_debits_amount']; ?>
</div>
          <table border="0" cellpadding="2" cellspacing="0" class="t_grouping_table t_table bordered_cell" style="margin: 5px 0;">
            <tr>
              <th class="t_border" style="width:50px">
                <div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_issue']; ?>
</div>
                <div style="text-align: right;"><input type="checkbox" checked="checked" onclick="checkAll(this, 'debits', 'class')" /></div>
              </th>
              <th class="t_border" style="width:70px">
                <div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_auto_send']; ?>
</div>
                <div style="text-align: center;"><input type="checkbox" onclick="checkAll(this, 'debits_send', 'class')" /></div>
              </th>
              <th class="t_border"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_for_invoice']; ?>
</div></th>
              <th class="t_border" style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_cd_reason']; ?>
&nbsp;<span class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</span></div></th>
              <th class="t_border" style="width:100px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_amount']; ?>
</div></th>
              <th class="t_border" style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_fiscal_event_date']; ?>
</div></th>
              <th style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_issue_date']; ?>
</div></th>
            </tr>
        <?php endif; ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 pointer">
          <td class="t_border hright">
            <div class="switch_expand" id="switch_debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" onclick="if($('debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display == 'none') {$('debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display = ''; $('switch_debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').className = 'switch_collapse';} else {$('debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display = 'none'; $('switch_debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').className = 'switch_expand';}"></div>
            <input type="checkbox" value="1" checked="checked" <?php if ($this->_tpl_vars['invoice']->get('readonly')): ?>disabled="disabled"<?php else: ?>name="debit[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" class="debits"<?php endif; ?> title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
            <?php if ($this->_tpl_vars['invoice']->get('readonly')): ?>
              <input type="hidden" value="1" name="debit[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" />
            <?php endif; ?>
          </td>
          <td class="t_border hcenter" style="width:50px">
            <input type="checkbox" name="debit_send[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" value="1" <?php if ($this->_tpl_vars['invoice']->get('auto_send')): ?>checked="checked"<?php endif; ?> title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_auto_send'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="debits_send" />
          </td>
          <td class="t_border"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view=<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" target="_blank">[<?php echo $this->_tpl_vars['invoice']->get('num'); ?>
] <?php echo ((is_array($_tmp=$this->_tpl_vars['invoice']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></td>
          <td class="t_border" style="width:120px">
            <?php if ($this->_tpl_vars['agreement_subtype'] == 'annex'): ?>
              <?php $this->assign('current_code', 'dnrannex'); ?>
              <?php echo smarty_function_array(array('assign' => 'hidden_reasons','1' => 'dnragreement','2' => 'cnrannex','3' => 'cnragreement'), $this);?>

            <?php else: ?>
              <?php $this->assign('current_code', 'dnragreement'); ?>
              <?php echo smarty_function_array(array('assign' => 'hidden_reasons','1' => 'dnrannex','2' => 'cnrannex','3' => 'cnragreement'), $this);?>

            <?php endif; ?>
            <?php $this->assign('rcount', 0); ?>
            <?php $_from = $this->_tpl_vars['cd_reasons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['reason']):
?>
              <?php if ($this->_tpl_vars['reason']['code'] == $this->_tpl_vars['current_code'] || ! in_array ( $this->_tpl_vars['reason']['code'] , $this->_tpl_vars['hidden_reasons'] ) || empty ( $this->_tpl_vars['reason']['code'] )): ?>
                <?php $this->assign('rcount', $this->_tpl_vars['rcount']+1); ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            <select class="selbox<?php if ($this->_tpl_vars['rcount'] > 1): ?> undefined<?php endif; ?>" name="debit_reason[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" id="debit_reason_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_cd_reason'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 100%;" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);">
              <?php if ($this->_tpl_vars['rcount'] > 1): ?>
                <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
              <?php endif; ?>
            <?php $_from = $this->_tpl_vars['cd_reasons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['reason']):
?>
              <?php if ($this->_tpl_vars['reason']['code'] == $this->_tpl_vars['current_code'] || ! in_array ( $this->_tpl_vars['reason']['code'] , $this->_tpl_vars['hidden_reasons'] ) || empty ( $this->_tpl_vars['reason']['code'] )): ?>
                <option value="<?php echo $this->_tpl_vars['reason']['option_value']; ?>
"><?php echo $this->_tpl_vars['reason']['label']; ?>
</option>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
          <td class="t_border hright" style="width:100px"><?php echo $this->_tpl_vars['debit']->get('total_with_vat'); ?>
 <?php echo $this->_tpl_vars['debit']->get('currency'); ?>
</td>
          <td class="t_border" style="width:120px">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'debit_fiscal_date','index' => $this->_tpl_vars['invoice']->get('id'),'eq_indexes' => 1,'required' => 1,'label' => $this->_config[0]['vars']['contracts_fiscal_event_date'],'value' => ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td style="width:120px">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'debit_issue_date','index' => $this->_tpl_vars['invoice']->get('id'),'eq_indexes' => 1,'required' => 1,'label' => $this->_config[0]['vars']['contracts_issue_date'],'value' => ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')),'readonly' => $this->_tpl_vars['readonly_dates'],'disallow_date_after' => $this->_tpl_vars['disallow_date_after'],'disallow_date_before' => $this->_tpl_vars['disallow_date_before'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr style="display: none;" id="debit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
">
          <td colspan="7">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_view.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['debit']->get('grouping_table_2'),'hide_script' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php if (($this->_foreach['invoices']['iteration'] == $this->_foreach['invoices']['total']) && ! $this->_tpl_vars['headers_show']): ?>
        </table>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
    <?php if ($this->_tpl_vars['has_debit']): ?>
    <div class="floatl" style="white-space: nowrap;"><?php echo $this->_config[0]['vars']['contracts_email_template']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'debit_email','options' => $this->_tpl_vars['debit_emails'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="floatl" style="white-space: nowrap; padding-left: 40px"><?php echo $this->_config[0]['vars']['contracts_pattern']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'debit_pattern','options' => $this->_tpl_vars['debit_patterns'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="clear"></div>
    <?php endif; ?>
    
    <?php $this->assign('headers_show', true); ?>
    <?php $_from = $this->_tpl_vars['result']['old_invoices']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['invoices'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['invoices']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['invoice']):
        $this->_foreach['invoices']['iteration']++;
?>
      <?php if ($this->_tpl_vars['invoice']->get('credit')): ?>
        <?php $this->assign('credit', $this->_tpl_vars['invoice']->get('credit')); ?>
        <?php $this->assign('has_credit', true); ?>
        <?php if ($this->_tpl_vars['headers_show']): ?>
          <?php $this->assign('headers_show', false); ?>
          <?php if ($this->_tpl_vars['has_previous']): ?><br /><br /><br /><?php endif; ?>
          <?php $this->assign('has_previous', true); ?>
          <div class="red"><?php echo $this->_config[0]['vars']['message_contracts_credits_amount']; ?>
</div>
          <table border="0" cellpadding="2" cellspacing="0" class="t_grouping_table t_table bordered_cell" style="margin: 5px 0;">
            <tr>
              <th class="t_border" style="width:50px">
                <div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_issue']; ?>
</div>
                <div style="text-align: right;"><input type="checkbox" checked="checked" onclick="checkAll(this, 'credits', 'class')" /></div>
              </th>
              <th class="t_border" style="width:70px">
                <div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_auto_send']; ?>
</div>
                <div style="text-align: center;"><input type="checkbox" onclick="checkAll(this, 'credits_send', 'class')" /></div>
              </th>
              <th class="t_border"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_for_invoice']; ?>
</div></th>
              <th class="t_border" style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_cd_reason']; ?>
&nbsp;<span class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</span></div></th>
              <th class="t_border" style="width:100px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_amount']; ?>
</div></th>
              <th class="t_border" style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_fiscal_event_date']; ?>
</div></th>
              <th style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_issue_date']; ?>
</div></th>
            </tr>
        <?php endif; ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 pointer">
          <td class="t_border hright">
            <div class="switch_expand" id="switch_credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" onclick="if($('credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display == 'none') {$('credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display = ''; $('switch_credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').className = 'switch_collapse';} else {$('credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display = 'none'; $('switch_credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').className = 'switch_expand';}"></div>
            <input type="checkbox" value="1" checked="checked" <?php if ($this->_tpl_vars['invoice']->get('readonly')): ?>disabled="disabled"<?php else: ?>name="credit[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" class="credits"<?php endif; ?> title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
            <?php if ($this->_tpl_vars['invoice']->get('readonly')): ?>
              <input type="hidden" value="1" name="credit[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" />
            <?php endif; ?>
          </td>
          <td class="t_border hcenter" style="width:50px">
            <input type="checkbox" name="credit_send[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" value="1" <?php if ($this->_tpl_vars['invoice']->get('auto_send')): ?>checked="checked"<?php endif; ?> title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_auto_send'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="credits_send" />
          </td>
          <td class="t_border"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view=<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" target="_blank">[<?php echo $this->_tpl_vars['invoice']->get('num'); ?>
] <?php echo ((is_array($_tmp=$this->_tpl_vars['invoice']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></td>
          <td class="t_border" style="width:120px">
            <?php if ($this->_tpl_vars['agreement_subtype'] == 'annex'): ?>
              <?php $this->assign('current_code', 'dnrannex'); ?>
              <?php echo smarty_function_array(array('assign' => 'hidden_reasons','1' => 'dnragreement','2' => 'cnrannex','3' => 'cnragreement'), $this);?>

            <?php else: ?>
              <?php $this->assign('current_code', 'dnragreement'); ?>
              <?php echo smarty_function_array(array('assign' => 'hidden_reasons','1' => 'dnrannex','2' => 'cnrannex','3' => 'cnragreement'), $this);?>

            <?php endif; ?>
            <?php $this->assign('rcount', 0); ?>
            <?php $_from = $this->_tpl_vars['cd_reasons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['reason']):
?>
              <?php if ($this->_tpl_vars['reason']['code'] == $this->_tpl_vars['current_code'] || ! in_array ( $this->_tpl_vars['reason']['code'] , $this->_tpl_vars['hidden_reasons'] ) || empty ( $this->_tpl_vars['reason']['code'] )): ?>
                <?php $this->assign('rcount', $this->_tpl_vars['rcount']+1); ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            <select class="selbox<?php if ($this->_tpl_vars['rcount'] > 1): ?> undefined<?php endif; ?>" name="credit_reason[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" id="credit_reason_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_cd_reason'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 100%;" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);">
              <?php if ($this->_tpl_vars['rcount'] > 1): ?>
                <option value="" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
              <?php endif; ?>
            <?php $_from = $this->_tpl_vars['cd_reasons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['reason']):
?>
              <?php if ($this->_tpl_vars['reason']['code'] == $this->_tpl_vars['current_code'] || ! in_array ( $this->_tpl_vars['reason']['code'] , $this->_tpl_vars['hidden_reasons'] ) || empty ( $this->_tpl_vars['reason']['code'] )): ?>
                <option value="<?php echo $this->_tpl_vars['reason']['option_value']; ?>
"><?php echo $this->_tpl_vars['reason']['label']; ?>
</option>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
          <td class="t_border hright" style="width:100px"><?php echo $this->_tpl_vars['credit']->get('total_with_vat'); ?>
 <?php echo $this->_tpl_vars['credit']->get('currency'); ?>
</td>
          <td class="t_border" style="width:120px">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'credit_fiscal_date','index' => $this->_tpl_vars['invoice']->get('id'),'eq_indexes' => 1,'required' => 1,'label' => $this->_config[0]['vars']['contracts_fiscal_event_date'],'value' => ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td style="width:120px">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'credit_issue_date','index' => $this->_tpl_vars['invoice']->get('id'),'eq_indexes' => 1,'required' => 1,'label' => $this->_config[0]['vars']['contracts_issue_date'],'value' => ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')),'readonly' => $this->_tpl_vars['readonly_dates'],'disallow_date_after' => $this->_tpl_vars['disallow_date_after'],'disallow_date_before' => $this->_tpl_vars['disallow_date_before'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr style="display: none;" id="credit_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
">
          <td colspan="7">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_view.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['credit']->get('grouping_table_2'),'hide_script' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php if (($this->_foreach['invoices']['iteration'] == $this->_foreach['invoices']['total']) && ! $this->_tpl_vars['headers_show']): ?>
        </table>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
    <?php if ($this->_tpl_vars['has_credit']): ?>
    <div class="floatl" style="white-space: nowrap;"><?php echo $this->_config[0]['vars']['contracts_email_template']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'credit_email','options' => $this->_tpl_vars['credit_emails'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="floatl" style="white-space: nowrap; padding-left: 40px"><?php echo $this->_config[0]['vars']['contracts_pattern']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'credit_pattern','options' => $this->_tpl_vars['credit_patterns'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="clear"></div>
    <?php endif; ?>
    
    <?php if ($this->_tpl_vars['result']['new_invoices']): ?>
      <?php if ($this->_tpl_vars['has_previous']): ?><br /><br /><br /><?php endif; ?>
      <div class="red"><?php echo $this->_config[0]['vars']['message_contracts_invoices_amount']; ?>
</div>
      <table border="0" cellpadding="2" cellspacing="0" class="t_grouping_table t_table bordered_cell" style="margin: 5px 0;">
        <tr>
          <th class="t_border" style="width:50px">
            <div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_issue']; ?>
</div>
            <div style="text-align: right;"><input type="checkbox" checked="checked" onclick="checkAll(this, 'invoices', 'class')" /></div>
          </th>
          <th class="t_border" style="width:70px">
            <div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_auto_send']; ?>
</div>
            <div style="text-align: center;"><input type="checkbox" onclick="checkAll(this, 'invoices_send', 'class')" /></div>
          </th>
          <th class="t_border"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_invoice_period_start']; ?>
</div></th>
          <th class="t_border"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_invoice_period_finish']; ?>
</div></th>
          <th class="t_border" style="width:100px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_amount']; ?>
</div></th>
          <th class="t_border" style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_fiscal_event_date']; ?>
</div></th>
          <th style="width:120px"><div class="t_caption3_title"><?php echo $this->_config[0]['vars']['contracts_issue_date']; ?>
</div></th>
        </tr>
      <?php $this->assign('ii', 1); ?>
      <?php $_from = $this->_tpl_vars['result']['new_invoices']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['invoice']):
?>
        <?php if ($this->_tpl_vars['invoice']->get('type') == PH_FINANCE_TYPE_INVOICE): ?>
          <?php $this->assign('has_invoice', true); ?>
        <?php else: ?>
          <?php $this->assign('has_proforma', true); ?>
        <?php endif; ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 pointer">
          <td class="t_border hright">
            <div class="switch_expand" id="switch_invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
" onclick="if($('invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display == 'none') {$('invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display = ''; $('switch_invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').className = 'switch_collapse';} else {$('invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').style.display = 'none'; $('switch_invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
').className = 'switch_expand';}"></div>
            <input type="checkbox" <?php if ($this->_tpl_vars['invoice']->get('readonly')): ?>disabled="disabled"<?php else: ?>name="invoice[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" class="invoices"<?php endif; ?> value="<?php if ($this->_tpl_vars['invoice']->get('type') == PH_FINANCE_TYPE_PRO_INVOICE): ?>proforma_<?php endif; ?>1" checked="checked" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
             <?php if ($this->_tpl_vars['invoice']->get('readonly')): ?>
               <input type="hidden" name="invoice[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" value="<?php if ($this->_tpl_vars['invoice']->get('type') == PH_FINANCE_TYPE_PRO_INVOICE): ?>proforma_<?php endif; ?>1" />
             <?php endif; ?>
          </td>
          <td class="t_border hcenter" style="width:50px">
            <input type="checkbox" name="invoice_send[<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
]" value="1" <?php if ($this->_tpl_vars['invoice']->get('auto_send')): ?>checked="checked"<?php endif; ?> title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_auto_send'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="invoices_send" />
          </td>
          <td class="t_border"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['invoice']->get('from'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td class="t_border"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['invoice']->get('to'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td class="t_border hright" style="width: 120px"><?php echo $this->_tpl_vars['invoice']->get('total_with_vat'); ?>
 <?php echo $this->_tpl_vars['invoice']->get('currency'); ?>
</td>
          <td class="t_border" style="width:120px">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'invoice_fiscal_date','index' => $this->_tpl_vars['invoice']->get('id'),'eq_indexes' => 1,'required' => 1,'label' => $this->_config[0]['vars']['contracts_fiscal_event_date'],'value' => ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td style="width:120px">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'invoice_issue_date','index' => $this->_tpl_vars['invoice']->get('id'),'eq_indexes' => 1,'required' => 1,'label' => $this->_config[0]['vars']['contracts_issue_date'],'value' => ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')),'readonly' => $this->_tpl_vars['readonly_dates'],'disallow_date_after' => $this->_tpl_vars['disallow_date_after'],'disallow_date_before' => $this->_tpl_vars['disallow_date_before'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr style="display: none;" id="invoice_<?php echo $this->_tpl_vars['invoice']->get('id'); ?>
">
          <td colspan="7">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_view.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['invoice']->get('grouping_table_2'),'hide_script' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php endforeach; endif; unset($_from); ?>
    </table>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['has_proforma']): ?>
    <div class="floatl" style="white-space: nowrap;"><?php echo $this->_config[0]['vars']['contracts_email_template']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'proforma_email','options' => $this->_tpl_vars['proforma_emails'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="floatl" style="white-space: nowrap; padding-left: 40px"><?php echo $this->_config[0]['vars']['contracts_pattern']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'proforma_pattern','options' => $this->_tpl_vars['proforma_patterns'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="clear"></div>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['has_invoice']): ?>
    <div class="floatl" style="white-space: nowrap;"><?php echo $this->_config[0]['vars']['contracts_email_template']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'invoice_email','options' => $this->_tpl_vars['invoice_emails'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="floatl" style="white-space: nowrap; padding-left: 40px"><?php echo $this->_config[0]['vars']['contracts_pattern']; ?>
:</div>
    <div class="floatl" style="white-space: nowrap; padding-left: 20px">
      <?php echo $this->_config[0]['vars']['required']; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('name' => 'invoice_pattern','options' => $this->_tpl_vars['invoice_patterns'],'standalone' => true,'required' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
    <div class="clear"></div>
    <?php endif; ?>
<?php else: ?>
     <br/><br/>
    <div class="warning" style="padding-left: 5px; font-weight: bold"><?php echo $this->_config[0]['vars']['contracts_agreement_no_difference']; ?>
</div>
<?php endif; ?>
<br /><br />

<input type="hidden" name="templates_generated" id="templates_generated" value="sure" />
<button type="submit" class="button" name="ok1" id="ok1" title="" onclick="return validateInvoicedVSContracted(this.form)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['ok'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
<button type="button" class="button" name="cancel1" id="cancel1" title="" onclick="toggleActionOptions($('<?php if ($this->_tpl_vars['annul']): ?>annulmentsubtype<?php else: ?>setstatus<?php endif; ?>_action'))"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>