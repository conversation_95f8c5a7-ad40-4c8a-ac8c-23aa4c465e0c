<?php /* Smarty version 2.6.33, created on 2025-01-10 18:46:09
         compiled from _history_activity.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_history_activity.html', 3, false),array('modifier', 'date_format', '_history_activity.html', 10, false),array('modifier', 'nl2br', '_history_activity.html', 22, false),array('modifier', 'url2href', '_history_activity.html', 22, false),array('modifier', 'default', '_history_activity.html', 22, false),array('function', 'math', '_history_activity.html', 43, false),)), $this); ?>
<?php if ($this->_tpl_vars['pagination']['page'] == 1): ?>
  <h2>
    <span class="material-icons">history_edu</span> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['history_activity'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    <a class="history_activity-item__title-link" href="<?php echo $this->_tpl_vars['submitLink']; ?>
"><span class="material-icons">link</span></a>
  </h2>
  <table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
<?php endif; ?>
    <?php $this->assign('activity_date', ''); ?>
    <?php $_from = $this->_tpl_vars['history']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['event']):
        $this->_foreach['i']['iteration']++;
?>
      <?php if ($this->_tpl_vars['activity_date'] != ((is_array($_tmp=$this->_tpl_vars['event']['h_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))): ?>
      <?php $this->assign('activity_date', ((is_array($_tmp=$this->_tpl_vars['event']['h_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))); ?>
      <tr class="activity_date activity_date_<?php echo $this->_tpl_vars['activity_date']; ?>
">
        <td colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['activity_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
      <?php endif; ?>
      <tr id="history_<?php echo $this->_tpl_vars['event']['h_id']; ?>
" class="activity_content">
        <td class="activity_icon <?php echo $this->_tpl_vars['event']['action_type']; ?>
" title="<?php echo $this->_tpl_vars['event']['action_type_name']; ?>
"></td>
        <td class="activity_data">
          <div><a class="activity_audit-link"
              href="<?php echo $this->_tpl_vars['submitLink']; ?>
<?php if ($this->_tpl_vars['event']): ?>&audit=<?php echo $this->_tpl_vars['event']['h_id']; ?>
#history_<?php echo $this->_tpl_vars['event']['h_id']; ?>
<?php endif; ?>"
            ><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['event']['h_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_full']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_full'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></div>
          <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['event']['data'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          <?php if ($this->_tpl_vars['event']['content']): ?>
          <div class="content_container"<?php if ($this->_tpl_vars['event']['audit']): ?> data-audit="<?php echo ((is_array($_tmp=$this->_tpl_vars['event']['audit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php endif; ?>>
           <div class="content">
             <?php if ($this->_tpl_vars['event']['subject']): ?><div><strong><?php echo ((is_array($_tmp=$this->_tpl_vars['event']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong></div><?php endif; ?>
             <div><?php echo $this->_tpl_vars['event']['content']; ?>
</div>
           </div>
          </div>
          <?php endif; ?>
        </td>
      </tr>
    <?php endforeach; else: ?>
      <tr>
        <td class="error" colspan="2"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endif; unset($_from); ?>
    <?php if ($this->_tpl_vars['pagination']['rpp'] * $this->_tpl_vars['pagination']['page'] < $this->_tpl_vars['pagination']['total']): ?>
      <tr class="show_more">
        <td>&nbsp;</td>
        <td>
          <div class="loading hidden" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['loading'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"></div>
          <a class="" href="#" onclick="return showMore(event, {url: '<?php echo $this->_tpl_vars['submitLink']; ?>
&amp;source=ajax&amp;history_activity=1&amp;page=<?php echo smarty_function_math(array('equation' => 'a+1','a' => $this->_tpl_vars['pagination']['page']), $this);?>
'});"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['show_more'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
        </td>
      </tr>
    <?php endif; ?>
<?php if ($this->_tpl_vars['pagination']['page'] == 1): ?>
  </table>
<?php endif; ?>