<?php /* Smarty version 2.6.33, created on 2025-02-13 18:05:51
         compiled from /var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 24, false),array('modifier', 'date_format', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 135, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 213, false),array('modifier', 'url2href', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 213, false),array('function', 'help', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 47, false),array('function', 'popup', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 59, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Evolution/_libs/modules/tasks/view/templates/view.html', 59, false),)), $this); ?>
<div class="nz-page-wrapper<?php if (! empty ( $this->_tpl_vars['side_panels'] ) && count ( $this->_tpl_vars['side_panels'] )): ?> nz--has-side-panel<?php endif; ?>">
  <div class="nz-page-main-column nz-content-surface<?php if (empty ( $this->_tpl_vars['_isPopup'] )): ?> nz-elevation--z3<?php endif; ?>">
    <div class="nz-page-title"><h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
      <div class="nz-page-title-tools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['general'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['general'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
      <div class="nz-page-title-sidetools">
        <?php if (isset ( $this->_tpl_vars['available_page_actions']['quick'] )): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['quick'],'onlyIcons' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      </div>
    </div>
    <div class="nz-page-actions">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_page_actions']['context'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_add_popout_xtemplate.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_translations_menu.html", 'smarty_include_vars' => array('translations' => $this->_tpl_vars['translations'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

    <div id="form_container" class="nz-page-content main_panel_container">
      <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['task']->get('id'); ?>
" />
      <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => '_timesheet_stopwatch_button.html', 'smarty_include_vars' => array('model' => $this->_tpl_vars['task'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <?php $_from = $this->_tpl_vars['task']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
              <?php if ($this->_tpl_vars['layout']['view']): ?>
              <?php if ($this->_tpl_vars['lkey'] != 'configurtor'): ?>
              <tr<?php if (! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                <td colspan="3" class="t_caption3 pointer">
                  <div class="floatr index_arrow_anchor">
                    <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                  </div>
                  <div class="layout_switch" onclick="toggleViewLayouts(this)" id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch">
                    <a name="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                  </div>
                </td>
              </tr>
              <?php endif; ?>

              <?php if ($this->_tpl_vars['lkey'] == 'full_num'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('full_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
                  <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>
</span>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'status'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php ob_start(); ?>tasks_statuses_<?php echo $this->_tpl_vars['task']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('task_status_icon_path', ob_get_contents());ob_end_clean(); ?>
                  <span class="material-icons nz-status-icon nz-status__<?php echo $this->_tpl_vars['task']->get('status'); ?>
"><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['task_status_icon_path']); ?>
</span>
                  <?php if ($this->_tpl_vars['task']->get('status') == 'planning'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_status_planning'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <?php elseif ($this->_tpl_vars['task']->get('status') == 'progress'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_status_progress'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <?php elseif ($this->_tpl_vars['task']->get('status') == 'finished'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_status_finished'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                  <?php if ($this->_tpl_vars['task']->checkPermissions('setstatus') && $this->_tpl_vars['layout']['edit']): ?>
                    <a href="javascript:void(0)" onclick="changeStatus(<?php echo $this->_tpl_vars['task']->get('id'); ?>
, 'tasks')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'substatus'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php echo ((is_array($_tmp=@$this->_tpl_vars['task']->get('substatus_name'))) ? $this->_run_mod_handler('default', true, $_tmp, ' ') : smarty_modifier_default($_tmp, ' ')); ?>

                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <a href="<?php echo $this->_tpl_vars['customer_view_link']; ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                  <?php if ($this->_tpl_vars['task']->get('branch') && $this->_tpl_vars['customer_branch']): ?>
                    <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['task']->getBranchLabels('tasks_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                    <span<?php if (! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['task']->get('contact_person') && $this->_tpl_vars['contact_person']): ?>
                    <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                    <span<?php if (! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td>
                  <?php if ($this->_tpl_vars['task']->get('trademark')): ?>
                    <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo $this->_tpl_vars['task']->get('trademark'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                  <?php else: ?>
                    &nbsp;
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td>
                  <?php if ($this->_tpl_vars['task']->get('project')): ?>
                    <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=projects&amp;projects=view&amp;view=<?php echo $this->_tpl_vars['task']->get('project'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php if ($this->_tpl_vars['task']->get('phase')): ?> <span class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['tasks_phase']), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('phase_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                  <?php else: ?>
                    &nbsp;
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'planned_start_date'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('planned_start_date'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'planned_finish_date'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td>
                  <?php ob_start(); ?>
                    <?php if ($this->_tpl_vars['task']->get('status') != 'finished' && $this->_tpl_vars['task']->get('planned_finish_date') && ((is_array($_tmp=$this->_tpl_vars['task']->get('planned_finish_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                      <?php echo $this->_config[0]['vars']['tasks_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('planned_finish_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
                    <?php endif; ?>
                  <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('task_expired', ob_get_contents());ob_end_clean(); ?>
                  <?php if ($this->_tpl_vars['task']->get('status') != 'finished' && ( ( $this->_tpl_vars['task']->get('planned_finish_date') && ((is_array($_tmp=$this->_tpl_vars['task']->get('planned_finish_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) )): ?>
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" class="t_info_image" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['task_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['tasks_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
                  <?php endif; ?>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('planned_finish_date'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'planned_time'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['task']->get('planned_time'))) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo $this->_config[0]['vars']['minutes']; ?>
</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'timesheet_time'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('timesheet_time_formatted'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <?php if ($this->_tpl_vars['task']->get('timesheet_time') > 0): ?>
                    (<?php echo $this->_config[0]['vars']['total']; ?>
: <?php echo $this->_tpl_vars['task']->get('timesheet_time'); ?>
 <?php echo $this->_config[0]['vars']['minutes']; ?>
)
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'severity'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <?php ob_start(); ?>tasks_<?php echo $this->_tpl_vars['task']->get('severity'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('severity_name', ob_get_contents());ob_end_clean(); ?>
                <td class="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('severity'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['severity_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'progress'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('progress'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 %</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'equipment'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('equipment'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'task_field'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td><?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('task_field'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'source'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td>
                  <?php if ($this->_tpl_vars['task']->get('source')): ?>
                    <?php ob_start(); ?>tasks_<?php echo $this->_tpl_vars['task']->get('source'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('source_lang_var', ob_get_contents());ob_end_clean(); ?>
                    <?php echo $this->_config[0]['vars'][$this->_tpl_vars['source_lang_var']]; ?>

                  <?php else: ?>
                    &nbsp;
                  <?php endif; ?>
                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['task']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                </td>
              </tr>
              <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
              <tr id="task_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                <td>
                <?php if ($this->_tpl_vars['department']): ?>
                  <?php if (! $this->_tpl_vars['department']->isActivated()): ?>
                    <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                  <?php else: ?>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['task']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                  <?php endif; ?>
                <?php endif; ?>
                </td>
              </tr>
              <?php endif; ?>

              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
              <?php if ($this->_tpl_vars['task']->get('buttons')): ?>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr>
                  <td colspan="3">
                    <?php echo ''; ?><?php $_from = $this->_tpl_vars['task']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>

                  </td>
                </tr>
              <?php endif; ?>
            </table>
          </td>
        </tr>
      </table>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['task'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
  </div>

  <?php if (isset ( $this->_tpl_vars['side_panels'] )): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_side_panel.html", 'smarty_include_vars' => array('side_panels' => $this->_tpl_vars['side_panels'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endif; ?>
</div>