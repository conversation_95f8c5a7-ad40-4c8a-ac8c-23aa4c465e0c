<?php

declare (strict_types=1);
namespace <PERSON>\PostRector\Application;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Stmt;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\NodeTraverser;
use <PERSON>\Configuration\Option;
use <PERSON>\Configuration\Parameter\SimpleParameterProvider;
use <PERSON>\Configuration\RenamedClassesDataCollector;
use <PERSON>\Contract\DependencyInjection\ResetableInterface;
use <PERSON>\PostRector\Contract\Rector\PostRectorInterface;
use <PERSON>\PostRector\Rector\ClassRenamingPostRector;
use <PERSON>\PostRector\Rector\DocblockNameImportingPostRector;
use <PERSON>\PostRector\Rector\NameImportingPostRector;
use <PERSON>\PostRector\Rector\UnusedImportRemovingPostRector;
use <PERSON>\PostRector\Rector\UseAddingPostRector;
use <PERSON>\Renaming\Rector\Name\RenameClassRector;
use Rector\Skipper\Skipper\Skipper;
use Rector\ValueObject\Application\File;
final class PostFileProcessor implements ResetableInterface
{
    /**
     * @readonly
     * @var \Rector\Skipper\Skipper\Skipper
     */
    private $skipper;
    /**
     * @readonly
     * @var \Rector\PostRector\Rector\UseAddingPostRector
     */
    private $useAddingPostRector;
    /**
     * @readonly
     * @var \Rector\PostRector\Rector\NameImportingPostRector
     */
    private $nameImportingPostRector;
    /**
     * @readonly
     * @var \Rector\PostRector\Rector\ClassRenamingPostRector
     */
    private $classRenamingPostRector;
    /**
     * @readonly
     * @var \Rector\PostRector\Rector\DocblockNameImportingPostRector
     */
    private $docblockNameImportingPostRector;
    /**
     * @readonly
     * @var \Rector\PostRector\Rector\UnusedImportRemovingPostRector
     */
    private $unusedImportRemovingPostRector;
    /**
     * @readonly
     * @var \Rector\Configuration\RenamedClassesDataCollector
     */
    private $renamedClassesDataCollector;
    /**
     * @var PostRectorInterface[]
     */
    private $postRectors = [];
    public function __construct(Skipper $skipper, UseAddingPostRector $useAddingPostRector, NameImportingPostRector $nameImportingPostRector, ClassRenamingPostRector $classRenamingPostRector, DocblockNameImportingPostRector $docblockNameImportingPostRector, UnusedImportRemovingPostRector $unusedImportRemovingPostRector, RenamedClassesDataCollector $renamedClassesDataCollector)
    {
        $this->skipper = $skipper;
        $this->useAddingPostRector = $useAddingPostRector;
        $this->nameImportingPostRector = $nameImportingPostRector;
        $this->classRenamingPostRector = $classRenamingPostRector;
        $this->docblockNameImportingPostRector = $docblockNameImportingPostRector;
        $this->unusedImportRemovingPostRector = $unusedImportRemovingPostRector;
        $this->renamedClassesDataCollector = $renamedClassesDataCollector;
    }
    public function reset() : void
    {
        $this->postRectors = [];
    }
    /**
     * @param Stmt[] $stmts
     * @return Stmt[]
     */
    public function traverse(array $stmts, File $file) : array
    {
        foreach ($this->getPostRectors() as $postRector) {
            // file must be set early into PostRector class to ensure its usage
            // always match on skipping process
            $postRector->setFile($file);
            if ($this->shouldSkipPostRector($postRector, $file->getFilePath(), $stmts)) {
                continue;
            }
            $nodeTraverser = new NodeTraverser();
            $nodeTraverser->addVisitor($postRector);
            $stmts = $nodeTraverser->traverse($stmts);
        }
        return $stmts;
    }
    /**
     * @param Stmt[] $stmts
     */
    private function shouldSkipPostRector(PostRectorInterface $postRector, string $filePath, array $stmts) : bool
    {
        if ($this->skipper->shouldSkipElementAndFilePath($postRector, $filePath)) {
            return \true;
        }
        // skip renaming if rename class rector is skipped
        if ($postRector instanceof ClassRenamingPostRector && $this->skipper->shouldSkipElementAndFilePath(RenameClassRector::class, $filePath)) {
            return \true;
        }
        return !$postRector->shouldTraverse($stmts);
    }
    /**
     * Load on the fly, to allow test reset with different configuration
     * @return PostRectorInterface[]
     */
    private function getPostRectors() : array
    {
        if ($this->postRectors !== []) {
            return $this->postRectors;
        }
        $isRenamedClassEnabled = $this->renamedClassesDataCollector->getOldToNewClasses() !== [];
        $isNameImportingEnabled = SimpleParameterProvider::provideBoolParameter(Option::AUTO_IMPORT_NAMES);
        $isDocblockNameImportingEnabled = SimpleParameterProvider::provideBoolParameter(Option::AUTO_IMPORT_DOC_BLOCK_NAMES);
        $isRemovingUnusedImportsEnabled = SimpleParameterProvider::provideBoolParameter(Option::REMOVE_UNUSED_IMPORTS);
        $postRectors = [];
        // sorted by priority, to keep removed imports in order
        if ($isRenamedClassEnabled) {
            $postRectors[] = $this->classRenamingPostRector;
        }
        // import names
        if ($isNameImportingEnabled) {
            $postRectors[] = $this->nameImportingPostRector;
        }
        // import docblocks
        if ($isNameImportingEnabled && $isDocblockNameImportingEnabled) {
            $postRectors[] = $this->docblockNameImportingPostRector;
        }
        $postRectors[] = $this->useAddingPostRector;
        if ($isRemovingUnusedImportsEnabled) {
            $postRectors[] = $this->unusedImportRemovingPostRector;
        }
        $this->postRectors = $postRectors;
        return $this->postRectors;
    }
}
