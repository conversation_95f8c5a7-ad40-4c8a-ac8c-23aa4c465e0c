<?php

declare (strict_types=1);
namespace RectorPrefix202410;

use <PERSON>\Arguments\Rector\ClassMethod\ReplaceArgumentDefaultValueRector;
use <PERSON>\Arguments\ValueObject\ReplaceArgumentDefaultValue;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Symfony\Symfony28\Rector\MethodCall\GetToConstructorInjectionRector;
use <PERSON>\Symfony\Symfony28\Rector\StaticCall\ParseFileRector;
return static function (RectorConfig $rectorConfig) : void {
    $rectorConfig->rules([ParseFileRector::class, GetToConstructorInjectionRector::class]);
    $rectorConfig->ruleWithConfiguration(ReplaceArgumentDefaultValueRector::class, [
        // @see https://github.com/symfony/symfony/commit/912fc4de8fd6de1e5397be4a94d39091423e5188
        new ReplaceArgumentDefaultValue('Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface', 'generate', 2, \true, 'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface::ABSOLUTE_URL'),
        new ReplaceArgumentDefaultValue('Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface', 'generate', 2, \false, 'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface::ABSOLUTE_PATH'),
        new ReplaceArgumentDefaultValue('Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface', 'generate', 2, 'relative', 'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface::RELATIVE_PATH'),
        new ReplaceArgumentDefaultValue('Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface', 'generate', 2, 'network', 'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface::NETWORK_PATH'),
    ]);
};
