<?php
namespace Bgs\FlightLandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor for virtual route data
 * 
 * This processor makes virtual route data available to Fluid templates
 * when rendering virtual routes. It only activates when virtual route
 * data is present in the GLOBALS.
 */
class VirtualRouteDataProcessor implements DataProcessorInterface
{
    /**
     * Process virtual route data and make it available to templates
     *
     * @param ContentObjectRenderer $cObj The content object renderer
     * @param array $contentObjectConfiguration The TypoScript configuration
     * @param array $processorConfiguration The processor configuration
     * @param array $processedData The processed data from previous processors
     * @return array The processed data with virtual route data added
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        // Get the target variable name (default: flightRouteData)
        $targetVariableName = $processorConfiguration['as'] ?? 'flightRouteData';

        // Check if virtual route data is available in GLOBALS
        $virtualRouteData = $GLOBALS['TYPO3_CONF_VARS']['USER']['flightRouteData'] ?? null;

        if ($virtualRouteData !== null) {
            // Virtual route data is available - add it to processed data
            $processedData[$targetVariableName] = $virtualRouteData;
        } else {
            // No virtual route data - add default structure for normal pages
            $processedData[$targetVariableName] = [
                'isVirtualRoute' => false,
                'currentFlightRoute' => null,
                'templatePageContent' => []
            ];
        }

        return $processedData;
    }
}
