<?php
namespace Bgs\FlightLandingPages\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Middleware to handle virtual route URLs for Flight Landing Pages
 * 
 * This middleware intercepts requests that match virtual route patterns
 * (e.g., /flights/ber-sof) and redirects them to the appropriate landing page
 * with the route information preserved for processing.
 */
class VirtualRouteMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $site = $request->getAttribute('site');

        if (!$site instanceof Site) {
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');
        
        // Check if this path matches a virtual route pattern
        $virtualRouteMatch = $this->matchVirtualRoute($path, $site);
        var_dump($virtualRouteMatch);exit;
        if ($virtualRouteMatch) {
            // Modify the request to point to the landing page
            $modifiedRequest = $this->createModifiedRequest($request, $virtualRouteMatch);
            return $handler->handle($modifiedRequest);
        }

        return $handler->handle($request);
    }

    /**
     * Check if the path matches a virtual route pattern
     */
    protected function matchVirtualRoute(string $path, Site $site): ?array
    {
        // Extract potential route pattern from path
        $pathParts = explode('/', $path);

        if (count($pathParts) < 2) {
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ber-sof")
        $routeSlug = end($pathParts);
        var_dump($routeSlug,$pathParts);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);
        var_dump($landingPagePath);
        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($landingPagePath, $site);
        var_dump($landingPageData);
        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $flightRoute = $this->findFlightRoute($originCode, $destinationCode, $landingPageData['uid']);
        
        if (!$flightRoute) {
            return null;
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => $routeSlug,
            'originCode' => $originCode,
            'destinationCode' => $destinationCode,
            'landingPagePath' => $landingPagePath
        ];
    }

    /**
     * Find landing page by path within the site
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Build the path condition - we need to find pages that match the path
        $siteRootPageId = $site->getRootPageId();

        // Ensure path starts with /
        $searchPath = '/' . ltrim($path, '/');

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($searchPath))
            )
            ->executeQuery();
        $t = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($searchPath, \PDO::PARAM_STR))
            );
var_dump('<br/>',
    $t->getSQL(),
    $t->getParameters()
);
        $pages = $result->fetchAllAssociative();
        var_dump('<br/>', $path,$searchPath, $pages);
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Check if a page belongs to a specific site
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        // Get the page's root line to check if it belongs to this site
        $currentPageUid = $pageUid;
        
        while ($currentPageUid > 0) {
            if ($currentPageUid === $siteRootPageId) {
                return true;
            }
            
            $result = $queryBuilder
                ->select('pid')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($currentPageUid, \PDO::PARAM_INT))
                )
                ->executeQuery();
            
            $page = $result->fetchAssociative();
            if (!$page) {
                break;
            }
            
            $currentPageUid = (int)$page['pid'];
        }

        return false;
    }

    /**
     * Find flight route by origin/destination codes and landing page
     */
    protected function findFlightRoute(string $originCode, string $destinationCode, int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('landing_page', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('origin_code', $queryBuilder->createNamedParameter($originCode)),
                $queryBuilder->expr()->eq('destination_code', $queryBuilder->createNamedParameter($destinationCode)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Create a modified request that points to the landing page
     */
    protected function createModifiedRequest(ServerRequestInterface $request, array $virtualRouteMatch): ServerRequestInterface
    {
        $landingPage = $virtualRouteMatch['landingPage'];
        
        // Create new URI pointing to the landing page
        $uri = $request->getUri();
        $newPath = $landingPage['slug'];
        
        // Ensure the path starts with /
        if (!str_starts_with($newPath, '/')) {
            $newPath = '/' . $newPath;
        }
        
        $newUri = $uri->withPath($newPath);
        
        // Create modified request with the new URI
        $modifiedRequest = $request->withUri($newUri);
        
        // Add virtual route information as request attributes
        $modifiedRequest = $modifiedRequest->withAttribute('flightLandingPages.virtualRoute', $virtualRouteMatch);
        
        return $modifiedRequest;
    }
}
