<?php
namespace Bgs\FlightLandingPages\Service;

/**
 * Service for rendering virtual route pages
 * 
 * This service handles the rendering of virtual route pages with template content
 * and flight data, providing a clean separation from the middleware logic.
 */
class VirtualRouteRenderingService
{
    /**
     * Render virtual page using template page data and content
     * This method renders content from the template page without custom HTML structure
     */
    public function renderVirtualPage(array $virtualContent, array $virtualRouteMatch, $controller = null): string
    {
        // For now, always use content-only rendering to avoid TypoScript issues
        // TODO: Implement proper TypoScript integration when lib.dynamicContent is available
        return $this->renderContentOnly($virtualContent, $virtualRouteMatch, $controller);
    }

    /**
     * Render only the content elements without any HTML structure
     * This is used as a fallback when TypoScript is not available
     */
    protected function renderContentOnly(array $virtualContent, array $virtualRouteMatch): string
    {
        $html = '';

        // Render only the content elements without any wrapper HTML
        if (!empty($virtualContent)) {
            foreach ($virtualContent as $contentElement) {
                $html .= '<div class="content-element">';

                if (!empty($contentElement['header'])) {
                    $html .= '<h2>' . htmlspecialchars($contentElement['header']) . '</h2>';
                }

                if (!empty($contentElement['subheader'])) {
                    $html .= '<h3>' . htmlspecialchars($contentElement['subheader']) . '</h3>';
                }

                if (!empty($contentElement['bodytext'])) {
                    $html .= '<div>' . nl2br(htmlspecialchars($contentElement['bodytext'])) . '</div>';
                }

                $html .= '</div>';
            }
        } else {
            $html .= '<p>Test content</p>';
        }

        return $html;
    }

    /**
     * Render a more advanced virtual page using TYPO3 templates (future enhancement)
     */
    public function renderWithTypoScript(array $virtualContent, array $virtualRouteMatch, $controller): string
    {
        // This method can be implemented later to integrate with TYPO3's template system
        // when the TypoScript issues are resolved
        return $this->renderVirtualPage($virtualContent, $virtualRouteMatch);
    }

    /**
     * Generate page metadata for virtual routes
     */
    public function generatePageMetadata(array $virtualRouteMatch): array
    {
        $flightRoute = $virtualRouteMatch['flightRoute'] ?? [];
        $landingPage = $virtualRouteMatch['landingPage'] ?? [];
        
        $originCode = $flightRoute['origin_code'] ?? '';
        $destinationCode = $flightRoute['destination_code'] ?? '';
        $originName = $flightRoute['origin_name'] ?? $originCode;
        $destinationName = $flightRoute['destination_name'] ?? $destinationCode;
        
        return [
            'title' => "Flight from {$originName} to {$destinationName}",
            'description' => "Find flights from {$originName} ({$originCode}) to {$destinationName} ({$destinationCode})",
            'keywords' => "flights, {$originCode}, {$destinationCode}, {$originName}, {$destinationName}",
            'canonical' => $virtualRouteMatch['requestedPath'] ?? '',
            'og_title' => "Flight Route: {$originCode} → {$destinationCode}",
            'og_description' => "Book your flight from {$originName} to {$destinationName}",
        ];
    }
}
