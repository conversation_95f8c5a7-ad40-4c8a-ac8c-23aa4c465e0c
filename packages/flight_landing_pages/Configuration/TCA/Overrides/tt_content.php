<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register FlightReference plugin (for content elements)
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
        'FlightLandingPages',
        'FlightReference',
        'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:plugin.flight_reference.title',
        'mimetypes-x-content-list',
        'flight_landing_pages'
    );

    // Add FlexForm for FlightPage plugin
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
        '*',
        'FILE:EXT:flight_landing_pages/Configuration/FlexForms/FlightPage.xml',
        'flightlandingpages_flightpage'
    );



    // Add FlexForm for FlightReference plugin
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
        '*',
        'FILE:EXT:flight_landing_pages/Configuration/FlexForms/FlightReference.xml',
        'flightlandingpages_flightreference'
    );
});
