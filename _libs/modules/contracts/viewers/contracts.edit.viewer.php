<?php

class Contracts_Edit_Viewer extends Viewer {
    public $template = 'edit.html';

    public function prepare() {

        if (!isset($this->model->counter)) {
            //get counter for the contract and sanitize it
            $this->model->getCounter();
            if ($this->model->counter) {
                $this->model->counter->sanitize();
            }
        }

        // prepare layout index
        $this->prepareLayoutIndex();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        // GET DEPARTMENTS
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $departments = Departments::getTree($this->registry);
        $this->data['departments'] = $departments;

        // GET GROUPS
        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $groups = Groups::getTree($this->registry);
        $this->data['groups'] = $groups;

        //prepare contract type
        require_once($this->modelsDir . 'contracts.types.factory.php');
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('model_lang'),
                         'where' => array('cot.id = ' . $this->model->get('type')));
        $this->data['contract_type'] = Contracts_Types::searchOne($this->registry, $filters);

        //prepare company
        require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('model_lang'),
                         'where' => array('fc.id = ' . $this->model->get('company')));
        $this->data['company'] = Finance_Companies::searchOne($this->registry, $filters);
        if ($this->data['company']) {
            $this->model->set('company_name', $this->data['company']->get('name'), true);

            //prepare offices
            require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
            $params = array($this->registry,
                            'lang' => $this->model->get('model_lang'),
                            'company_id' => $this->model->get('company'),
                            'active' => 1);
            $this->data['offices'] = Finance_Dropdown::getCompanyOffices($params);
        }

        $office = '';
        if ($this->model->get('office')) {
            $office = $this->model->get('office');
        } elseif ($this->registry['currentUser']->get('office')) {
            foreach ($this->data['offices'] as $cmp) {
                if ($cmp['option_value'] == $this->registry['currentUser']->get('office')) {
                    $office = $cmp['option_value'];
                    $this->model->set('office', $office, true);
                    break;
                }
            }
        }
        if (!$office && count($this->data['offices']) == 1) {
            $office = $this->data['offices'][0]['option_value'];
            $this->model->set('office', $office, true);
        }
        if ($office) {
            foreach ($this->data['offices'] as $o) {
                if ($o['option_value'] == $office) {
                    $this->model->set('office_name', $o['label'], true);
                    break;
                }
            }
        }

        //prepare customer
        $customer_id = $this->model->get('customer');
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id,
                                              'c.deleted IS NOT NULL'),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer_is_company', $customer->get('is_company'), true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }

                if ($customer->get('is_company')) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                    $filters_branches = array('sanitize' => true,
                                              'model_lang' => $this->model->get('model_lang'),
                                              'where' => array('c.parent_customer = ' . $customer_id,
                                                               'c.subtype = \'branch\''),
                                              'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'));
                    $this->data['customer_branches'] = Customers_Branches::search($this->registry, $filters_branches);

                    if ($this->model->get('branch')) {
                        foreach ($this->data['customer_branches'] as $branch) {
                            if ($branch->get('id') == $this->model->get('branch')) {
                                $this->model->set('branch_name', $branch->get('name'), true);
                                $this->model->set('branch_active', $branch->get('active'), true);
                                break;
                            }
                        }

                        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                        $filters_contacts = array('sanitize' => true,
                                                  'model_lang' => $this->model->get('model_lang'),
                                                  'where' => array('c.parent_customer = ' . $this->model->get('branch'),
                                                                   'c.subtype = \'contact\''),
                                                  'sort' => array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC'));
                        $this->data['contact_persons'] = Customers_Contactpersons::search($this->registry, $filters_contacts);
                        if ($this->model->get('contact_person')) {
                            foreach ($this->data['contact_persons'] as $contact_person) {
                                if ($contact_person->get('id') == $this->model->get('contact_person')) {
                                    $this->model->set('contact_person_name', $contact_person->get('name') . ' ' . $contact_person->get('lastname'), true);
                                    $this->model->set('contact_person_active', $contact_person->get('active'), true);
                                    break;
                                }
                            }
                        }
                    }
                }
            } else {
                $customer_id = 0;
            }
        }
        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('customer_is_company', '', true);
            $this->model->set('branch', '', true);
            $this->model->set('branch_name', '', true);
            $this->model->set('contact_person', '', true);
            $this->model->set('contact_person_name', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = 0;
        if ($this->model->get('trademark')) {
            $trademark_id = $this->model->get('trademark');
        } elseif (isset($this->data['contract_type']) && $this->data['contract_type']->get('default_trademark')) {
            $trademark_id = $this->data['contract_type']->get('default_trademark');
        }
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('n.deleted IS NOT NULL',
                                              'n.id = ' . $trademark_id));
            //flag to search in customers trademarks
            $filters['session_param'] = 'filter_trademark_nomenclature';
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare project
        $project_id = $this->model->get('project');
        if ($project_id) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('p.id = ' . $project_id,
                                              'p.deleted IS NOT NULL'));
            $project = Projects::searchOne($this->registry, $filters);
            if ($project) {
                $this->model->set('project_code', $project->get('code'), true);
                $this->model->set('project_name', $project->get('name'), true);
            } else {
                $project_id = 0;
            }
        }
        if (!$project_id) {
            // clear all related properties
            $this->model->set('project_code', '', true);
            $this->model->set('project_name', '', true);
            $this->model->set('project', '', true);
        }

        //prepare employee
        $employee_id = $this->model->get('employee');
        if ($employee_id) {
            require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('c.type = ' . PH_CUSTOMER_EMPLOYEE, 'c.id = ' . $employee_id));
            $employee = Customers::searchOne($this->registry, $filters);
            if ($employee) {
                $this->model->set('employee_code', $employee->get('code'), true);
                $this->model->set('employee_name', $employee->get('name') . ' ' . $employee->get('lastname'), true);
                $this->model->set('employee', $employee_id, true);
            } else {
                $employee_id = 0;
            }
        }
        if (!$employee_id) {
            // clear all related properties
            $this->model->set('employee_code', '', true);
            $this->model->set('employee_name', '', true);
            $this->model->set('employee', '', true);
        }
        $this->data['autocomplete_employee_filters'] = array('<type>' => (string)PH_CUSTOMER_EMPLOYEE);

        $fields = $this->registry['config']->getParamAsArray($this->module, 'validate_' . $this->model->get('type'));
        $this->data['required_fields'] = array_filter($fields, function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; });

        //remove the counter from the contract
        unset($this->model->counter);

        $this->prepareSubpanels();

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = '';
        if ($this->model->get('subtype') == 'contract') {
            $title = $this->i18n('contracts_edit');
        } elseif ($this->model->get('subtype') == 'annex') {
            $title = $this->i18n('contracts_annex_edit');
        }

        $title = sprintf($title, $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
