<?php

class Customers_Contactpersons_Viewer extends Viewer {
    public $template = 'contact_persons.html';

    public function prepare() {
        $this->model = $this->registry['customer'];
        $this->data['customer'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $session_param = 'contact_persons_ajax_customers_' . $this->model->get('id') . '_';

        //search the contact persons for the current customer
        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
        $filters['where'] = array('bn.parent_customer = ' . $this->model->get('id'),
                                  'c.subtype = \'contact\'');
        $filters = Customers_Contactpersons::saveSearchParams($this->registry, $filters, $session_param);
        $filters['model_lang'] = $this->model->get('model_lang');
        // display main contact persons first only when list has no column sorting specified by user
        if (empty($filters['sort'])) {
            $filters['sort'] = array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC');
        }

        // change controller in registry in order to get rights of models correctly
        $this->registry->set('controller', 'contactpersons', true);
        list($contact_persons, $pagination) = Customers_Contactpersons::pagedSearch($this->registry, $filters);
        $this->registry->set('controller', 'customers', true);

        $this->data['customers_contact_persons'] = $contact_persons;
        $this->data['pagination'] = $pagination;
        $this->data['customers_contact_persons_use_ajax'] = true;
        $this->data['customers_contact_persons_session_param'] = $session_param;
        $this->data['selected_items'] = $this->registry['session']->get($session_param, 'selected_items');

        $sort_base_link  =  sprintf("%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s",
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'],
                                'customers',
                                $this->registry['controller_param'],
                                'contactpersons',
                                'contactpersons',
                                $this->model->get('id'),
                                'parent_customer_id',
                                $this->model->get('id'),
                                'model_lang',
                                $this->model->get('model_lang')
                            );

        $this->data['customers_contact_persons_sort'] = $this->prepareAjaxSort($filters, $session_param, '', $sort_base_link, true);

        $this->prepareTranslations();
        $this->prepareTitleBar();

        if ($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->registry->push('custom_js', "{$this->viewJsUrl}list.js");
            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }

    public function prepareTitleBar() {
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('customers_contactpersons'), $this->model->getModelTypeName());
        }
        $this->data['title'] = $title;
    }
}

?>
