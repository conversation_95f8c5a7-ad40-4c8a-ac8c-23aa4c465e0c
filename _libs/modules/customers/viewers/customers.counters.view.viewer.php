<?php

class Customers_Counters_View_Viewer extends Viewer {
    public $template = 'counters_view.html';

    public function prepare() {
        $this->model = $this->registry['customers_counter'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare groups
        if ($this->model->get('group')) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('g.id = ' . $this->model->get('group')));
            $group = Groups::searchOne($this->registry, $filters);
            if ($group) {
                $this->data['group'] = $group->get('name');
            }
        }
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('customers_counters_view');
    }
}

?>
