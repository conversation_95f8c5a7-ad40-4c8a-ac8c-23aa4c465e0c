      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      {assign var='super_layout_offset' value='0'}
      {assign var='num_view_layouts' value=$customer->get('num_view_layouts')}
      {foreach from=$customer->get('layouts_details') key='lkey' item='layout'}

      {if $layout.place gt $super_layout_offset}
        {if $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_MAIN_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_MAIN_TO}
          {assign var='super_layout' value='main_data'}
          {assign var='super_layout_class' value='customers_main_data'}
        {elseif $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_ADDRESS_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_ADDRESS_TO}
          {assign var='super_layout' value='contact_data'}
          {assign var='super_layout_class' value='customers_contact_data'}
        {elseif $layout.place lte $smarty.const.PH_CUSTOMERS_LAYOUTS_REG_TO}
          {assign var='super_layout_offset' value=$smarty.const.PH_CUSTOMERS_LAYOUTS_REG_TO}
          {assign var='super_layout' value='personal_data'}
          {assign var='super_layout_class' value='customers_personal_data'}
        {else}
          {assign var='super_layout' value=''}
          {assign var='super_layout_class' value=''}
        {/if}
        {assign var='super_layout_cookie' value=''}
        {if $super_layout && $customer->get('super_layouts') && in_array($super_layout, $customer->get('super_layouts'))}
        <tr>
          <td colspan="7" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="customers_{$super_layout}_switch">
              {capture assign='super_layout_cookie'}customers_{$super_layout}_box{/capture}
              {capture assign='super_layout_name'}customers_{if $super_layout eq 'contact_data'}address_data{else}{$super_layout}{/if}{/capture}
              <a name="customer_{$super_layout}_index"></a><div class="switch_{if $smarty.cookies.$super_layout_cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$smarty.config.$super_layout_name|escape}</div>
            </div>
          </td>
        </tr>
        <tr class="{$super_layout_class}"{if $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="{$num_view_layouts.$super_layout|default:0}">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr class="{$super_layout_class}"{if $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$customer->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$customer->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>
        {/if}
      {/if}

        {if $lkey eq 'name'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{if !($layout.view && $layout.edit)}{$base_model->get('name')|escape}{else}{$customer->get('name')|escape}{/if}" title="{#customers_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{#customers_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_lastname"><label for="lastname"{if $messages->getErrors('lastname')} class="error"{/if}>{help label='lastname'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="lastname" id="lastname" value="{if !($layout.view && $layout.edit)}{$base_model->get('lastname')|escape}{else}{$customer->get('lastname')|escape}{/if}" title="{#customers_lastname#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_lastname" id="copy_lastname" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_lastname" id="bm_lastname" value="{$base_model->get('lastname')|escape}" title="{#customers_lastname#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'type'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$customer->get('type_name')|escape}
            <input type="hidden" name="type" id="type" value="{$customer->get('type')|escape}" />
            <input type="hidden" name="type_name" id="type_name" value="{$customer->get('type_name')|escape}" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'is_company'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_is_company"><label for="is_company">{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $customer->get('is_company')}
              {#customers_company#|escape}
            {else}
              {#customers_person#|escape}
            {/if}
            <input type="hidden" name="is_company" id="is_company" value="{$customer->get('is_company')}" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'code'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_code"><label for="code"{if $messages->getErrors('code')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="code" id="code" value="{$customer->get('code')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'num'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_num"><label for="num"{if $messages->getErrors('num')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$customer->get('num')|escape}
            <input type="hidden" value="{$customer->get('num')|escape}" name="num" id="num" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'company_department'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_company_department"><label for="company_department"{if $messages->getErrors('company_department')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="company_department" id="company_department" value="{if !($layout.view && $layout.edit)}{$base_model->get('company_department')|escape}{else}{$customer->get('company_department')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_company_department" id="copy_company_department" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_company_department" id="bm_company_department" value="{$base_model->get('company_department')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'position'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_position"><label for="position"{if $messages->getErrors('position')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="position" id="position" value="{if !($layout.view && $layout.edit)}{$base_model->get('position')|escape}{else}{$customer->get('position')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_position" id="copy_position" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_position" id="bm_position" value="{$base_model->get('position')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'country'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_country"><label for="country"{if $messages->getErrors('country')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {$customer->get('country_name')|escape}
            <input type="hidden" name="country" id="country" value="{$customer->get('country')}" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'city'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_city"><label for="city"{if $messages->getErrors('city')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="city" id="city" value="{if !($layout.view && $layout.edit)}{$base_model->get('city')|escape}{else}{$customer->get('city')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_city" id="copy_city" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_city" id="bm_city" value="{$base_model->get('city')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'postal_code'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_postal_code"><label for="postal_code"{if $messages->getErrors('postal_code')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="postal_code" id="postal_code" value="{$customer->get('postal_code')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'address'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_address"><label for="address"{if $messages->getErrors('address')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="address" id="address" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('address')|escape}{else}{$customer->get('address')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_address" id="copy_address" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_address" id="bm_address" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('address')|escape}</textarea>
          </td>
        </tr>
        {elseif $lkey eq 'notes'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_notes"><label for="notes"{if $messages->getErrors('notes')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="notes" id="notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('notes')|escape}{else}{$customer->get('notes')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_notes" id="copy_notes" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_notes" id="bm_notes" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('notes')|escape}</textarea>
          </td>
        </tr>
        {elseif $lkey eq 'contacts'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td colspan="7" class="nopadding">
            {include file=`$templatesDir`_contact_data.html object=$customer predefined_contact_params=$customer->getPredefinedContactParameters()}
          </td>
        </tr>
        {elseif $lkey eq 'ucn'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_ucn"><label for="ucn"{if $messages->getErrors('ucn')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="ucn" id="ucn" value="{$customer->get('ucn')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'identity_num'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_identity_num"><label for="identity_num"{if $messages->getErrors('identity_num')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td>{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="identity_num" id="identity_num" value="{$customer->get('identity_num')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'identity_date'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_identity_valid"><label for="identity_valid"{if $messages->getErrors('identity_valid')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {capture assign='required'}{if in_array($lkey, $required_fields)}1{else}0{/if}{/capture}
            {include file='input_date.html'
                     standalone=true
                     name='identity_date'
                     label=$layout.name
                     help=$layout.description
                     value=$customer->get('identity_date')
                     required=$required
                     width=200
            }
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'identity_by'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_identity_by"><label for="identity_by"{if $messages->getErrors('identity_by')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="identity_by" id="identity_by" value="{if !($layout.view && $layout.edit)}{$base_model->get('identity_by')|escape}{else}{$customer->get('identity_by')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_identity_by" id="copy_identity_by" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_identity_by" id="bm_identity_by" value="{$base_model->get('identity_by')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {elseif $lkey eq 'identity_valid'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_identity_valid"><label for="identity_valid"{if $messages->getErrors('identity_valid')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {capture assign='required'}{if in_array($lkey, $required_fields)}1{else}0{/if}{/capture}
            {include file='input_date.html'
                     standalone=true
                     name='identity_valid'
                     label=$layout.name
                     help=$layout.description
                     value=$customer->get('identity_valid')
                     required=$required
                     width=200
            }
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'address_by_personal_id'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_address_by_personal_id"><label for="address_by_personal_id"{if $messages->getErrors('address_by_personal_id')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="address_by_personal_id" id="address_by_personal_id" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{if !($layout.view && $layout.edit)}{$base_model->get('address_by_personal_id')|escape}{else}{$customer->get('address_by_personal_id')|escape}{/if}</textarea>
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_address_by_personal_id" id="copy_address_by_personal_id" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <textarea class="areabox distinctive" name="bm_address_by_personal_id" id="bm_address_by_personal_id" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('address_by_personal_id')|escape}</textarea>
          </td>
        </tr>
        {elseif $lkey eq 'in_dds'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          {capture assign='label_in_dds'}{#customers_in_dds#}{/capture}
          <td class="labelbox"><a name="error_in_dds"><label for="in_dds"{if $messages->getErrors('in_dds')} class="error"{/if}>{help label_content=$label_in_dds|escape text_content=""}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="in_dds" id="in_dds" value="{$customer->get('in_dds')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <img src="{$theme->imagesUrl}small/refresh.png" class="icon_button pointer vmiddle" width="14" height="14" alt="{#customers_refresh_person_info#|escape}" title="{#customers_refresh_person_info#|escape}" border="0" onclick="return confirmAction('refresh_company_info', function() {ldelim} getCompanyInfoByVat(); {rdelim}, this);" />
          </td>
          <td colspan="3">&nbsp;</td>
        </tr>
        {elseif $lkey eq 'bank'}
        <tr{if ($layout.view && $layout.edit)} class="{$super_layout_class}"{counter assign='translate_fields_count'}{/if}{if !($layout.view && $layout.edit) || $smarty.cookies.$super_layout_cookie eq 'off'} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_bank"><label for="bank"{if $messages->getErrors('bank')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
          <td class="required">{if in_array($lkey, $required_fields)}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="bank" id="bank" value="{if !($layout.view && $layout.edit)}{$base_model->get('bank')|escape}{else}{$customer->get('bank')|escape}{/if}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td class="vtop">
            <button type="button" name="copy_bank" id="copy_bank" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_bank" id="bm_bank" value="{$base_model->get('bank')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        </tr>
        {/if}
      {/foreach}
        <tr>
          <td colspan="7">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="7">
            {if $translate_fields_count}
            <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}<button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
            {/if}
          </td>
        </tr>
      </table>