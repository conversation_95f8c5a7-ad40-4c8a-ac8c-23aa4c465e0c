<?php

/**
 * Customers_Type model class
 */
Class Customers_Type extends Model {
    public $modelName = 'Customers_Type';

    public $width = 16;

    public $height = 16;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
        if ($this->origin == 'database' && $registry['action'] != 'translate') {
            $ref_doc_types = ($this->get('referent_document_types')) ? preg_split('#\s*,\s*#', $this->get('referent_document_types')) : array();
            $this->set('referent_document_types', $ref_doc_types, true);

            $ref_proj_types = ($this->get('referent_project_types')) ? preg_split('#\s*,\s*#', $this->get('referent_project_types')) : array();
            $this->set('referent_project_types', $ref_proj_types, true);

            $ref_con_types = ($this->get('referent_contract_types')) ? preg_split('#\s*,\s*#', $this->get('referent_contract_types')) : array();
            $this->set('referent_contract_types', $ref_con_types, true);

            $ref_cust_types = ($this->get('referent_customer_types')) ? preg_split('#\s*,\s*#', $this->get('referent_customer_types')) : array();
            $this->set('referent_customer_types', $ref_cust_types, true);

            $transfer_types = $this->get('transfer_types') ? preg_split('#\s*,\s*#', $this->get('transfer_types')) : array();
            $this->set('transfer_types', $transfer_types, true);

            if (!$registry->get('skipRelatedTypes')) {
                $this->getTypesRelations();
            }
        }
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_typename_specified', 'name');
        }
        if (!$this->get('name_plural')) {
            $this->raiseError('error_no_typename_plural_specified', 'name_plural');
        }
        if ($this->get('branch_label') && !$this->get('branch_label_plural')) {
            $this->raiseError('error_no_branch_label_plural_specified', 'branch_label_plural');
        }
        if ($this->get('branch_label_plural') && !$this->get('branch_label')) {
            $this->raiseError('error_no_branch_label_specified', 'branch_label');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //prepare main data from post
        $set = array();
        $set['type_section']            = sprintf("type_section=%d", $this->get('type_section'));
        $set['referent_document_types'] = sprintf("referent_document_types='%s'",
            ($this->isDefined('referent_document_types') ? implode(', ', $this->get('referent_document_types')) : ''));
        $set['referent_project_types']  = sprintf("referent_project_types='%s'",
            ($this->isDefined('referent_project_types') ? implode(', ', $this->get('referent_project_types')) : ''));
        $set['referent_contract_types'] = sprintf("referent_contract_types='%s'",
            ($this->isDefined('referent_contract_types') ? implode(', ', $this->get('referent_contract_types')) : ''));
        $set['referent_customer_types'] = sprintf("referent_customer_types='%s'",
            ($this->isDefined('referent_customer_types') ? implode(', ', $this->get('referent_customer_types')) : ''));

        if ($this->isDefined('counter')) {
            $set['counter'] = sprintf("counter=%d", $this->get('counter'));

            $transfer_types = $this->get('transfer_types');
            if ($transfer_types) {
                $this->set(
                    'transfer_types',
                    $db->GetCol('SELECT id FROM ' . DB_TABLE_CUSTOMERS_TYPES .
                                ' WHERE id IN (' . implode(', ', $transfer_types) . ') AND counter = ' . intval($this->get('counter'))),
                    true);
            }

            if (array_diff($transfer_types, $this->get('transfer_types'))) {
                $warning_transfer_types = 1;
            }
        }

        $set['transfer_types']          = sprintf("transfer_types='%s'",
            ($this->isDefined('transfer_types') ? implode(', ', $this->get('transfer_types')) : ''));
        $set['position']                = sprintf("position=%d", $this->get('position'));
        $set['modified']                = sprintf("modified=now()");
        $set['modified_by']             = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        $set['added']                   = sprintf("added=now()");
        $set['added_by']                = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('kind')) {
            $set['kind'] = sprintf("`kind`='%s'", $this->get('kind'));
        }

        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        }
        if ($this->isDefined('default_group')) {
            $set['default_group'] = sprintf("default_group='%s'", $this->get('default_group'));
        }

        //query to insert the main table
        $query = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_TYPES . "\n" .
                 'SET ' . implode(', ', $set) . "\n";

        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new customer type base details', $db, $query);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //UPDATE THE ROLE DEFINITIONS
        $this->updateRoleDefinitions();

        //UPDATE LAYOUTS
        $this->updateLayouts();

        //UPDATE ADDITIONAL REQUIRED FIELDS AND UNIQUE FIELDS
        $this->updateAdditionalValidateFields();

        //update relation between the types
        $this->updateTypesRelations();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if (!$db->HasFailedTrans() && !empty($warning_transfer_types)) {
            $this->raiseWarning('warning_customers_types_transfer_types_different_counter');
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //prepare update values
        $update = array();
        $update['type_section']    = sprintf("type_section=%d", $this->get('type_section'));
        if ($this->registry['action'] != 'translate') {
            $update['referent_document_types']  = sprintf("referent_document_types='%s'",
                ($this->isDefined('referent_document_types') ? implode(', ', $this->get('referent_document_types')) : ''));
            $update['referent_project_types']   = sprintf("referent_project_types='%s'",
                ($this->isDefined('referent_project_types') ? implode(', ', $this->get('referent_project_types')) : ''));
            $update['referent_contract_types']  = sprintf("referent_contract_types='%s'",
                ($this->isDefined('referent_contract_types') ? implode(', ', $this->get('referent_contract_types')) : ''));
            $update['referent_customer_types']  = sprintf("referent_customer_types='%s'",
                ($this->isDefined('referent_customer_types') ? implode(', ', $this->get('referent_customer_types')) : ''));

            if ($this->isDefined('counter')) {
                $update['counter'] = sprintf("counter=%d", $this->get('counter'));

                // remove invalid transfer_types
                $transfer_types = $this->get('transfer_types');
                if ($transfer_types) {
                    $this->set(
                        'transfer_types',
                        $db->GetCol('SELECT id FROM ' . DB_TABLE_CUSTOMERS_TYPES .
                                    ' WHERE id IN (' . implode(', ', $transfer_types) . ') AND counter = ' . intval($this->get('counter'))),
                        true);
                }

                // update reciprocal transfer_types
                $query = 'SELECT id, transfer_types' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS_TYPES . "\n" .
                         'WHERE transfer_types RLIKE \'(^|, *)' . $this->get('id') . '( *,|$)\' AND counter != ' . intval($this->get('counter'));
                $transfer_types_from = $db->GetAssoc($query);

                $query = 'UPDATE ' . DB_TABLE_CUSTOMERS_TYPES . ' SET transfer_types = ? WHERE id = ?';
                foreach ($transfer_types_from as $id => $types) {
                    $types = array_filter(array_diff(preg_split('#\s*,\s*#', $types), array($this->get('id'))));
                    $db->Execute($query, array(implode(', ', $types), $id));
                }

                if (is_array($transfer_types) && is_array($this->get('transfer_types')) &&
                    array_diff($transfer_types, $this->get('transfer_types')) || $transfer_types_from) {
                    $warning_transfer_types = 1;
                }
            }

            $update['transfer_types']           = sprintf("transfer_types='%s'",
                ($this->isDefined('transfer_types') ? implode(', ', $this->get('transfer_types')) : ''));
        }
        $update['position']         = sprintf("position=%d", $this->get('position'));
        $update['default_group']    = sprintf("default_group='%s'", $this->get('default_group'));
        $update['default_pattern']  = sprintf("default_pattern=%d", $this->get('default_pattern'));

        if ($this->isDefined('group')) {
            $update['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('active')) {
            $update['active'] = sprintf("active=%d", $this->get('active'));
        }
        if ($this->isDefined('kind')) {
            $update['kind'] = sprintf("`kind`='%s'", $this->get('kind'));
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CUSTOMERS_TYPES . "\n" .
                  'SET ' . implode(', ', $update) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        $this->updateI18N();
        $this->updateLayouts(false);
        if ($this->registry->get('action') != 'translate') {
            //UPDATE ADDITIONAL REQUIRED FIELDS AND UNIQUE FIELDS
            $this->updateAdditionalValidateFields();

            //update relation between the types
            $this->updateTypesRelations();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if (!$db->HasFailedTrans() && !empty($warning_transfer_types)) {
            $this->raiseWarning('warning_customers_types_transfer_types_different_counter');
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['name']        = sprintf("name='%s'", $this->get('name'));
        $update['name_plural'] = sprintf("name_plural='%s'", $this->get('name_plural'));
        $update['branch_label']        = sprintf("branch_label='%s'", $this->get('branch_label'));
        $update['branch_label_plural'] = sprintf("branch_label_plural='%s'", $this->get('branch_label_plural'));
        $update['main_branch_name']    = sprintf("main_branch_name='%s'", $this->get('main_branch_name'));

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        //query to insert/update the i18n table for the selected model language
        $query2 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_TYPES_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Update role definitions
     *
     * @return bool - result of the operation
     */
    public function updateRoleDefinitions() {
        // Load the main data
        $db               = $this->registry['db'];
        $model_id         = $this->get('id');
        $actions_arr      = array(
            'list' => 1,
            'search' => 1,
            'add' => 0,
            'multiadd' => 0,
            'edit' => 1,
            'view' => 1,
            'transfer' => 1,
            'system_settings_active' => 0,
            'system_settings_group' => 0,
            'system_settings_portal' => 0,
            'communications' => 1,
            'comments' => 1,
            'comments_add' => 1,
            'emails' => 1,
            'emails_add' => 1,
            'trademarks' => 1,
            'create' => 0,
        );
        $actions_arr_defs = array_keys($actions_arr);

        // Load some specific data
        $module     = 'customers';
        $controller = '';

        // Get the roles definitions for the current model type
        $query = 'SELECT `action`, `requires_model`, `position` ' . "\n" .
                 'FROM `' . DB_TABLE_ROLES_DEFINITIONS . '` ' . "\n" .
                 'WHERE `module` = \'' . $module . '\' ' . "\n" .
                 '  AND `controller` = \'' . $controller . '\' ' . "\n" .
                 '  AND `model_type` = \'' . $model_id . '\' ' . "\n" .
                 'ORDER BY `position` ASC';
        $action_definitions = $db->GetAssoc($query);

        // Check for definition changes
        // Flag: should the permissions be INSERTED / UPDATED
        $update_permissions = false;
        // Prepare an array for the INSERT sets
        $set = array();
        // For each hard-coded action definition
        foreach ($actions_arr as $action => $requires_model) {
            // Get the hard-coded definition`s position
            $position = array_search($action, $actions_arr_defs) + 1;

            // If the actions is NOT into the database
            //   or it has a different "requires_model"
            //   or it has a different "position"
            if (!isset($action_definitions[$action]) || (isset($action_definitions[$action]) && ($action_definitions[$action]['requires_model'] != $requires_model || $action_definitions[$action]['position'] != $position))) {
                // Build the set for the INSERT query
                $set[]    = '(NULL, \'' . $module . '\', \'' . $controller . '\', \'' . $action . '\', \'' . $model_id . '\', \'' . $requires_model . '\', \'' . $position . '\')';

                // If the hard-coded definition is not into the database
                if (!isset($action_definitions[$action]) && !$update_permissions) {
                    // Set that the permissions should be INSERTED / UPDATED
                    $update_permissions = true;
                }
            }
        }

        // INSERT / UPDATE: definitions
        if (empty($set)) {
            return true;
        } else {
            $query = 'INSERT INTO `' . DB_TABLE_ROLES_DEFINITIONS . '` ' . "\n" .
                     '  (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES ' . "\n" .
                     implode(', ' . "\n", $set) . "\n" .
                     'ON DUPLICATE KEY UPDATE `requires_model` = VALUES(`requires_model`), `position` = VALUES(`position`)';
            $db->Execute($query);
        }

        // INSERT / UPDATE: permissions
        if ($update_permissions) {
            $query = 'INSERT INTO `' . DB_TABLE_ROLES_PERMISSIONS . '` (`parent_id`, `definition_id`, `permission`) ' . "\n" .
                     '  SELECT \'1\', `id`, \'all\' FROM `' . DB_TABLE_ROLES_DEFINITIONS . '` ' . "\n" .
                     '  WHERE `module` = \'' . $module . '\' ' . "\n" .
                     '  AND `controller` = \'' . $controller . '\' ' . "\n" .
                     '  AND `model_type` = \'' . $model_id . '\' ' . "\n" .
                     'ON DUPLICATE KEY UPDATE `permission` = \'all\'';
            $db->Execute($query);
        } else {
            return true;
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Update layouts
     *
     * @param bool $set_permissions - a flag defining whether to insert default permissions to layouts (usually when adding one)
     * @return bool - result of the operation
     */
    public function updateLayouts($set_permissions = true) {
        $db = $this->registry['db'];
        $model_id = $this->get('id');
        $basic_layouts_arr = array(
            // main_data (start from offset 0: place from 1 to 20)
            0 => array(
                'salutation'             => 'customers_salutation',
                'name'                   => 'customers_name',
                'type'                   => 'customers_type',
                'is_company'             => 'customers_company_person',
                'code'                   => 'customers_code',
                'num'                    => 'customers_num',
                'admit_VAT_credit'       => 'customers_admit_VAT_credit',
                'department'             => 'customers_department',
                'assigned'               => 'customers_assigned',
                'company_department'     => 'customers_company_department',
                'position'               => 'customers_position',
            ),
            // contact_data/address_data (start from offset 20: place from 21 to 40)
            PH_CUSTOMERS_LAYOUTS_MAIN_TO => array(
                'country'                => 'customers_country',
                'city'                   => 'customers_city',
                'postal_code'            => 'customers_postal_code',
                'address'                => 'customers_address',
                'notes'                  => 'customers_notes',
                // contacts
                'contacts'               => 'customers_contacts',
            ),
            // personal_data/company_data (start from offset 40: place from 41 to 80)
            PH_CUSTOMERS_LAYOUTS_ADDRESS_TO => array(
                'ucn'                    => 'customers_ucn',
                'identity_num'           => 'customers_identity_num',
                'identity_date'          => 'customers_identity_date',
                'identity_by'            => 'customers_identity_by',
                'identity_valid'         => 'customers_identity_valid',
                'address_by_personal_id' => 'customers_address_by_personal_id',
                'company_name'           => 'customers_company_name',
                'in_dds'                 => 'customers_in_dds',
                'eik'                    => 'customers_eik',
                'registration_file'      => 'customers_registration_file',
                'registration_volume'    => 'customers_registration_volume',
                'registration_number'    => 'customers_registration_number',
                'registration_address'   => 'customers_registration_address',
                'mol'                    => 'customers_mol',
                'bank'                   => 'customers_bank',
                'iban'                   => 'customers_iban',
                'bic'                    => 'customers_bic',
            )
        );
        // layouts that will not be permitted by default
        $not_permitted_layouts_regexp = '^(salutation|is_company|code|num|admit_VAT_credit|department|assigned|company_department|position|notes|identity_by|registration_file|registration_volume|registration_number|bank|iban|bic)$';

        $set      = array();
        foreach ($basic_layouts_arr as $offset => $layout_group) {
            $i = 0;
            foreach ($layout_group as $keyname => $name) {
                $set[] = "(NULL, 'Customer', $model_id, '$keyname', 1, 0, " . (++$i + $offset) . ", 1, now(), 1, now(), 1, '0000-00-00 00:00:00', 0)";
            }
        }

        //insert layouts
        $query = "INSERT IGNORE INTO " . DB_TABLE_LAYOUTS .
                 "(`layout_id`, `model`, `model_type`, `keyname`, `system`, `visible`, `place`, `active`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES " .
                 implode(",\n", $set);
        $db->Execute($query);

        //insert layouts_i18n
        // take the supported langs
        $supported_langs            = $this->registry['config']->getParamAsArray('i18n', 'supported_langs', ',', 'db');
        $current_lang               = $this->registry['lang'];
        $revert_original_lang_files = false;

        // load language file (needed when the method is called from configuration model)
        $lang_file = PH_MODULES_DIR .
            preg_replace('#([^\_]*)_.*#', '$1', strtolower($this->modelName)) .
            '/i18n/' . $current_lang . '/'.
            strtolower(General::singular2plural($this->modelName)) . '.ini';
        if (!$this->registry['translater']->isLoadedFile($lang_file)) {
            $this->registry['translater']->loadFile($lang_file);
        }

        // adds i18n information for the layouts for all the supported langs
        foreach ($supported_langs as $supp_lang) {
            if ($supp_lang != $this->registry['lang'] || $revert_original_lang_files) {
                $this->registry['translater']->reloadFiles($supp_lang);
                $revert_original_lang_files = true;
            }
            foreach ($basic_layouts_arr as $offset => $layout_group) {
                foreach ($layout_group as $keyname => $name) {
                    $query = "INSERT IGNORE INTO " . DB_TABLE_LAYOUTS_I18N . " SELECT layout_id,
                                '". $this->registry['translater']->translate($name) ."', '',
                                '". $supp_lang ."', now()
                                FROM " . DB_TABLE_LAYOUTS . " WHERE `model`='Customer' AND `keyname`='$keyname'
                                AND `system`=1 AND `model_type`='$model_id'";
                    $db->Execute($query);
                }
            }
        }

        // if the lang has been changed during the previous operation it is reverted to the currently used one
        if ($revert_original_lang_files) {
            $this->registry['translater']->reloadFiles($current_lang);
        }

        if ($set_permissions) {
            //insert permissions for group 'all'
            $query = "INSERT IGNORE INTO " . DB_TABLE_LAYOUTS_PERMISSIONS . " (`parent_id`, `action_type`, `group_id`)
                        SELECT layout_id, 'view', 1 FROM " . DB_TABLE_LAYOUTS . " WHERE `model`='Customer' AND
                        `system`=1 AND `model_type`='$model_id' AND `keyname` NOT RLIKE '$not_permitted_layouts_regexp'";
            $db->Execute($query);

            $query = "INSERT IGNORE INTO " . DB_TABLE_LAYOUTS_PERMISSIONS . " (`parent_id`, `action_type`, `group_id`)
                        SELECT layout_id, 'edit', 1 FROM " . DB_TABLE_LAYOUTS . " WHERE `model`='Customer' AND
                        `system`=1 AND `model_type`='$model_id' AND `keyname` NOT RLIKE '$not_permitted_layouts_regexp'";
            $db->Execute($query);
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Saves settings for additional required fields and unique fields for type
     *
     * @return bool - result of the operation
     */
    public function updateAdditionalValidateFields() {
        $db = $this->registry['db'];

        $update = array();
        $value = $this->isDefined('validate') ? $this->get('validate') : array();
        if ($this->isDefined('validate_unique')) {
            $value = array_merge($value, $this->get('validate_unique'));
        }
        if ($this->get('validate_unique_current_year')) {
            $value[] = 'current_year';
        }
        $update['value'] = sprintf("value='%s'", implode(', ', $value));

        $insert = $update;
        $insert['section'] = sprintf("section='customers'");
        $insert['name']    = sprintf("name='validate_%d'", $this->get('id'));

        $query = 'INSERT INTO ' . DB_TABLE_SETTINGS . "\n" .
                 'SET ' . implode(', ', $insert) . "\n" .
                 'ON DUPLICATE KEY UPDATE ' . "\n" .
                 implode(', ', $update);

        $db->Execute($query);

        return !$db->HasFailedTrans();
    }

    /**
     * Gets default group of the customer type.
     * The default group could be set as default group of the current user
     *
     * @return int - the id of the default group
     */
    public function getDefaultGroup() {
        if (isset($this->registry)) {
            $registry = $this->registry;
        } else {
            $registry = $GLOBALS['registry'];
        }

        $group_id = $this->get('default_group');
        if ($group_id == '[default_user_group]') {
            //get the default group of the user
            if ($registry->isRegistered('originalUser')) {
                //check if the process of automation is started
                //in this case the current user is saved in registry's originalUser
                $group_id = $registry['originalUser']->get('default_group');
            } else {
                //get the default group id from the current user
                $group_id = $registry['currentUser']->get('default_group');
            }
        }
        $group_id = sprintf('%d', $group_id);

        return $group_id;
    }

    /**
     * Checks model translations
     *
     * @return string[] - array of available languages
     */
    public function getTranslations() {
        if (!$this->get('id')) {
            return array();
        }

        if ($this->isDefined('translations')) {
            return $this->get('translations');
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $sql = array();
        //select clause
        $sql['select'] = 'SELECT cti18n.lang ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n";

        //where clause
        $sql['where'] = 'WHERE cti18n.parent_id=' . $this->get('id') . "\n";

        $sql['order'] = 'ORDER BY cti18n.translated' . "\n";

        $query = implode("\n", $sql);

        $records = $this->registry['db']->GetCol($query);

        if ($records) {
            $this->set('translations', $records, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Updates data used for autocomplete relations
     * between customers types and other models types
     *
     */
    public function updateTypesRelations() {

        $related_models = array('Document', 'Project', 'Contract', 'Event', 'Task', 'Finance', 'Minitask');

        //prepare inserts
        $insert = array();
        foreach ($related_models as $model) {
            if ($model == 'Finance') {
                // get financial (documents + payments) types
                $types = array();
                if ($this->get('related_finance_documents_types')) {
                    $types = array_merge($types, $this->get('related_finance_documents_types'));
                }
                if ($this->get('related_finance_payments_types')) {
                    $types = array_merge($types, $this->get('related_finance_payments_types'));
                }
            } else {
                $types = $this->get('related_' . General::singular2plural(strtolower($model)) . '_types');
            }
            if (empty($types)) {
                continue;
            }
            if (!is_array($types)) {
                $types = array($types);
            }
            foreach ($types as $type) {
                $insert[] = '("Customer", ' . $this->get('id') . ', "autocompleter", "' . $model . '", "' . $type . '")';
            }
        }

        //delete old records from the table
        $query = "DELETE FROM " . DB_TABLE_TYPES_RELATIONS . " \n" .
                 'WHERE relation="autocompleter"' . "\n" .
                 '  AND model="Customer"' . "\n" .
                 '  AND model_type = "' . $this->get('id') . '"';
        $this->registry['db']->Execute($query);

        if (!empty($insert)) {
            $query = 'INSERT INTO ' . DB_TABLE_TYPES_RELATIONS .
                     ' (model, model_type, relation, relate_to_model, relate_to_model_type) VALUES' . "\n" .
                     implode(",\n", $insert);
            $this->registry['db']->Execute($query);
        }
    }

    /**
     * Gets data used for autocomplete relations
     * between customers types and other models types
     * and sets it as properties of model
     */
    public function getTypesRelations() {

        if ($this->get('id')) {
            //get records from the table
            $query = "SELECT relate_to_model, relate_to_model_type FROM " . DB_TABLE_TYPES_RELATIONS . " \n" .
                     'WHERE relation="autocompleter"' . "\n" .
                     '  AND model="Customer"' . "\n" .
                     '  AND model_type = "' . $this->get('id') . '"';
            $records = $this->registry['db']->GetAll($query);
        } else {
            $records = array();
        }

        $relations = array();
        foreach ($records as $record) {
            $key = 'related_' .
                    (preg_match('#^finance$#i', $record['relate_to_model']) ?
                    strtolower($record['relate_to_model']) . '_' .
                        (intval($record['relate_to_model_type']) > 0 ? 'documents' : 'payments') :
                    General::singular2plural(strtolower($record['relate_to_model']))) .
                    '_types';
            if ($record['relate_to_model'] == 'Minitask') {
                $relations[$key] = $record['relate_to_model_type'];
            } else {
                $relations[$key][] = $record['relate_to_model_type'];
            }
        }

        foreach ($relations as $key => $record) {
            $this->set($key, $record, true);
        }
    }

    /**
     * Checks if image is selected and uploads it to the server
     *
     * @return bool - if the action was successful or not
     */
    public function imageCreate() {
        if (! empty($_FILES['branch_icon_file']['name'])) {
            $tmp_file_name = $_FILES['branch_icon_file']['tmp_name'];
            $file_info = getimagesize($tmp_file_name);
            $file_extension = FilesLib::getImageType($file_info[2]);
            if ($file_extension) {
                if (! is_dir(PH_CUSTOMERS_TYPES_DIR)) {
                    FilesLib::createDir(PH_CUSTOMERS_TYPES_DIR, 0777, true);
                }
                $filename = 'branch_type_' . $this->get('id') . '.' . $file_extension;
                $restrictions = array('max_width' => $this->width, 'max_height' => $this->height);
                $result_upload = FilesLib::uploadFile($_FILES['branch_icon_file'], PH_CUSTOMERS_TYPES_DIR, $filename, $restrictions);
                if ($result_upload) {
                    $result = $this->updateTableIcon($this->get('id'), $file_extension);
                    chmod(PH_CUSTOMERS_TYPES_DIR . $filename, 0777);
                    if ($result) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * Updates customers_types table when the branch icon is uploaded
     *
     * @param int id - the id of the type to be updated
     * @param string $file_extension - the file extension of the uploaded file
     * @return bool - result of the operation
     */
    public function updateTableIcon($id, $file_extension = '', $delete_file = '') {
        if ($delete_file) {
            $file_name = '';
        } else {
            $file_name = 'branch_type_' . $id . '.' . $file_extension;
        }

        $db = $this->registry['db'];
        $db->StartTrans();
        $query = 'UPDATE ' . DB_TABLE_CUSTOMERS_TYPES . "\n" .
                 ' SET branch_icon_file="' . $file_name . '"' . "\n" .
                 ' WHERE id="' . $id . '"';
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        if ($result && $delete_file) {
            $icon_file_delete = PH_CUSTOMERS_TYPES_DIR . $delete_file;
            unlink($icon_file_delete);
        }

        return $result;
    }
}

?>
