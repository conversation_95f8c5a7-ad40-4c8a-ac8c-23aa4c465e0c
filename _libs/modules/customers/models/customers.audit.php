<?php

class Customers_Audit extends Audit {
    //module name
    public static $module = 'customers';

    /**
     * prepare audit data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, &$params) {

        //replace ids with labels for basic variables
        $replace_labels = array('customer'      => 'customer_name',
                                'project'       => 'project_name',
                                'department'    => 'department_name',
                                'assigned'      => 'assigned_to_name',
                                'media'         => 'media_name',
                                'office'        => 'office_name',
                                'employee'      => 'employee_name',
                                'group'         => 'group_name',
                                'trademark'     => 'trademark_name'
        );
        $basic_vars = parent::getBasicAuditVars($registry, self::$module);

        $audit_vars = array();

        switch ($params['action_type']) {
        case 'add':
        case 'translate':
        case 'edit':
        case 'multiadd':
        case 'multiedit':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                $is_add_action = in_array($params['action_type'], array('add', 'multiadd'));
                foreach ($basic_vars as $var) {
                    if ($is_add_action && $params['model']->get($var) || !$is_add_action && $params['model']->get($var) != $params['old_model']->get($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        $audit_vars[$i]['field_value'] = $params['model']->get($var);
                        $audit_vars[$i]['old_value'] = '';
                        if (isset($replace_labels[$var])) {
                            $audit_vars[$i]['label'] = $params['model']->get($replace_labels[$var]);
                            if (!$is_add_action) {
                                $audit_vars[$i]['old_value'] = $params['old_model']->get($replace_labels[$var]);
                            }
                        } else {
                            switch ($var) {
                                case 'active':
                                    $audit_vars[$i]['label'] = ($params['model']->get('active') ? ($registry['translater']->translate('activated')) : $registry['translater']->translate('deactivated'));
                                    if (!$is_add_action) {
                                        $audit_vars[$i]['old_value'] = ($params['old_model']->get('active') ? ($registry['translater']->translate('activated')) : $registry['translater']->translate('deactivated'));
                                    }
                                    break;
                                default:
                                    $audit_vars[$i]['label'] = $params['model']->get($var);
                                    if (!$is_add_action) {
                                        $audit_vars[$i]['old_value'] = $params['old_model']->get($var);
                                    }
                                    break;
                            }
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }
            // no break here, processing continues
        case 'managevars':
        case self::BB_AUDIT_ACTION:
            $i = count($audit_vars);
            $vars = $params['model']->get('vars') ? $params['model']->getAssocVars() : array();
            $old_vars = $params['old_model']->get('vars') ? $params['old_model']->getAssocVars() : array();
            if ($vars) {
                foreach ($vars as $var_name => $var) {
                    if ($var['auditable'] && !in_array($var['type'], self::$no_audit_var_types)) {
                        $add_audit = false;
                        $old_not_exist = true;
                        $old_value = '';

                        if (!empty($old_vars[$var_name])) {
                            $same_object = false;
                            $old_not_exist = false;
                            $old_var = $old_vars[$var_name];
                            //objects - compare ids
                            if (is_object($var['value']) && is_object($old_var['value']) &&
                                $var['value']->get('id') == $old_var['value']->get('id')) {
                                $same_object = true;
                            } elseif (is_array($var['value']) || is_array($old_var['value'])) {
                                if (!is_array($var['value'])) {
                                    $var['value'] = array();
                                }
                                if (!is_array($old_var['value'])) {
                                    $old_var['value'] = array();
                                }
                                //array of objects - get only ids
                                $tmp = array_filter($var['value']);
                                $tmp = reset($tmp);
                                if (!is_object($tmp)) {
                                    $tmp = array_filter($old_var['value']);
                                    $tmp = reset($tmp);
                                }

                                if (is_object($tmp)) {
                                    $tmp = array();
                                    foreach ($var['value'] as $obj) {
                                        $tmp[] = is_object($obj) ? $obj->get('id') : $obj;
                                    }
                                    $var['value'] = $tmp;
                                    $tmp = array();
                                    foreach ($old_var['value'] as $obj) {
                                        $tmp[] = is_object($obj) ? $obj->get('id') : $obj;
                                    }
                                    $old_var['value'] = $tmp;
                                }
                                unset($tmp);

                                $diff = array_diff($var['value'], $old_var['value']);
                                $diff2 = array_diff($old_var['value'], $var['value']);
                                $diff = array_unique(array_merge($diff, $diff2));
                            }
                            if ((!is_array($var['value']) && $var['value'] !== $old_var['value'] && !($var['value'] === '' && is_null($old_var['value'])) && empty($same_object)) ||
                            (is_array($var['value']) && !empty($diff))) {
                                $add_audit = true;
                                $old_value = $old_var['value'];
                            }
                        }

                        if ($add_audit || $old_not_exist) {
                            if ($params['model']->get('bb_id') && $params['action_type'] == self::BB_AUDIT_ACTION && !isset($bb_delimiter_added)) {
                                // add audit of non-existent variable to display start of a variant
                                $bb_delimiter_added = true;
                                $audit_vars[$i]['field_name'] = self::BB_DELIMITER_VAR;
                                $audit_vars[$i]['field_value'] = $params['model']->get('bb_id');
                                $audit_vars[$i]['label'] = '';
                                $audit_vars[$i]['old_value'] = '';
                                $audit_vars[$i]['var_type'] = PH_VAR_ADDITIONAL;
                                $i++;
                            }
                            $audit_vars[$i]['field_name'] = $var['name'];
                            if (is_array($var['value']) || is_array($old_value)) {
                                $audit_vars[$i]['field_value'] = serialize($var['value']);
                                $audit_vars[$i]['is_array'] = 1;
                                if (!empty($var['options'])) {
                                    foreach ($var['options'] as $opt) {
                                        if (is_array($var['value'])) {
                                            foreach ($var['value'] as $k => $val) {
                                                if ($opt['option_value'] == $val) {
                                                    $var['value'][$k] = $opt['label'];
                                                }
                                            }
                                        }
                                        if (is_array($old_value)) {
                                            foreach ($old_value as $k => $val) {
                                                if ($opt['option_value'] == $val) {
                                                    $old_value[$k] = $opt['label'];
                                                }
                                            }
                                        }
                                    }
                                }
                                $audit_vars[$i]['label'] = is_array($var['value']) ? implode("\n", $var['value']) : '';
                                $audit_vars[$i]['old_value'] = is_array($old_value) ? implode("\n", $old_value) : '';
                            } else {
                                $audit_vars[$i]['field_value'] = $var['value'];
                                if (!empty($var['options'])) {
                                    foreach ($var['options'] as $opt) {
                                        if ($opt['option_value'] == $var['value']) {
                                            $audit_vars[$i]['label'] = $opt['label'];
                                        }
                                        if ($opt['option_value'] == $old_value) {
                                            $audit_vars[$i]['old_value'] = $opt['label'];
                                        }
                                    }
                                } else {
                                    $audit_vars[$i]['label'] = $var['value'];
                                    $audit_vars[$i]['old_value'] = $old_value;
                                }
                            }
                            $audit_vars[$i]['var_type'] = PH_VAR_ADDITIONAL;
                            $i++;
                        }
                    }
                }
            }
            break;
        case 'tag':
        case 'multitag':
            $i = count($audit_vars);

            if (in_array('tag', $basic_vars)) {
                $diff = array_diff($params['old_model']->get('tags'), $params['model']->get('tags'));
                $diff2 = array_diff($params['model']->get('tags'), $params['old_model']->get('tags'));
                if (!empty($diff) || !empty($diff2)) {
                    $new_var_value =
                        $params['model']->get('tag_names_for_audit') ?
                        implode("\n", $params['model']->get('tag_names_for_audit')) :
                        '';
                    $old_var_value =
                        $params['old_model']->get('tag_names_for_audit') ?
                        implode("\n", $params['old_model']->get('tag_names_for_audit')) :
                        '';
                    $audit_vars[$i]['field_name'] = 'tags';
                    $audit_vars[$i]['field_value'] = serialize($params['model']->get('tags'));
                    $audit_vars[$i]['label'] = $new_var_value;
                    $audit_vars[$i]['old_value'] = $old_var_value;
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = 1;
                }
            }
            break;
        case 'add_attachments':
            $i = count($audit_vars);

            $old_model_attachments = array();
            $new_model_attachments = array();
            foreach ($params['old_model']->get('attachments') as $file) {
                $old_model_attachments[] = $file->get('id');
            }
            foreach ($params['model']->get('attachments') as $file) {
                $new_model_attachments[] = $file->get('id');
            }
            $new_attachments_ids = array_diff($new_model_attachments, $old_model_attachments);

            if (!empty($new_attachments_ids)) {
                $new_files = array();
                foreach ($params['model']->get('attachments') as $file) {
                    if (in_array($file->get('id'), $new_attachments_ids)) {
                        $new_files[] = $file;
                    }
                }
                $new_var_value = '';

                foreach ($new_files as $new_file) {
                    $new_var_value .= $new_file->get('name') . ' (' . $new_file->get('revision') . ')' . "\n";
                }

                $audit_vars[$i]['field_name'] = 'added_attachments';
                $audit_vars[$i]['field_value'] = serialize($new_attachments_ids);
                $audit_vars[$i]['label'] = $new_var_value;
                $audit_vars[$i]['old_value'] = '';
                $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                $audit_vars[$i]['is_array'] = 1;
                $i++;
            }
            break;
        case 'del_attachments':
            $i = count($audit_vars);
            $old_model_attachments = array();
            $new_model_attachments = array();
            foreach ($params['old_model']->get('attachments') as $file) {
                $old_model_attachments[] = $file->get('id');
            }
            foreach ($params['model']->get('attachments') as $file) {
                $new_model_attachments[] = $file->get('id');
            }
            $deleted_ids = array_diff($old_model_attachments, $new_model_attachments);
            if (!empty($deleted_ids)) {
                $deleted_files = array();

                foreach ($params['old_model']->get('attachments') as $file) {
                    if (in_array($file->get('id'), $deleted_ids)) {
                        $deleted_files[] = $file;
                    }
                }

                $new_var_value = '';

                foreach ($deleted_files as $deleted_file) {
                    $new_var_value .= $deleted_file->get('name') . ' (' . $deleted_file->get('revision') . ')' . "\n";
                }

                $audit_vars[$i]['field_name'] = 'deleted_attachments';
                $audit_vars[$i]['field_value'] = serialize($deleted_ids);
                $audit_vars[$i]['label'] = $new_var_value;
                $audit_vars[$i]['old_value'] = '';
                $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                $audit_vars[$i]['is_array'] = 1;
                $i++;
            }

            break;
        case 'receive_email':
        case 'email':
            $basic_vars = array('mail_from', 'mail_code', 'mail_to', 'mail_cc', 'mail_bcc', 'mail_subject', 'mail_content', 'attached_files');
            $basic_vars_array = array('mail_to', 'mail_cc', 'mail_bcc', 'attached_files');
            $i = count($audit_vars);

            foreach ($basic_vars as $var) {
                $field_value = '';
                if (in_array($var, $basic_vars_array)) {
                    $mails = $params['model']->get($var) ?: array();
                    $mail_names = $params['model']->get("{$var}_name") ?: array();
                    $field_value = array();
                    foreach ($mails as $key => $value) {
                        $field_value[] = !empty($mail_names[$key]) ? $mail_names[$key] . ' (' . $value . ')' : $value;
                    }
                    $field_value = $field_value ? serialize($field_value) : '';
                } else {
                    switch ($var) {
                        case 'mail_subject':
                            $prop = 'email_subject';
                            break;
                        case 'mail_content':
                            $prop = 'body_formated';
                            break;
                        default:
                            $prop = $var;
                            break;
                    }
                    $field_value = $params['model']->get($prop) ?: '';
                }
                if ($field_value) {
                    $audit_vars[$i]['field_value'] = $field_value;
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = in_array($var, $basic_vars_array);
                    $audit_vars[$i]['label'] = '';
                    $i++;
                }
            }
            break;
        case 'add_comment':
        case 'edit_comment':
            $basic_vars = array('subject', 'content', 'is_portal');
            $comment = $params['model']->get('comment');
            $old_comment = $params['old_model']->get('comment');
            $i = count($audit_vars);
            $is_add_action = $params['action_type'] == 'add_comment';

            foreach ($basic_vars as $var) {
                if ($is_add_action && $comment->get($var) || !$is_add_action && $old_comment->get($var) != $comment->get($var) || $var == 'is_portal' && $comment->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $comment->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $comment->get($replace_labels[$var]);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($replace_labels[$var]);
                        }
                    } else {
                        $audit_vars[$i]['label'] = $comment->get($var);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($var);
                        }
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'transfer':
            if ($params['model']->get('type') != $params['old_model']->get('type')) {
                $audit_vars[0]['field_name'] = 'type';
                $audit_vars[0]['field_value'] = $params['model']->get('type');
                $audit_vars[0]['label'] = $params['model']->get('type_name');
                $audit_vars[0]['old_value'] = $params['old_model']->get('type_name');
                $audit_vars[0]['var_type'] = PH_VAR_BASIC;
            }
            break;
        case 'create_document':
            if ($params['model']->get('created_document_num')) {
                $audit_vars[0]['field_name'] = 'created_document_full_num';
                $audit_vars[0]['field_value'] = $params['model']->get('created_document_num');
                $audit_vars[0]['label'] = $params['model']->get('created_document_num');
                $audit_vars[0]['old_value'] = '';
                $audit_vars[0]['var_type'] = PH_VAR_BASIC;
            }
            if ($params['model']->get('created_document_name')) {
                $audit_vars[1]['field_name'] = 'created_document_name';
                $audit_vars[1]['field_value'] = $params['model']->get('created_document_name');
                $audit_vars[1]['label'] = $params['model']->get('created_document_name');
                $audit_vars[1]['old_value'] = '';
                $audit_vars[1]['var_type'] = PH_VAR_BASIC;
            }
            break;
        case 'create_task':
            if ($params['model']->get('created_task_num')) {
                $audit_vars[0]['field_name'] = 'created_task_full_num';
                $audit_vars[0]['field_value'] = $params['model']->get('created_task_num');
                $audit_vars[0]['label'] = $params['model']->get('created_task_num');
                $audit_vars[0]['old_value'] = '';
                $audit_vars[0]['var_type'] = PH_VAR_BASIC;
            }
            if ($params['model']->get('created_task_name')) {
                $audit_vars[1]['field_name'] = 'created_task_name';
                $audit_vars[1]['field_value'] = $params['model']->get('created_task_name');
                $audit_vars[1]['label'] = $params['model']->get('created_task_name');
                $audit_vars[1]['old_value'] = '';
                $audit_vars[1]['var_type'] = PH_VAR_BASIC;
            }
            break;
        case 'create_event':
            if ($params['model']->get('created_event_name')) {
                $audit_vars[0]['field_name'] = 'created_event_name';
                $audit_vars[0]['field_value'] = $params['model']->get('created_event_name');
                $audit_vars[0]['label'] = $params['model']->get('created_event_name');
                $audit_vars[0]['old_value'] = '';
                $audit_vars[0]['var_type'] = PH_VAR_BASIC;
            }
            break;
        case 'create_project':
            if ($params['model']->get('created_project_code')) {
                $audit_vars[0]['field_name'] = 'created_project_code';
                $audit_vars[0]['field_value'] = $params['model']->get('created_project_code');
                $audit_vars[0]['label'] = $params['model']->get('created_project_code');
                $audit_vars[0]['old_value'] = '';
                $audit_vars[0]['var_type'] = PH_VAR_BASIC;
            }
            if ($params['model']->get('created_project_name')) {
                $audit_vars[1]['field_name'] = 'created_project_name';
                $audit_vars[1]['field_value'] = $params['model']->get('created_project_name');
                $audit_vars[1]['label'] = $params['model']->get('created_project_name');
                $audit_vars[1]['old_value'] = '';
                $audit_vars[1]['var_type'] = PH_VAR_BASIC;
            }
            break;
        case 'add_branch':
        case 'edit_branch':
        case 'translate_branch':
            $auditable_branch_vars = array('name', 'city', 'postal_code', 'address');
            foreach ($auditable_branch_vars as $key => $var) {
                if ($params['model']->get($var) != $params['old_model']->get($var)) {
                    $audit_vars[] = array(
                        'field_name'    => 'branch_' . $var,
                        'field_value'   => $params['model']->get($var),
                        'label'         => $params['model']->get($var),
                        'old_value'     => ($params['old_model']->get($var) ? $params['old_model']->get($var) : ''),
                        'var_type'      => PH_VAR_BASIC
                    );
                }
            }
            break;
        case 'add_contact_person':
        case 'edit_contact_person':
        case 'translate_contact_person':
            $auditable_contact_person_vars = array('name', 'lastname', 'is_main', 'financial_person', 'position', 'parent_customer', 'assigned', 'permission');
            foreach ($auditable_contact_person_vars as $key => $var) {
                if ($params['model']->get($var) != $params['old_model']->get($var)) {
                    $var_label = '';
                    $old_var_label = '';
                    switch ($var) {
                        case 'is_main':
                        case 'financial_person':
                            if ($params['old_model']->isDefined($var)) {
                                $old_var_label = ($params['old_model']->get($var) ? $registry['translater']->translate('yes') : $registry['translater']->translate('no'));
                            } else {
                                $old_var_label = '';
                            }
                            $var_label = ($params['model']->get($var) ? $registry['translater']->translate('yes') : $registry['translater']->translate('no'));
                            break;
                        case 'parent_customer':
                            $var_label = $params['model']->get('branch_name');
                            $old_var_label = $params['old_model']->get('branch_name');
                            break;
                        case 'permission':
                            $var_label = $registry['translater']->translate('customers_contact_persons_permission_' . $params['model']->get('permission'));
                            $old_var_label = ($params['old_model']->get($var) ? $registry['translater']->translate('customers_contact_persons_permission_' . $params['old_model']->get('permission')) : '');
                            break;
                        case 'assigned':
                            $var_label = $params['model']->get('assigned_to_name');
                            $old_var_label = $params['old_model']->get('assigned_to_name');
                            break;
                        default:
                            $var_label = $params['model']->get($var);
                            $old_var_label = ($params['old_model']->get($var) ? $params['old_model']->get($var) : '');
                            break;
                    }
                    $audit_vars[] = array(
                        'field_name'    => 'contact_person_' . $var,
                        'field_value'   => $params['model']->get($var),
                        'label'         => $var_label,
                        'old_value'     => $old_var_label,
                        'var_type'      => PH_VAR_BASIC
                    );
                }
            }
            break;
        case 'add_minitask':
            $auditable_minitask_vars = array('customer', 'description', 'deadline', 'assigned_to', 'severity');
            $replace_labels['assigned_to'] = 'assigned_to_name';
            $replace_labels['severity'] = 'severity_name';
            $minitask = $params['model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($minitask->isDefined($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['label'] = isset($replace_labels[$var]) ?
                                               $minitask->get($replace_labels[$var]) :
                                               $minitask->get($var);
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'edit_minitask':
            $auditable_minitask_vars = array('customer', 'description', 'deadline', 'assigned_to', 'severity');
            $replace_labels['assigned_to'] = 'assigned_to_name';
            $replace_labels['severity'] = 'severity_name';
            $minitask = $params['model']->get('minitask');
            $old_minitask = $params['old_model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($minitask->get($var) != $old_minitask->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $minitask->get($replace_labels[$var]);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($replace_labels[$var]);
                    } else {
                        $audit_vars[$i]['label'] = $minitask->get($var);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($var);
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'status_minitask':
        case 'multistatus_minitask':
            $auditable_minitask_vars = array('status', 'comment');
            $minitask = $params['model']->get('minitask');
            $old_minitask = $params['old_model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($old_minitask->get($var) != $minitask->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    if ($var == 'status') {
                        $audit_vars[$i]['label'] = ($minitask->get('status') ? ($registry['translater']->translate('minitasks_status_' . $minitask->get('status'))) : ('-'));
                        $audit_vars[$i]['old_value'] = ($old_minitask->get('status') ? ($registry['translater']->translate('minitasks_status_' . $old_minitask->get('status'))) : ('-'));
                    } else {
                        $audit_vars[$i]['label'] = $minitask->get($var);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($var);
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'trademarks':
            $old_trademarks = $params['old_model']->get('trademarks') ?: array();
            $new_trademarks = $params['model']->get('trademarks') ?: array();

            $diff = false;
            foreach ($new_trademarks as $tm) {
                $tm_found = array_filter(
                    $old_trademarks,
                    function($a) use ($tm) {
                        return $tm['id'] == $a['id'] && $tm['is_default'] == $a['is_default'];
                    }
                );
                if (!$tm_found) {
                    $diff = true;
                    break;
                }
            }
            if (!$diff) {
                foreach ($old_trademarks as $tm) {
                    $tm_found = array_filter(
                        $new_trademarks,
                        function($a) use ($tm) {
                            return $tm['id'] == $a['id'] && $tm['is_default'] == $a['is_default'];
                        }
                    );
                    if (!$tm_found) {
                        $diff = true;
                        break;
                    }
                }
            }

            if ($diff) {
                $new_var_value = '';
                $new_field_value = array();
                foreach ($new_trademarks as $tm) {
                    $new_var_value .= sprintf('[%s] %s%s',
                        $tm['code'], $tm['name'],
                        ($tm['is_default'] ? ' (' . $registry['translater']->translate('main_trademark') . ')' : '')) . "\n";
                    $new_field_value[] = $tm['id'];
                }

                $old_var_value = '';
                foreach ($old_trademarks as $tm) {
                    $old_var_value .= sprintf('[%s] %s%s',
                        $tm['code'], $tm['name'],
                        ($tm['is_default'] ? ' (' . $registry['translater']->translate('main_trademark') . ')' : '')) . "\n";
                }

                $audit_vars[0]['field_name'] = 'trademarks';
                $audit_vars[0]['field_value'] = serialize($new_field_value);
                $audit_vars[0]['label'] = $new_var_value;
                $audit_vars[0]['old_value'] = $old_var_value;
                $audit_vars[0]['var_type'] = PH_VAR_BASIC;
                $audit_vars[0]['is_array'] = 1;
            }
            break;
        }

        $params['data'] = $audit_vars;
        return true;
    }

    /**
     * prepare audit data for view
     *
     * @return bool
     */
    public static function prepareGetData($records, $params) {
        foreach ($records as $k => $rec) {
            if ($rec['is_array']) {
                $arr = unserialize($rec['field_value']);
                if (empty($arr)) {
                    $arr = array();
                }
                if (is_array($arr)) {
                    if (count($arr) == count($arr, COUNT_RECURSIVE)) {
                        //array is NOT multidimensional, it is safe to implode it into string
                        $records[$k]['field_value'] = implode("\n", $arr);
                    } else {
                        //array is multidimensional, usually wrong assignments array is saved
                        //it should not be displayed
                        unset($records[$k]);
                        continue;
                    }
                }

                if (!empty($rec['old_value']) &&
                preg_match("/(a|O|s|b)\x3a[0-9]*?((\x3a((\x7b?(.+)\x7d)|(\x22(.+)\x22\x3b)))|(\x3b))/", $rec['old_value'])) {
                    $arr = unserialize($rec['old_value']);
                    if (empty($arr)) {
                        $arr = array();
                    }
                    if (is_array($arr)) {
                        if (count($arr) == count($arr, COUNT_RECURSIVE)) {
                            //array is NOT multidimensional, it is safe to implode it into string
                            $records[$k]['old_value'] = implode("\n", $arr);
                        } else {
                            //array is multidimensional, usually wrong assignments array is saved
                            //it should not be displayed
                            unset($records[$k]);
                            continue;
                        }
                    }
                }

                if (empty($records[$k]['field_value']) && empty($records[$k]['old_value'])) {
                    unset($records[$k]);
                }
            }
        }

        return $records;
    }

    /**
     * save model audit
     *
     * @return bool
     */
    public static function saveData(&$registry, $params) {
        self::prepareData($registry, $params);
        parent::saveData($registry, $params);

        return true;
    }

    /**
     * get model audit
     *
     * @return array data
     */
    public static function getData(&$registry, $params) {
        if (!empty($params['action_type']) && in_array($params['action_type'], array('add', 'edit', 'transfer'))) {
            $params['get_merged_bb_audit'] = true;
        }
        $records = parent::getData($registry, $params);
        $records = self::prepareGetData($records, $params);

        return array('vars' => $records);
    }
}

?>
