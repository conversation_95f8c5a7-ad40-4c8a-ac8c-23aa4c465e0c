<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$company->get('model_lang')|default:$lang}" />
<input type="hidden" name="id" id="id" value="{$company->get('id')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='companies_name'}</td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            {$company->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <table id="offices_container" cellspacing="0" cellpadding="2" border="0">
              <tr>
                <td class="t_caption t_border t_border_left" style="width:50px">
                  <div class="t_caption_title">{#num#}</div>
                </td>
                 <td class="t_caption t_border" style="width:200px">
                  <div class="t_caption_title floatl">{#office#}</div>
                  <div class="t_buttons">
                    <div id="offices_container_plusButton" onclick="addField('offices_container');" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                    <div id="offices_container_minusButton"{if empty($company_offices) || count($company_offices) le 1} class="disabled"{/if} onclick="removeField('offices_container')" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
                  </div>
                </td>
              </tr>
              {foreach from=$company_offices key='j' item='office_id' name='i'} 
              <tr id="offices_container_{$smarty.foreach.i.iteration}">
                <td class="t_border t_v_border t_border_left" style="text-align: center;">
                  <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if empty($company_offices) || count($company_offices) le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('offices_container','{$smarty.foreach.i.iteration}'); {rdelim}, this);" />
                  <a href="javascript: disableField('offices_container','{$smarty.foreach.i.iteration}');">{$smarty.foreach.i.iteration}</a>
                </td>
                <td nowrap="nowrap" class="t_border t_v_border">
                  {include file="input_dropdown.html" var=$offices
                      standalone=true
                      index=$smarty.foreach.i.iteration
                      var_id=$offices.id
                      name=$offices.name
                      custom_id=$offices.custom_id
                      label=$offices.label
                      help=$offices.help
                      value=$office_id
                      options=$offices.options
                      optgroups=$offices.optgroups
                      option_value=$offices.option_value
                      onclick=$offices.onclick
                      check=$offices.check
                      scrollable=$offices.scrollable
                      calculate=$offices.calculate
                      readonly=$offices.readonly
                      required=$offices.required
                      hidden=$offices.hidden
                      disabled=$offices.disabled
                      origin='group'}
                </td>
              </tr>
              {/foreach}
            </table>
          </td>
        </tr>

        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
