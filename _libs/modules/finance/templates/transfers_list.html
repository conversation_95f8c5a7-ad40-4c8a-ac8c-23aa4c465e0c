<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=transfers&amp;{$action_param}={$action}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_transfer" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=transfers" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall" width="15">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.num.link}">{#finance_transfers_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_transfers_from_company_data#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#finance_transfers_to_company_data#|escape}</div></td>
          <td class="t_caption t_border {$sort.amount.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.amount.link}">{#finance_transfers_amount#|escape}</div></td>
          <td class="t_caption t_border {$sort.currency.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.currency.link}">{#finance_transfers_currency#|escape}</div></td>
          <td class="t_caption t_border {$sort.transfer_date.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.transfer_date.link}">{#finance_transfers_transfer_date#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$finance_transfers item='transfer'}
      {strip}
      {capture assign='info'}
        <strong>{#finance_transfers_name#|escape}:</strong> {$transfer->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$transfer->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$transfer->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$transfer->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$transfer->get('modified_by_name')|escape}<br />

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$transfer->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $transfer->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$transfer->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($transfer->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($transfer->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $transfer->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=transfers&amp;transfers=attachments&amp;attachments={$transfer->get('id')}">
                <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$transfer->get('id')})"
                     onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.num.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=transfers&amp;{$action_param}=view&amp;view={$transfer->get('id')}">{if $transfer->get('num')}{$transfer->get('num')|escape}{else}<i>{#finance_transfers_unfinished_transfer#|escape}</i>{/if}</a></td>
          <td class="t_border" nowrap="nowrap">
            {capture assign='from_container_type'}{if $transfer->get('payment_type') eq 'bank'}bank_accounts{else}cashboxes{/if}{/capture}
            <span class="strong">{$transfer->get('from_company_name')|escape}</span> {$transfer->get('from_office_name')|escape} [{$transfer->get('from_container_name')|escape}]
          </td>
          <td class="t_border" nowrap="nowrap">
            <span class="strong">{$transfer->get('to_company_name')|escape}</span> {$transfer->get('to_office_name')|escape} [{$transfer->get('to_container_name')|escape}]
          </td>
          <td class="t_border hright {$sort.amount.isSorted}" nowrap="nowrap">{$transfer->get('amount')|escape}</td>
          <td class="t_border {$sort.currency.isSorted}" nowrap="nowrap">{$transfer->get('currency')|escape}</td>
          <td class="t_border {$sort.transfer_date.isSorted}" nowrap="nowrap">{$transfer->get('transfer_date')|date_format:#date_short#}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$transfer exclude='delete'}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="9">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="9"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html
               tags=$tags
               include='tags'
               exclude='delete,restore,multiedit,activate,deactivate'
               session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
