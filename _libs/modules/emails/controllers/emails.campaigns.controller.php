<?php

require_once PH_MODULES_DIR . 'emails/models/emails.campaigns.history.php';
require_once PH_MODULES_DIR . 'emails/models/emails.campaigns.audit.php';

class Emails_Campaigns_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Emails_Campaign';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Emails_Campaigns';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit',
        'attachments', 'setstatus', 'clone', 'statistics', 'history'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'history', 'statistics'
    );

    /**
     * Action definitions for the upper right menu
     */
    public $actionDefinitionsUpRight = array();

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate', 'attachments'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'setstatus':
            $this->_status();
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'search':
            $this->_search();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'clone':
            $this->_clone();
            break;
        case 'history':
            $this->_history();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'statistics':
            $this->_statistics();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'ajax_get_targetlist_recipients':
            $this->_getTargetlistRecipients();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
            break;
        }
    }

    /**
     * Listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * adds new model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //build the model from request
        $emails_campaign = Emails_Campaigns::buildModel($this->registry);

        if ($emails_campaign->save()) {
            //show corresponding message
            $this->registry['messages']->setMessage($this->i18n('message_emails_campaigns_add_success'), '', -1);

            //save history
            $filters = array('where' => array('ec.id = ' . $emails_campaign->get('id')),
                             'model_lang' => $emails_campaign->get('model_lang'));
            $new_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
            $this->old_model = new Emails_Campaign($this->registry);
            $this->old_model->sanitize();

            $history_options = array('model'        => $emails_campaign,
                                     'action_type'  => 'add',
                                     'new_model'    => $new_emails_campaign,
                                     'old_model'    => $this->old_model);
            Emails_Campaigns_History::saveData($this->registry, $history_options);

            $this->registry['messages']->insertInSession($this->registry);

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            //show corresponding error(s)
            $this->registry['messages']->setError($this->i18n('error_emails_campaigns_add_failed'), '', -1);

            $this->registry['messages']->insertInSession($this->registry);

            $params = array();
            foreach ($request->getAll() as $k => $v) {
                if (!in_array($k, array($this->registry['module_param'], $this->registry['controller_param'], $this->registry['action_param']))) {
                    $params[$k] = $v;
                }
            }

            $this->redirect($this->module, 'list', $params);
        }

        if (!empty($emails_campaign)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('emails_campaign', $emails_campaign->sanitize());
            //redirect to edit mode
            $this->redirect($this->module, 'edit', array('edit' => $emails_campaign->get('id')));
        } else {
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Edits existing model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $emails_campaign = Emails_Campaigns::buildModel($this->registry);
            $filters = array('where' => array('ec.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

            if ($emails_campaign->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_emails_campaigns_edit_success'), '', -1);

                //save history
                $this->old_model = clone $old_emails_campaign;
                $this->old_model->sanitize();
                $filters = array('where' => array('ec.id = ' . $emails_campaign->get('id')),
                                 'model_lang' => $emails_campaign->get('model_lang'));
                $new_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

                $history_options = array('model' => $emails_campaign,
                                         'action_type'  => 'edit',
                                         'new_model'    => $new_emails_campaign,
                                         'old_model'    => $this->old_model);
                Emails_Campaigns_History::saveData($this->registry, $history_options);

                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                $emails_campaign->getAttachments();
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_emails_campaigns_edit_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('ec.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);
        }

        if (!empty($emails_campaign)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('emails_campaign', $emails_campaign->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $emails_campaign = Emails_Campaigns::buildModel($this->registry);
            $filters = array('where' => array('ec.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

            if ($emails_campaign->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_emails_campaigns_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $filters = array('where' => array('ec.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

                $history_options = array('model'        => $emails_campaign,
                                         'action_type'  => 'translate',
                                         'new_model'    => $new_emails_campaign,
                                         'old_model'    => $old_emails_campaign
                                        );
                Emails_Campaigns_History::saveData($this->registry, $history_options);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_emails_campaigns_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('ec.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);
        }

        if (!empty($emails_campaign)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('emails_campaign', $emails_campaign->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Shows model in view mode
     */
    private function _view() {
        $request = &$this->registry['request'];

        //id of the model
        $id = $request->get($this->action);

        $email = false;
        if ($id > 0) {
            $filters = array('where' => array('ec.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
        }

        if (!empty($emails_campaign)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('emails_campaign', $emails_campaign->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //the model was not found redirect to list mode
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Emails_Campaigns::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            foreach ($ids as $id) {
                $emails_campaign = new Emails_Campaign($this->registry);
                $emails_campaign->set('id', $id, true);
                Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => $this->action));
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete documents
        $result = Emails_Campaigns::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));

            foreach ($ids as $id) {
                $emails_campaign = new Emails_Campaign($this->registry);
                $emails_campaign->set('id', $id, true);
                Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'delete'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Emails_Campaigns::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage($this->i18n('message_items_restored'));

            foreach ($ids as $id) {
                $emails_campaign = new Emails_Campaign($this->registry);
                $emails_campaign->set('id', $id, true);
                Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'restore'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Emails_Campaigns::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage($this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError($this->i18n('error_items_not_purged'), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * status of models
     */
    private function _status() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //get referer's action
        preg_match('/&campaigns=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $emails_campaign = Emails_Campaigns::buildModel($this->registry);
        $filters = array('where' => array('ec.id = ' . $request->get('id')),
                         'model_lang' => $request->get('model_lang'));
        $old_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
        $this->old_model = clone $old_emails_campaign;
        $this->old_model->sanitize();

        $emails_campaign->set('send_date', $old_emails_campaign->get('send_date'), true);

        if ($emails_campaign->setStatus()) {
            $this->registry['messages']->setMessage($this->i18n('message_emails_campaigns_status_success'), '', -2);

            $filters = array('where' => array('ec.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang')
                       );
            $new_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
            $this->model = clone $new_emails_campaign;
            $this->model->sanitize();

            $history_options = array('model' => $emails_campaign,
                                     'action_type' => 'status',
                                     'new_model' => $new_emails_campaign,
                                     'old_model' => $old_emails_campaign
                                    );
            $audit_parent = Emails_Campaigns_History::saveData($this->registry, $history_options);

            //set after action to view if have not permission
            if (!$new_emails_campaign->checkPermissions($after_action)) {
                $after_action = 'view';
            }

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_emails_campaigns_status_failed'), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            header("Location: " . $_SERVER['HTTP_REFERER']);
            exit;
        }

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        if (!isset($matches[1]) || $matches[1] == 'search' || $matches[1] == 'list') {
            //set parameters in registry - check them in router
            //set redirect url
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
        }

        return true;
    }

    /**
     * Attaches files to email campaign
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('ec.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
        if ($emails_campaign) {
            $emails_campaign->getAttachments();
        }

        $added_files = array(0 => array());

        if ($request->isPost()) {
            $modified_files = array();

            $erred_modified_files = array();
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($names as $idx => $name) {
                    $index = $indices[$idx];

                    if (!empty($files['tmp_name'][$idx])) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $emails_campaign->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);

                        //explain the failed upload with more details
                        foreach (FilesLib::$_errors as $err) {
                            $this->registry['messages']->setError($err);
                        }
                        FilesLib::$_errors = array();
                    }

                    $modified_files[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files)) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $emails_campaign->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));

                            //explain the failed upload with more details
                            foreach (FilesLib::$_errors as $err) {
                                $this->registry['messages']->setError($err);
                            }
                            FilesLib::$_errors = array();
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }

                    $added_files[$idx] = $params;
                }
            }

            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'modified_attachments'));
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $filters = array('where' => array('ec.id = ' . $emails_campaign->get('id')),
                                 'model_lang' => $emails_campaign->get('model_lang'));
                $emails_campaign_attached_files = Emails_Campaigns::searchOne($this->registry, $filters);
                $emails_campaign_attached_files->getAttachments();
                $emails_campaign_attached_files->sanitize();

                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'add_attachments', 'new_model' => $emails_campaign_attached_files, 'old_model' => $emails_campaign));
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files)) {
                $this->actionCompleted = true;
            }
        }

        $this->registry['added_files'] = $added_files;

        if (!empty($emails_campaign)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);

            if (!$emails_campaign->get('registry')) {
                $emails_campaign->unsanitize();
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('emails_campaign')) {
                $this->registry->set('emails_campaign', $emails_campaign->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * View, get or delete file attached to email campaign
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'attachments' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('ec.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

        if (!empty($emails_campaign)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $filters = array('where' => array('f.id = ' . $request->get('file')),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
                    }
                    break;
                case 'delfile':
                    // get the files info needed for the audit
                    $old_emails_campaign = clone $emails_campaign;

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('ec.id = ' . $emails_campaign->get('id')),
                                         'model_lang' => $emails_campaign->get('model_lang')
                                        );
                        $emails_campaign_del_files = Emails_Campaigns::searchOne($this->registry, $filters);
                        $emails_campaign_del_files->getAttachments();
                        $emails_campaign_del_files->sanitize();

                        Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'del_attachments', 'new_model' => $emails_campaign_del_files, 'old_model' => $old_emails_campaign));
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    // get the files info needed for the audit
                    $old_emails_campaign = clone $emails_campaign;

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('ec.id = ' . $emails_campaign->get('id')),
                                         'model_lang' => $emails_campaign->get('model_lang')
                                        );
                        $emails_campaign_del_files = Emails_Campaigns::searchOne($this->registry, $filters);
                        $emails_campaign_del_files->sanitize();

                        Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'del_attachments', 'new_model' => $emails_campaign_del_files, 'old_model' => $old_emails_campaign));

                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $emails_campaign->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * History of model
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'ec.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($emails_campaign && $this->checkAccessOwnership($emails_campaign, false)) {
                if (!$this->registry->isRegistered('emails_campaign')) {
                    $this->registry->set('emails_campaign', $emails_campaign->sanitize());
                }

                require_once PH_MODULES_DIR . 'emails/viewers/emails.campaigns.history.viewer.php';
                $viewer = new Emails_Campaigns_History_Viewer($this->registry);
                $viewer->prepare();
                $viewer->setFrameset('_history.html');
                $viewer->display();
            }
            exit;
        }

        if (!empty($emails_campaign)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('emails_campaign')) {
                $this->registry->set('emails_campaign', $emails_campaign->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Statistics of campaign
     */
    private function _statistics() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('ec.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
        if (!empty($emails_campaign)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('emails_campaign')) {
                $this->registry->set('emails_campaign', $emails_campaign->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Clone model
     */
    private function _clone() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('ec.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

        if (empty($emails_campaign)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_emails_campaign'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        } else {
            //check access and ownership of the model
            $this->checkAccessOwnership($emails_campaign);

            //clone
            $model_lang = $emails_campaign->get('model_lang');
            //get configurations of campaign
            $emails_campaign->getConfigurations();

            //selected attached files for configurations
            $sel_files = array();
            foreach ($emails_campaign->get('sel_files') as $idx => $value) {
                $sel_files[$idx] = array();
            }
            $emails_campaign->set('sel_files', $sel_files, true);

            //get translations of source model
            $langs = $emails_campaign->getTranslations();

            //clear the id and set the source (original) id
            $emails_campaign->set('id', null, true);
            $emails_campaign->set('origin_id', $id, true);

            //clear dates and users stats
            $emails_campaign->set('added', null, true);
            $emails_campaign->set('added_by', null, true);
            $emails_campaign->set('modified', null, true);
            $emails_campaign->set('modified_by', null, true);
            $emails_campaign->set('status_modified', null, true);
            $emails_campaign->set('status_modified_by', null, true);
            $emails_campaign->set('status', 'preparation', true);

            //do the clone
            if ($emails_campaign->cloneModel()) {
                foreach ($langs as $t_lang) {
                //copy other translations
                    if ($model_lang != $t_lang) {
                        $filters = array('where' => array('ec.id = ' . $id,
                                                          'ec.active = 1'),
                                         'model_lang' => $t_lang);
                        $t_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);
                        if (!empty($t_emails_campaign)) {
                            $t_emails_campaign->set('id', $emails_campaign->get('id'), true);
                            $t_emails_campaign->updateI18N();
                        }
                    }
                }

                $filters = array('where' => array('ec.id = ' . $emails_campaign->get('id')),
                                 'model_lang' => $model_lang);
                $new_emails_campaign = Emails_Campaigns::searchOne($this->registry, $filters);

                Emails_Campaigns_History::saveData($this->registry, array('model' => $emails_campaign, 'action_type' => 'clone', 'new_model' => $new_emails_campaign));
                //clone is successful
                //redirect view cloned model
                $this->registry['messages']->setMessage($this->i18n('message_emails_campaigns_clone_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $emails_campaign->get('id'));
            } else {
                //unsuccessful clone
                //redirect view parent model
                $this->registry['messages']->setError($this->i18n('error_emails_campaigns_clone'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $id);
            }
        }
    }

    /**
     * Gets recipients in a targetlist
     */
    private function _getTargetlistRecipients() {

        $targetlist_id = $this->registry['request']->get('targetlist_id');

        if ($targetlist_id > 0) {
            require_once PH_MODULES_DIR . 'emails/models/emails.targetlists.factory.php';
            $filters = array('where' => array('et.id = ' . $targetlist_id));
            $targetlist = Emails_Targetlists::searchOne($this->registry, $filters);

            //prepare data for the template
            $this->viewer = new Viewer($this->registry);
            $this->viewer->data['max_display'] = 10;
            $this->viewer->data['targetlist_id'] = $targetlist_id;
            $this->viewer->data['view_mode'] = 1;
            $this->viewer->data['for_campaign'] = 1;
            $this->viewer->data['recipients'] = $targetlist ? $targetlist->getRecipients() : array();
            $this->viewer->setFrameset(PH_MODULES_DIR . 'emails/templates/_target_list_recipients.html');
            $this->viewer->display();
            exit;
        }

        return true;
    }

    public function getActions($action_defs = array()) {

        if (empty($action_defs)) {
            $action_defs = $this->actionDefinitions;
        }

        $actions = parent::getActions($action_defs);

        if (isset($actions['add'])) {
            $actions['add']['ajax_no'] = 1;
            $actions['add']['expanded'] = $this->action == 'list' && $this->registry['request']->isRequested('name');

            $sender_custom_user_emails = $this->registry['config']->getParam('emails', 'sender_custom_user_emails');

            $add_options = array (
                array (
                    'custom_id' => 'name_',
                    'name' => 'name',
                    'type' => 'text',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_name'),
                    'help' => $this->i18n('emails_campaigns_add_legend'),
                    'value' => $this->registry['request']->get('name', 'get')
                ),
                array(
                    'custom_id' => 'send_date_',
                    'name' => 'send_date',
                    'type' => 'datetime',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_send_date'),
                    'help' => $this->i18n('help_emails_campaigns_send_date'),
                    'js_methods' => array('disallow_date_before' => '1'),
                    'value' => $this->registry['request']->get('send_date', 'get')
                ),
                array(
                    'custom_id' => 'send_period_',
                    'name' => 'send_period',
                    'type' => 'text',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_send_period'),
                    'help' => $this->i18n('help_emails_campaigns_send_period'),
                    'custom_class' => 'hright small',
                    'js_filter' => 'insertOnlyPositiveIntegers',
                    'value' => $this->registry['request']->get('send_period', 'get')
                ),
                array(
                    'custom_id' => 'send_limit_',
                    'name' => 'send_limit',
                    'type' => 'text',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_send_limit'),
                    'help' => $this->i18n('help_emails_campaigns_send_limit'),
                    'custom_class' => 'hright small',
                    'js_filter' => 'insertOnlyPositiveIntegers',
                    'value' => $this->registry['request']->get('send_limit', 'get')
                ),
                array(
                    'custom_id' => 'sender_',
                    'name' => 'sender',
                    'type' => 'text',
                    'required' => 1,
                    'readonly' => $sender_custom_user_emails,
                    'label' => $this->i18n('emails_campaigns_sender'),
                    'help' => $this->i18n('help_emails_campaigns_sender'),
                    'value' => $sender_custom_user_emails ? : $this->registry['request']->get('sender', 'get')
                ),
                array(
                    'custom_id' => 'sender_name_',
                    'name' => 'sender_name',
                    'type' => 'text',
                    'required' => 1,
                    'readonly' => $sender_custom_user_emails,
                    'label' => $this->i18n('emails_campaigns_sender_name'),
                    'help' => $this->i18n('help_emails_campaigns_sender_name'),
                    'value' => $sender_custom_user_emails ?
                        $this->registry['config']->getParam('emails', 'sender_custom_user_emails_name') :
                        $this->registry['request']->get('sender_name', 'get')
                )
            );
            $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }

        if ($this->model && isset($actions['clone'])) {
            $actions['clone']['confirm'] = 'confirm_clone';
        }

        if ($this->model && $this->model->get('id') && isset($actions['setstatus'])) {
            $actions['setstatus']['options'] = array('label' => $this->i18n('emails_campaigns_status_btn'), 'form_method' => 'post');
            $actions['setstatus']['ajax_no'] = 1;
            $actions['setstatus']['template'] = PH_MODULES_DIR . 'emails/templates/_action_status.html';
            $actions['setstatus']['model_id'] = $this->model->get('id');
        } else {
            unset($actions['setstatus']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // check the current action and sets the alternative actions for view, edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } else if ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit or view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } else if (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && !empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions();

        if (isset($actions['add'])) {
            $sender_custom_user_emails = $this->registry['config']->getParam('emails', 'sender_custom_user_emails');
            // prepare add options
            $add_options = array (
                array (
                    'custom_id' => 'name____',
                    'name' => 'aa2_name',
                    'type' => 'text',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_name'),
                    'help' => $this->i18n('emails_campaigns_add_legend')
                ),
                array(
                    'custom_id' => 'send_date____',
                    'name' => 'aa2_send_date',
                    'type' => 'datetime',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_send_date'),
                    'help' => $this->i18n('help_emails_campaigns_send_date'),
                    'js_methods' => array('disallow_date_before' => '1'),
                ),
                array(
                    'custom_id' => 'send_period____',
                    'name' => 'aa2_send_period',
                    'type' => 'text',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_send_period'),
                    'help' => $this->i18n('help_emails_campaigns_send_period'),
                    'custom_class' => 'hright small',
                    'js_filter' => 'insertOnlyPositiveIntegers',
                ),
                array(
                    'custom_id' => 'send_limit____',
                    'name' => 'aa2_send_limit',
                    'type' => 'text',
                    'required' => 1,
                    'label' => $this->i18n('emails_campaigns_send_limit'),
                    'help' => $this->i18n('help_emails_campaigns_send_limit'),
                    'custom_class' => 'hright small',
                    'js_filter' => 'insertOnlyPositiveIntegers',
                ),
                array(
                    'custom_id' => 'sender____',
                    'name' => 'aa2_sender',
                    'type' => 'text',
                    'required' => 1,
                    'readonly' => $sender_custom_user_emails,
                    'label' => $this->i18n('emails_campaigns_sender'),
                    'help' => $this->i18n('help_emails_campaigns_sender'),
                    'value' => $sender_custom_user_emails
                ),
                array(
                    'custom_id' => 'sender_name____',
                    'name' => 'aa2_sender_name',
                    'type' => 'text',
                    'required' => 1,
                    'readonly' => $sender_custom_user_emails,
                    'label' => $this->i18n('emails_campaigns_sender_name'),
                    'help' => $this->i18n('help_emails_campaigns_sender_name'),
                    'value' => $sender_custom_user_emails ?
                        $this->registry['config']->getParam('emails', 'sender_custom_user_emails_name') : ''
                )
            );
            $actions['add']['options'] = $add_options;
        }

        return $actions;
    }
}

?>
