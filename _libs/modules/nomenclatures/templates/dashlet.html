<table cellpadding="0" border="0" cellspacing="0" width="100%" class="t_table t_list">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
    {foreach from=$columns item='column'}
    <td class="t_caption t_border {$sort.$column.class}" nowrap="nowrap">
      <div class="t_caption_title" onclick="{$sort.$column.link}">
        {capture assign='column_name'}nomenclatures_{$column}{/capture}
        {$vars_labels.$column|default:$smarty.config.$column_name|escape}
      </div>
    </td>
    {/foreach}
    <td class="t_caption" nowrap="nowrap">
      &nbsp;
    </td>
  </tr>
  {counter start=$pagination.start name='item_counter' print=false}
  {foreach name='i' from=$nomenclatures item='nomenclature'}
  {assign var='assoc_vars' value=$nomenclature->get('assoc_vars')}
  {assign var='layouts_view' value=$nomenclature->getPermittedLayouts('view')}
  {strip}
  {capture assign='info'}
    <strong><u>{$vars_labels.code|default:#nomenclatures_code#|escape}:</u></strong> {$nomenclature->get('code')|escape}<br />
    <strong>{$vars_labels.type|default:#nomenclatures_type#|escape}:</strong> {$nomenclature->get('type_name')|escape}<br />
    <strong>{$vars_labels.name|default:#nomenclatures_name#|escape}:</strong> {$nomenclature->get('name')|escape}<br />
    <strong>{#added#|escape}:</strong> {$nomenclature->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclature->get('added_by_name')|escape}<br />
    <strong>{#modified#|escape}:</strong> {$nomenclature->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclature->get('modified_by_name')|escape}<br />
    {if $nomenclature->isDeleted()}<strong>{#deleted#|escape}:</strong> {$nomenclature->get('deleted')|date_format:#date_mid#|escape}{if $nomenclature->get('deleted_by_name')} {#by#|escape} {$nomenclature->get('deleted_by_name')|escape}{/if}<br />{/if}

    <strong>{#translations#|escape}:</strong>
    <span class="translations">
    {foreach from=$nomenclature->get('translations') item='trans'}
      <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $nomenclature->get('model_lang')} class="selected"{/if} />
    {/foreach}
    </span>
  {/capture}
  {/strip}
  {include file="`$theme->templatesDir`row_link_action.html" object=$nomenclature assign='row_link'}
  {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
  {if !$nomenclature->checkPermissions('list')}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
      <td colspan="{$columns|@count}" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
    </tr>
  {else}
    <tr class="{cycle values='t_odd,t_even'}{if !$nomenclature->get('active')} t_inactive{/if}{if $nomenclature->get('deleted_by')} t_deleted{/if}">
      <td class="t_border hright" nowrap="nowrap">
      {if $nomenclature->get('files_count')}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=attachments&amp;attachments={$nomenclature->get('id')}">
            <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                 onmouseover="showFiles(this, 'nomenclatures', 'nomenclatures', {$nomenclature->get('id')})"
                 onmouseout="mclosetime()" />
          </a>
      {/if}
      {counter name='item_counter' print=true}
      </td>
      {foreach from=$columns item='column'}
      {if in_array($column, array('comments', 'emails', 'history_activity'))}
        {include file="`$smarty.const.PH_MODULES_DIR`/outlooks/templates/td/default_`$column`.html" single=$nomenclature}
      {else}
      <td class="t_border {$sort.$column.isSorted}{if preg_match('/price/', $column)} hright{/if}{if !in_array($column, array('tags'))} {$row_link_class}{/if}"{if !in_array($column, array('tags'))}{$row_link}{/if}>
      {if $column eq 'added'}
        {$nomenclature->get('added')|date_format:#date_mid#|escape}
      {elseif $column eq 'categories'}
        {if $nomenclature->get('categories_names')}
          {foreach name='cn' from=$nomenclature->get('categories_names') item='cat_name'}
            {$cat_name|escape}{if !$smarty.foreach.cn.last},{/if}
          {foreachelse}
            &nbsp;
          {/foreach}
        {/if}
      {elseif $column eq 'subtype'}
        {capture assign='subtype_i18n_param'}nomenclatures_subtype_{$nomenclature->get('subtype')|escape}{/capture}{$smarty.config.$subtype_i18n_param|escape}
      {elseif $column eq 'sell_price_and_currency'}
        {$nomenclature->get('sell_price')} {$nomenclature->get('sell_price_currency')|escape}
      {elseif $column eq 'last_delivery_price_and_currency'}
        {$nomenclature->get('last_delivery_price')} {$nomenclature->get('last_delivery_price_currency')|escape}
      {elseif $column eq 'average_weighted_delivery_price_and_currency'}
        {$nomenclature->get('average_weighted_delivery_price')} {$nomenclature->get('average_weighted_delivery_price_currency')|escape}
      {elseif $column eq 'tags'}
        <div{if $nomenclature->getModelTags() && $nomenclature->get('available_tags_count') gt 0 && $nomenclature->checkPermissions('tags_view') && $nomenclature->checkPermissions('tags_edit')} style="padding: 3px 0 3px 0; cursor: pointer;" onclick="changeTags({$nomenclature->get('id')}, 'nomenclatures')" title="{#tags_change#}"{/if}>
        {if $nomenclature->get('model_tags')|@count gt 0 && $nomenclature->checkPermissions('tags_view')}
          {foreach from=$nomenclature->get('model_tags') item='tag' name='ti'}
            <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
          {/foreach}
        {else}
          &nbsp;
        {/if}
        </div>
      {elseif $column eq 'has_batch'}
        {if $nomenclature->get('subtype') eq 'commodity'}{if $nomenclature->get('has_batch')}{#yes#}{else}{#no#}{/if}{else}&nbsp;{/if}
      {elseif $column eq 'name_code'}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$nomenclature->get('id')}">&#91;{$nomenclature->get('code')|escape|default:"&nbsp;"}&#93; {$nomenclature->get('name')|escape|default:"&nbsp;"}</a>
      {elseif preg_match('/^a__/', $column)}
        {assign var='additional_col_name' value=$column|regex_replace:'#^a__(.*)$#i':'$1'}
          {if !empty($layouts_view) && is_array($layouts_view) && in_array($assoc_vars.$additional_col_name.layout_id, $layouts_view)}
          {assign var='additional_col_type' value=$assoc_vars.$additional_col_name.type}
          {assign var='additional_col_value' value=''}
          {if is_array($assoc_vars.$additional_col_name.value)}
            {assign var='additional_col_value' value=$assoc_vars.$additional_col_name.value}
          {else}
            {array assign='additional_col_value' value=$assoc_vars.$additional_col_name.value}
          {/if}
          {assign var='has_value' value=false}
          {strip}
            {foreach from=$additional_col_value item='var_value' name='var_values'}
              {if $var_value || $var_value === '0'}
                {assign var='has_value' value=true}
                {if $additional_col_type eq 'date'}
                  {$var_value|escape|date_format:#date_short#}
                {elseif $additional_col_type eq 'datetime'}
                  {$var_value|escape|date_format:#date_mid#}
                {elseif in_array($additional_col_type, array('checkbox_group', 'dropdown', 'radio'))}
                  {foreach from=$assoc_vars.$additional_col_name.options item='var_option'}
                    {if $var_option.option_value eq $var_value}
                      {if $additional_col_type eq 'checkbox_group' && trim($var_option.label) === ''}
                        <img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" style="margin-left: 5px;" />
                      {else}
                        {$var_option.label|escape|default:$var_value}
                      {/if}
                    {/if}
                  {foreachelse}
                    {$var_value|escape}
                  {/foreach}
                {elseif $additional_col_type eq 'file_upload'}
                  {if !empty($var_value) && is_object($var_value) && !$var_value->get('deleted_by')}
                  {if !$var_value->get('not_exist')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=viewfile&amp;viewfile={$var_value->get('model_id')}&amp;file={$var_value->get('id')}" target="_blank">
                  {/if}
                    <img border="0" width="16" height="16" src="{$theme->imagesUrl}{$var_value->getIconName()}.png" alt="{$var_value->get('filename')}" title="{$var_value->get('filename')}" class="{if $var_value->get('not_exist')}dimmed{else}pointer{/if}" />
                  {if !$var_value->get('not_exist')}
                    </a>
                  {/if}
                  {else}
                    &nbsp;
                    {if $smarty.foreach.var_values.first}{assign var='has_value' value=false}{/if}
                  {/if}
                {elseif $additional_col_type eq 'autocompleter'}
                  {include file=_view_autocompleter.html
                           value=$var_value
                           value_id=$assoc_vars.$additional_col_name.value_id
                           view_mode_url=$assoc_vars.$additional_col_name.autocomplete.view_mode_url}
                {else}
                  {$var_value|escape}
                {/if}
              {elseif count($additional_col_value) > 1 && $additional_col_type ne 'file_upload'}
                {#no_data#}
              {/if}
              {if !$smarty.foreach.var_values.last && $additional_col_type ne 'file_upload'}
                ,<br />
              {/if}
            {/foreach}
          {/strip}
          {if $has_value && $assoc_vars.$additional_col_name.back_label}{$assoc_vars.$additional_col_name.back_label}{/if}
        {else}
          &nbsp;
        {/if}
      {else}
        {$nomenclature->get($all_columns.$column)|escape|default:"&nbsp;"}
      {/if}
      </td>
      {/if}
      {/foreach}
      <td class="hcenter" nowrap="nowrap">
        {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclature}
      </td>
    </tr>
    {/if}
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="{math equation='count+2' count=$columns|@count}">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td colspan="{math equation='count+2' count=$columns|@count}" class="t_footer"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=nomenclatures&amp;nomenclatures=dashlet&amp;dashlet={$dashlet_id}&amp;page={/capture}
{capture assign='container'}content_dashlet_{$dashlet_id}{/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  target=$container
  link=$link
  use_ajax=1
  hide_rpp=1
  hide_stats=1
}
    </td>
  </tr>
</table> 