<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="pricelists" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$nomenclatures_pricelist->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$nomenclatures_pricelist->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="vtop">
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="3">
            {#message_translatable_items#|escape}
          </td>
          <td class="vtop t_border divider_cell" rowspan="8">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="2">&nbsp;</td>
          {capture assign='source_lang'}lang_{$nomenclatures_pricelist->get('model_lang')}{/capture}
          <td><img src="{$theme->imagesUrl}flags/{$nomenclatures_pricelist->get('model_lang')}.png" alt="" title="{$smarty.config.$source_lang}" class="t_flag" /> {$smarty.config.$source_lang}</td>
          <td>&nbsp;</td>
          {capture assign='target_lang'}lang_{$base_model->get('model_lang')}{/capture}
          <td colspan="2"><img src="{$theme->imagesUrl}flags/{$base_model->get('model_lang')}.png" alt="" title="{$smarty.config.$target_lang}" class="t_flag" /> {$smarty.config.$target_lang}</td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='pricelists_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox distinctive" name="name" id="name" value="{$nomenclatures_pricelist->get('name')|escape}" title="{#nomenclatures_pricelists_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
          <td>&nbsp;</td>
          <td>
            <button type="button" name="copy_name" id="copy_name" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td>
            <input type="text" class="txtbox distinctive" name="bm_name" id="bm_name" value="{$base_model->get('name')|escape}" title="{#nomenclatures_pricelists_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
          </td>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="name"{if $messages->getErrors('description')} class="error"{/if}>{help label='pricelists_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox distinctive" name="description" id="description" title="{#nomenclatures_pricelists_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$nomenclatures_pricelist->get('description')|escape}</textarea>
          </td>
          <td>&nbsp;</td>
          <td>
            <button type="button" name="copy_description" id="copy_description" class="button copy_button" onclick="copyField(this)" title="{#copy#|escape}">&laquo;</button>
          </td>
          <td class="vtop">
            <textarea class="areabox distinctive" name="bm_description" id="bm_description" title="{#nomenclatures_pricelists_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly">{$base_model->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#translate#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
          <td>&nbsp;</td>
          <td colspan="2">
            <button type="button" name="copyAll" class="button" title="{#copy_all#|escape}" onclick="return confirmAction('copy_all', function(el) {ldelim} copyAllFields(el); {rdelim}, this);">&laquo; {#copy_all#|escape}</button>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$nomenclatures_pricelist }
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
