<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='categories_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$category->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='categories_ancestor'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$category->get('ancestor_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='categories_path'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
{foreach from=$categories_parents item='item'}
           &raquo;
           {if $item->isDeleted() || !$item->isActivated()}
             <span class="inactive_option" title="{#inactive_option#}">{$item->get('name')|escape}</span>
           {else}
             {$item->get('name')|escape}
           {/if}
{/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='categories_position'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$category->get('position')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='categories_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$category->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$category}
</div>
