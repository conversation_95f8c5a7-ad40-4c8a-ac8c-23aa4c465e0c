<?php

use Nzoom\Mvc\ControllerTrait\GridBasedListTrait;

class Nomenclatures_Types_Controller extends Controller {
    use GridBasedListTrait;

    /**
     * Model name of this controller
     */
    public $modelName = 'Nomenclatures_Type';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Nomenclatures_Types';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'distribute'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'translate', 'distribute'
    );

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'n.type';

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'search':
            $this->_search();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'getadvancedsearchoptions':
            $this->_getAdvancedSearchOptions();
            break;
        case 'distribute':
            $this->_distribute();
            break;
        case 'getListColumnsDefinitions':
            $this->_getListColumnsDefinitions();
            break;
        case 'listData':
            $this->_listData();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }


    /**
     * listing of all models
     */
    private function _listData() {
        /** @var Request $request */
        $request = $this->registry['request'];
        $accept = $request->getHeader('Accept');
        if (!preg_match("/.*json/i", $accept)
            || false !== stripos($accept, "html")) {
            return;
        }

        // The rights are cheked based on the action name. Documents::prepareRightsFilters() is called in the model
        $this->registry->set('action', 'list', true);

        $filters = $this->modelFactoryName::saveSearchParams($this->registry);
        $this->registry->set('getTags', true, true);

        $friendlyFilters = $this->generateFriendlyFilters($filters);

        $outlook = $this->getCurrentOutlook($filters);

        $shouldCheckPermissions= [];

        if (isset($outlook) && $outlook) {
            $modelFields = $outlook->get('current_custom_fields');
            $modelFieldsNames = array_column($modelFields, 'name');
            $filters['get_fields'] = $modelFields;

            if (in_array('tags', $modelFieldsNames)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }

            $shouldCheckAssignmentsPermissions = in_array('owner', $modelFieldsNames)
                || in_array('observer', $modelFieldsNames)
                || in_array('responsible', $modelFieldsNames)
                || in_array('decision', $modelFieldsNames);
        }

        list($records, $pagination) = $this->modelFactoryName::pagedSearch($this->registry, $filters);

        if (isset($outlook) && $outlook) {
            $additionalVars = $outlook->getModelAdditionalFields();
            $basicVars = $outlook->getModelFields();;
            foreach ($records as $record) {
                foreach ($additionalVars as $k=>$v) {
                    $record->getVarValue($k, 0);
                    $record->properties[$k] = $record->getVar($k);
                }

                if (array_key_exists('relatives_children',$basicVars)) {
                    $record->set('relatives_children', $record->getFirstLevelRelatedDocuments('child'), true);
                }
                if (array_key_exists('relatives_parent',$basicVars)) {
                    $record->set('relatives_parent', $record->getFirstLevelRelatedDocuments('parent'), true);
                }

                if (in_array('tags', $modelFieldsNames)) {
                    $record->checkPermissions('tags_view');
                    $record->checkPermissions('tags_edit');
                }
                if ($shouldCheckAssignmentsPermissions) {
                    $record->checkPermissions('assign');
                }

                $record->sanitize();
                $record->properties['cached_assoc_vars'] = null;

                unset($record->properties['cached_assoc_vars']);
            }
        }

        if (in_array(true, $shouldCheckPermissions, true)) {
            foreach ($records as $record) {
                foreach ($shouldCheckPermissions as $action=>$test) {
                    $record->checkPermissions($action);
                }
            }
        }
        $data = [
            'pagination' => $pagination,
            'records' => $record,
            'friendlyFilters' => $friendlyFilters,
        ];
        echo json_encode($data);
        exit;
    }

    /**
     * listing of all models
     */
    /*private function _getListColumnsDefinitions() {
        /** @var Request $request */
        /*$request = $this->registry['request'];
        $accept = $request->getHeader('Accept');

        $alias = Documents::getAlias('nomenclatures', 'nomenclatures');

        if ($request->get('value')) {
            $sesFilters = [
                'search_fields' => [
                    self::$searchAdditionalVarsSwitch,
                ],
                'compare_options' => ["= '%s'"],
                'values' => explode(',', $request->get('value')),
            ];
        } else {
            $sesFilters = [];
        }

        list($advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search) = $this->getSearchOptions($sesFilters);

        $searchColumsByFieldName = [];
        foreach ($advanced_search as $k=>$v) {
            $searchColumsByFieldName[$v['field_name']] = $v;
        }

        foreach ($additional_search?:[] as $k=>$v) {
            $searchColumsByFieldName[$v['field_name']] = $v;
        }

        $user_id = (int) $this->registry['currentUser']->get('id');
        $role_id = (int) $this->registry['currentUser']->get('role');

        $name = $request->get('name');
        $value = (int) $request->get('value');
        $outlook = \Outlooks::getOutlook($this->registry, $name === 'section', $value, $user_id, $role_id, $this->module, $this->controller);
        $output = [];
        if ($outlook) {
            $columnsDef = [];
            foreach ($outlook->get('current_custom_fields') as $k => $v) {
                if(!$v['position']) {
                    continue;
                }
                $def = [
                    'fieldName' => $v['name'],
                    'type' => $v['field_type'],
                    'label' => $v['label'],
                    'origin' => $v['origin'],
                    'fieldPath' => "properties.{$v['name']}",
                    'sort' => null,
                    'model_type' => $v['model_type'],
                    'visible' => (bool)$v['position'],
                    'width' => $v['column_width'],
                ];

                if (array_key_exists($v['name'], $searchColumsByFieldName)) {
                    $searchField = $searchColumsByFieldName[$v['name']];
                    if ($searchField) {
                        $def['searchFieldName'] = $searchField['option_value'] ?? null;
                        $def['searchCompareOptions'] = $searchField['compare_options'] ?? null;
                        $def['sort'] = $searchField['option_value'] ?? null;
                    }
                }
                $columnsDef[] = $def;
            }
            $output = [
                'outlook' => [
                    'id' => $outlook->get('id')
                ],
                'columns' => $columnsDef
            ];
            echo json_encode($output);
            exit;
        }
        // default columns
        $labels = (new Viewer($this->registry))->getBasicVarsLabels('nomenclature',
            ['name'=>$request->get('name'), 'value'=>$request->get('value')]);

        $columnsDef = [
            ['fieldName' => 'name', 'type' => 'text', 'visible' => 'true'],
            ['fieldName' => 'auto_code_suffix', 'type' => 'text', 'labelKey' => 'code', 'visible' => 'true', 'width' => '50'],
            ['fieldName' => 'section_name', 'type' => 'text', 'labelKey' => 'section', 'visible' => 'true', 'width' => '100'],
            ['fieldName' => 'count_nomenclatures', 'type' => 'text', 'visible' => 'true', 'width' => '150']
        ];

        foreach ($columnsDef as $k => $v) {
            $columnsDef[$k]['origin'] = 'basic';
            if (array_key_exists('label', $v)) {
                $columnsDef[$k]['label'] = $v['label'];
            } else {
                $labelKey = array_key_exists('labelKey', $v) ? $v['labelKey'] : $v['fieldName'];
                $columnsDef[$k]['label'] = $labels[$labelKey] ?? $this->i18n('nomenclatures_types_' . $labelKey);
            }
            $columnsDef[$k]['fieldPath'] = "properties.{$v['fieldName']}";
            $columnsDef[$k]['sort'] = null;
            $columnsDef[$k]['model_type'] = "";

            if (array_key_exists($v['fieldName'], $searchColumsByFieldName)) {
                $searchField = $searchColumsByFieldName[$v['fieldName']];
                if ($searchField) {
                    $columnsDef[$k]['searchFieldName'] = $searchField['option_value'] ?? null;
                    $columnsDef[$k]['searchCompareOptions'] = $searchField['compare_options'] ?? null;
                    $columnsDef[$k]['sort'] = $searchField['option_value'] ?? null;
                }
            }
        }

        $output = [
            'outlook' => [
                'id' => null,
                'module' => $this->module,
                'controller' => $this->controller,
                'model_id' => $name === 'type' ? $value: 0,
                'section' => $name === 'section' ? $value: 0,
                //'lang' => $this->registry['lang'],
                //'model_lang' => $this->model_lang,
                'model_type_id' => $name === 'type' ? $value: 0,
                'assignments_type' => 'User',
                'assignments' => [$user_id],
                'role_id' => $role_id,
            ],
            'columns' => $columnsDef
        ];
        //header('Content-Type: text/JSON;charset=UTF-8');
        echo json_encode($output);
        exit;
    }*/

    /**
     * listing of all models
     */
    private function _list() {
        $this->viewer = $this->getViewer();
        //$this->viewer->setTemplate('types_list.html');
        $this->viewer->data['db_col_alias'] = Documents::getAlias('nomenclatures', 'nomenclatures');
        /** @var Request $request */
        $request = $this->registry['request'];

        $get = $request->getGet();
        if (!($get['key']??false)) {
            $request->set('key', '', true);
        }

        $fieldDefsMap = [];

        $this->viewer->data['columnsDefinitions']  = "{$request->getServer()['REQUEST_SCHEME']}://{$request->getServer()['SERVER_NAME']}";
        $this->viewer->data['columnsDefinitions'] .= "{$request->getServer()['SCRIPT_NAME']}?".Router::MODULE_PARAM."={$this->module}&controller=types&types=getListColumnsDefinitions&lang={$this->registry->get('lang')}&model_lang={$this->registry->get('lang')}";

        return true;
    }


    private function _getAdvancedSearchOptions(): void
    {
        $session_filters = [

        ];
        list($advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search) = $this->getSearchOptions($session_filters);
        //var_dump($advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search);

        $factory_name = $this->modelFactoryName;
        $alias_words = explode('_', $this->modelFactoryName, 2);
        $alias = $factory_name::getAlias($alias_words[0], (isset($alias_words[1]) ? $alias_words[1] : $alias_words[0]));

        $this->viewer = new Viewer($this->registry);
        $search_fields = array('basic_vars' => $advanced_search, 'additional_vars' => $additional_search);
        $this->viewer->data['session_filters'] = $session_filters;
        $this->viewer->data['saved_filters'] = $saved_filters;
        $this->viewer->data['simple_search_defs'] = $simple_search;
        $this->viewer->data['search_fields'] = $search_fields;
        $this->viewer->data['advanced_search_options'] = json_encode($advanced_search);
        $this->viewer->data['additional_search_options'] = json_encode($additional_search) . (isset($additional_columns) ? ', additional_columns = ' .json_encode($additional_columns) : '');
        $this->viewer->data['switch_additional'] = isset($this::$searchAdditionalVarsSwitch) ? $this::$searchAdditionalVarsSwitch : false;
        $this->viewer->data['alias'] = $alias;

        $this->viewer->data['additional_hidden_filters'] = !empty($additional_hidden_filters) ? $additional_hidden_filters : array();
        $this->viewer->data['system_fields'] = $system_fields;
        $this->viewer->setFrameset('_action_search_advanced.html');
        $this->viewer->display();
        exit;
    }


    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        // Prepare to work with counters
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.counters.factory.php';

        // Prepare some default properties for a counter model
        $counter_params = array(
            'next_number' => '1',
            'name' => $request->get('counter_name')
        );
        // counter data to take from request
        $counter_post = array(
            'formula', 'prefix', 'leading_zeroes', 'delimiter', 'date_format', 'group'
        );

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_type = Nomenclatures_Types::buildModel($this->registry);

            // If we are trying to add a new counter
            if ($request->get('counter') == 'add_counter') {
                // Build a counter model from the request and default params
                $counter = Nomenclatures_Counters::buildFromRequest($this->registry, 'Nomenclatures_Counter', $counter_post);
                foreach ($counter_params as $k => $v) {
                    $counter->set($k, $v, true);
                }

                if ($counter->save()) {
                    // Show success messages
                    $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_counters_add_success'), '', -4);

                    // Set the new counter as counter for the current type
                    $nomenclatures_type->set('counter', $counter->get('id'), true);

                    // counter is now saved in db, clean up its data
                    unset($counter);
                } else {
                    $errors = $this->registry['messages']->getErrors();
                    $this->registry['messages']->flush();
                    $idx = count($errors) + 2;
                    // Show failed message
                    $this->registry['messages']->setError($this->i18n('error_nomenclatures_counters_add_failed'), '', -1 * $idx);
                    foreach ($errors as $k => $v) {
                        if ($k == 'name') {
                            $k = 'counter_' . $k;
                        }
                        $this->registry['messages']->setError($v, $k, -1 * (--$idx));
                    }

                    // Fail the validation of the current type
                    $nomenclatures_type->valid = false;
                }
            }

            // clean up counter data from type model
            if (!isset($counter)) {
                foreach ($counter_post as $k) {
                    $nomenclatures_type->unsetProperty($k, true);
                }
                $nomenclatures_type->unsetProperty('counter_name', true);
            }

            if ($nomenclatures_type->save()) {
                if (!$nomenclatures_type->imageCreate()) {
                    $this->registry['messages']->setWarning($this->i18n('warning_nomenclatures_types_add_success'), '', -1);
                }
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_types_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_types_add_failed'), '', -1);
            }
        } else {
            //create empty model
            $nomenclatures_type = Nomenclatures_Types::buildModel($this->registry);
        }

        // If no counter is added yet
        if (!isset($counter)) {
            // Build an empty counter model, to be used for some display purposes
            $counter = new Nomenclatures_Counter($this->registry, $counter_params);
        }

        // Prepare the model to be displayed
        $counter->prepare();
        // Set the counter model into the type model
        $nomenclatures_type->set('counter_model', $counter->sanitize(), true);

        if (!empty($nomenclatures_type)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclatures_type', $nomenclatures_type->sanitize());
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_type = Nomenclatures_Types::buildModel($this->registry);
            if ($nomenclatures_type->save()) {
                if ($request->get('icon_delete')) {
                    $nomenclatures_type->updateTableIcon($id, '', $request->get('icon_name'));
                }
                if (!$nomenclatures_type->imageCreate()) {
                    $this->registry['messages']->setWarning($this->i18n('warning_nomenclatures_types_edit_success'),'',-1);
                }
                //show message 'message_nomenclatures_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_types_edit_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_types_edit_failed'),'',-1);

                //register the model, with all the posted details
                $this->registry->set('nomenclatures_type', $nomenclatures_type);
            }

        } elseif ($id > 0) {

            // the model from the DB
            $filters = array('where' => array('nt.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclatures_type = Nomenclatures_Types::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclatures_type);
        }

        if (!empty($nomenclatures_type)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclatures_type')) {
                $this->registry->set('nomenclatures_type', $nomenclatures_type->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('nt.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $nomenclatures_type = Nomenclatures_Types::searchOne($this->registry, $filters);

        //check access and ownership of the model
        $this->checkAccessOwnership($nomenclatures_type);

        if (!empty($nomenclatures_type)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclatures_type')) {
                $this->registry->set('nomenclatures_type', $nomenclatures_type->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }


    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_type = Nomenclatures_Types::buildModel($this->registry);

            if ($nomenclatures_type->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_types_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_types_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('nt.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclatures_type = Nomenclatures_Types::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclatures_type);
        }

        if (!empty($nomenclatures_type)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclatures_type', $nomenclatures_type->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Nomenclatures_Types::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete items
        $result = Nomenclatures_Types::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Nomenclatures_Types::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Nomenclatures_Types::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Sets default percentage distribution of nomenclature type by items
     */
    private function _distribute() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_type = Nomenclatures_Types::buildModel($this->registry);

            if ($nomenclatures_type->updateItemsDefaultDistributionValues()) {
                //show message 'message_nomenclatures_types_distribute_success'
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_types_distribute_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_types_distribute_failed'), '', -1);
            }
        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('nt.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclatures_type = Nomenclatures_Types::searchOne($this->registry, $filters);

            if (!empty($nomenclatures_type)) {
                //check access and ownership of the model
                $this->checkAccessOwnership($nomenclatures_type);
            }
        }

        if (!empty($nomenclatures_type)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclatures_type')) {
                $this->registry->set('nomenclatures_type', $nomenclatures_type->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_type'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * @param $name
     * @param int $value
     * @param array $searchColumsByFieldName
     * @return array[]
     */
    private function getDefaultColumnsDefinitions($name, int $value, array $searchColumsByFieldName): array
    {
        $labels = (new Viewer($this->registry))->getBasicVarsLabels('nomenclature',
            ['name' => $name, 'value' => $value]);

        $columnsDef = [
            ['fieldName' => 'name', 'type' => 'text', 'visible' => 'true'],
            ['fieldName' => 'auto_code_suffix', 'type' => 'text', 'labelKey' => 'code', 'visible' => 'true', 'width' => '50'],
            ['fieldName' => 'section_name', 'type' => 'text', 'labelKey' => 'section', 'visible' => 'true', 'width' => '100'],
            ['fieldName' => 'count_nomenclatures', 'type' => 'text', 'visible' => 'true', 'width' => '150']
        ];

        foreach ($columnsDef as $k => $v) {
            $columnsDef[$k]['origin'] = 'basic';
            if (array_key_exists('label', $v)) {
                $columnsDef[$k]['label'] = $v['label'];
            } else {
                $labelKey = array_key_exists('labelKey', $v) ? $v['labelKey'] : $v['fieldName'];
                $columnsDef[$k]['label'] = $labels[$labelKey] ?? $this->i18n('nomenclatures_types_' . $labelKey);
            }
            $columnsDef[$k]['fieldPath'] = "properties.{$v['fieldName']}";
            $columnsDef[$k]['sort'] = null;
            $columnsDef[$k]['model_type'] = "";

            if (array_key_exists($v['fieldName'], $searchColumsByFieldName)) {
                $searchField = $searchColumsByFieldName[$v['fieldName']];
                if ($searchField) {
                    $columnsDef[$k]['searchFieldName'] = $searchField['option_value'] ?? null;
                    $columnsDef[$k]['searchCompareOptions'] = $searchField['compare_options'] ?? null;
                    $columnsDef[$k]['sort'] = $searchField['option_value'] ?? null;
                }
            }
        }
        return $columnsDef;
    }
}

?>
