nomenclatures_counters = Nomenklaturenzähler
nomenclatures_counters_name = Name
nomenclatures_counters_formula = Formel
nomenclatures_counters_description = Beschreibung
nomenclatures_counters_next_number = Nächste Nummer
nomenclatures_counters_count_nomenclatures = Anzahl der Nomenklaturen
nomenclatures_counters_types_used = Verwendet in Typen
nomenclatures_counters_status = Status
nomenclatures_counters_status_active = Aktiv
nomenclatures_counters_status_inactive = Nicht aktiv
nomenclatures_counters_added_by = Hinzugefügt von
nomenclatures_counters_modified_by = Geändert von
nomenclatures_counters_added = Hinzugefügt am
nomenclatures_counters_modified = Geändert am

nomenclatures_counters_add = Nomenklaturenzähler hinzufügen
nomenclatures_counters_edit = Nomenklaturenzähler bearbeiten
nomenclatures_counters_view = Nomenklaturenzähler einsehen
nomenclatures_counters_translate = Nomenklaturenzähler übersetzen

message_nomenclatures_counters_add_success = Die Daten des Zählers wurden erfolgreich hinzugefügt!
message_nomenclatures_counters_edit_success = Die Daten des Zählers wurden erfolgreich bearbeitet!
message_nomenclatures_counters_translate_success = Der Zähler wurde erfolgreich übersetzt!

error_nomenclatures_counters_edit_failed = Die Daten des Zählers wurden nicht erfolgreich bearbeitet:
error_nomenclatures_counters_add_failed = Die Daten des Zählers wurden nicht hinzugefügt:
error_nomenclatures_counters_translate_failed = Der Zähler wurde nicht erfolgreich übersetzt:

error_no_such_nomenclature_counter = Dieser Eintrag ist für Sie nicht verfügbar!
error_no_counter_name_specified = Name nicht eingegeben!
error_no_counter_formula_specified = Formel nicht eingegeben!
error_invalid_next_number = Bitte nächste Zählernummer eingeben, die nur aus Ziffern größer als 0 besteht!

error_no_types_used = Kein einziger Nomenklaturentyp wurde verwendet

nomenclatures_counters_formula_delimiter = Trennzeichen
nomenclatures_counters_empty_delimiter = ohne Trennzeichen
nomenclatures_counters_formula_leading_zeroes = Anzahl der führenden Nullen
nomenclatures_counters_formula_date_format = Format
nomenclatures_counters_formula_date_delimiter = mit Divider

nomenclatures_counters_formula_date_format_year = JJJJ
nomenclatures_counters_formula_date_format_year_short = JJ
nomenclatures_counters_formula_date_format_month = MM
nomenclatures_counters_formula_date_format_day = TT

nomenclatures_counters_formula_date_format1 = JJJJ
nomenclatures_counters_formula_date_format2 = MM/JJJJ
nomenclatures_counters_formula_date_format3 = MM/JJ
nomenclatures_counters_formula_date_format4 = JJJJ/MM
nomenclatures_counters_formula_date_format5 = JJ/MM
nomenclatures_counters_formula_date_format6 = TT/MM/JJJJ
nomenclatures_counters_formula_date_format7 = TT/MM/JJ
nomenclatures_counters_formula_date_format8 = MM/TT/JJJJ
nomenclatures_counters_formula_date_format9 = MM/TT/JJ
nomenclatures_counters_formula_date_format10 = JJJJ/TT/MM
nomenclatures_counters_formula_date_format11 = JJ/TT/MM
nomenclatures_counters_formula_date_format12 = JJJJ/MM/TT
nomenclatures_counters_formula_date_format13 = JJ/MM/TT
nomenclatures_counters_formula_date_format14 = JJ
nomenclatures_counters_formula_date_format15 = JJJ/MM

nomenclatures_counters_formula_legend = Legende zum Ausfüllen der Zählerformel
nomenclatures_counters_formula_prefix = Präfix
nomenclatures_counters_formula_num = Nummer der Nomenklatur
nomenclatures_counters_formula_code_suffix = Code-Suffix
nomenclatures_counters_formula_user_code = Benutzercode
nomenclatures_counters_formula_added = Datum der Nomenklatur

nomenclatures_counters_formula_prefix_descr = direkt 2-3 Buchstaben ausfüllen, z.B. ART zum Artikel.
nomenclatures_counters_formula_num_descr = die laufende Nummer der Nomenklatur eintragen.
nomenclatures_counters_formula_code_suffix_descr = Suffix den Code für die Art der Nomenklatur automatisch eingestellt.
nomenclatures_counters_formula_user_code_descr = füllt den Kode des Benutzers aus, der die Nomenklatur erstellt hatte.
nomenclatures_counters_formula_added_descr = Datum des Hinzufügens der Nomenklatur.

nomenclatures_counters_formula_note = <strong>HINSWEIS:</strong> <strong>Nur 5 Elemente</strong> können zur Zählerformel hinzugefügt werden

help_nomenclatures_counters_next_number = Nächste Nummer. Bitte dieses Feld verwenden, um eine Nummer anzugeben, von der an der Zähler zu zählen beginnt.
