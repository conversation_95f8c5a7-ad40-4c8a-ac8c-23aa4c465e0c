
<h1><img src="{$theme->imagesUrl}nomenclatures_add.png" border="0" alt="{#add#|escape}" /> {$title|escape}</h1>

<div id="form_container">
  {if !empty($nomenclature_type) && !empty($smarty.request.uniqid) && !empty($nomenclature) && $nomenclature->get('autocomplete_params')}
  <form name="nomenclatures_add" id="nomenclatures_add" action="{$submitLink}" method="post" enctype="multipart/form-data" onsubmit="return calculateBeforeSubmit(this);">
  <input type="hidden" name="id" id="id" value="{$nomenclature->get('id')}" />
  <input type="hidden" name="model_id" id="model_id" value="{$model_id}" />
  <input type="hidden" name="model_lang" id="model_lang" value="{$nomenclature->get('model_lang')|default:$lang}" />
  <input type="hidden" name="uniqid" id="uniqid" value="{$smarty.request.uniqid|escape}" />
  <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$nomenclature->get('autocomplete_params')|escape}" />
    <table border="0" cellpadding="0" cellspacing="0" class="t_table">
      <tr>
        <td class="t_footer"></td>
      </tr>
      <tr>
        <td>
          {include file='layouts_index.html' display='abs_div'}
          {assign var='layouts_vars' value=$nomenclature->get('vars')}
          <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
          {foreach from=$nomenclature->get('layouts_details') key='lkey' item='layout'}

            {if $layout.system || $layout.view && array_key_exists($layout.id, $layouts_vars)}
            <tr{if !$layout.view || !$layout.visible || $lkey eq 'batch_options' && $nomenclature->get('subtype') ne 'commodity'} style="display: none;"{/if}>
              <td colspan="3" class="t_caption3 pointer">
                <div class="floatr index_arrow_anchor">
                  <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                </div>
                <div class="layout_switch" {if $layout.system}onclick="toggleViewLayouts(this)" id="nomenclature_{$layout.keyword}_switch"{else}onclick="toggleLayouts(this)" id="layout_{$layout.id}_switch"{/if}>
                  <a name="nomenclature_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
                </div>
              </td>
            </tr>
            {/if}

            {if $lkey eq 'type'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view && $nomenclatures_types|@count eq 1} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="required">{#required#}</td>
              <td>
                {if $nomenclatures_types|@count eq 1}
                  {foreach from=$nomenclatures_types item='nomenclatureType'}
                    {$nomenclatureType->get('name')|escape}
                    <input type="hidden" id="type" name="type" value="{$nomenclatureType->get('id')}" />
                  {/foreach}
                {else}
                  <select class="selbox" name="type" id="type" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="Effect.Center('loading'); Effect.Appear('loading'); window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}={$action}&amp;type=' + this.value + '&amp;uniqid={$smarty.request.uniqid|escape}';">
                  {foreach from=$nomenclatures_types item='nomenclatureType'}
                    <option value="{$nomenclatureType->get('id')}"{if $nomenclatureType->get('id') eq $nomenclature->get('type')} selected="selected"{/if}>{$nomenclatureType->get('name')|escape}</option>
                  {/foreach}
                  </select>
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'code'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_code"><label for="code"{if $messages->getErrors('code')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="required">{#required#}</td>
              <td>
                {if $layout.edit}
                  <input type="text" class="txtbox" name="code" id="code" value="{$nomenclature->get('code')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                {else}
                  {$nomenclature->get('code')|escape}
                  <input type="hidden" name="code" id="code" value="{$nomenclature->get('code')|escape}" />
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'name'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="required">{#required#}</td>
              <td>
                {if $layout.edit}
                  <input type="text" class="txtbox" name="name" id="name" value="{$nomenclature->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                {else}
                  {mb_truncate_overlib text=$nomenclature->get('name')|escape|default:"&nbsp;"}
                  <input type="hidden" name="name" id="name" value="{$nomenclature->get('name')|escape}" />
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'last_delivery_price'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_last_delivery_price"><label for="last_delivery_price"{if $messages->getErrors('last_delivery_price')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="unrequired">&nbsp;</td>
              <td>
                {if $layout.edit}
                  <input type="text" class="txtbox hright" name="last_delivery_price" id="last_delivery_price" style="width: 110px;" onkeypress="return changeKey(this, event, insertOnlyFloats);" value="{$nomenclature->get('last_delivery_price')}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                  {include file=`$theme->templatesDir`input_dropdown.html
                           required=1
                           standalone=true
                           width='80'
                           name=last_delivery_price_currency
                           options=$currencies
                           value=$nomenclature->get('last_delivery_price_currency')
                           label=#nomenclatures_last_delivery_price_currency#}
                {else}
                  {$nomenclature->get('last_delivery_price')|escape|default:0} {$nomenclature->get('last_delivery_price_currency')|escape|default:$default_currency}
                  <input type="hidden" name="last_delivery_price" id="last_delivery_price" value="{$nomenclature->get('last_delivery_price')|escape}" />
                  <input type="hidden" name="last_delivery_price_currency" id="last_delivery_price_currency" value="{$nomenclature->get('last_delivery_price_currency')|escape|default:$default_currency}" />
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'average_weighted_delivery_price'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_average_weighted_delivery_price"><label for="average_weighted_delivery_price"{if $messages->getErrors('average_weighted_delivery_price')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="unrequired">&nbsp;</td>
              <td>
                {if $layout.edit}
                  <input type="text" class="txtbox hright" name="average_weighted_delivery_price" id="average_weighted_delivery_price" style="width: 110px;" onkeypress="return changeKey(this, event, insertOnlyFloats);" value="{$nomenclature->get('average_weighted_delivery_price')}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                  {include file=`$theme->templatesDir`input_dropdown.html
                           required=1
                           standalone=true
                           width='80'
                           name=average_weighted_delivery_price_currency
                           options=$currencies
                           value=$nomenclature->get('average_weighted_delivery_price_currency')
                           label=#nomenclatures_average_weighted_delivery_price_currency#}
                {else}
                  {$nomenclature->get('average_weighted_delivery_price')|escape|default:0} {$nomenclature->get('average_weighted_delivery_price_currency')|escape|default:$default_currency}
                  <input type="hidden" name="average_weighted_delivery_price" id="average_weighted_delivery_price" value="{$nomenclature->get('average_weighted_delivery_price')|escape}" />
                  <input type="hidden" name="average_weighted_delivery_price_currency" id="average_weighted_delivery_price_currency" value="{$nomenclature->get('average_weighted_delivery_price_currency')|escape|default:$default_currency}" />
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'sell_price'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_sell_price"><label for="sell_price"{if $messages->getErrors('sell_price')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="unrequired">&nbsp;</td>
              <td>
                {if $layout.edit}
                  <input type="text" class="txtbox hright" name="sell_price" id="sell_price" style="width: 110px;" onkeypress="return changeKey(this, event, insertOnlyFloats);" value="{$nomenclature->get('sell_price')}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
                  {include file=`$theme->templatesDir`input_dropdown.html
                           required=1
                           standalone=true
                           width='80'
                           name=sell_price_currency
                           options=$currencies
                           value=$nomenclature->get('sell_price_currency')
                           label=#nomenclatures_sell_price_currency#}
                {else}
                  {$nomenclature->get('sell_price')|escape|default:0} {$nomenclature->get('sell_price_currency')|escape|default:$default_currency}
                  <input type="hidden" name="sell_price" id="sell_price" value="{$nomenclature->get('sell_price')|escape}" />
                  <input type="hidden" name="sell_price_currency" id="sell_price_currency" value="{$nomenclature->get('sell_price_currency')|escape|default:$default_currency}" />
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'subtype'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_subtype"><label for="subtype"{if $messages->getErrors('subtype')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="required">{#required#}</td>
              <td>
                {if $layout.edit}
                  {if $nomenclature_type->get('keyword') eq 'system'}
                    <input type="radio" name="subtype" id="subtype_advance" value="advance"{if $nomenclature->get('subtype') eq 'advance' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'advance'} checked="checked"{/if} /><label for="subtype_advance">{#nomenclatures_subtype_advance#|escape}</label>
                    <input type="radio" name="subtype" id="subtype_discount" value="discount"{if $nomenclature->get('subtype') eq 'discount' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'discount'} checked="checked"{/if} /><label for="subtype_discount">{#nomenclatures_subtype_discount#|escape}</label>
                    <input type="radio" name="subtype" id="subtype_surplus" value="surplus"{if $nomenclature->get('subtype') eq 'surplus' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'surplus'} checked="checked"{/if} /><label for="subtype_surplus">{#nomenclatures_subtype_surplus#|escape}</label>
                  {else}
                    <input type="radio" name="subtype" id="subtype_commodity" value="commodity"{if $nomenclature->get('subtype') eq 'commodity' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'commodity'} checked="checked"{/if} onclick="$('nomenclature_batch_options').style.display = '';" /><label for="subtype_commodity">{#nomenclatures_subtype_commodity#|escape}</label>
                    <input type="radio" name="subtype" id="subtype_service" value="service"{if $nomenclature->get('subtype') eq 'service' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'service'} checked="checked"{/if} onclick="$('nomenclature_batch_options').style.display = 'none';" /><label for="subtype_service">{#nomenclatures_subtype_service#|escape}</label>
                    <input type="radio" name="subtype" id="subtype_other" value="other"{if $nomenclature->get('subtype') eq 'other' || !$nomenclature->isDefined('subtype') && $nomenclature_type->get('subtype') eq 'other'} checked="checked"{/if} onclick="$('nomenclature_batch_options').style.display = 'none';" /><label for="subtype_other">{#nomenclatures_subtype_other#|escape}</label>
                  {/if}
                {else}
                  {capture assign='subtype_label'}nomenclatures_subtype_{$nomenclature->get('subtype')}{/capture}
                  {$smarty.config.$subtype_label|escape}
                  <input type="hidden" name="subtype" id="subtype" value="{$nomenclature->get('subtype')}" />
                {/if}
              </td>
            </tr>
            {elseif $lkey eq 'batch_options'}
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view || $nomenclature->get('subtype') ne 'commodity'} style="display: none;"{/if}>
              <td class="labelbox"><a name="error_batch_options"><label for="has_batch"{if $messages->getErrors('has_batch')} class="error"{/if}>{help label_content=$layout.name|escape text_content=$layout.description|escape}</label></a></td>
              <td class="unrequired">&nbsp;</td>
              <td>
                {if $layout.edit}
                  {assign var=bo_hidden value=false}
                {else}
                  {if $nomenclature->get('has_batch')}
                    {#nomenclatures_has_batch#}
                    {if $nomenclature->get('has_serial')}/ {#nomenclatures_has_serial#}{/if}
                    {if $nomenclature->get('has_expire')}/ {#nomenclatures_has_expire#}{/if}
                    {if $nomenclature->get('has_batch_code')}/ {#nomenclatures_has_batch_code#}{/if}
                  {else}
                    {#no#}
                  {/if}
                  {assign var=bo_hidden value=true}
                {/if}
                {capture assign=clickf}if (this.checked && !$('has_batch').checked) $('has_batch').checked = true;{/capture}
                {capture assign=clickf1}if (!this.checked) {ldelim}$('has_serial').checked = false;$('has_expire').checked = false;$('has_batch_code').checked = false;{rdelim}{/capture}
                {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclature->get('has_batch') label=#nomenclatures_has_batch# name=has_batch onclick=$clickf1 hidden=$bo_hidden}
                {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclature->get('has_serial') label=#nomenclatures_has_serial# name=has_serial onclick=$clickf hidden=$bo_hidden}
                {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclature->get('has_expire') label=#nomenclatures_has_expire# name=has_expire onclick=$clickf hidden=$bo_hidden}
                {include file=input_checkbox.html option_value=1 standalone=true no_br=true value=$nomenclature->get('has_batch_code') label=#nomenclatures_has_batch_code# name=has_batch_code onclick=$clickf hidden=$bo_hidden}
              </td>
            </tr>
            {elseif $layout.view && array_key_exists($layout.id, $layouts_vars)}
            <!-- Nomenclature Additional Vars -->
              {assign var='layout_id' value=$layout.id}
              {assign var='vars' value=$layouts_vars.$layout_id}
              {if $layout.id}
              <tr id="layout_{$layout.id}_box"{if $layout.cookie eq 'off'} style="display: none;"{/if}>
                <td class="nopadding" colspan="3">
                  <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {/if}
              {foreach name='j' from=$vars item='var'}
                {if $var.type}
                  {strip}
                    {capture assign='info'}{if $var.help}{$var.help}{else}{$var.label}{/if}{/capture}
                    {if !empty($var.js_filter)}
                      {assign var='restrict' value=$var.js_filter}
                    {else}
                      {assign var='restrict' value=''}
                    {/if}
                  {/strip}
                  {if $layout.edit}
                    {* var=$var SHOULD BE REMOVED LATER *}
                    {include file="input_`$var.type`.html"
                      var=$var
                      standalone=false
                      var_id=$var.id
                      name=$var.name
                      custom_id=$var.custom_id
                      label=$var.label
                      help=$var.help
                      back_label=$var.back_label
                      back_label_style=$var.back_label_style
                      value=$var.value
                      value_id=$var.value_id
                      options=$var.options
                      optgroups=$var.optgroups
                      option_value=$var.option_value
                      first_option_label=$var.first_option_label
                      onclick=$var.onclick
                      on_change=$var.on_change
                      sequences=$var.sequences
                      check=$var.check
                      scrollable=$var.scrollable
                      calculate=$var.calculate
                      readonly=$var.readonly
                      source=$var.source
                      onchange=$var.onchange
                      map_params=$var.map_params
                      width=$var.width
                      hidden=$var.hidden
                      really_required=$var.required
                      required=$var.required
                      disabled=$var.disabled
                      options_align=$var.options_align
                      autocomplete=$var.autocomplete
                      js_methods=$var.js_methods
                      restrict=$restrict
                      deleteid=$var.deleteid
                      show_placeholder=$var.show_placeholder
                      text_align=$var.text_align
                      custom_class=$var.custom_class
                    }
                  {else}
                    {include file="view_`$var.type`.html"}
                  {/if}
                {/if}
              {/foreach}
              {if $layout.id}
                  </table>
                </td>
              </tr>
              {/if}
            {elseif $lkey eq 'categories'}
            <!-- Nomenclature categories -->
              {include file=`$templatesDir`_categories.html}
            {elseif $lkey eq 'attachments'}
            <!-- Nomenclature attachments -->
            <tr id="nomenclature_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
              <td colspan="3" class="nopadding">
                {include file=`$templatesDir`_attachments.html}
              </td>
            </tr>
            {/if}
          {/foreach}
            <tr>
              <td colspan="3">&nbsp;</td>
            </tr>
            <tr>
              <td colspan="3">
                {strip}
                  {if $nomenclature->get('buttons')}
                    {foreach from=$nomenclature->get('buttons') item='button'}
                      {include file=`$theme->templatesDir`input_button.html
                              label=$button.label
                              standalone=true
                              name=$button.name
                              source=$button.source
                              disabled=$button.disabled
                              hidden=$button.hidden
                              width=$button.width
                              height=$button.height}
                    {/foreach}
                  {/if}

                  <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>
                  {if !$exclude || !preg_match('#cancel#', $exclude)}
                    {include file=`$theme->templatesDir`cancel_button.html}
                  {/if}
                {/strip}
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
    {include file=`$theme->templatesDir`system_settings_box.html object=$nomenclature exclude='is_portal'}
  </form>
  {/if}
</div>
