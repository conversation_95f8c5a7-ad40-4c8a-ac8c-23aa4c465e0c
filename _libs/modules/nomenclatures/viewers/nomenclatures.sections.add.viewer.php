<?php

class Nomenclatures_Sections_Add_Viewer extends Viewer {
    public $template = 'sections_add.html';

    public function prepare() {
        $this->model = $this->registry['nomenclatures_section'];
        $this->data['nomenclatures_section'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures');
        $href = sprintf('%s=%s&amp;type=&amp;type_section=',
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_sections');
        $href = sprintf('%s=%s&amp;%s=%s',
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_sections_add');
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s',
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
