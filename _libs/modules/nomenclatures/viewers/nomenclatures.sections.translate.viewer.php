<?php

class Nomenclatures_Sections_Translate_Viewer extends Viewer {
    public $template = 'sections_translate.html';

    public function prepare() {
        $this->model = $this->registry['nomenclatures_section'];
        $this->data['nomenclatures_section'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('ns.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Nomenclatures_Sections::searchOne($this->registry, $filters);
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures');
        $href = sprintf('%s=%s&amp;type=&amp;type_section=',
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_sections');
        $href = sprintf('%s=%s&amp;%s=%s',
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('nomenclatures_sections_translate');
        $href = sprintf('%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s&amp;model_lang=%s',
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'), $this->model->get('model_lang'));

        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
