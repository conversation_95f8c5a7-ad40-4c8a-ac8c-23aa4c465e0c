<?php

class Nomenclatures_Distribute_Viewer extends Viewer {
    public $template = 'distribute.html';

    public function prepare() {
        $registry = &$this->registry;
        $request = &$registry['request'];

        $this->model = $this->registry['nomenclature'];
        $this->data['nomenclature'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        require_once(PH_MODULES_DIR . 'finance/models/finance.dropdown.php');
        $params = array($this->registry,
                        'lang' => $this->model->get('model_lang'),
                        'leaves_active_only' => 1);
        $params['type'] = 'income';
        $analysis_items_income = Finance_Dropdown::getFinanceAnalysisItems($params);
        $this->data['analysis_items_income'] = $analysis_items_income;
        $params['type'] = 'expense';
        $analysis_items_expense = Finance_Dropdown::getFinanceAnalysisItems($params);
        $this->data['analysis_items_expense'] = $analysis_items_expense;

        if (!$request->isPost()) {
            $income_values = array();
            $expense_values = array();

            list($income_values, $expense_values) = $this->model->getItemsDefaultDistributionValues();

            $this->model->set('items_income', (!empty($income_values['items']) ? $income_values['items'] : array()), true);
            $this->model->set('percentage_income', (!empty($income_values['percentage']) ? $income_values['percentage'] : array()), true);
            $this->model->set('items_expense', (!empty($expense_values['items']) ? $expense_values['items'] : array()), true);
            $this->model->set('percentage_expense', (!empty($expense_values['percentage']) ? $expense_values['percentage'] : array()), true);
        }

        $precision = $this->registry['config']->getParam('precision', 'finance_analysis_percentage');

        $this->data['distributed_percentage_income'] = $this->model->get('percentage_income') ?
                                                       round(array_sum($this->model->get('percentage_income')), $precision) :
                                                       0;
        $this->data['remaining_percentage_income'] = round(100 - $this->data['distributed_percentage_income'], $precision);

        $this->data['distributed_percentage_expense'] = $this->model->get('percentage_expense') ?
                                                        round(array_sum($this->model->get('percentage_expense')), $precision) :
                                                        0;
        $this->data['remaining_percentage_expense'] = round(100 - $this->data['distributed_percentage_expense'], $precision);

        $this->prepareTitleBar();
        $this->prepareTranslations();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('nomenclatures_distribute'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
