<?php

class Minitasks_Search_Viewer extends Viewer {
    public $template = 'list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'minitasks.factory.php';

        if ($this->registry['request']->get('session_param_prefix')) {
            $session_param_prefix = $this->registry['request']->get('session_param_prefix') . '_';
            $filters = Minitasks::saveSearchParams($this->registry, array(), $session_param_prefix);
            // sets the display param from the Search session
            // $display = $this->registry['session']->get('display', 'search_minitask');
            $this->registry['session']->set('search_minitask', $this->registry['session']->get($session_param_prefix . 'minitask'), '', true);
            // $this->registry['session']->set('display', $display, 'search_minitask', true);
            // $filters['display'] = $display;
        } else {
            $filters = Minitasks::saveSearchParams($this->registry, array(), 'search_');
        }

        list ($minitasks, $pagination) = Minitasks::pagedSearch($this->registry, $filters);

        $this->data['minitasks'] = $minitasks;
        $this->data['pagination'] = $pagination;

        require_once PH_MODULES_DIR . 'minitasks/models/minitasks.dropdown.php';

        //prepare statuses
        $statuses = Minitasks_Dropdown::getStatuses(array($this->registry));
        foreach ($statuses as $status) {
            if ($status['option_value'] != 'opened') {
                $_options_statuses[] = array('id' => $status['option_value'],
                                             'name' => $status['label'],
                                             'requires_comment' => 'without_comment');
            }
        }
        $this->data['statuses'] = $_options_statuses;

        // prepare predefined deadlines
        $predefined_deadlines = Minitasks_Dropdown::getPredefinedDeadlines(array($this->registry));
        $this->data['predefined_deadlines'] = $predefined_deadlines;

        // prepare model types for records
        $records_model_types = Minitasks_Dropdown::getRecordsModelTypes(array($this->registry));
        $this->data['records_model_types'] = $records_model_types;

        // empty model to be used in templates in add mode
        $currentUser = $this->registry['currentUser'];
        $empty_params = array (
            'assigned_to' => $currentUser->get('id'),
            'assigned_to_code' => $currentUser->get('code'),
            'assigned_to_name' => $currentUser->get('firstname') . ' ' . $currentUser->get('lastname')
        );
        $empty_minitask = new Minitask($this->registry, $empty_params);
        $empty_minitask->sanitize();
        $this->data['empty_minitask'] = $empty_minitask;

        //prepare filters for customers autocompleter
        require_once PH_MODULES_DIR . 'minitasks/models/minitasks.factory.php';
        $cstm_types = Minitasks::getRelatedCustomersTypes($this->registry);
        $cstm_types = $cstm_types ? implode(', ', $cstm_types) : '0';
        $this->data['customer_autocomplete_filters'] = array('<type>' => $cstm_types);

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();

        if ($this->theme->isModern()) {
            $this->scriptsDir = PH_MODULES_DIR . 'minitasks/view/javascript/';
            $this->scriptsUrl = PH_MODULES_URL . 'minitasks/view/javascript/';
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('minitasks');
        $this->data['title'] = $title;
    }
}

?>
