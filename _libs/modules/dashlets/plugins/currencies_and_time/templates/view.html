{if $dashlet}
<h1>{$title}</h1>

<div class="form_container">
{include file=`$theme->templatesDir`actions_box.html}
<table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="1" cellpadding="3" border="0" class="t_table">
      <!-- FIELDS FOR ALL PLUGINS -->
        <tr>
          <td class="labelbox">{help label='dashlet_for'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
              {$module_name_i18n}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label_content=#plugin_name# text_content=#help_plugin_name#}</td>
          <td class="required">&nbsp;</td>
          <td>{$dashlet->get('name')}</td></tr>
        <tr>
          <td class="labelbox">{help label_content=#plugin_description# text_content=#help_plugin_description#}</td>
          <td class="required">&nbsp;</td>
          <td>{$dashlet->get('description')|mb_wordwrap|url2href}</td></tr>
        {if $dashlet->get('default') eq '1'}
        <tr>
          <td class="labelbox">{help label='default'}</td>
          <td class="unrequired">&nbsp;</td>
          <td colspan="2"><img src="{$theme->imagesUrl}small/check_yes.png" alt="{#yes#}" title="{#yes#}" /></td>
        </tr>
        {/if}
        <tr>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>
            {include file="input_checkbox.html"
                standalone=true
                name='full_width'
                required=0
                custom_id='full_width'
                label=#plugin_full_width#
                help=#help_plugin_full_width#
                value=$dashlet->get('full_width')
                onclick=""
                disabled=true
                option_value=1
            }
          </td>
        </tr>
        <tr>
          <td colspan="3">
        <!-- END OF FIELDS FOR ALL PLUGINS -->
        <!-- PLUGIN SPECIFIC FIELDS -->
        {include file=`$templatesDir`_plugin_fields.html}
        <!-- END OF PLUGIN SPECIFIC FIELDS -->
          </td>
       </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td>
      <div class="t_footer">&nbsp;</div>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`system_settings_box.html object=$dashlet}
{*<table class="t_table">
  <tr>
    <td>&nbsp;</td>
  </tr>
  <tr>
    <td style="padding-left:20px">
      <button type="submit" name="editButton1" class="button">{#edit#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
    </td>
  </tr>
  <tr>
    <td>&nbsp;</td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html} *}
</div>
{/if}