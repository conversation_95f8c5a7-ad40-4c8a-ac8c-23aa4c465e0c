{assign var='custom_filters' value=$dashlet->get('filters')}
{assign var='employees_value' value=$custom_filters.employees}
<table cellpadding="0" cellspacing="0" border="0" class="t_table" id="custom_fields" style="width:650px;">
  <tr>
    <td class="labelbox">
      {help label_content=#plugin_multiple_check_in_employees# text_content=''}
    </td>
    <td class="required">{#required#}</td>
    <td style="padding-left:10px">
    {if $action ne 'view'}
      {include file=`$theme->templatesDir`input_checkbox_group.html
               options=$employees_options
               value=$employees_value
               name='employees'
               standalone=true
      }
    {else}
      {foreach from=$employees_options item='employee' key='key_id'}
        {if in_array($employee.option_value, $employees_value)}
          {$employee.label|escape}<br />
        {/if}
      {/foreach}
    {/if}
    </td>
  </tr>
  <tr>
    <td colspan="3">
      &nbsp;
    </td>
  </tr>
</table>