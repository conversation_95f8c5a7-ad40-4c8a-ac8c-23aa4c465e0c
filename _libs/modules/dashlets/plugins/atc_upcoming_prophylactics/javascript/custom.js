/**
 * Object for the current dashlet
 */
var dashletATCUpcomingProphylactics = {
    /**
     * prepare some defaults for the dashlet
     *
     * @param dashlet_id - the id of the dashlet
     * @param base_url - base URL used for AJAX requests
     * @returns {Boolean}
     */
    setDefaults: function (dashlet_id, base_url) {
        // Set the dashlet ID
        this.id = dashlet_id;

        this.container_list = $('dashlet_' + this.id + '_custom');

        this.date_from = $('date_from');
        this.date_from_formatted = $('date_from_formatted');
        this.date_to = $('date_to');
        this.date_to_formatted = $('date_to_formatted');

        this.confirm_container = $('dashlet_' + this.id + '_confirm_container');

        this.errors_container = $('dashlet_' + this.id + '_errors_container');
        this.messages_container = $('dashlet_' + this.id + '_messages_container');
        this.warnings_container = $('dashlet_' + this.id + '_warnings_container');

        this.base_url = base_url;

        return true;
    },

    /**
     * Object for collectin the selected records
     */
    selected: {},

    /**
     * Object for collectin the unselected records
     */
    unselected: {},

    /**
     * Flag to check if the "all" checkbox is checked
     */
    selected_all: false,

    /**
     * Record the clicked checkbox
     *
     * @param element - the checkbox
     */
    click: function (element) {
        var element_value = element.value.split('|');
        var document_id = element_value[0];
        var row_num = element_value[1];
        if (element.checked) {
            this.selected[document_id] = row_num;
            delete this.unselected[document_id];
        } else {
            delete this.selected[document_id];
            if (this.selected_all || Object.values(this.unselected).length > 0) {
                this.selected_all = false;
                $('dashlet_' + this.id + '_documents_all').checked = false;
                this.unselected[document_id] = row_num;
            }
        }
    },

    /**
     * Record that the "all" checkbox is clicked
     *
     * @param element - the "all" checkbox
     */
    clickAll: function (element) {
        this.selected = {};
        this.unselected = {};
        var checkboxes = $$('[id^=dashlet_' + this.id + '_document_]');
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = element.checked;
        }
        this.selected_all = element.checked;
    },

    /**
     * Check the checkboxes which are recorded as checked
     */
    checkSelected: function () {
        if (this.selected_all) {
            var documents_all = $('dashlet_' + this.id + '_documents_all');
            documents_all.checked = this.selected_all;
            this.clickAll(documents_all);
        } else if (Object.values(this.unselected).length > 0) {
            var checkboxes = $$('[id^=dashlet_' + this.id + '_document_]');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = true;
            }
            for (var i in this.unselected) {
                var checkbox = $('dashlet_' + this.id + '_document_' + i);
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        } else {
            for (var i in this.selected) {
                var checkbox = $('dashlet_' + this.id + '_document_' + i);
                if (checkbox) {
                    checkbox.checked = true;
                }
            }
        }
    },

    /**
     * Set message
     *
     * @param type - errors, messages or warnings
     * @param messages - array of messages
     */
    _setMessages: function (type, messages) {
        if (messages.length > 0) {
            messages = messages.join('<br/>');
            switch (type) {
                case 'errors':
                    this.errors_container.innerHTML = messages;
                    this.errors_container.parentNode.style.display = '';
                    break;
                case 'warnings':
                    this.warnings_container.innerHTML = messages;
                    this.warnings_container.parentNode.style.display = '';
                    break;
                default:
                    this.messages_container.innerHTML = messages;
                    this.messages_container.parentNode.style.display = '';
            }
        }
    },

    /**
     * Clear all messages
     */
    _clearMessages: function () {
        this.errors_container.innerHTML = '';
        this.errors_container.parentNode.style.display = 'none';
        this.messages_container.innerHTML = '';
        this.messages_container.parentNode.style.display = 'none';
        this.warnings_container.innerHTML = '';
        this.warnings_container.parentNode.style.display = 'none';
    },

    /**
     * Search for documents
     *
     * @param element
     */
    search: function (element) {
        Effect.Center('loading');
        Effect.Appear('loading');

        // Clear the selected records
        this.selected = {};
        this.unselected = {};
        this.selected_all = false;

        // Clear all messages
        this._clearMessages();

        var opt = {
            method: 'post',
            parameters: Form.serialize(element.form),
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var ajax_result = eval('(' + (t.responseText) + ')');

                // Set messages
                if (typeof ajax_result.errors !== 'undefined' && ajax_result.errors.length > 0) {
                    dashletATCUpcomingProphylactics._setMessages('errors', ajax_result.errors);
                }
                if (typeof ajax_result.messages !== 'undefined' && ajax_result.messages.length > 0) {
                    dashletATCUpcomingProphylactics._setMessages('messages', ajax_result.messages);
                }
                if (typeof ajax_result.warnings !== 'undefined' && ajax_result.warnings.length > 0) {
                    dashletATCUpcomingProphylactics._setMessages('warnings', ajax_result.warnings);
                }
                // Set result
                if (typeof ajax_result.list !== 'undefined' && ajax_result.list != '') {
                    dashletATCUpcomingProphylactics.container_list.innerHTML = ajax_result.list;
                    dashletATCUpcomingProphylactics.confirm_container.style.display = '';
                } else {
                    dashletATCUpcomingProphylactics.container_list.innerHTML = '';
                    dashletATCUpcomingProphylactics.confirm_container.style.display = 'none';
                }

                // Set default dates
                if (typeof ajax_result.date_from !== 'undefined' && typeof ajax_result.date_from_formatted !== 'undefined') {
                    dashletATCUpcomingProphylactics.date_from.value = ajax_result.date_from;
                    dashletATCUpcomingProphylactics.date_from_formatted.value = ajax_result.date_from_formatted;
                }
                if (typeof ajax_result.date_to !== 'undefined' && typeof ajax_result.date_to_formatted !== 'undefined') {
                    dashletATCUpcomingProphylactics.date_to.value = ajax_result.date_to;
                    dashletATCUpcomingProphylactics.date_to_formatted.value = ajax_result.date_to_formatted;
                }

                // Load scripts
                var scripts = dashletATCUpcomingProphylactics.container_list.getElementsByTagName('script');
                for (var j = 0; j < scripts.length; j++) {
                    ajaxLoadJS(scripts[j]);
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
                return false;
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
                return false;
            }
        };

        var url = this.base_url + 'search';

        new Ajax.Request(url, opt);
    },

    /**
     * Edit documents
     *
     * @param element
     */
    confirm: function (element) {
        Effect.Center('loading');
        Effect.Appear('loading');

        // Clear all messages
        this._clearMessages();

        // Collect some params for the AJAX request
        var prms = Form.serialize(element.form);
        if (Object.values(this.selected).length > 0) {
            if (prms) {
                prms += '&';
            }
            prms += 'documents_ids=' + encodeURIComponent(Object.toJSON(this.selected));
        }
        if (Object.values(this.unselected).length > 0) {
            if (prms) {
                prms += '&';
            }
            prms += 'documents_ids_unselected=' + encodeURIComponent(Object.toJSON(this.unselected));
        }

        var opt = {
            method: 'post',
            parameters: prms,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var ajax_result = eval('(' + (t.responseText) + ')');

                // Set messages
                if (typeof ajax_result.errors !== 'undefined' && ajax_result.errors.length > 0) {
                    dashletATCUpcomingProphylactics._setMessages('errors', ajax_result.errors);
                }
                if (typeof ajax_result.messages !== 'undefined' && ajax_result.messages.length > 0) {
                    dashletATCUpcomingProphylactics._setMessages('messages', ajax_result.messages);
                }
                if (typeof ajax_result.warnings !== 'undefined' && ajax_result.warnings.length > 0) {
                    dashletATCUpcomingProphylactics._setMessages('warnings', ajax_result.warnings);
                }

                // If no errors after the confirmation
                if (ajax_result.confirmation_is_successfull){
                    // Clear the selected records
                    dashletATCUpcomingProphylactics.selected = {};
                    dashletATCUpcomingProphylactics.unselected = {};
                    dashletATCUpcomingProphylactics.selected_all = false;

                    // Set result
                    if (typeof ajax_result.list !== 'undefined' && ajax_result.list != '') {
                        dashletATCUpcomingProphylactics.container_list.innerHTML = ajax_result.list;
                        dashletATCUpcomingProphylactics.confirm_container.style.display = '';
                    } else {
                        dashletATCUpcomingProphylactics.container_list.innerHTML = '';
                        dashletATCUpcomingProphylactics.confirm_container.style.display = 'none';
                    }
                }

                // Set default dates (this is not necessary here)
                if (typeof ajax_result.date_from !== 'undefined' && typeof ajax_result.date_from_formatted !== 'undefined') {
                    dashletATCUpcomingProphylactics.date_from.value = ajax_result.date_from;
                    dashletATCUpcomingProphylactics.date_from_formatted.value = ajax_result.date_from_formatted;
                }
                if (typeof ajax_result.date_to !== 'undefined' && typeof ajax_result.date_to_formatted !== 'undefined') {
                    dashletATCUpcomingProphylactics.date_to.value = ajax_result.date_to;
                    dashletATCUpcomingProphylactics.date_to_formatted.value = ajax_result.date_to_formatted;
                }

                // Load scripts
                var scripts = dashletATCUpcomingProphylactics.container_list.getElementsByTagName('script');
                for (var j = 0; j < scripts.length; j++) {
                    ajaxLoadJS(scripts[j]);
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
                return false;
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
                return false;
            }
        };

        var url = this.base_url + 'confirm';

        new Ajax.Request(url, opt);
    }
};
