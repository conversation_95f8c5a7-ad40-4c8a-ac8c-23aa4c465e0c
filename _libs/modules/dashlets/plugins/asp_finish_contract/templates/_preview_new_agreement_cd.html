
{if !empty($result)}
{capture assign=right_invoice}finance_incomes_reasons{$smarty.const.PH_FINANCE_TYPE_INVOICE}{/capture}
{if $currentUser->checkRights($right_invoice,'issue_date') || $currentUser->checkRights($right_invoice,'future_issue_date')}
  {assign var=readonly_dates value=0}
  {if $currentUser->checkRights($right_invoice,'issue_date')}
    {assign var='disallow_date_before' value=0}
  {else}
    {assign var='disallow_date_before' value=1}
  {/if}
  {if $currentUser->checkRights($right_invoice,'future_issue_date')}
    {assign var='disallow_date_after' value=0}
  {else}
    {assign var='disallow_date_after' value=1}
  {/if}
{else}
  {assign var=readonly_dates value=1}
{/if}
<form action="" method="post" name="issue_CDI" class="t_table" style="padding: 10px;">
<input type="hidden" name="model_id" value="{$model_id}" id="model_id" />
<input type="hidden" name="dashlet" value="{$dashlet}" id="dashlet" />
{assign var=headers_show value=true}
{assign var=has_previous value=false}
{foreach from=$result.old_invoices item=invoice name=invoices}
  {if $invoice->get('debit')}
    {assign var=debit value=$invoice->get('debit')}
    {assign var=has_debit value=true}
    {if $headers_show}
      {assign var=headers_show value=false}
      {assign var=has_previous value=true}
      <br />
      <div class="red">{#message_contracts_debits_amount#}</div>
      <table border="0" cellpadding="2" cellspacing="0" class="t_grouping_table t_table bordered_cell" style="margin: 5px 0;">
        <tr>
          <th class="t_border" style="width:50px">
            <div class="t_caption3_title">{#contracts_issue#}</div>
            <div style="text-align: right;"><input type="checkbox" checked="checked" onclick="checkAll(this, 'debits', 'class')" /></div>
          </th>
          <th class="t_border" style="width:70px">
            <div class="t_caption3_title">{#contracts_auto_send#}</div>
            <div style="text-align: center;"><input type="checkbox" onclick="checkAll(this, 'debits_send', 'class')" /></div>
          </th>
          <th class="t_border"><div class="t_caption3_title">{#contracts_for_invoice#}</div></th>
          <th class="t_border" style="width:120px"><div class="t_caption3_title">{#contracts_cd_reason#}&nbsp;<span class="required">{#required#}</span></div></th>
          <th class="t_border" style="width:100px"><div class="t_caption3_title">{#contracts_amount#}</div></th>
          <th class="t_border" style="width:120px"><div class="t_caption3_title">{#contracts_fiscal_event_date#}</div></th>
          <th style="width:120px"><div class="t_caption3_title">{#contracts_issue_date#}</div></th>
        </tr>
    {/if}
    <tr class="{cycle values='t_odd,t_even'} pointer">
      <td class="t_border hright">
        <div class="switch_expand" id="switch_debit_{$invoice->get('id')}" onclick="if($('debit_{$invoice->get('id')}').style.display == 'none') {ldelim}$('debit_{$invoice->get('id')}').style.display = ''; $('switch_debit_{$invoice->get('id')}').className = 'switch_collapse';{rdelim} else {ldelim}$('debit_{$invoice->get('id')}').style.display = 'none'; $('switch_debit_{$invoice->get('id')}').className = 'switch_expand';{rdelim}"></div>
        <input type="checkbox" name="debit[{$invoice->get('id')}]" value="1" checked="checked" title="{#contracts_issue#|escape}" class="debits" />
      </td>
      <td class="t_border hcenter" style="width:50px">
        <input type="checkbox" name="debit_send[{$invoice->get('id')}]" value="1" {if $debit->get('auto_send')}checked="checked"{/if} title="{#contracts_auto_send#|escape}" class="debits_send" />
      </td>
      <td class="t_border"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$invoice->get('id')}" target="_blank">[{$invoice->get('num')}] {$invoice->get('name')|escape}</a></td>
      <td class="t_border" style="width:120px">
        {if $agreement_subtype eq 'annex'}
          {assign var=current_code value='dnrannex'}
          {array assign=hidden_reasons 1='dnragreement' 2='cnrannex' 3='cnragreement'}
        {else}
          {assign var=current_code value='dnragreement'}
          {array assign=hidden_reasons 1='dnrannex' 2='cnrannex' 3='cnragreement'}
        {/if}
        {assign var=rcount value=0}
        {foreach from=$cd_reasons item=reason}
          {if $reason.code eq $current_code || !in_array($reason.code, $hidden_reasons) || empty($reason.code)}
            {assign var=rcount value=$rcount+1}
          {/if}
        {/foreach}
        <select class="selbox{if $rcount gt 1} undefined{/if}" name="debit_reason[{$invoice->get('id')}]" id="debit_reason_{$invoice->get('id')}" title="{#contracts_cd_reason#|escape}" style="width: 100%;" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);">
          {if $rcount gt 1}
            <option value="" class="undefined">[{#please_select#|escape}]</option>
          {/if}
        {foreach from=$cd_reasons item=reason}
          {if $reason.code eq $current_code || !in_array($reason.code, $hidden_reasons) || empty($reason.code)}
            <option value="{$reason.option_value}">{$reason.label}</option>
          {/if}
        {/foreach}
        </select>
      </td>
      <td class="t_border hright" style="width:100px">{$debit->get('total_with_vat')} {$debit->get('currency')}</td>
      <td class="t_border" style="width:120px">
        {include file="input_date.html"
            standalone=true
            name=debit_fiscal_date
            index=$invoice->get('id')
            eq_indexes=1
            required=1
            label=#contracts_fiscal_event_date#
            value=$smarty.now|date_format:'%Y-%m-%d'
        }
      </td>
      <td style="width:120px">
        {include file="input_date.html"
            standalone=true
            name=debit_issue_date
            index=$invoice->get('id')
            eq_indexes=1
            required=1
            label=#contracts_issue_date#
            value=$smarty.now|date_format:'%Y-%m-%d'
            readonly=$readonly_dates
            disallow_date_after=$disallow_date_after
            disallow_date_before=$disallow_date_before
        }
      </td>
    </tr>
    <tr style="display: none;" id="debit_{$invoice->get('id')}">
      <td colspan="7">
        {include file="_gt2_view.html" table=$debit->get('grouping_table_2') hide_script=1}
      </td>
    </tr>
  {/if}
  {if $smarty.foreach.invoices.last && !$headers_show}
    </table>
  {/if}
{/foreach}
{if $has_debit}
<div class="floatl" style="white-space: nowrap;">{#contracts_email_template#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=debit_email
    options=$debit_emails
    standalone=true
    required=1
  }
</div>
<div class="floatl" style="white-space: nowrap; padding-left: 40px">{#contracts_pattern#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=debit_pattern
    options=$debit_patterns
    standalone=true
    required=1
  }
</div>
<div class="clear"></div>
{/if}

{assign var=headers_show value=true}
{foreach from=$result.old_invoices item=invoice name=invoices}
  {if $invoice->get('credit')}
    {assign var=credit value=$invoice->get('credit')}
    {assign var=has_credit value=true}
    {if $headers_show}
      {assign var=headers_show value=false}
      {if $has_previous}<br /><br /><br />{/if}
      {assign var=has_previous value=true}
      <div class="red">{#message_contracts_credits_amount#}</div>
      <table border="0" cellpadding="2" cellspacing="0" class="t_grouping_table t_table bordered_cell" style="margin: 5px 0;">
        <tr>
          <th class="t_border" style="width:50px">
            <div class="t_caption3_title">{#contracts_issue#}</div>
            <div style="text-align: right;"><input type="checkbox" checked="checked" onclick="checkAll(this, 'credits', 'class')" /></div>
          </th>
          <th class="t_border" style="width:70px">
            <div class="t_caption3_title">{#contracts_auto_send#}</div>
            <div style="text-align: center;"><input type="checkbox" onclick="checkAll(this, 'credits_send', 'class')" /></div>
          </th>
          <th class="t_border"><div class="t_caption3_title">{#contracts_for_invoice#}</div></th>
          <th class="t_border" style="width:120px"><div class="t_caption3_title">{#contracts_cd_reason#}&nbsp;<span class="required">{#required#}</span></div></th>
          <th class="t_border" style="width:100px"><div class="t_caption3_title">{#contracts_amount#}</div></th>
          <th class="t_border" style="width:120px"><div class="t_caption3_title">{#contracts_fiscal_event_date#}</div></th>
          <th style="width:120px"><div class="t_caption3_title">{#contracts_issue_date#}</div></th>
        </tr>
    {/if}
    <tr class="{cycle values='t_odd,t_even'} pointer">
      <td class="t_border hright">
        <div class="switch_expand" id="switch_credit_{$invoice->get('id')}" onclick="if($('credit_{$invoice->get('id')}').style.display == 'none') {ldelim}$('credit_{$invoice->get('id')}').style.display = ''; $('switch_credit_{$invoice->get('id')}').className = 'switch_collapse';{rdelim} else {ldelim}$('credit_{$invoice->get('id')}').style.display = 'none'; $('switch_credit_{$invoice->get('id')}').className = 'switch_expand';{rdelim}"></div>
        <input type="checkbox" name="credit[{$invoice->get('id')}]" value="1" checked="checked" title="{#contracts_issue#|escape}" class="credits" />
      </td>
      <td class="t_border hcenter" style="width:50px">
        <input type="checkbox" name="credit_send[{$invoice->get('id')}]" value="1" {if $credit->get('auto_send')}checked="checked"{/if} title="{#contracts_auto_send#|escape}" class="credits_send" />
      </td>
      <td class="t_border"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=incomes_reasons&amp;incomes_reasons=view&amp;view={$invoice->get('id')}" target="_blank">[{$invoice->get('num')}] {$invoice->get('name')|escape}</a></td>
      <td class="t_border" style="width:120px">
        {if $agreement_subtype eq 'annex'}
          {assign var=current_code value='dnrannex'}
          {array assign=hidden_reasons 1='dnragreement' 2='cnrannex' 3='cnragreement'}
        {else}
          {assign var=current_code value='dnragreement'}
          {array assign=hidden_reasons 1='dnrannex' 2='cnrannex' 3='cnragreement'}
        {/if}
        {assign var=rcount value=0}
        {foreach from=$cd_reasons item=reason}
          {if $reason.code eq $current_code || !in_array($reason.code, $hidden_reasons) || empty($reason.code)}
            {assign var=rcount value=$rcount+1}
          {/if}
        {/foreach}
        <select class="selbox{if $rcount gt 1} undefined{/if}" name="credit_reason[{$invoice->get('id')}]" id="credit_reason_{$invoice->get('id')}" title="{#contracts_cd_reason#|escape}" style="width: 100%;" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);">
          {if $rcount gt 1}
            <option value="" class="undefined">[{#please_select#|escape}]</option>
          {/if}
        {foreach from=$cd_reasons item=reason}
          {if $reason.code eq $current_code || !in_array($reason.code, $hidden_reasons) || empty($reason.code)}
            <option value="{$reason.option_value}">{$reason.label}</option>
          {/if}
        {/foreach}
        </select>
      </td>
      <td class="t_border hright" style="width:100px">{$credit->get('total_with_vat')} {$credit->get('currency')}</td>
      <td class="t_border" style="width:120px">
        {include file="input_date.html"
            standalone=true
            name=credit_fiscal_date
            index=$invoice->get('id')
            eq_indexes=1
            required=1
            label=#contracts_fiscal_event_date#
            value=$smarty.now|date_format:'%Y-%m-%d'
        }
      </td>
      <td style="width:120px">
        {include file="input_date.html"
            standalone=true
            name=credit_issue_date
            index=$invoice->get('id')
            eq_indexes=1
            required=1
            label=#contracts_issue_date#
            value=$smarty.now|date_format:'%Y-%m-%d'
            readonly=$readonly_dates
            disallow_date_after=$disallow_date_after
            disallow_date_before=$disallow_date_before
        }
      </td>
    </tr>
    <tr style="display: none;" id="credit_{$invoice->get('id')}">
      <td colspan="7">
        {include file="_gt2_view.html" table=$credit->get('grouping_table_2') hide_script=1}
      </td>
    </tr>
  {/if}
  {if $smarty.foreach.invoices.last && !$headers_show}
    </table>
  {/if}
{/foreach}
{if $has_credit}
<div class="floatl" style="white-space: nowrap;">{#contracts_email_template#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=credit_email
    options=$credit_emails
    standalone=true
    required=1
  }
</div>
<div class="floatl" style="white-space: nowrap; padding-left: 40px">{#contracts_pattern#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=credit_pattern
    options=$credit_patterns
    standalone=true
    required=1
  }
</div>
<div class="clear"></div>
{/if}
{if $result.new_invoices}
  {if $has_previous}<br /><br /><br />{/if}
  <div class="red">{#message_contracts_invoices_amount#}</div>
  <table border="0" cellpadding="2" cellspacing="0" class="t_grouping_table t_table bordered_cell" style="margin: 5px 0;">
    <tr>
      <th class="t_border" style="width:50px">
        <div class="t_caption3_title">{#contracts_issue#}</div>
        <div style="text-align: right;"><input type="checkbox" checked="checked" onclick="checkAll(this, 'invoices', 'class')" /></div>
      </th>
      <th class="t_border" style="width:70px">
        <div class="t_caption3_title">{#contracts_auto_send#}</div>
        <div style="text-align: center;"><input type="checkbox" onclick="checkAll(this, 'invoices_send', 'class')" /></div>
      </th>
      <th class="t_border"><div class="t_caption3_title">{#contracts_invoice_period_start#}</div></th>
      <th class="t_border"><div class="t_caption3_title">{#contracts_invoice_period_finish#}</div></th>
      <th class="t_border" style="width:100px"><div class="t_caption3_title">{#contracts_amount#}</div></th>
      <th class="t_border" style="width:120px"><div class="t_caption3_title">{#contracts_fiscal_event_date#}</div></th>
      <th style="width:120px"><div class="t_caption3_title">{#contracts_issue_date#}</div></th>
    </tr>
  {assign var=ii value=1}
  {foreach from=$result.new_invoices item='invoice' key=key}
    {if $invoice->get('type') eq PH_FINANCE_TYPE_INVOICE}
      {assign var=has_invoice value=true}
    {else}
      {assign var=has_proforma value=true}
    {/if}
    <tr class="{cycle values='t_odd,t_even'} pointer">
      <td class="t_border hright">
        <div class="switch_expand" id="switch_invoice_{$invoice->get('id')}" onclick="if($('invoice_{$invoice->get('id')}').style.display == 'none') {ldelim}$('invoice_{$invoice->get('id')}').style.display = ''; $('switch_invoice_{$invoice->get('id')}').className = 'switch_collapse';{rdelim} else {ldelim}$('invoice_{$invoice->get('id')}').style.display = 'none'; $('switch_invoice_{$invoice->get('id')}').className = 'switch_expand';{rdelim}"></div>
        <input type="checkbox" name="invoice[{$invoice->get('id')}]" value="{if $invoice->get('type') eq PH_FINANCE_TYPE_PRO_INVOICE}proforma_{/if}1" checked="checked" title="{#contracts_issue#|escape}" class="invoices" />
      </td>
      <td class="t_border hcenter" style="width:50px">
        <input type="checkbox" name="invoice_send[{$invoice->get('id')}]" value="1" {if $invoice->get('auto_send')}checked="checked"{/if} title="{#contracts_auto_send#|escape}" class="invoices_send" />
      </td>
      <td class="t_border">{$invoice->get('from')|date_format:#date_short#|escape}</td>
      <td class="t_border">{$invoice->get('to')|date_format:#date_short#|escape}</td>
      <td class="t_border hright" style="width: 120px">{$invoice->get('total_with_vat')} {$invoice->get('currency')}</td>
      <td class="t_border" style="width:120px">
        {include file="input_date.html"
            standalone=true
            name=invoice_fiscal_date
            index=$invoice->get('id')
            eq_indexes=1
            required=1
            label=#contracts_fiscal_event_date#
            value=$smarty.now|date_format:'%Y-%m-%d'
        }
      </td>
      <td style="width:120px">
        {include file="input_date.html"
            standalone=true
            name=invoice_issue_date
            index=$invoice->get('id')
            eq_indexes=1
            required=1
            label=#contracts_issue_date#
            value=$smarty.now|date_format:'%Y-%m-%d'
            readonly=$readonly_dates
            disallow_date_after=$disallow_date_after
            disallow_date_before=$disallow_date_before
        }
      </td>
    </tr>
    <tr style="display: none;" id="invoice_{$invoice->get('id')}">
      <td colspan="7">
        {include file="_gt2_view.html" table=$invoice->get('grouping_table_2') hide_script=1}
      </td>
    </tr>
  {/foreach}
</table>
{/if}
{if $has_proforma}
<div class="floatl" style="white-space: nowrap;">{#contracts_email_template#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=proforma_email
    options=$proforma_emails
    standalone=true
    required=1
  }
</div>
<div class="floatl" style="white-space: nowrap; padding-left: 40px">{#contracts_pattern#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=proforma_pattern
    options=$proforma_patterns
    standalone=true
    required=1
  }
</div>
<div class="clear"></div>
{/if}
{if $has_invoice}
<div class="floatl" style="white-space: nowrap;">{#contracts_email_template#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=invoice_email
    options=$invoice_emails
    standalone=true
    required=1
  }
</div>
<div class="floatl" style="white-space: nowrap; padding-left: 40px">{#contracts_pattern#}:</div>
<div class="floatl" style="white-space: nowrap; padding-left: 20px">
  {#required#}
  {include file=input_dropdown.html
    name=invoice_pattern
    options=$invoice_patterns
    standalone=true
    required=1
  }
</div>
<div class="clear"></div>
{/if}
<div style="clear: both">
  <br /><br />
  <button type="button" class="button" name="ok1" id="ok1" title="" onclick="fastContractFinish(this.form)">{#ok#|escape}</button><button type="button" class="button" name="cancel1" id="cancel1" title="" onclick="dashletsLoad('content_dashlet_{$dashlet}', 'plugin', '{$plugin}', '{$dashlet}');">{#cancel#|escape}</button>
  <br /><br />
</div>
</form>
{/if}