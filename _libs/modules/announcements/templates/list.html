<h1>{$title}</h1>

<br />
{strip}
<a{if empty($filtered_categories)} class="strong"{/if} href="{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements={$action}&amp;category=&amp;type=">
  {#all#|escape}
</a>,&nbsp;
{assign var='unread_count' value=0}
{foreach name='c' from=$categories item='category'}
  {math x=$unread_count y=$category->get('count_unread') assign='unread_count' equation=x+y}
{/foreach}
<a{if in_array('unread',$filtered_categories)} class="strong"{/if} href="{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements={$action}&amp;category=unread&amp;type=">
  {#announcements_not_read#|escape} ({$unread_count})
</a>,&nbsp;
{foreach name='c' from=$categories item='category'}
  <a{if in_array($category->get('id'),$filtered_categories)} class="strong"{/if} href="{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements={$action}&amp;category={$category->get('id')}&amp;type=">
    {$category->get('name')} ({$category->get('count_announcements')})
  </a>
  {if !$smarty.foreach.c.last},&nbsp;{/if}
{/foreach}
{/strip}

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=announcements&amp;announcements={$action}{if !empty($filtered_categories)}&amp;category={$filtered_categories[0]}{/if}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="announcements" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=announcements" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.full_num.link}">{#announcements_full_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.subject.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.subject.link}">{#announcements_subject#|escape}</div></td>
          <td class="t_caption t_border {$sort.content.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.content.link}">{#announcements_content#|escape}</div></td>
          <td class="t_caption t_border {$sort.priority.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.priority.link}">{#announcements_priority#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#announcements_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.category.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.category.link}">{#announcements_category#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$announcements item='announcement'}
      {strip}
      {capture assign='info'}
        <strong><u>{#announcements_full_num#|escape}:</u></strong> {$announcement->get('full_num')}<br />
        <strong>{#announcements_subject#|escape}:</strong> {$announcement->get('subject')|escape}<br />
        <strong>{#added#|escape}:</strong> {$announcement->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$announcement->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$announcement->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$announcement->get('modified_by_name')|escape}<br />
        {if $announcement->isDeleted()}<strong>{#deleted#|escape}:</strong> {$announcement->get('deleted')|date_format:#date_mid#|escape}{if $announcement->get('deleted_by_name')} {#by#|escape} {$announcement->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$announcement->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $announcement->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}

        <tr class="{cycle values='t_odd,t_even'}{if !$announcement->get('active')} t_inactive{/if}{if $announcement->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$announcement->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($announcement->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($announcement->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright" nowrap="nowrap">
          {if $announcement->get('files_count')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=announcements&amp;announcements=view&amp;view={$announcement->get('id')}">
              <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$announcement->get('id')})"
                     onmouseout="mclosetime()" />
            </a>
          {else}
            &nbsp;
          {/if}
          {if $announcement->get('user_read')}
            <img src="{$theme->imagesUrl}announcements_read.png" alt="" title="{#announcements_is_read#|escape}" />
          {else}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$announcement->get('id')}">
              <img src="{$theme->imagesUrl}announcements.png" style="border: none;" alt="" title="{#announcements_is_not_read#|escape}" />
            </a>
          {/if}
          {counter name='item_counter' print=true}</td>

          <td class="t_border {$sort.full_num.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$announcement->get('id')}">{$announcement->get('full_num')|escape|default:"&nbsp;"}</a></td>

          {if $announcement->get('user_read') eq 1}
            <td class="t_border {$sort.subject.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$announcement->get('id')}">{$announcement->get('subject')|escape|default:"&nbsp;"}</a></td>
            <td class="t_border {$sort.content.isSorted}">{$announcement->get('content')|strip_tags:false|strip|mb_truncate|default:"&nbsp;"}</td>
            {capture assign='priority'}announcements_{$announcement->get('priority')}{/capture}
            <td class="t_border {$sort.priority.isSorted}">
              {strip}
                <img src="{$theme->imagesUrl}{$announcement->get('priority')}.png" />&nbsp;
                {$smarty.config.$priority}
              {/strip}
            </td>
            <td class="t_border {$sort.type.isSorted}">{$announcement->get('type_name')}</td>
            <td class="t_border {$sort.category.isSorted}">{$announcement->get('category_name')}</td>
          {else}
            <td class="t_border {$sort.subject.isSorted}"><strong><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$announcement->get('id')}">{$announcement->get('subject')|escape|default:"&nbsp;"}</a></strong></td>
            <td class="t_border {$sort.content.isSorted}">
              <strong>{$announcement->get('content')|strip_tags:false|strip|mb_truncate|default:"&nbsp;"}</strong>
            </td>
            {capture assign='priority'}announcements_{$announcement->get('priority')}{/capture}
            <td class="t_border {$sort.priority.isSorted}">
              {strip}
                <img src="{$theme->imagesUrl}{$announcement->get('priority')}.png" />&nbsp;
                <strong>{$smarty.config.$priority}</strong>
              {/strip}
            </td>
            <td class="t_border {$sort.type.isSorted}"><strong>{$announcement->get('type_name')}</strong></td>
            <td class="t_border {$sort.category.isSorted}"><strong>{$announcement->get('category_name')}</strong></td>
          {/if}
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$announcement}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="9">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="9"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
