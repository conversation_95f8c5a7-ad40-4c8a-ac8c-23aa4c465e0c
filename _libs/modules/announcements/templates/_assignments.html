{* tree initialization *}
<script type='text/javascript'>
  var func1 = function() {ldelim}initTree('departments'){rdelim}
  Event.observe(window, 'load', func1);
</script>

{if !$assignments_type}{assign var='assignments_type' value='Departments'}{/if}

<input type="hidden" name="assignments_type" id="assignments_type" value="{$assignments_type}" />

<div class="m_header_menu t_table" style="width: 550px;">
  <ul style="float: left; padding: 0 5px;">
    {* Departments Tab *}
    <li id="tab_departments">
      <span{if $assignments_type eq 'Departments'} class="selected"{/if}><a onclick="toggleTabs(this);$('assignments_type').value='Departments'">{#menu_departments#|escape}</a></span>
    </li>

    {* Normal Users Tab *}
    <li id="tab_normal_users">
      <span{if $assignments_type eq 'Users'} class="selected"{/if}><a onclick="toggleTabs(this);$('assignments_type').value='Users'">{#normal_users#|escape}</a></span>
    </li>

    {* Portal Users Tab *}
    <li id="tab_portal_users">
      <span><a onclick="toggleTabs(this);$('assignments_type').value='Users'">{#portal_users#|escape}</a></span>
    </li>
  </ul>

  {* Departments Toggler *}
  <div style="float: right; {if $assignments_type ne 'Departments' || $action eq 'view'} display: none;{/if}" id="tab_toggler_departments"{if $action eq 'view'} class="hidden"{/if}>
    <img src="{$theme->imagesUrl}collapse_tree.png" onclick="Zapatec.Tree.all['tree_departments'].collapseAll();this.style.display='none'; $('expand_tree').style.display=''" id="collapse_tree" class="pointer" width="16" height="16" align="top" />
    <img src="{$theme->imagesUrl}expand_tree.png" onclick="Zapatec.Tree.all['tree_departments'].expandAll();this.style.display='none'; $('collapse_tree').style.display=''" id="expand_tree" class="pointer" style="display: none" align="top" width="16" height="16" />
    <span onclick="toggleCheckboxes(this, '{$name}', true, 'tab_container_departments')" class="pointer">{#check_all#|escape}</span> |
    <span onclick="toggleCheckboxes(this, '{$name}', false, 'tab_container_departments')" class="pointer">{#check_none#|escape}</span>
  </div>

  {* Normal Users Toggler *}
  <div style="float: right;{if $assignments_type eq 'Departments'} display: none;{/if}" id="tab_toggler_normal_users"{if $action eq 'view'} class="hidden"{/if}>
    <span onclick="toggleCheckboxes(this, '{$name}', true, 'tab_container_normal_users')" class="pointer">{#check_all#|escape}</span> |
    <span onclick="toggleCheckboxes(this, '{$name}', false, 'tab_container_normal_users')" class="pointer">{#check_none#|escape}</span>
  </div>

  {* Portal Users Toggler *}
  <div style="float: right; display: none;" id="tab_toggler_portal_users"{if $action eq 'view'} class="hidden"{/if}>
    <span onclick="toggleCheckboxes(this, '{$name}', true, 'tab_container_portal_users')" class="pointer">{#check_all#|escape}</span> |
    <span onclick="toggleCheckboxes(this, '{$name}', false, 'tab_container_portal_users')" class="pointer">{#check_none#|escape}</span>
  </div>
</div>
<div class="m_header_m_menu scroll_box_container" style="margin-top: 19px;">

  {* Departments Options *}
  <div class="scroll_box" id="tab_container_departments" style="{if $assignments_type ne 'Departments'}display: none;{/if}">
    <ul id="tree_departments">
    {strip}
    {foreach name='i' from=$departments_assignments.options item='department' key='idx'}
    {assign var='level' value=$department.level}
    {if $smarty.foreach.i.last}
      {assign var='next_level' value=0}
    {else}
      {assign var='next_idx' value=$idx+1}
      {assign var='next_department' value=$departments_assignments.options.$next_idx}
      {assign var='next_level' value=$next_department.level}
    {/if}
      <li>
        <input type="checkbox"
               name="{$departments_assignments.name}[]"
               id="{$departments_assignments.name}_{$smarty.foreach.i.iteration}"
               value="{$department.option_value}"
               {if @in_array($department.option_value, $departments_assignments.value)}checked="checked"{/if}
               {if $action eq 'view' || $department.deleted || !$department.active}disabled="disabled"{/if}
        />
        <label for="{$departments_assignments.name}_{$smarty.foreach.i.iteration}"{if $department.deleted || !$department.active} class="inactive_option" title="{#inactive_option#}"{/if}>{$department.label|escape}</label>
    {if $next_level > $level}
    <ul>
    {elseif $next_level eq $level}
      </li>
    {else}
      {repeat string='</li></ul>' num=$level-$next_level}</li>
    {/if}
    {/foreach}
    {/strip}
    </ul>
  </div>

  {* Normal Users Options *}
  <div class="scroll_box" id="tab_container_normal_users" style="{if $assignments_type eq 'Departments'}display: none;{/if}">
    {foreach name='cb' from=$users.optgroups.normal_users item='user'}
    {if !(isset($user.active_option) && !$user.active_option && !in_array($user.option_value, $users.value))}
    <input type="checkbox"
           name="users_assignments[]"
           id="users_assignments_{counter name='users' print=true assign='users_cnt'}"
           value="{$user.option_value|escape}"
           title="{$user.label|strip_tags:false|escape}"
           onfocus="highlight(this)"
           onblur="unhighlight(this)"
           {if is_array($users.value) && in_array($user.option_value, $users.value)}checked="checked"{/if}
           {if $action eq 'view'}disabled="disabled"{/if} />
    <label for="users_assignments_{$users_cnt}"{if isset($user.active_option) && !$user.active_option} class="inactive_option" title="{#inactive_option#}">* {else}>{/if}{$user.label|escape|mb_wordwrap|default:'&nbsp;'}</label>
    <br />
    {/if}
    {/foreach}
  </div>

  {* Portal Users Options *}
  <div class="scroll_box" id="tab_container_portal_users" style="display: none;">
    {foreach name='cb' from=$users.optgroups.portal_users item='user'}
    {if !(isset($user.active_option) && !$user.active_option && !in_array($user.option_value, $users.value))}
    <input type="checkbox"
           name="users_assignments[]"
           id="users_assignments_{counter name='users' print=true assign='users_cnt'}"
           value="{$user.option_value|escape}"
           title="{$user.label|strip_tags:false|escape}"
           onfocus="highlight(this)"
           onblur="unhighlight(this)"
           {if is_array($users.value) && in_array($user.option_value, $users.value)}checked="checked"{/if}
           {if $action eq 'view'}disabled="disabled"{/if} />
    <label for="users_assignments_{$users_cnt}"{if isset($user.active_option) && !$user.active_option} class="inactive_option" title="{#inactive_option#}">* {else}>{/if}{$user.label|escape|mb_wordwrap|default:'&nbsp;'}</label>
    <br />
    {/if}
    {/foreach}
  </div>
</div>
