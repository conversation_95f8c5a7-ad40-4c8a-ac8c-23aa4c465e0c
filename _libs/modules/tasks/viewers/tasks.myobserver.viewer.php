<?php

class Tasks_MyObserver_Viewer extends Viewer {
    public $template = 'myobserver.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'tasks.factory.php';

        /*check for referer filters
        $filters = array();
        $ref_filters = array();
        if (isset($_SERVER['HTTP_REFERER'])) {
            $ref_url = $_SERVER['HTTP_REFERER'];
            if (strpos($ref_url,'tasks=search')) {
                $ref_filters = Tasks::saveSearchParams($this->registry, array(), 'search_');
            } elseif (!strpos($ref_url,'tasks=myobserver')) {
                $ref_filters = Tasks::saveSearchParams($this->registry, array(), 'list_');
            }
        }
        $filters = array_merge($ref_filters, $filters);
        $filters = Tasks::saveSearchParams($this->registry, $filters, 'myobserver_');*/

        if (isset($_SERVER['HTTP_REFERER'])) {
            $ref_url = $_SERVER['HTTP_REFERER'];
            $search_filters = $this->registry['session']->get('search_task');
            if (strpos($ref_url,'tasks=search') || isset($search_filters['active'])
                                                && $search_filters['active'] == 1) {
                $filters = Tasks::saveSearchParams($this->registry, array(), 'search_');
            } else {
                $filters = Tasks::saveSearchParams($this->registry, array(), 'list_');
            }
        } else {
            $filters = Tasks::saveSearchParams($this->registry, array(), 'list_');
        }
        $filters['where'][] = 'ta.assigned_to assignment_observer = ' . $this->registry['currentUser']->get('id');
        if (!strpos(implode(' ', $filters['where']), '.status')) {
            $filters['where'][] = 't.status != \'finished\'';
        }

        $customize = array();
        if (!empty($filters['where'])) {
            $found = 0;
            foreach ($filters['where'] as $where) {
                if (preg_match('/t\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/t\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize['name'] = 'type';
                    $customize['value'] = $val;
                    $found++;

                    //get type for multi actions
                    $type = $val;
                }
                if (preg_match('/tt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/tt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize['name'] = 'section';
                    $customize['value'] = $val;
                    $found++;
                }
            }
            if ($found == 1 && $customize) {
                if (!$this->setCustomTemplate($customize) && $customize['name'] == 'type') {
                    //the list of types is only shown when there is no custom outlook template
                    $this->data['type'] = $customize['value'];
                }
            } else {
                $this->setCustomTemplate($customize);
            }
        } else {
             $this->setCustomTemplate($customize);
        }

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {

            $filters['get_fields'] = $this->modelFields;

            if (in_array('tags', $this->modelFields)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }
        }

        list($tasks, $pagination) = Tasks::pagedSearch($this->registry, $filters);

        $this->data['tasks'] = $tasks;
        $this->data['pagination'] = $pagination;

        require_once $this->modelsDir . 'tasks.types.factory.php';
        $filters_menu_types_custom_listing = array('where'    => array('tt.active = 1'),
                                                   'sort'     => array('tti18n.name ASC'),
                                                   'sanitize' => true);
        // gets the non permitted tasks types ids
        $not_permitted_tasks_types = $this->registry->get('currentUser')->getNotPermittedTypes('task', 'list');
        if (!empty($not_permitted_tasks_types)) {
            $filters_menu_types_custom_listing['where'][] = 'tt.id NOT IN (' . implode(',', $not_permitted_tasks_types) . ')';
        }

        $this->data['menu_types_custom_listing'] = Tasks_Types::search($this->registry, $filters_menu_types_custom_listing);

        // prepare available tags for multitagging
        $this->prepareMultitagOptions(!empty($type) ? $type : null);

        if (!empty($type)) {
            //prepare statuses
            $filters_types = array('model_lang' => $this->registry->get('lang'),
                                   'sanitize' => true,
                                   'where' => array('tt.id = ' . $type));
            $type_model = Tasks_Types::searchOne($this->registry, $filters_types);

            require_once PH_MODULES_DIR . 'tasks/models/tasks.substatuses.factory.php';
            $filters_substatuses = array('model_lang' => $this->registry->get('lang'),
                                         'sanitize' => true,
                                         'where' => array('ts.task_type = ' . $type,
                                                          'ts.active = 1'));
            $substatuses = Tasks_Substatuses::search($this->registry, $filters_substatuses);

            $_options_statuses = array();
            foreach ($substatuses as $key => $substatuses_by_status) {
                $_options_statuses[] = array(
                    'id' => $key,
                    'name' => $this->i18n('tasks_status_' . $key),
                    'requires_comment' => ($type_model ? $type_model->get($key . '_requires_comment') : 'without_comment'));
                foreach ($substatuses_by_status as $substatus) {
                    $_options_statuses[] = array(
                        'id' => $substatus['parent_status'] . '_' . $substatus['id'],
                        'name' => '-- ' . $substatus['name'],
                        'requires_comment' => $substatus['requires_comment']);
                }
            }
            $this->data['statuses'] = $_options_statuses;

            // set the default portal comment option in case status multi action is used
            $this->data['default_portal_comment'] = $this->registry['config']->getParam('comments', 'default_portal');
        }

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        if (isset($this->title)) {
            $title = $this->title;
        } else {
            $title = $this->i18n('tasks');
        }
        if (isset($this->subtitle)) {
            $subtitle = $this->subtitle;
        } else {
            $subtitle = '';
        }

        $this->data['title'] = $title;
        $this->data['subtitle'] = $subtitle;
    }
}

?>
