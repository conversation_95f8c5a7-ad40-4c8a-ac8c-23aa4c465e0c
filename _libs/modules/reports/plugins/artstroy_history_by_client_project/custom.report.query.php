<?php
    Class Artstroy_History_By_Client_Project Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            $final_results = array();

            if (empty($filters['task']) && empty($filters['customer']) && empty($filters['project'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_at_least_one_filter'));
            } else {
                // GET THE DOCUMENTS
                if (empty($filters['task'])) {
                    $documents_list = array();
                    $query = 'SELECT "documents" as module, d.id, d.full_num, dti18n.name as record_name, d.added, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by, di18n.name, d.status, ds.name as substatus_name, di18n.description, ' . "\n" .
                             '  d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, d.project, p.added as project_added, p.num as project_full_num, pi18n.name as project_name, pti18n.name as project_type_name' . "\n" .
                             'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                             '  ON (di18n.parent_id=d.id AND di18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                             '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                             '  ON (ds.id=d.substatus AND ds.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                             '  ON (ui18n.parent_id=d.added_by AND ui18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                             '  ON (p.id=d.project)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                             '  ON (pi18n.parent_id=p.id AND pi18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                             '  ON (pti18n.parent_id=p.type AND pti18n.lang="' . $lang . '")' . "\n";

                    $where = array();
                    $where[] = 'd.active=1';
                    $where[] = 'd.deleted_by=0';
                    if (!empty($filters['customer'])) {
                        $where[] = 'd.customer="' . $filters['customer'] . '"';
                    }
                    if (!empty($filters['project'])) {
                        $where[] = 'd.project="' . $filters['project'] . '"';
                    }
                    $query .= 'WHERE ' . implode(' AND ', $where) . "\n";
                    $query .= 'ORDER BY d.added DESC' . "\n";
                    $documents_data = $registry['db']->GetAll($query);

                    // process the relations
                    $documents_avb = array();
                    foreach ($documents_data as $dd) {
                        $documents_avb[] = $dd['id'];
                        $documents_list['doc_' . $dd['id']] = $dd;
                        $documents_list['doc_' . $dd['id']]['relations'] = array();
                        if ($dd['project']) {
                            $documents_list['doc_' . $dd['id']]['relations']['proj_' . $dd['project']] = array(
                                'module'   => 'projects',
                                'id'       => $dd['project'],
                                'full_num' => $dd['project_full_num'],
                                'name'     => $dd['project_name'],
                                'added'    => $dd['project_added'],
                                'type'     => $dd['project_type_name']
                            );
                        }
                        unset($documents_list['doc_' . $dd['id']]['project']);
                        unset($documents_list['doc_' . $dd['id']]['project_name']);
                        unset($documents_list['doc_' . $dd['id']]['project_added']);
                        unset($documents_list['doc_' . $dd['id']]['project_type_name']);
                    }

                    $documents_avb = array_unique($documents_avb);

                    // examine transformations
                    if (!empty($documents_avb)) {
                        $query = 'SELECT dr.link_to as related_to, "documents" as module, dr.parent_id as id, d.full_num, d.added, di18n.name, dti18n.name as type_name' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 '  ON (d.id=dr.parent_id)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                 '  ON (di18n.parent_id=d.id AND di18n.lang="' . $lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                 '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $lang . '")' . "\n" .
                                 'WHERE dr.link_to IN (' . implode(',', $documents_avb) . ') AND dr.parent_model_name="Document" AND dr.link_to_model_name="Document"' . "\n";
                        $documents_trans = $registry['db']->GetAll($query);

                        foreach ($documents_trans as $dt) {
                            if (isset($documents_list['doc_' . $dt['related_to']])) {
                                $documents_list['doc_' . $dt['related_to']]['relations']['doc_' . $dt['id']] = $dt;
                                unset($documents_list['doc_' . $dt['related_to']]['relations']['doc_' . $dt['id']]['related_to']);
                            }
                        }

                        // the other side of the transformations
                        $query = 'SELECT dr.parent_id as related_to, "documents" as module, dr.link_to as id, d.full_num, d.added, di18n.name, dti18n.name as type_name' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 '  ON (d.id=dr.link_to)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                 '  ON (di18n.parent_id=d.id AND di18n.lang="' . $lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                 '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $lang . '")' . "\n" .
                                 'WHERE dr.parent_id IN (' . implode(',', $documents_avb) . ') AND dr.parent_model_name="Document" AND dr.link_to_model_name="Document"' . "\n";
                        $documents_trans = $registry['db']->GetAll($query);

                        foreach ($documents_trans as $dt) {
                            if (isset($documents_list['doc_' . $dt['related_to']])) {
                                $documents_list['doc_' . $dt['related_to']]['relations']['doc_' . $dt['id']] = $dt;
                                unset($documents_list['doc_' . $dt['related_to']]['relations']['doc_' . $dt['id']]['related_to']);
                            }
                        }
                    }
                    unset($documents_avb);

                    foreach ($documents_list as $key => $dl) {
                        uasort($dl['relations'], array('self', 'sortingResultsByAddedDate'));
                        $documents_list[$key] = $dl;
                        $documents_list[$key]['status_name'] = $registry['translater']->translate('reports_statuses_documents_' . $dl['status']);
                        $documents_list[$key]['encoded_data'] = base64_encode(json_encode($documents_list[$key]));
                    }
                    $final_results = array_merge($final_results, $documents_list);
                    unset($documents_list);
                }


                /*
                 * GET THE TASKS
                 */
                $query = 'SELECT "tasks" as module, t.id, t.full_num, tti18n.name as record_name, t.added, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by, t.status, ts.name as substatus_name, ti18n.name, ti18n.description, ' . "\n" .
                             '  t.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, t.project, p.num as project_full_num, p.added as project_added, pi18n.name as project_name, pti18n.name as project_type_name' . "\n" .
                         'FROM ' . DB_TABLE_TASKS . ' AS t' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti18n' . "\n" .
                         '  ON (ti18n.parent_id=t.id AND ti18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_TASKS_TYPES_I18N . ' AS tti18n' . "\n" .
                         '  ON (tti18n.parent_id=t.type AND tti18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_TASKS_STATUSES . ' AS ts' . "\n" .
                         '  ON (ts.id=t.substatus AND ts.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                         '  ON (ui18n.parent_id=t.added_by AND ui18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                         '  ON (ci18n.parent_id=t.customer AND ci18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                         '  ON (p.id=t.project)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                         '  ON (pi18n.parent_id=p.id AND pi18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                         '  ON (pti18n.parent_id=p.type AND pti18n.lang="' . $lang . '")' . "\n";

                $where = array();
                $where[] = 't.active=1';
                $where[] = 't.deleted_by=0';
                if (!empty($filters['customer'])) {
                    $where[] = 't.customer="' . $filters['customer'] . '"';
                }
                if (!empty($filters['project'])) {
                    $where[] = 't.project="' . $filters['project'] . '"';
                }
                if (!empty($filters['task'])) {
                    $where[] = 't.id="' . $filters['task'] . '"';
                }
                $query .= 'WHERE ' . implode(' AND ', $where);

                $tasks_data = $registry['db']->GetAll($query);

                $tasks_list = array();
                foreach ($tasks_data as $td) {
                    $tasks_list['task_' . $td['id']] = $td;
                    $tasks_list['task_' . $td['id']]['relations'] = array();
                    if ($td['project']) {
                        $tasks_list['task_' . $td['id']]['relations']['proj_' . $td['project']] = array(
                            'module' => 'projects',
                            'id'     => $td['project'],
                            'name'   => $td['project_name'],
                            'added'  => $td['project_added'],
                            'type'   => $td['project_type_name']
                        );
                    }
                    unset($tasks_list['task_' . $td['id']]['project']);
                    unset($tasks_list['task_' . $td['id']]['project_name']);
                    unset($tasks_list['task_' . $td['id']]['project_added']);
                    unset($tasks_list['task_' . $td['id']]['project_type_name']);
                    unset($tasks_list['task_' . $td['id']]['project_full_num']);
                }

                foreach ($tasks_list as $key => $tl) {
                    uasort($tl['relations'], array('self', 'sortingResultsByAddedDate'));
                    $tasks_list[$key] = $tl;
                    $tasks_list[$key]['status_name'] = $registry['translater']->translate('reports_statuses_tasks_' . $tl['status']);
                    $tasks_list[$key]['encoded_data'] = base64_encode(json_encode($tasks_list[$key]));
                }
                $final_results = array_merge($final_results, $tasks_list);
                unset($tasks_list);


                /*
                 * GET THE PROJECTS
                 */
                $query = 'SELECT "projects" as module, p.id, p.num as full_num, pti18n.name as record_name, p.added, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by, p.status, "" as substatus_name, ' . "\n" .
                         '  pi18n.name, pi18n.description, p.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, p.parent_project as project, pp.num as project_full_num, pp.added as project_added, ' . "\n" .
                         '  ppi18n.name as project_name, ppti18n.name as project_type_name, p.budget as event, e.added as event_added, ei18n.name as event_name, eti18n.name as event_type_name' . "\n" .
                         'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                         '  ON (pi18n.parent_id=p.id AND pi18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                         '  ON (pti18n.parent_id=p.type AND pti18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                         '  ON (ui18n.parent_id=p.added_by AND ui18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                         '  ON (ci18n.parent_id=p.customer AND ci18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS pp' . "\n" .
                         '  ON (pp.id=p.parent_project)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS ppi18n' . "\n" .
                         '  ON (ppi18n.parent_id=pp.id AND ppi18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS ppti18n' . "\n" .
                         '  ON (ppti18n.parent_id=pp.type AND pti18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                         '  ON (p.budget=e.id)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS_I18N . ' AS ei18n' . "\n" .
                         '  ON (ei18n.parent_id=e.id AND ei18n.lang="' . $lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' AS eti18n' . "\n" .
                         '  ON (eti18n.parent_id=e.type AND eti18n.lang="' . $lang . '")' . "\n";

                $where = array();
                $where[] = 'p.active=1';
                $where[] = 'p.deleted_by=0';
                if (!empty($filters['customer'])) {
                    $where[] = 'p.customer="' . $filters['customer'] . '"';
                }
                if (!empty($filters['project'])) {
                    $where[] = 'p.parent_project="' . $filters['project'] . '"';
                }
                if (!empty($filters['task'])) {
                    $where[] = 'p.budget="' . $filters['task'] . '"';
                }
                $query .= 'WHERE ' . implode(' AND ', $where);

                $projects_data = $registry['db']->GetAll($query);

                $projects_list = array();
                $projects_avb = array();
                foreach ($projects_data as $pd) {
                    $projects_list['proj_' . $pd['id']] = $pd;
                    $projects_list['proj_' . $pd['id']]['relations'] = array();
                    $projects_avb[] = $pd['id'];
                    if ($pd['project']) {
                        $projects_list['proj_' . $pd['id']]['relations']['proj_' . $pd['project']] = array(
                            'module'   => 'projects',
                            'id'       => $pd['project'],
                            'full_num' => $pd['project_full_num'],
                            'name'     => $pd['project_name'],
                            'added'    => $pd['project_added'],
                            'type'     => $pd['project_type_name']
                        );
                    }
                    unset($projects_list['proj_' . $pd['id']]['project']);
                    unset($projects_list['proj_' . $pd['id']]['project_full_num']);
                    unset($projects_list['proj_' . $pd['id']]['project_name']);
                    unset($projects_list['proj_' . $pd['id']]['project_added']);
                    unset($projects_list['proj_' . $pd['id']]['project_type_name']);

                    if ($pd['event']) {
                        $projects_list['proj_' . $pd['id']]['relations']['evnt_' . $pd['event']] = array(
                            'module'   => 'events',
                            'id'       => $pd['event'],
                            'full_num' => "",
                            'name'     => $pd['event_name'],
                            'added'    => $pd['event_added'],
                            'type'     => $pd['event_type_name']
                        );
                    }
                    unset($projects_list['proj_' . $pd['id']]['event']);
                    unset($projects_list['proj_' . $pd['id']]['event_name']);
                    unset($projects_list['proj_' . $pd['id']]['event_added']);
                    unset($projects_list['proj_' . $pd['id']]['event_type_name']);
                }

                // get relations with additional records
                $projects_avb = array_unique($projects_avb);
                if (!empty($projects_avb)) {
                    $query = '(SELECT CONCAT("proj_", p.id) as key_rec, p.id, "projects" as module, p.num as full_num, p.added, pi18n.name, pti18n.name as type_name, p.parent_project as project' . "\n" .
                             ' FROM ' . DB_TABLE_PROJECTS . ' AS p' .
                             ' LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                             '   ON (pi18n.parent_id=p.id AND pi18n.lang="' . $lang . '")' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                             '   ON (pti18n.parent_id=p.type AND pti18n.lang="' . $lang . '")' . "\n" .
                             ' WHERE p.parent_project IN (' . implode(',', $projects_avb) . ') AND p.active=1 AND p.deleted_by=0)' . "\n" .
                             'UNION' . "\n" .
                             '(SELECT CONCAT("doc_", d.id) as key_rec, d.id, "documents" as module, d.full_num, d.added, di18n.name, dti18n.name as type_name, d.project' . "\n" .
                             ' FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                             '   ON (di18n.parent_id=d.id AND di18n.lang="' . $lang . '")' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                             '   ON (dti18n.parent_id=d.type AND dti18n.lang="' . $lang . '")' . "\n" .
                             ' WHERE d.project IN (' . implode(',', $projects_avb) . ') AND d.active=1 AND d.deleted_by=0)' . "\n" .
                             'UNION' . "\n" .
                             '(SELECT CONCAT("task_", t.id) as key_rec, t.id, "tasks" as module, t.full_num, t.added, ti18n.name, tti18n.name as type_name, t.project' . "\n" .
                             ' FROM ' . DB_TABLE_TASKS . ' AS t' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_TASKS_I18N . ' AS ti18n' . "\n" .
                             '   ON (ti18n.parent_id=t.id AND ti18n.lang="' . $lang . '")' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_TASKS_TYPES_I18N . ' AS tti18n' . "\n" .
                             '   ON (tti18n.parent_id=t.type AND tti18n.lang="' . $lang . '")' . "\n" .
                             ' WHERE t.project IN (' . implode(',', $projects_avb) . ') AND t.active=1 AND t.deleted_by=0)' . "\n" .
                             'UNION' . "\n" .
                             '(SELECT CONCAT("evnt_", e.id) as key_rec, e.id, "events" as module, "" as full_num, e.added, ei18n.name, eti18n.name as type_name, e.project' . "\n" .
                             ' FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_EVENTS_I18N . ' AS ei18n' . "\n" .
                             '   ON (ei18n.parent_id=e.id AND ei18n.lang="' . $lang . '")' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' AS eti18n' . "\n" .
                             '   ON (eti18n.parent_id=e.type AND eti18n.lang="' . $lang . '")' . "\n" .
                             ' WHERE e.project IN (' . implode(',', $projects_avb) . ') AND e.active=1 AND e.deleted_by=0 AND e.type!="' . PH_TIMESHEET_EVENT_TYPE . '" AND e.type!="' . PH_REMINDER_EVENT_TYPE . '")' . "\n" .
                             'UNION' . "\n" .
                             '(SELECT CONCAT("con_", con.id) as key_rec, con.id, "contracts" as module, con.num as full_num, con.added, coni18n.name, conti18n.name as type_name, con.project' . "\n" .
                             ' FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coni18n' . "\n" .
                             '   ON (coni18n.parent_id=con.id AND coni18n.lang="' . $lang . '")' . "\n" .
                             ' LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS conti18n' . "\n" .
                             '   ON (conti18n.parent_id=con.type AND conti18n.lang="' . $lang . '")' . "\n" .
                             ' WHERE con.project IN (' . implode(',', $projects_avb) . ') AND con.active=1 AND con.deleted_by=0 AND con.annulled_by=0 AND con.subtype="contract")' . "\n";
                    $projects_relations = $registry['db']->GetAll($query);

                    foreach ($projects_relations as $proj_rel) {
                        if (isset($projects_list['proj_' . $proj_rel['project']])) {
                            $projects_list['proj_' . $proj_rel['project']]['relations'][$proj_rel['key_rec']] = $proj_rel;
                            unset($projects_list['proj_' . $proj_rel['project']]['relations'][$proj_rel['key_rec']]['key_rec']);
                            unset($projects_list['proj_' . $proj_rel['project']]['relations'][$proj_rel['key_rec']]['project']);
                        }
                    }
                }

                foreach ($projects_list as $key => $tl) {
                    uasort($tl['relations'], array('self', 'sortingResultsByAddedDate'));
                    $projects_list[$key] = $tl;
                    $projects_list[$key]['status_name'] = $registry['translater']->translate('reports_statuses_projects_' . $tl['status']);
                    $projects_list[$key]['encoded_data'] = base64_encode(json_encode($projects_list[$key]));
                }
                $final_results = array_merge($final_results, $projects_list);
                unset($projects_list);


                /*
                 * GET THE EVENTS
                 */
                if (empty($filters['task'])) {
                    $query = 'SELECT "events" as module, e.id, "" as full_num, eti18n.name as record_name, e.added, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by, e.status, "" as substatus_name, ei18n.name, ei18n.description, e.customer, ' . "\n" .
                             '  CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, e.project, p.num as project_full_num, p.added as project_added, pi18n.name as project_name, pti18n.name as project_type_name' . "\n" .
                             'FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_EVENTS_I18N . ' AS ei18n' . "\n" .
                             '  ON (ei18n.parent_id=e.id AND ei18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' AS eti18n' . "\n" .
                             '  ON (eti18n.parent_id=e.type AND eti18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                             '  ON (ui18n.parent_id=e.added_by AND ui18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=e.customer AND ci18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                             '  ON (p.id=e.project)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                             '  ON (pi18n.parent_id=p.id AND pi18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                             '  ON (pti18n.parent_id=p.type AND pti18n.lang="' . $lang . '")' . "\n";

                    $where = array();
                    $where[] = 'e.active=1';
                    $where[] = 'e.deleted_by=0';
                    $where[] = 'e.type!="' . PH_TIMESHEET_EVENT_TYPE . '"';
                    $where[] = 'e.type!="' . PH_REMINDER_EVENT_TYPE . '"';
                    if (!empty($filters['customer'])) {
                        $where[] = 'e.customer="' . $filters['customer'] . '"';
                    }
                    if (!empty($filters['project'])) {
                        $where[] = 'e.project="' . $filters['project'] . '"';
                    }
                    $query .= 'WHERE ' . implode(' AND ', $where);

                    $events_data = $registry['db']->GetAll($query);

                    $events_list = array();
                    foreach ($events_data as $ed) {
                        $events_list['evnt_' . $ed['id']] = $ed;
                        $events_list['evnt_' . $ed['id']]['relations'] = array();
                        if ($ed['project']) {
                            $events_list['evnt_' . $ed['id']]['relations']['proj_' . $ed['project']] = array(
                                'module'   => 'projects',
                                'id'       => $ed['project'],
                                'full_num' => $ed['project'],
                                'name'     => $ed['project_name'],
                                'added'    => $ed['project_added'],
                                'type'     => $ed['project_type_name']
                            );
                        }
                        unset($events_list['evnt_' . $ed['id']]['project']);
                        unset($events_list['evnt_' . $ed['id']]['project_full_num']);
                        unset($events_list['evnt_' . $ed['id']]['project_name']);
                        unset($events_list['evnt_' . $ed['id']]['project_added']);
                        unset($events_list['evnt_' . $ed['id']]['project_type_name']);
                    }

                    foreach ($events_list as $key => $el) {
                        uasort($el['relations'], array('self', 'sortingResultsByAddedDate'));
                        $events_list[$key] = $el;
                        $events_list[$key]['status_name'] = $registry['translater']->translate('reports_statuses_events_' . $el['status']);
                        $events_list[$key]['encoded_data'] = base64_encode(json_encode($events_list[$key]));
                    }
                    $final_results = array_merge($final_results, $events_list);
                    unset($events_list);
                }


                // GET THE CONTRACTS
                if (empty($filters['task'])) {
                    $query = 'SELECT "contracts" as module, con.id, con.num as full_num, conti18n.name as record_name, con.added, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by, coni18n.name, con.status, cs.name as substatus_name, coni18n.description, ' . "\n" .
                             '  con.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, con.project, p.num as project_full_num, p.added as project_added, pi18n.name as project_name, pti18n.name as project_type_name' . "\n" .
                             'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coni18n' . "\n" .
                             '  ON (coni18n.parent_id=con.id AND coni18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS conti18n' . "\n" .
                             '  ON (conti18n.parent_id=con.type AND conti18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CONTRACTS_STATUSES . ' AS cs' . "\n" .
                             '  ON (cs.id=con.substatus AND cs.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                             '  ON (ui18n.parent_id=con.added_by AND ui18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON (ci18n.parent_id=con.customer AND ci18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                             '  ON (p.id=con.project)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                             '  ON (pi18n.parent_id=p.id AND pi18n.lang="' . $lang . '")' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                             '  ON (pti18n.parent_id=p.type AND pti18n.lang="' . $lang . '")' . "\n";

                    $where = array();
                    $where[] = 'con.active=1';
                    $where[] = 'con.deleted_by=0';
                    $where[] = 'con.annulled_by=0';
                    $where[] = 'con.subtype="contract"';
                    if (!empty($filters['customer'])) {
                        $where[] = 'con.customer="' . $filters['customer'] . '"';
                    }
                    if (!empty($filters['project'])) {
                        $where[] = 'con.project="' . $filters['project'] . '"';
                    }
                    $query .= 'WHERE ' . implode(' AND ', $where);

                    $contracts_data = $registry['db']->GetAll($query);

                    $contracts_list = array();
                    foreach ($contracts_data as $cd) {
                        $contracts_list['con_' . $cd['id']] = $cd;
                        $contracts_list['con_' . $cd['id']]['relations'] = array();
                        if ($cd['project']) {
                            $contracts_list['con_' . $cd['id']]['relations']['proj_' . $cd['project']] = array(
                                'module'   => 'projects',
                                'id'       => $cd['project'],
                                'full_num' => $cd['project_full_num'],
                                'name'     => $cd['project_name'],
                                'added'    => $cd['project_added'],
                                'type'     => $cd['project_type_name']
                            );
                        }
                        unset($contracts_list['con_' . $cd['id']]['project']);
                        unset($contracts_list['con_' . $cd['id']]['project_full_num']);
                        unset($contracts_list['con_' . $cd['id']]['project_name']);
                        unset($contracts_list['con_' . $cd['id']]['project_added']);
                        unset($contracts_list['con_' . $cd['id']]['project_type_name']);
                    }

                    foreach ($contracts_list as $key => $cl) {
                        uasort($cl['relations'], array('self', 'sortingResultsByAddedDate'));
                        $contracts_list[$key] = $cl;
                        $contracts_list[$key]['status_name'] = $registry['translater']->translate('reports_statuses_contracts_' . $cl['status']);
                        $contracts_list[$key]['encoded_data'] = base64_encode(json_encode($contracts_list[$key]));
                    }
                    $final_results = array_merge($final_results, $contracts_list);
                    unset($contracts_list);
                }
            }

            uasort($final_results, array('self', 'sortingResultsByAddedDate'));
            $final_results['additional_options']['dont_show_export_button'] = true;

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        public static function sortingResultsByAddedDate($a, $b) {
            return $a['added'] > $b['added'] ? -1 : 1;
        }
    }
?>