.presence_form_holiday {
    background-color: #FFFDC0!important;
}
th.salary_titles_table {
    text-align: center;
    vertical-align: middle!important;
    box-sizing: border-box;
}
.salary_titles_table_days > div {
    width: 35px;
}
.salary_titles_table_sideways_text {
    height: 110px; /* Adjust height to fit content */
    text-align: center; /* Center text horizontally */
    vertical-align: bottom; /* Align the rotated text properly */
    position: relative;
    min-width: 50px;
}
.salary_titles_table_sideways_text div.title_text_conttainer {
    writing-mode: vertical-rl;
    transform: rotate(180deg);
}
.salary_titles_table_sideways_text>div {
/*    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    height: 100px;*/
    height: 100%;
    overflow-wrap: break-word;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 100%;
}
/*
Freeze table headers
 */
table.freeze_table_headers {
    position: relative;
    box-sizing: border-box;
}
table.freeze_table_headers thead th,
table.freeze_table_headers td.freeze_column {
    position: sticky;
}

table.freeze_table_headers thead tr:nth-child(1) th {
    top: 0px;
}
table.freeze_table_headers thead tr:nth-child(2) th {
    top: 55px;
}
table.freeze_table_headers {
    border-collapse: separate!important;
}
table.freeze_table_headers td {
    z-index: 0;
}
table.freeze_table_headers th,
table.freeze_table_headers td.freeze_column {
    z-index: 1;
}
table.freeze_table_headers th.freeze_column {
    z-index: 2;
}
table.freeze_table_headers td.freeze_column,
table.freeze_table_headers th.freeze_column {
    left: 0px;
}
tr.reports_title_row {
    -moz-osx-font-smoothing: grayscale;
}
