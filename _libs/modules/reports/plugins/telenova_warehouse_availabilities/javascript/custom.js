/*
 * Function to run AJAX which will prepare the link to be redirected to
 */
function activateInfoPanel(searched_category) {
    Effect.Center('loading');
    Effect.Appear('loading');

    // prepare the ajax url
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_prepare_custom_data&report_type=' + $('report_type').value + '&searched_category=' + searched_category + '&key_words=' + $('key_words').value + '&include_zero_avb=' + ($('include_zero_availabilities_1').checked ? 1 : 0);

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            $('data_panel').innerHTML = t.responseText;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}
