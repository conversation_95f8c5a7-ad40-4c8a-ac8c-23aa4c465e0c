<?php
    Class Credilink_Expected_Earnings_Per_Period Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            if (!defined('DOCUMENT_CONTRACT_DEADLINE')) {
                define('DOCUMENT_CONTRACT_DEADLINE', 'repayment_period');
            }

            // Prepare array for the results
            $results = array();

            // Get the lang
            $lang = $registry['lang'];

            // Get current date
            $currency_rates = array('BGN->BGN' => 1);
            $curdate = General::strftime($registry['translater']->translate('date_iso_short'));

            // Check required filters
            if (empty($filters['period_from']) || empty($filters['period_to'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_required_filters'));
            } else if ($filters['period_from'] < $curdate || $filters['period_to'] < $curdate) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_past_period'));
            } else {
                $registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents_statuses.ini');

                $db = $registry['db'];

                // get the required additional vars ids
                $vars = array(DOCUMENT_CONTRACT_PERIOD, DOCUMENT_CONTRACT_CURRENCY, DOCUMENT_CONTRACT_DEADLINE);
                $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
                       'WHERE `model`="Document" AND `model_type`="' . DOCUMENT_TYPE_LOAN_AGREEMENT . '" AND `name` IN ("' . implode('","', $vars) . '")' . "\n";
                $vars = $registry['db']->GetAssoc($sql);
                $periods_count_var = $registry['db']->GetOne($sql);

                // Get future installments
                $query = "
                    SELECT d.id                                 AS id,
                        di.name                                 AS name,
                        d.date                                  AS date,
                        c.id                                    AS customer_id,
                        TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                        IF(c.is_company, c.eik, c.ucn)          AS customer_eik_ucn,
                        c.gsm                                   AS customer_gsm,
                        g.id                                    AS gt2_row_id,
                        d_months.value                          AS months,
                        DATE(g.article_code)                    AS date_of_payment,
                        g.average_weighted_delivery_price*1     AS principal,
                        g.free_field1*1                         AS interest_rate,
                        g.free_field2*1                         AS guarantor_fee,
                        g.free_field5*1                         AS amount,
                        d_curr.value                            AS currency,
                        gi18n.article_description               AS tax_management,
                        g.free_field3*1                         AS tax_angagement
                      FROM " . DB_TABLE_DOCUMENTS . " AS d
                      JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                        ON (!d.deleted
                          AND d.active
                          AND d.type = " . DOCUMENT_TYPE_LOAN_AGREEMENT . "
                          AND d.substatus = " . DOCUMENT_LOAN_AGREEMENT_SUBSTATUS_ACTIVE . "
                          AND di.parent_id = d.id
                          AND di.lang = '{$lang}'" .
                          (!empty($filters['client']) ? ' AND d.customer=' . $filters['client'] : '') .
                          (!empty($filters['contract_filt']) ? ' AND d.id=' . $filters['contract_filt'] : '') .
                      ")
                      JOIN " . DB_TABLE_GT2_DETAILS . " AS g
                        ON (g.model = 'Document'
                          AND g.model_id = d.id
                          AND DATE(g.article_code) BETWEEN DATE('{$filters['period_from']}') AND DATE('{$filters['period_to']}'))
                      JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gi18n
                        ON (gi18n.parent_id=g.id AND gi18n.lang='{$registry['lang']}')
                      JOIN " . DB_TABLE_CUSTOMERS . " AS c
                        ON (c.id = d.customer)
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS d_months
                        ON (d_months.model_id=d.id AND d_months.var_id='{$vars[DOCUMENT_CONTRACT_PERIOD]}')
                      JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS d_curr
                        ON (d_curr.model_id=d.id AND d_curr.var_id='{$vars[DOCUMENT_CONTRACT_CURRENCY]}')
                      JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                        ON (ci.parent_id = c.id
                          AND ci.lang = '{$lang}')
                      ORDER BY di.name ASC, DATE(g.article_code) ASC";
                $data = $db->GetAll($query);

                if (!empty($data)) {
                    $results['additional_options']['totals'] = array(
                        'principals'                        => 0,
                        'interest_rates'                    => 0,
                        'guarantor_fees'                    => 0,
                        'tax_management'                    => 0,
                        'tax_angagement'                    => 0,
                        'amounts'                           => 0,
                        'overdue_principals'                => 0,
                        'overdue_interest_rates'            => 0,
                        'overdue_guarantor_fees'            => 0,
                        'overdue_tax_management'            => 0,
                        'overdue_tax_angagement'            => 0,
                        'overdue_punishment_interest_rates' => 0,
                        'overdue_lpg'                       => 0,
                        'total_overdue'                     => 0,
                        'total_expected_earnings'           => 0
                    );
                    $documents_ids = array();
                    foreach ($data as $d) {
                        if (!array_key_exists($d['id'], $results)) {
                            $documents_ids[] = $d['id'];
                            $results[$d['id']] = array(
                                'name'                       => $d['name'],
                                'date'                       => $d['date'],
                                'customer_id'                => $d['customer_id'],
                                'customer_name'              => $d['customer_name'],
                                'customer_eik_ucn'           => $d['customer_eik_ucn'],
                                'installments_count'         => $d['months'],
                                'currency'                   => $d['currency'],
                                'overdue_installments_count' => 0,
                                'rowspan'                    => 0,
                                'percent_overdue_installments'=> 0,
                                'future_installments'        => array(),
                                'overdue_installments'       => array(),
                                'total_overdue'              => 0,
                                'total_expected_earning'     => 0
                            );
                            $d['customer_gsm'] = explode('|', $d['customer_gsm']);
                            if (!empty($d['customer_gsm'][0])) {
                                $results[$d['id']]['customer_gsm'] = $d['customer_gsm'][0];
                                if (!empty($d['customer_gsm'][1])) {
                                    $results[$d['id']]['customer_gsm'] .= " ({$d['customer_gsm'][1]})";
                                }
                            } else {
                                $results[$d['id']]['customer_gsm'] = '';
                            }
                        }
                        $results[$d['id']]['rowspan']++;
                        $results[$d['id']]['future_installments'][$d['gt2_row_id']] = array(
                            'date_of_payment' => $d['date_of_payment'],
                            'principal'       => $d['principal'],
                            'interest_rate'   => $d['interest_rate'],
                            'guarantor_fee'   => $d['guarantor_fee'],
                            'tax_management'  => $d['tax_management'],
                            'tax_angagement'  => $d['tax_angagement'],
                            'amount'          => $d['amount']
                        );
                    }

                    // Get overdue installments
                    if (!empty($documents_ids)) {
                        $query = "
                            SELECT d.id                             AS id,
                                g.id                                AS gt2_row_id,
                                DATE(g.article_code)                AS date_of_payment,
                                g.free_field5*1                     AS amount,
                                g.average_weighted_delivery_price*1 AS principal,
                                g.free_field1*1                     AS interest_rate,
                                g.free_field2*1                     AS guarantor_fee,
                                g.free_field4*1                     AS punishment_interest_rate,
                                gi18n.article_description           AS tax_management,
                                g.free_field3*1                     AS tax_angagement,
                                gi18n.free_text5*1                  AS lpg
                              FROM " . DB_TABLE_DOCUMENTS . " AS d
                              JOIN " . DB_TABLE_GT2_DETAILS . " AS g
                                ON (d.id IN (" . implode(',', $documents_ids) . ")
                                  AND g.model = 'Document'
                                  AND g.model_id = d.id
                                  AND DATE(g.article_code) < CURDATE()
                                  AND 0 <> g.free_field5*1)
                              JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gi18n
                                ON (gi18n.parent_id=g.id AND gi18n.lang='{$registry['lang']}')
                              ORDER BY DATE(g.article_code) ASC";
                        $data = $db->GetAll($query);
                        foreach ($data as $d) {
                            if (empty($results[$d['id']]['overdue_installments'])) {
                                $results[$d['id']]['overdue_installments'] = array(
                                    'dates_amounts' => array(),
                                    'principals' => 0,
                                    'interest_rates' => 0,
                                    'guarantor_fees' => 0,
                                    'tax_management' => 0,
                                    'tax_angagement' => 0,
                                    'lpg'            => 0,
                                    'punishment_interest_rates' => 0
                                );
                            }
                            $results[$d['id']]['overdue_installments']['dates_amounts'][$d['gt2_row_id']] = array(
                                'date_of_payment' => $d['date_of_payment'],
                                'amount' => $d['amount']
                            );
                            $results[$d['id']]['overdue_installments_count']++;
                            $results[$d['id']]['overdue_installments']['principals'] += $d['principal'];
                            $results[$d['id']]['overdue_installments']['interest_rates'] += $d['interest_rate'];
                            $results[$d['id']]['overdue_installments']['guarantor_fees'] += $d['guarantor_fee'];
                            $results[$d['id']]['overdue_installments']['tax_management'] += $d['tax_management'];
                            $results[$d['id']]['overdue_installments']['tax_angagement'] += $d['tax_angagement'];
                            $results[$d['id']]['overdue_installments']['lpg'] += $d['lpg'];
                            $results[$d['id']]['overdue_installments']['punishment_interest_rates'] += $d['punishment_interest_rate'];
                        }

                        foreach ($results as $res_k => $res) {
                            if ($res_k == 'additional_options') {
                                continue;
                            }

                            $payment_rate_key = sprintf('%s->BGN', $res['currency']);
                            if (!isset($currency_rates[$payment_rate_key])) {
                                $currency_rates[$payment_rate_key] = Finance_Currencies::getRate($registry, $res['currency'], 'BGN');
                            }

                            if (!empty($res['overdue_installments'])) {
                                if (!empty($filters['show_only_paid'])) {
                                    unset($results[$res_k]);
                                    continue;
                                } else {
                                    $results[$res_k]['total_overdue'] += ($res['overdue_installments']['principals'] +
                                                                          $res['overdue_installments']['interest_rates'] +
                                                                          $res['overdue_installments']['guarantor_fees'] +
                                                                          $res['overdue_installments']['punishment_interest_rates'] +
                                                                          $res['overdue_installments']['tax_management'] +
                                                                          $res['overdue_installments']['tax_angagement'] +
                                                                          $res['overdue_installments']['lpg']);
                                    $results[$res_k]['total_expected_earning'] += ($res['overdue_installments']['principals'] +
                                                                                   $res['overdue_installments']['interest_rates'] +
                                                                                   $res['overdue_installments']['guarantor_fees'] +
                                                                                   $res['overdue_installments']['punishment_interest_rates'] +
                                                                                   $res['overdue_installments']['tax_management'] +
                                                                                   $res['overdue_installments']['tax_angagement'] +
                                                                                   $res['overdue_installments']['lpg']);

                                    $results['additional_options']['totals']['overdue_principals']                += $res['overdue_installments']['principals'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['overdue_interest_rates']            += $res['overdue_installments']['interest_rates'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['overdue_guarantor_fees']            += $res['overdue_installments']['guarantor_fees'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['overdue_tax_management']            += $res['overdue_installments']['tax_management'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['overdue_tax_angagement']            += $res['overdue_installments']['tax_angagement'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['overdue_lpg']                       += $res['overdue_installments']['lpg'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['overdue_punishment_interest_rates'] += $res['overdue_installments']['punishment_interest_rates'] * $currency_rates[$payment_rate_key];

                                    $results['additional_options']['totals']['total_expected_earnings']           += $results[$res_k]['total_expected_earning'] * $currency_rates[$payment_rate_key];
                                    $results['additional_options']['totals']['total_overdue']                     += $results[$res_k]['total_overdue'] * $currency_rates[$payment_rate_key];
                                }
                            }

                            foreach ($res['future_installments'] as $instalment) {
                                $results['additional_options']['totals']['principals']              += $instalment['principal'] * $currency_rates[$payment_rate_key];
                                $results['additional_options']['totals']['interest_rates']          += $instalment['interest_rate'] * $currency_rates[$payment_rate_key];
                                $results['additional_options']['totals']['guarantor_fees']          += $instalment['guarantor_fee'] * $currency_rates[$payment_rate_key];
                                $results['additional_options']['totals']['tax_management']          += $instalment['tax_management'] * $currency_rates[$payment_rate_key];
                                $results['additional_options']['totals']['tax_angagement']          += $instalment['tax_angagement'] * $currency_rates[$payment_rate_key];
                                $results['additional_options']['totals']['amounts']                 += $instalment['amount'] * $currency_rates[$payment_rate_key];
                                $results['additional_options']['totals']['total_expected_earnings'] += $instalment['amount'] * $currency_rates[$payment_rate_key];

                                $results[$res_k]['total_expected_earning']                          += $instalment['amount'];
                            }

                            $results[$res_k]['percent_overdue_installments'] = (!empty($results[$res_k]['installments_count']) ? round(($results[$res_k]['overdue_installments_count'] / $results[$res_k]['installments_count'])*100, 2) : '0.00');
                        }
                    }
                }
            }

            if (empty($results)) {
                $results['additional_options']['dont_show_export_button'] = true;
            }

            if (!empty($filters['paginate'])) {
                $results = array($results, 0);
            }

            return $results;
        }
    }
?>
