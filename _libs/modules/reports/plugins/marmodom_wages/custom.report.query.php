<?php
    Class Marmodom_Wages Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            /*
             *  TAKING INFORMATION FOR THE INSTALLING CARDS
             */
            //sql to take the ids of the needed additional vars
            $sql_for_add_vars = 'SELECT fm.id, fm.name, fm.model_type FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_INSTALLING_CARD_ID . ' AND (fm.name="' . WORKER_NAME . '" OR fm.name="' . DATE . '" OR fm.name="' . OPERATION_NAME . '" OR fm.name="' . WORK_DONE . '" OR fm.name="' . WORK_MEASURE . '") ORDER BY fm.position';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            $worker_name_id = '';
            $date_id = '';
            $operation_name_id = '';
            $work_done_id = '';
            $work_measure_id = '';

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == WORKER_NAME) {
                    $worker_name_id = $vars['id'];
                } else if ($vars['name'] == DATE) {
                    $date_id = $vars['id'];
                } else if ($vars['name'] == OPERATION_NAME) {
                    $operation_name_id = $vars['id'];
                } else if ($vars['name'] == WORK_DONE) {
                    $work_done_id = $vars['id'];
                } else if ($vars['name'] == WORK_MEASURE) {
                    $work_measure_id = $vars['id'];
                }
            }

            //sql to take the information from protocols
            $sql_for_installing_card['select'] = 'SELECT d.id as id, ' . "\n" .
                                           'd_cstm_worker.value as worker, CONCAT(ci18n.name, " ", ci18n.lastname) as worker_name,' . "\n" .
                                           'DATE_FORMAT(d_cstm_date.value, "%Y-%m-%d") as date, ' . "\n" .
                                           'd_cstm_operation.value as operation_name, d_cstm_work_done.value as work_done, ' . "\n" .
                                           'd_cstm_work_measure.value as work_measure' . "\n";

            $sql_for_installing_card['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_worker' . "\n" .
                                           '  ON (d_cstm_worker.model_id=d.id AND d_cstm_worker.var_id="' . $worker_name_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                           '  ON (ci18n.parent_id=d_cstm_worker.value AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
                                           '  ON (d_cstm_date.model_id=d.id AND d_cstm_date.num=d_cstm_worker.num AND d_cstm_date.var_id="' . $date_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_operation' . "\n" .
                                           '  ON (d_cstm_operation.model_id=d.id AND d_cstm_operation.var_id="' . $operation_name_id . '" AND d_cstm_operation.num=d_cstm_worker.num)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_work_done' . "\n" .
                                           '  ON (d_cstm_work_done.model_id=d.id AND d_cstm_work_done.var_id="' . $work_done_id . '" AND d_cstm_work_done.num=d_cstm_worker.num)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_work_measure' . "\n" .
                                           '  ON (d_cstm_work_measure.model_id=d.id AND d_cstm_work_measure.var_id="' . $work_measure_id . '" AND d_cstm_work_measure.num=d_cstm_worker.num)' . "\n";

            // construct where
            $where = array();
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.type="' . DOCUMENT_INSTALLING_CARD_ID . '"';
            $where[] = 'd.active!=0';

            if (! empty($filters['employee'])) {
                $where[] = 'd_cstm_worker.value="' . $filters['employee'] . '"';
            }
            if (! empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_date.value, "%Y-%m-%d") <= "' . $filters['to_date'] . '"';
            }
            if (! empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_date.value, "%Y-%m-%d") >= "' . $filters['from_date'] . '"';
            }
            if (!empty($filters['key_words'])) {
                $clauses = array();
                $all_words = array();
                $all_words = explode(" ", $filters['key_words']);
                foreach ($all_words as $word) {
                    if (mb_strlen($word, mb_detect_encoding($word)) >= 3) {
                        $clauses[] = '(d_cstm_operation.value LIKE \'%' . $word . '%\')';
                    }
                }

                if (!empty($clauses)) {
                    $where[] = '(' . implode(' OR ', $clauses) . ')';
                }
            }

            $sql_for_installing_card['where'] = 'WHERE ' . implode(' AND ', $where);

            $sql_for_installing_card['order'] = ' ORDER BY ci18n.name, ci18n.lastname ASC ' . "\n";

            $query_installing_cards = implode("\n", $sql_for_installing_card);
            $records_installing_cards = $registry['db']->GetAll($query_installing_cards);

            /*
             *  TAKING INFORMATION FOR THE WORKING CARDS
             */
            //sql to take the ids of the needed additional vars
            $sql_for_add_vars = 'SELECT fm.id, fm.name, fm.model_type FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_WORKING_CARD_ID . ' AND (fm.name="' . WORKER_NAME . '" OR fm.name="' . DATE . '" OR fm.name="' . OPERATION_NAME . '" OR fm.name="' . WORK_DONE . '" OR fm.name="' . WORK_MEASURE . '" OR fm.name="' . MATERIAL . '") ORDER BY fm.position';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            $worker_name_id = '';
            $operation_name_id = '';
            $date_id = '';
            $work_num_id = '';
            $work_measure_id = '';
            $material_id = '';

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == WORKER_NAME) {
                    $worker_name_id = $vars['id'];
                } else if ($vars['name'] == OPERATION_NAME) {
                    $operation_name_id = $vars['id'];
                } else if ($vars['name'] == DATE) {
                    $date_id = $vars['id'];
                } else if ($vars['name'] == WORK_DONE) {
                    $work_num_id = $vars['id'];
                } else if ($vars['name'] == WORK_MEASURE) {
                    $work_measure_id = $vars['id'];
                } else if ($vars['name'] == MATERIAL) {
                    $material_id = $vars['id'];
                }
            }

            //sql to take the information from protocols
            $sql_for_working_card['select'] = 'SELECT d.id as id, ' . "\n" .
                                           'd_cstm_worker.value as worker, CONCAT(ci18n.name, " ", ci18n.lastname) as worker_name, ' . "\n" .
                                           'd_cstm_operation.value as operation_name, d_cstm_work_num.value as work_done, ' . "\n" .
                                           'd_cstm_work_measure.value as work_measure, d_cstm_material.value as material ' . "\n";

            $sql_for_working_card['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_worker' . "\n" .
                                           '  ON (d_cstm_worker.model_id=d.id AND d_cstm_worker.var_id="' . $worker_name_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                           '  ON (ci18n.parent_id=d_cstm_worker.value AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_operation' . "\n" .
                                           '  ON (d_cstm_operation.model_id=d.id AND d_cstm_operation.var_id="' . $operation_name_id . '" AND d_cstm_operation.num=d_cstm_worker.num)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
                                           '  ON (d_cstm_date.model_id=d.id AND d_cstm_date.num=d_cstm_worker.num AND d_cstm_date.var_id="' . $date_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_work_num' . "\n" .
                                           '  ON (d_cstm_work_num.model_id=d.id AND d_cstm_work_num.num=d_cstm_worker.num AND d_cstm_work_num.var_id="' . $work_num_id . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_work_measure' . "\n" .
                                           '  ON (d_cstm_work_measure.model_id=d.id AND d_cstm_work_measure.var_id="' . $work_measure_id . '" AND d_cstm_work_measure.num=d_cstm_worker.num)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_material' . "\n" .
                                           '  ON (d_cstm_material.model_id=d.id AND d_cstm_material.var_id="' . $material_id . '" AND d_cstm_material.num=d_cstm_worker.num)' . "\n";

            // construct where
            $where = array();
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.type="' . DOCUMENT_WORKING_CARD_ID . '"';
            $where[] = 'd.active!=0';

            if (! empty($filters['employee'])) {
                $where[] = 'd_cstm_worker.value="' . $filters['employee'] . '"';
            }
            if (! empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_date.value, "%Y-%m-%d") <= "' . $filters['to_date'] . '"';
            }
            if (! empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_date.value, "%Y-%m-%d") >= "' . $filters['from_date'] . '"';
            }
            if (!empty($filters['key_words'])) {
                $clauses = array();
                $all_words = array();
                $all_words = explode(" ", $filters['key_words']);
                foreach ($all_words as $word) {
                    if (mb_strlen($word, mb_detect_encoding($word)) >= 3) {
                        $clauses[] = '(d_cstm_operation.value LIKE \'%' . $word . '%\')';
                        $clauses[] = '(d_cstm_material.value LIKE \'%' . $word . '%\')';
                    }
                }

                if (!empty($clauses)) {
                    $where[] = '(' . implode(' OR ', $clauses) . ')';
                }
            }

            $sql_for_working_card['where'] = 'WHERE ' . implode(' AND ', $where);

            $sql_for_working_card['order'] = ' ORDER BY ci18n.name, ci18n.lastname ASC ' . "\n";

            $query_working_cards = implode("\n", $sql_for_working_card);
            $records_working_cards = $registry['db']->GetAll($query_working_cards);

            $all_records = array_merge($records_installing_cards, $records_working_cards);

            // Prepare the final records
            $final_results = array();

            foreach ($all_records as $all_rec) {
                if ($all_rec['worker']) {
                    if (! isset($final_results[$all_rec['worker']])) {
                        $final_results[$all_rec['worker']] = array(
                            'id'            => $all_rec['worker'],
                            'name'          => $all_rec['worker_name'],
                            'operations'    => array()
                        );
                    }

                    if (isset($all_rec['material'])) {
                        $material_name = $all_rec['material'];
                    } else {
                        $material_name = '';
                    }
                    if (! isset($final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']])) {
                        $final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']] = array(
                            'name'      => $all_rec['operation_name'],
                            'materials' => array(),
                            'total'     => 0
                        );
                    }

                    if (!isset($final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']]['materials'][$material_name])) {
                        $final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']]['materials'][$material_name] = array (
                            'name'      => $material_name,
                            'quantity'  => 0,
                            'measure'   => $all_rec['work_measure']
                        );
                    }

                    $final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']]['materials'][$material_name]['quantity'] = sprintf("%01.2f", ($final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']]['materials'][$material_name]['quantity'] + $all_rec['work_done']));
                    $final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']]['total'] = sprintf("%01.2f", ($final_results[$all_rec['worker']]['operations'][$all_rec['operation_name']]['total'] + $all_rec['work_done']));
                }
            }

            foreach ($final_results as $key_emp => $employee) {
                $employee_rowspan = 0;

                foreach($employee['operations'] as $key_op => $operation) {
                    $operation_rowspan = count($operation['materials']);
                    $employee_rowspan += $operation_rowspan;
                    $final_results[$key_emp]['operations'][$key_op]['rowspan'] = $operation_rowspan;
                }

                $final_results[$key_emp]['rowspan'] = $employee_rowspan;
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>