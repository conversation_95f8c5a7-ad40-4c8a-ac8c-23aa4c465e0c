<?php
    Class Gig_Paid_Leave_Reqests Extends Reports {
        /**
         * Name of the model
         */
        public static $filters = array();
        public static $lang = array();
        public static $models = array();
        public static $registry;


        // IMPORTANT!!!: This report is used in GIG automations plugins
        public static function buildQuery(&$registry, $filters = array(), $filter_employee = '') {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }
            self::$filters = $filters;
            self::$lang = $model_lang;
            self::$registry = $registry;

            $final_results = array();

            $current_year = !empty($filters['year']) ? $filters['year'] : General::strftime('%Y');

            // Check if the current user have access to view the leave requests of all users
            $allow_show_all = (!defined('SHOW_ALL_USERS') || in_array($registry['currentUser']->get('id'), preg_split('/\s*,\s*/', constant('SHOW_ALL_USERS'))));

            // get the additional vars for employees
            $employee_vars = array(EMPLOYEE_AVAILABLE_DAYS_OFF, EMPLOYEE_SPECIAL_DAYS_OFF, EMPLOYEE_SPECIAL_DAYS_OFF_LEAVE_REASON, EMPLOYEE_DATE_START_WORK, EMPLOYEE_LEVEL_APPROVAL_1, EMPLOYEE_LEVEL_APPROVAL_2, EMPLOYEE_SPECIAL_DAYS_OFF_FROM_YEAR);
            $document_vars = array(FREE_DAYS_TYPE, FREE_DAYS_COUNT, FREE_DAYS_YEAR, FREE_DAYS_START_DATE, FREE_DAYS_END_DATE);

            $sql_for_add_vars = 'SELECT fm.id, fm.name, fm.model FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                                'WHERE (fm.model="Document" AND fm.model_type="' . DOCUMENT_LEAVE_TYPE . '" AND fm.name IN ("' . implode('","', $document_vars) . '")) OR ' . "\n" .
                                '      (fm.model="Customer" AND fm.model_type="' . PH_CUSTOMER_EMPLOYEE . '" AND fm.name IN ("' . implode('","', $employee_vars) . '")) ORDER BY fm.position';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            $free_days_type_id = '';
            $free_days_count_id = '';
            $free_days_year_id = '';
            $free_days_start_date_id = '';
            $free_days_end_date_id = '';
            $employee_available_days_off = '';
            $employee_special_days_off = '';
            $employee_special_days_off_leave_reason = '';
            $employee_date_start_work = '';
            $employee_level_approval_1 = '';
            $employee_level_approval_2 = '';
            $employee_special_days_off_from_year = '';

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == FREE_DAYS_TYPE && $vars['model'] == 'Document') {
                    $free_days_type_id = $vars['id'];
                } else if ($vars['name'] == FREE_DAYS_COUNT && $vars['model'] == 'Document') {
                    $free_days_count_id = $vars['id'];
                } else if ($vars['name'] == FREE_DAYS_YEAR && $vars['model'] == 'Document') {
                    $free_days_year_id = $vars['id'];
                } else if ($vars['name'] == FREE_DAYS_START_DATE && $vars['model'] == 'Document') {
                    $free_days_start_date_id = $vars['id'];
                } else if ($vars['name'] == FREE_DAYS_END_DATE && $vars['model'] == 'Document') {
                    $free_days_end_date_id = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_AVAILABLE_DAYS_OFF && $vars['model'] == 'Customer') {
                    $employee_available_days_off = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_SPECIAL_DAYS_OFF && $vars['model'] == 'Customer') {
                    $employee_special_days_off = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_DATE_START_WORK && $vars['model'] == 'Customer') {
                    $employee_date_start_work = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_SPECIAL_DAYS_OFF_LEAVE_REASON && $vars['model'] == 'Customer') {
                    $employee_special_days_off_leave_reason = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_LEVEL_APPROVAL_1 && $vars['model'] == 'Customer') {
                    $employee_level_approval_1 = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_LEVEL_APPROVAL_2 && $vars['model'] == 'Customer') {
                    $employee_level_approval_2 = $vars['id'];
                } else if ($vars['name'] == EMPLOYEE_SPECIAL_DAYS_OFF_FROM_YEAR && $vars['model'] == 'Customer') {
                    $employee_special_days_off_from_year = $vars['id'];
                }
            }

            // get all the needed employees
            $sql = 'SELECT c.id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, c_cstm_days_off.value as available_days_off, c_cstm_date_start_work.value as date_start_work, ' . "\n" .
                   '  c_cstm_special_days_off.value as special_days_off, c_cstm_leave_reason.value as leave_reason, ' . "\n" .
                   '  c_cstm_level_approval_1.value as level_approval_1, c_cstm_level_approval_2.value as level_approval_2, c_cstm_special_days_off_from_year.value as days_off_from_year' . "\n" .
                   'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                   '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_days_off' . "\n" .
                   '  ON (c_cstm_days_off.model_id=c.id AND c_cstm_days_off.var_id="' . $employee_available_days_off . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_date_start_work' . "\n" .
                   '  ON (c_cstm_date_start_work.model_id=c.id AND c_cstm_date_start_work.var_id="' . $employee_date_start_work . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_special_days_off_from_year' . "\n" .
                   '  ON (c_cstm_special_days_off_from_year.model_id=c.id AND c_cstm_special_days_off_from_year.var_id="' . $employee_special_days_off_from_year . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_leave_reason' . "\n" .
                   '  ON (c_cstm_leave_reason.model_id=c.id AND c_cstm_leave_reason.var_id="' . $employee_special_days_off_leave_reason . '" AND c_cstm_leave_reason.num=c_cstm_special_days_off_from_year.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_special_days_off' . "\n" .
                   '  ON (c_cstm_special_days_off.model_id=c.id AND c_cstm_special_days_off.var_id="' . $employee_special_days_off . '" AND c_cstm_special_days_off_from_year.num=c_cstm_special_days_off.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_level_approval_1' . "\n" .
                   '  ON (c_cstm_level_approval_1.model_id=c.id AND c_cstm_level_approval_1.var_id="' . $employee_level_approval_1 . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_level_approval_2' . "\n" .
                   '  ON (c_cstm_level_approval_2.model_id=c.id AND c_cstm_level_approval_2.var_id="' . $employee_level_approval_2 . '")' . "\n";
            $where = array();

            $where[] = 'c.deleted_by="0"';
            $where[] = 'c.type="' . PH_CUSTOMER_EMPLOYEE . '"';

            if (empty($filter_employee)) {
                if (!empty($filters['active_customers'])) {
                    $where[] = 'c.active IN ("' . implode('","', $filters['active_customers']) . '")';
                }

                // If the current user is allowed to view the leave requests of all users
                // and he selected the filter to show them
                if ($allow_show_all && !empty($filters['show_all'])) {
                    // If there are fields for filtering the leave requests by level of approval
                    if (!empty($employee_level_approval_1) || !empty($employee_level_approval_2)) {
                        // Filter the leave requests by level of approval
                        $where[] = '(c_cstm_level_approval_1.value="' . $registry['currentUser']->get('id') . '" OR c_cstm_level_approval_2.value="' . $registry['currentUser']->get('id') . '")';
                    } else {
                        // Do nothing: do not filter the leave requests - i.e. show the leave requests of all users
                    }
                } else {
                    // Show only the leave requests of the current user
                    $sql .= 'LEFT JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                            '  ON (u.employee=c.id)' . "\n";
                    $where[] = 'u.id="' . $registry['currentUser']->get('id') . '"';
                }
                $sql .= '  WHERE ' . implode(' AND ', $where) . "\n" .
                        '  ORDER BY CONCAT(ci18n.name, " ", ci18n.lastname) ASC';
            } else if ($filter_employee == 'all') {
                $sql .= '  WHERE ' . implode(' AND ', $where);
            } else {
                $where[] = "c.id = '{$filter_employee}'";
                $sql .= '  WHERE ' . implode(' AND ', $where);
            }
            $sql_data = $registry['db']->GetAll($sql);

            $final_results = array();
            $other_years_unused_paid_days = array();
            foreach ($sql_data as $emp_dat) {
                $total_days_to_use = 0;

                if ($emp_dat['date_start_work']) {
                    $entrance_year = General::strftime('%Y', strtotime($emp_dat['date_start_work']));

                    if ($entrance_year == $current_year) {
                        // calculate the days available for the year when the employee start work
                        $month_start_work = 0;
                        $month_start_work = date("n", strtotime($emp_dat['date_start_work']));

                        $days_per_month = sprintf("%01.2f", ($emp_dat['available_days_off']/12));
                        $months_to_use = 12 - $month_start_work;

                        $month_sec = strtotime('+1 month', strtotime(General::strftime('%Y-%m-01', strtotime($emp_dat['date_start_work'])))) - strtotime(General::strftime('%Y-%m-01', strtotime($emp_dat['date_start_work'])));
                        $days_in_first_working_month = round($month_sec/(60*60*24));
                        $days_in_starting_month = ($days_per_month / $days_in_first_working_month) * ($days_in_first_working_month - General::strftime('%e', strtotime($emp_dat['date_start_work'])) + 1);

                        $total_days_to_use = round(sprintf("%01.1f", (($months_to_use * $days_per_month) + $days_in_starting_month)));
                    } else {
                        $total_days_to_use = $emp_dat['available_days_off'];
                    }
                } else {
                    $total_days_to_use = $emp_dat['available_days_off'];
                }
                $total_days_to_use = (float)$total_days_to_use;

                if (!isset($final_results[$emp_dat['id']])) {
                    $final_results[$emp_dat['id']] = array(
                        'id'                                    => $emp_dat['id'],
                        'name'                                  => $emp_dat['name'],
                        'days_for_the_current_year'             => $total_days_to_use,
                        'special_days_for_the_current_year'     => 0,
                        'free_days_for_the_year'                => $total_days_to_use,
                        'free_days_left'                        => $total_days_to_use,
                        'free_days_left_with_special'           => $total_days_to_use,
                        'free_days_left_without_special'        => $total_days_to_use,
                        'free_days_from_other_years'            => 0,
                        'free_days_from_other_years_info'       => array(),
                        'free_days_paid_asked_from_other_years' => 0,
                        'free_days_paid_asked'                  => 0,
                        'free_days_paid_used_from_other_years'  => 0,
                        'free_days_paid_used'                   => 0,
                        'free_days_unpaid_asked'                => 0,
                        'free_days_unpaid_used'                 => 0
                    );
                }
//                 $final_results[$emp_dat['id']]['free_days_left'] += intval($emp_dat['special_days_off']);
//                 $final_results[$emp_dat['id']]['free_days_for_the_year'] += intval($emp_dat['special_days_off']);

                if ($emp_dat['leave_reason'] == EMPLOYEE_SPECIAL_DAYS_OFF_OPTION_LEFT && $emp_dat['days_off_from_year'] != $current_year) {
                    if (!in_array($emp_dat['days_off_from_year'], $other_years_unused_paid_days)) {
                        $other_years_unused_paid_days[] = $emp_dat['days_off_from_year'];
                    }
                    if (empty($final_results[$emp_dat['id']]['free_days_from_other_years_info'][$emp_dat['days_off_from_year']])) {
                        $final_results[$emp_dat['id']]['free_days_from_other_years_info'][$emp_dat['days_off_from_year']] = 0;
                    }
                    $final_results[$emp_dat['id']]['free_days_from_other_years_info'][$emp_dat['days_off_from_year']] += intval($emp_dat['special_days_off']);
                }
                if ($emp_dat['leave_reason'] != EMPLOYEE_SPECIAL_DAYS_OFF_OPTION_LEFT && $emp_dat['days_off_from_year'] == $current_year) {
                    $final_results[$emp_dat['id']]['free_days_for_the_year'] += intval($emp_dat['special_days_off']);
                    $final_results[$emp_dat['id']]['free_days_left_with_special'] += intval($emp_dat['special_days_off']);
                    $final_results[$emp_dat['id']]['special_days_for_the_current_year'] += intval($emp_dat['special_days_off']);
                }
            }

            $special_days = array();
            if (!empty($final_results)) {
                //sql to take all rows of documents_cstm table where the needed employee is found
                $sql = 'SELECT d.id AS id, ' . "\n" .
                       '  d.status, ' . "\n" .
                       '  d.customer as customer, ' . "\n" .
                       '  d.substatus, ' . "\n" .
                       '  d_cstm_free_days_type.value AS free_days_type, ' . "\n" .
                       '  d_cstm_free_days_count.value AS free_days_count, ' . "\n" .
                       '  DATE_FORMAT(d_cstm_free_days_start_date.value, "%Y-%m-%d") AS free_days_start_date, ' . "\n" .
                       '  DATE_FORMAT(d_cstm_free_days_end_date.value, "%Y-%m-%d") AS free_days_end_date, ' . "\n" .
                       '  d_cstm_free_days_year.value AS free_days_year' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_type' . "\n" .
                       '  ON (d_cstm_free_days_type.model_id=d.id AND d_cstm_free_days_type.var_id="' . $free_days_type_id . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_count' . "\n" .
                       '  ON (d_cstm_free_days_count.model_id=d.id AND d_cstm_free_days_count.var_id="' . $free_days_count_id . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_year' . "\n" .
                       '  ON (d_cstm_free_days_year.model_id=d.id AND d_cstm_free_days_year.var_id="' . $free_days_year_id . '")'  . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_start_date' . "\n" .
                       '  ON (d_cstm_free_days_start_date.model_id=d.id AND d_cstm_free_days_start_date.var_id="' . $free_days_start_date_id . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_free_days_end_date' . "\n" .
                       '  ON (d_cstm_free_days_end_date.model_id=d.id AND d_cstm_free_days_end_date.var_id="' . $free_days_end_date_id . '")' . "\n";

                $where = array();
                $where[] = 'd.active=1';
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . DOCUMENT_LEAVE_TYPE . '"';
                $where[] = 'd.substatus="' . DAYS_OFF_SUBSTATUS_APPROVED . '"';
//                 $where[] = 'd_cstm_free_days_year.value IS NOT NULL AND d_cstm_free_days_year.value!=""';
                $where[] = 'd_cstm_free_days_year.value IN (\'' . implode('\', \'', array_merge($other_years_unused_paid_days, array($current_year))) . '\')';
                $where[] = 'd.customer IN ("' . implode('","', array_keys($final_results)) . '")';
//                 $where[] = '(d_cstm_free_days_year.value="' . $current_year . '" OR DATE_FORMAT(d_cstm_free_days_end_date.value, "%Y")="' . $current_year . '" OR DATE_FORMAT(d_cstm_free_days_start_date.value, "%Y")="' . $current_year . '")';
                $sql .= 'WHERE ' . implode(' AND ', $where);
                $sql .= 'GROUP BY d.id' . "\n";
                $sql .= 'ORDER BY d_cstm_free_days_start_date.value' . "\n";

                //search basic details with current lang parameters
                $records = $registry['db']->GetAll($sql);

                $today = General::strftime("%Y-%m-%d");
                foreach ($records as $key_rec => $recs) {
                    // process data from the document
//                     if ($recs['free_days_year'] == $current_year) {
                    // Get the year in which the leave request is used (i.e. the year of the leave request's period)
                    // Here we count on that the start date and the end date of the leave request should not be in different years
                    // (this is agreed with GIG and is validated by an automatic action, when adding/editing a leave request)
                    $leave_request_used_in_year = General::strftime('%Y', $recs['free_days_start_date']);
                    if ($recs['free_days_year'] < $current_year && $recs['free_days_year'] < $leave_request_used_in_year || $recs['free_days_year'] == $current_year && $recs['free_days_year'] <= $leave_request_used_in_year) {
                        if ($recs['free_days_type'] == DAYS_OFF_TYPE_PAID) {
                            // If the leave request is used
                            if ($recs['free_days_start_date'] <= $today) {
                                // If the "Year from" is the current year
                                if ($recs['free_days_year'] == $current_year) {
                                    // Collect this leave request days as from current year used in current year
                                    $final_results[$recs['customer']]['free_days_paid_used'] += $recs['free_days_count'];
                                } else if (isset($final_results[$recs['customer']]['free_days_from_other_years_info'][$recs['free_days_year']]) && $leave_request_used_in_year == $current_year) {
                                    // If the "Year from" is one of the years that will be shown and the leave request is used in the current year
                                    // then collect this leave request days as from other years used in current year
                                    $final_results[$recs['customer']]['free_days_paid_used_from_other_years'] += $recs['free_days_count'];
                                }
                            } else {
                                // If the "Year from" is the current year
                                if ($recs['free_days_year'] == $current_year) {
                                    // Collect this leave request days as from current year used in current year
                                    $final_results[$recs['customer']]['free_days_paid_asked'] += $recs['free_days_count'];
                                } else if (isset($final_results[$recs['customer']]['free_days_from_other_years_info'][$recs['free_days_year']]) && $leave_request_used_in_year == $current_year) {
                                    // If the "Year from" is one of the years that will be shown and the leave request is used in the current year
                                    // then collect this leave request days as from other years used in current year
                                    $final_results[$recs['customer']]['free_days_paid_asked_from_other_years'] += $recs['free_days_count'];
                                }
                            }
//                             $final_results[$recs['customer']]['free_days_left'] -= $recs['free_days_count'];

                            // If the leave request is one of the years that will be shown from the report and the period in which the request is used is after the "Year from" of the request
                            if (isset($final_results[$recs['customer']]['free_days_from_other_years_info'][$recs['free_days_year']]) && $recs['free_days_year'] < $leave_request_used_in_year) {
                                // Calculate how many days are left from this year
                                $final_results[$recs['customer']]['free_days_from_other_years_info'][$recs['free_days_year']] -= $recs['free_days_count'];
                            }
//                             if ($recs['free_days_year'] != $current_year) {
//                                 $final_results[$recs['customer']]['free_days_from_other_years_info'][$recs['free_days_year']] -= $recs['free_days_count'];
//                             }
                        } else if ($recs['free_days_type'] == DAYS_OFF_TYPE_UNPAID) {
                            if ($today >= $recs['free_days_start_date']) {
                                $final_results[$recs['customer']]['free_days_unpaid_used'] += $recs['free_days_count'];
                            } else {
                                $final_results[$recs['customer']]['free_days_unpaid_asked'] += $recs['free_days_count'];
                            }
                        }
                    }
//                     } else {
//                         $final_results[$recs['customer']]['free_days_from_other_years'] += $recs['free_days_count'];
//                     }

                    $current_day_off = $recs['free_days_start_date'];
                    while($current_day_off <= $recs['free_days_end_date']) {
                        if (!isset($special_days[$current_day_off])) {
                            $special_days[$current_day_off] = array(
                                'class'     => '',
                                'employees' => array()
                            );
                        }

//                         if ($recs['free_days_year'] == $current_year) {
                            $special_days[$current_day_off]['employees'][$recs['customer']] = array(
                                'name'  => $final_results[$recs['customer']]['name'],
                                'type'  => '',
                                'label' => ''
                            );

                            $type = '';
                            if ($recs['free_days_type'] == DAYS_OFF_TYPE_PAID) {
                                if ($today >= $recs['free_days_start_date']) {
                                    $type = 'paid_used';
                                } else {
                                    $type = 'paid_requested';
                                }
                            } else {
                                if ($today >= $recs['free_days_start_date']) {
                                    $type = 'unpaid_used';
                                } else {
                                    $type = 'unpaid_requested';
                                }
                            }
                            $special_days[$current_day_off]['employees'][$recs['customer']]['type'] = $type;
                            $special_days[$current_day_off]['employees'][$recs['customer']]['label'] = $registry['translater']->translate('reports_type_days_off_' . $type);

                            // check the general day class
                            $current_day_included_types = array();
                            foreach ($special_days[$current_day_off]['employees'] as $emp_id => $emp_dat) {
                                $current_day_included_types[] = $emp_dat['type'];
                            }
                            $current_day_included_types = array_unique($current_day_included_types);
                            if (count($current_day_included_types) == 1) {
                                $special_days[$current_day_off]['class'] = reset($current_day_included_types);
                            } else {
                                $special_days[$current_day_off]['class'] = 'mixed_days_off';
                            }
//                         }
                        $current_day_off = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($current_day_off)));
                    }
                }
            }

            $other_years_unused_paid_days_have_days = array();
            foreach ($other_years_unused_paid_days as $year) {
                $other_years_unused_paid_days_have_days[$year] = false;
            }

            // Go through each customer (employee)
            foreach ($final_results as $customer_id => $customer_data) {
                // Set "-" for years for which there is no data from the employee file
                foreach ($other_years_unused_paid_days as $year) {
                    if (!array_key_exists($year, $customer_data['free_days_from_other_years_info'])) {
                        $customer_data['free_days_from_other_years_info'][$year] = '-';
                    } else {
                        // Calculate total allowed leave requests for the current year
                        $customer_data['free_days_for_the_year'] += $customer_data['free_days_from_other_years_info'][$year];
                        $customer_data['free_days_from_other_years'] += $customer_data['free_days_from_other_years_info'][$year];

                        // If there is at least one value for the year, then set that it have days (i.e. that it should be shown)
                        if ($customer_data['free_days_from_other_years_info'][$year] != 0) {
                            $other_years_unused_paid_days_have_days[$year] = true;
                        }
                    }
                }
//                 ksort($customer_data['free_days_from_other_years_info']);

                // Calculate total allowed leave requests for the current year
                $customer_data['free_days_for_the_year'] += $customer_data['free_days_paid_used_from_other_years'];
                $customer_data['free_days_for_the_year'] += $customer_data['free_days_paid_asked_from_other_years'];
                $customer_data['free_days_from_other_years'] += $customer_data['free_days_paid_used_from_other_years'];
                $customer_data['free_days_from_other_years'] += $customer_data['free_days_paid_asked_from_other_years'];

                // Calculate total leave requests left for the year
                $customer_data['free_days_left'] = $customer_data['free_days_for_the_year'] - $customer_data['free_days_paid_used_from_other_years'] - $customer_data['free_days_paid_used'] - $customer_data['free_days_paid_asked_from_other_years'] - $customer_data['free_days_paid_asked'];

                /*
                 * Calculate free days left for the current year including the special days
                 */
                // This will be used from automatic action for validation when adding/editing a leave request
                $customer_data['free_days_left_with_special'] = $customer_data['free_days_left_with_special'] - $customer_data['free_days_paid_used'] - $customer_data['free_days_paid_asked'];
                // This will be used from automatic action which in the end of the year will add into the employees files the leave requests left from the current year so they can be used into the next year
                $customer_data['free_days_left_without_special'] = $customer_data['free_days_left_without_special'] - $customer_data['free_days_paid_used'];

                // If it's set to not show employees without any days left for the current year
                // and the current employee have no days for the curent year
                if (SKIP_NO_FREE_DAYS_AVAILABLE_EMPLOYEES && empty($customer_data['free_days_left'])) {
                    // Remove this employee from the results
                    unset($final_results[$customer_id]);
                } else {
                    // Set the customer data back into the final results
                    $final_results[$customer_id] = $customer_data;
                }
            }

            // Hide years without days for all employees
            $other_years_unused_paid_days = array_flip($other_years_unused_paid_days);
            foreach ($other_years_unused_paid_days_have_days as $year => $have_days) {
                if (!$have_days) {
                    unset($other_years_unused_paid_days[$year]);
                }
            }
            $other_years_unused_paid_days = array_keys($other_years_unused_paid_days);

            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $calendar = new Calendars_Calendar(date('Y-m-d', strtotime($current_year.'-01-01')));
            $final_results['additional_options']['calendar'] = $calendar;
            $final_results['additional_options']['special_days'] = $special_days;
            sort($other_years_unused_paid_days);
            $final_results['additional_options']['other_years_unused_paid_days'] = $other_years_unused_paid_days;

            $calendar_settings = Calendars_Calendar::getSettings($registry);
            $final_results['additional_options']['monday_start'] = $calendar_settings['week_start_day'];

            $final_results['additional_options']['current_year'] = $current_year;

            // query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                //no pagination required return only the models
                $results = $final_results;
            }

            return $results;
        }
    }
?>