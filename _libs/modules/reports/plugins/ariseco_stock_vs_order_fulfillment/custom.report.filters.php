<?php

class Custom_Report_Filters extends Report_Filters
{
    /**
     * Defining filters for the certain type report
     *
     * @param Registry $registry - the main registry
     *
     * @return array - filter definitions of report
     */
    function defineFilters($registry) {
        // Get the report settings
        $settings = Reports::getReportSettings($registry);

        // Prepare an array for the filters
        $filters = [];

        // Load report i18n params to JavaScript
        $report_i18n_file_path = implode(DIRECTORY_SEPARATOR, [realpath(dirname(__FILE__)), 'i18n', $registry['lang'], 'reports.ini']);
        $report_i18n = $this->translater->getFileParams($report_i18n_file_path);
        $report_i18n_js = [];
        foreach ($report_i18n as $lang_param => $lang_param_value) {
            $report_i18n_js[] = sprintf(
                "i18n['%s'] = '%s';",
                $lang_param,
                $lang_param_value
            );
        }
        self::loadDefaultFilter($registry, $filters, 'scripts', array(
            'custom_scripts' => array(
                array(
                    'type' => 'inline',
                    'src' => implode("\n", $report_i18n_js),
                )
            ),
        ));

        // Load EJ2
        $registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
        $registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
        $registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

        /*
         * FILTERS
         */
        // Date
        $filters['date_from'] = array (
            'name'              => 'date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'date_to',
            'label'             => $this->i18n('filter_date'),
            'required'          => true,
        );
        $filters['date_to'] = array (
            'name' => 'date_to'
        );

        // Deadline
        $filters['deadline_from'] = array (
            'name'              => 'deadline_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'deadline_to',
            'label'             => $this->i18n('filter_deadline'),
        );
        $filters['deadline_to'] = array (
            'name' => 'deadline_to'
        );

        // Sale order num
        // $filters['sale_order_id'] = [
        //     'name'            => 'sale_order_id',
        //     'type'            => 'custom_filter',
        //     'actual_type'     => 'autocompleter',
        //     'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        //     'width'           => 244,
        //     'label'           => $this->i18n('filter_sale_order_num'),
        //     'autocomplete'    => [
        //         'type'         => 'documents',
        //         'search'       => ['<a__sale_order_num>'],
        //         'suggestions'  => '<a_sale_order_num>',
        //         'clear'        => 1,
        //         'fill_options' => [
        //             '$sale_order_id => <id>',
        //             '$sale_order_id_autocomplete => <a_sale_order_num>',
        //             '$sale_order_id_oldvalue => <a_sale_order_num>',
        //         ],
        //         'filters'      => ['<type>' => strval($settings['doc_type_sales_orders_customers'])],
        //         'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'documents', 'documents', 'ajax_select')
        //     ]
        // ];
        $filters['sale_order_num'] = [
            'name'            => 'sale_order_num',
            'type'            => 'custom_filter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'actual_type'     => 'text',
            'label'           => $this->i18n('filter_sale_order_num'),
        ];

        // Order status
        $filters['status'] = [
            'name'    => 'status',
            'label'   => $this->i18n('filter_status'),
            'type'    => 'dropdown',
            'options' => Documents_Dropdown::getStatuses([$registry, 'model_types' => [$settings['doc_type_sales_orders_customers']]])
        ];

        $channel_options = Dropdown::getCustomDropdown([
            $registry,
            'table'      => 'DB_TABLE_NOMENCLATURES',
            'table_i18n' => 'DB_TABLE_NOMENCLATURES_I18N',
            'where'      => "type = {$settings['nom_type_channel_for_sales']}",
        ]);

        // Channel
        $filters['channel'] = [
            'name'            => 'channel',
            'label'           => $this->i18n('filter_channel'),
            'type'            => 'custom_filter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'actual_type'     => 'dropdown',
            'options'         => $channel_options,
        ];

        // Marketplace
        $filters['marketplace'] = [
            'name'            => 'marketplace',
            'label'           => $this->i18n('filter_marketplace'),
            'type'            => 'custom_filter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'actual_type'     => 'text',
            // 'type'    => 'dropdown',
            // 'options' => $channel_options,
        ];

        // Child SKU
        $filters['sku'] = [
            'name'            => 'sku',
            'type'            => 'custom_filter',
            'actual_type'     => 'autocompleter',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'width'           => 244,
            'label'           => $this->i18n('filter_child_sku'),
            'autocomplete'    => [
                'type'         => 'nomenclatures',
                'search'       => ['<code>', '<name>'],
                'suggestions'  => '[<code>] <name>',
                'clear'        => 1,
                'fill_options' => [
                    '$sku => <id>',
                    '$sku_autocomplete => [<code>] <name>',
                    '$sku_oldvalue => [<code>] <name>',
                ],
                'filters'      => ['<type>' => strval($settings['nom_type_child_sku'])],
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
            ]
        ];

        // Exclude Closed orders statuses
        $filters['exclude_closed'] = [
            'name'    => 'exclude_closed',
            'label'   => $this->i18n('filter_status_not'),
            'type'    => 'checkbox_group',
            'options' => [
                [
                    'option_value' => 1
                ]
            ]
        ];

        return $filters;
    }

    function processDependentFilters(&$filters) {
        $unset_filters = [];
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        return $filters;
    }
}
