<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
      <table border="1" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
        {if $reports_additional_options.total_working_project_time}
          <tr>
            <td colspan="9"><strong>{#reports_total_project_working_time#|escape}:</strong></td>
            <td><strong>{$reports_additional_options.total_working_project_time|default:"0:00"}</strong></td>
          </tr>
        {/if}
        <tr>
          <td nowrap="nowrap">{#num#|escape}</td>
          <td nowrap="nowrap">{#reports_full_num#|escape}</td>
          <td nowrap="nowrap">{#reports_customer#|escape}</td>
          <td nowrap="nowrap">{#reports_about#|escape}</td>
          <td nowrap="nowrap">{#reports_priority#|escape}</td>
          <td nowrap="nowrap">{#reports_status#|escape}</td>
          <td nowrap="nowrap">{#reports_responsible#|escape}</td>
          <td nowrap="nowrap">{#reports_observer#|escape}</td>
          <td nowrap="nowrap">{#reports_worker#|escape}</td>
          <td nowrap="nowrap">{#reports_man_total_working_time#|escape}</td>
        </tr>
        {counter start=0 name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr>
            <td nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td>
              {$result.full_num|numerate:$result.direction|default:"&nbsp;"}
            </td>
            <td>
              {$result.customer|default:""}
            </td>
            <td>
              {$result.about|default:"0"}
            </td>
            <td>
              {$result.priority|default:""}
            </td>
            <td>
              {$result.status|default:""}
            </td>
            <td nowrap="nowrap">
              {$result.responsible|default:""}
            </td>
            <td nowrap="nowrap">
              {$result.observer|default:""}
            </td>
            <td nowrap="nowrap">
              {$result.worker_name|default:""}
            </td>
            <td>
              {$result.man_working_minutes|default:"0"}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="10">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td colspan="10"></td>
        </tr>
      </table>
  </body>
</html>