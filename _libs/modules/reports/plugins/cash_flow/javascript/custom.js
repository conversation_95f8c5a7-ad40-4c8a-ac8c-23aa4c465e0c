Event.observe(window, 'load', function () {
    let reportOptions = {
        i18n: i18n,
    };
    const precision = document.querySelector('#precision');
    if (precision) {
        reportOptions.precision = precision.value;
    }
    const ferTypeInvoice = document.querySelector('#fer_type_invoice');
    if (ferTypeInvoice) {
        reportOptions.ferTypeInvoice = ferTypeInvoice.value;
    }
    const ferTypeProInvoice = document.querySelector('#fer_type_pro_invoice');
    if (ferTypeProInvoice) {
        reportOptions.ferTypeProInvoice = ferTypeProInvoice.value;
    }
    const gridDivBalance = document.querySelector('#grid_balance');
    if (gridDivBalance) {
        reportOptions.gridDivBalance = gridDivBalance;
    }
    const gridDivExpenses = document.querySelector('#grid_expenses');
    if (gridDivExpenses) {
        reportOptions.gridDivExpenses = gridDivExpenses;
    }
    const gridDivIncomes = document.querySelector('#grid_incomes');
    if (gridDivIncomes) {
        reportOptions.gridDivIncomes = gridDivIncomes;
    }

    let report = new CashFlow(reportOptions);
    report.prepareGrids();
});
