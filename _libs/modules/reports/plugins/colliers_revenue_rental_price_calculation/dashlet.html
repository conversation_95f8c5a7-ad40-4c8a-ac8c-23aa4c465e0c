{if $custom_css}
<script type="text/javascript">
    var fileref=document.createElement("link");
    fileref.setAttribute("rel", "stylesheet");
    fileref.setAttribute("type", "text/css");
    fileref.setAttribute("href", '{$custom_css}?{$system_options.build}');
    document.getElementsByTagName("head")[0].appendChild(fileref);
</script>
{/if}
<table border="0" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_contract_num#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_customer#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_trademark#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_trade_area#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_base_price#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_turovers_price#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_invoiced#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_difference#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_currency#|escape}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_financial#|escape}</div></td>
      <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_preview#|escape}</div></td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="t_border hright" nowrap="nowrap" width="25">
          {counter name='item_counter' print=true}
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$result.contract_id}" target="_blank">
            {$result.contract_num|default:"&nbsp;"}
          </a>
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer}" target="_blank">
            {$result.customer_name|default:"&nbsp;"}
          </a>
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=trademarks&amp;trademarks={$result.customer}" target="_blank">
            {$result.trademark_name|default:"&nbsp;"}
          </a>
        </td>
        <td class="t_border">
          {$result.object_name}
        </td>
        <td class="t_border" style="text-align: right">
          {$result.base_price|default:0}
        </td>
        <td class="t_border" style="text-align: right">
          {$result.turnovers_price|default:0}
        </td>
        <td class="t_border" style="text-align: right">
          {$result.invoiced}
        </td>
        <td class="t_border" style="text-align: right">
          {$result.difference}
        </td>
        <td class="t_border">
          {$result.currency}
        </td>
        <td class="t_border">
        {foreach from=$result.financial item=contact}
          {$contact.name}<br/>
        {/foreach}
        </td>
        <td class="t_border hcenter">
          {if $result.total_days > $result.incomes_count || $result.difference eq '---'}
            <img src="{$theme->imagesUrl}pdf.png" alt="{#reports_preview#|escape}" title="{#reports_preview#|escape}" border="0" class="dimmed" />
          {else}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=reports&amp;report_type={$report_type}&amp;reports=generate_preview&amp;generate_preview={$result.idx}"
             onmousedown="prepareURL(this);"
             target="_blank">
            <img src="{$theme->imagesUrl}pdf.png" alt="{#reports_preview#|escape}" title="{#reports_preview#|escape}" border="0" />
          </a>
          {/if}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="14">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="14"></td>
    </tr>
  </table>
</td>
</tr>
<tr>
  <td>&nbsp;</td>
</tr>
{*    <tr>
      <td class="pagemenu" bgcolor="#FFFFFF" colspan="9">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
        }
      </td>
    </tr>*}
</table>