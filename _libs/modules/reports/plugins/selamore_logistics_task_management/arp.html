<!-- Headers templates -->
<script id="template_th_notification_to_supplier" type="text/x-template">
  <span class="e-headertext">{#th_notification_to_supplier#}</span>
  <div><input{if ($reports_additional_options.edit_allowed ne 'allowed')} disabled="disabled"{/if} type="checkbox" id="notification_to_supplier_all" /></div>
</script>
<script id="template_th_request_date" type="text/x-template">
  <span class="e-headertext" title="{#th_loading_arriving_at_warehouse#}">{#th_request_date#}</span>
</script>

<script id="template_th_confirmed_correct_date" type="text/x-template">
  <span class="e-headertext">{#th_confirmed_correct_date#}</span>
  <input type="text" id="confirmed_date_all" />
</script>

<script id="template_th_final_delivery_week" type="text/x-template">
  <span class="e-headertext" title="{#th_as_per_customer_contract#}">{#th_final_delivery_week#}</span>
</script>

<script id="template_th_remaining_weeks" type="text/x-template">
  <span class="e-headertext" title="{#th_from_now_until_delivery#}">{#th_remaining_weeks#}</span>
</script>

<!-- Column fields templates -->
<script id="template_supplier_information" type="text/x-template">
  {literal}${if(os.supplier_id)}{/literal}
    <div>
      <img src="{$theme->imagesUrl}small/customer.png" width="16" height="16" border="0" alt="" title="" />&nbsp;
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${os.supplier_id}{/literal}" target="_blank">
        {literal}${os.supplier_name}{/literal}
      </a>
      {literal}
        ${if(os.terms_of_delivery_name)}
          &nbsp;(${os.terms_of_delivery_name})
        ${/if}
      {/literal}
    </div>
  {literal}${/if}{/literal}
  {literal}${if(logistics)}{/literal}
    {literal}${for(logistic of logistics)}{/literal}
      <div>
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${logistic.id}{/literal}" target="_blank">
          {literal}${logistic.name}{/literal}
        </a>
      </div>
    {literal}${/for}{/literal}
  {literal}${/if}{/literal}
  <div>{#supplier_loading_point_sequence#}:&nbsp;
    {literal}${if(supplier_additional_data.loading_point_sequence)}{/literal}
      {literal}${supplier_additional_data.loading_point_sequence}{/literal}
    {literal}${/if}{/literal}
  </div>
</script>

<script id="template_terms_of_purchase" type="text/x-template">
  {literal}${os.terms_of_purchase_name}{/literal}
  {literal}${if(os.show_delayed_payment_terms)}{/literal}
    <div>
      {literal}${os.supplier_credit_limit_term}{/literal} {#days#} {literal}${os.supplier_credit_limit_repayment_name}{/literal}
    </div>
    <div>
      <b>{literal}${os.supplier_credit_limit} ${os.supplier_credit_limit_currency}{/literal}</b>
    </div>
  {literal}${/if}{/literal}
</script>

<script id="template_invoices" type="text/x-template">
  {literal}${if(invoice)}{/literal}
    <div>
      ({literal}${invoice.type_code_name}{/literal})&nbsp;
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=expenses_reasons&amp;expenses_reasons=view&amp;view={literal}${invoice.id}{/literal}" target="_blank">
        {literal}${invoice.invoice_num} / ${invoice.issue_date_formatted}{/literal}
      </a>
    </div>

    {literal}${if(invoice.invoice)}{/literal}
      <div>
        ({literal}${invoice.invoice.type_code_name}{/literal})&nbsp;
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=expenses_reasons&amp;expenses_reasons=view&amp;view={literal}${invoice.invoice.id}{/literal}" target="_blank">
          {literal}${invoice.invoice.invoice_num} / ${invoice.invoice.issue_date_formatted}{/literal}
        </a>
      </div>
    {literal}${/if}{/literal}

    <div>
      <span class="invoices_total_amount">{#th_invoices_total_amount#}: </span>
      {literal}${last_invoice.total_with_vat_formatted} ${last_invoice.currency}{/literal}
    </div>
    <div>
      <span class="invoices_total_paid_amount">{#th_invoices_total_paid_amount#}: </span>
      {literal}${last_invoice.paid_formatted} ${last_invoice.currency}{/literal}
    </div>
    <div>
      <span class="invoices_for_payment_amount">{#th_invoices_for_payment_amount#}: </span>
      {literal}${last_invoice.for_payment_formatted} ${last_invoice.currency}{/literal}
    </div>
  {literal}${/if}{/literal}
</script>

<script id="template_arps" type="text/x-template">
  {literal}${if(invoice)}{/literal}
    {literal}${if(invoice.arps && invoice.arps.records)}{/literal}
      {literal}${for(arp of invoice.arps.records)}{/literal}
        {include file=`$templatesDirPlugin``$report_type`/_arps.html}
      {literal}${/for}{/literal}
    {literal}${/if}{/literal}
    {literal}${if(invoice.invoice && invoice.invoice.arps && invoice.invoice.arps.records)}{/literal}
      {literal}${for(arp of invoice.invoice.arps.records)}{/literal}
        {include file=`$templatesDirPlugin``$report_type`/_arps.html}
      {literal}${/for}{/literal}
    {literal}${/if}{/literal}
  {literal}${/if}{/literal}
  {literal}${if(os.arps)}{/literal}
    {literal}${for(arp of os.arps.records)}{/literal}
      {include file=`$templatesDirPlugin``$report_type`/_arps.html}
    {literal}${/for}{/literal}
  {literal}${/if}{/literal}
  {literal}${if(!invoice || proposed_amount_for_payment && proposed_amount_for_payment > 0)}
    <div>
      <input type="text" name="amount_for_payment[${arps_key}]" value="${if(proposed_amount_for_payment && proposed_amount_for_payment > 0)}${proposed_amount_for_payment_formatted}${/if}" class="amount_for_payment" autocomplete="off">
      <input type="hidden" name="proposed_amount_for_payment[${arps_key}]" value="${if(proposed_amount_for_payment && proposed_amount_for_payment > 0)}${proposed_amount_for_payment_formatted}${/if}">
      <input type="hidden" name="total_paid_amount[${arps_key}]" value="${if(last_invoice)}${last_invoice.paid}${/if}">
      <input type="checkbox" name="create_arp[${arps_key}]" value="${arps_key}">
      <div class="payment_deadline_container">
        <input type="text" name="payment_deadline_formatted[${arps_key}]" value=""/>
        <input type="hidden" name="payment_deadline[${arps_key}]" value=""/>
      </div>
      <input type="hidden" name="terms_of_purchase_id[${arps_key}]" value="${os.terms_of_purchase_id}">
      <input type="hidden" name="terms_of_purchase_name[${arps_key}]" value="${os.terms_of_purchase_name}">
    </div>
  ${/if}{/literal}
</script>

<script id="template_order_to_supplier" type="text/x-template">
  {literal}${if(os)}{/literal}
    <img src="{$theme->imagesUrl}documents_{literal}${os.status}{/literal}.png" width="16" height="16" border="0" alt="" title="{literal}${os.status_name}${if(os.substatus_name)}: ${os.substatus_name}${/if}{/literal}" />&nbsp;
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${os.id}{/literal}" target="_blank">
      {literal}${os.order_number}{/literal}
    </a>
    <div>
      ({literal}${os.total_with_vat} ${os.currency}{/literal})
    </div>
  {literal}${/if}{/literal}
</script>

<script id="template_notification_to_supplier" type="text/x-template">
  {literal}${if (notification_to_supplier_id)}{/literal}
    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${notification_to_supplier_id}{/literal}" target="_blank">
      {literal}${notification_to_supplier_full_num}{/literal}
    </a>
  {literal}${else if (invoice)}{/literal}
    <div class="notification-to-supplier-input-container">
      <input{if ($reports_additional_options.edit_allowed ne 'allowed')} disabled="disabled"{/if} type="checkbox" {literal}name="notification_to_supplier[${gt2_id}]" id="notification_to_supplier_${gt2_id}" value="${gt2_id}"{/literal} class="notification_to_supplier" />
      {literal}
        <input type="hidden" name="invoice_id[${gt2_id}]" value="${invoice.id}">
        <input type="hidden" name="invoice_num[${gt2_id}]" value="${invoice.invoice_num} / ${invoice.issue_date_formatted}">
      {/literal}
    <div>
  {literal}${else}{/literal}
    {#no_invoice#}
  {literal}${/if}{/literal}
</script>

<script id="template_connected_documents" type="text/x-template">
  {literal}${if(ls)}{/literal}
    <div>
      <img src="{$theme->imagesUrl}documents_{literal}${ls.status}{/literal}.png" width="16" height="16" border="0" alt="" title="{literal}${ls.status_name}${if(ls.substatus_name)}: ${ls.substatus_name}${/if}{/literal}" />&nbsp;
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${ls.id}{/literal}" target="_blank">
        {literal}${ls.full_num} / ${ls.date_formatted}{/literal}
      </a>
    </div>

    {literal}${if(ls.trading_company_id)}{/literal}
      <div>
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${ls.trading_company_id}{/literal}" target="_blank">
          {literal}${ls.trading_company_name}{/literal}
        </a>
      </div>
    {literal}${/if}{/literal}

    {literal}${if(ls.office_name)}{/literal}
      <div>
        {literal}${ls.office_name}{/literal}
      </div>
    {literal}${/if}{/literal}

    {literal}${if(ls.designer_id || ls.other_designers)}{/literal}
      <div>
    {literal}${/if}{/literal}
    {literal}${if(ls.designer_id)}{/literal}
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${ls.designer_id}{/literal}" target="_blank">
        {literal}${ls.designer_name}{/literal}
      </a>
    {literal}${/if}{/literal}
    {literal}${if(ls.other_designers)}{/literal}
      {literal}${for(other_designer of ls.other_designers)}{/literal}
        {literal}${if(other_designerIndex == 0 && ls.designer_id)},&nbsp;${/if}{/literal}
        {literal}${if(other_designerIndex)},&nbsp;${/if}{/literal}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${other_designer.id}{/literal}" target="_blank">
          {literal}${other_designer.name}{/literal}
        </a>
      {literal}${/for}{/literal}
    {literal}${/if}{/literal}
    {literal}${if(ls.designer_id || ls.other_designers)}{/literal}
      <div>
    {literal}${/if}{/literal}

    {literal}${if(ls.customer_id)}{/literal}
      <div>
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${ls.customer_id}{/literal}" target="_blank">
          {literal}${ls.customer_name}{/literal}
        </a>
      </div>
    {literal}${/if}{/literal}
  {literal}${/if}{/literal}
</script>

<script id="template_confirmed_date" type="html/x-template">
  {literal}
    <input type="text" name="confirmed_date_formatted[${gt2_id}]" value="${confirmed_date_formatted}"/>
    <input type="hidden" name="confirmed_date[${gt2_id}]" value="${confirmed_date}" disabled/>
  {/literal}
</script>

<script id="template_final_delivery_week" type="text/x-template">
  {literal}
    ${if(ls.delivery_deadline_week_number)}
      ${ls.delivery_deadline_week_number}${if(ls.delivery_deadline_year)} (${ls.delivery_deadline_year})${/if}
    ${/if}
  {/literal}
</script>

<script id="template_product_name" type="text/x-template">
  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={literal}${product_id}{/literal}" target="_blank">
    {literal}${product_name}{/literal}
  </a>
</script>

<div id="dialog_validate_add_arps_save_confirmed_dates"></div>
