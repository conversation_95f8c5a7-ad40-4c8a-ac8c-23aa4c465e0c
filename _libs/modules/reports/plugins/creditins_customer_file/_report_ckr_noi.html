<h1>{#reports_report_ckr_noi#|escape}</h1>
<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="margin-bottom: 5px;">
  <tr class="reports_title_row">
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 55px;">{#reports_report_view_ckr#|escape}</div></td>
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 105px;">{#reports_report_last_date_report_ckr#|escape}</div></td>
    <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 55px;">{#reports_report_view_noi#|escape}</div></td>
    <td style="text-align: center; vertical-align: middle;"><div style="width: 105px;">{#reports_report_last_date_report_noi#|escape}</div></td>
  </tr>
  {if $ckr_noi}
    <tr class="t_odd1 t_odd2">
      <td class="t_border" style="text-align: center;">
        {assign var='file_info' value=$ckr_noi.ckr_file}
        {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
          {if !$file_info->get('not_exist')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=viewfile&amp;viewfile={$var.model_id}&amp;file={$file_info->get('id')}" target="_blank"><img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" title="{#open#|escape}" /></a>
          {else}
            <img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" class="pointer dimmed" />
          {/if}
        {else}
          &nbsp;
        {/if}
      </td>
      <td class="t_border hcenter">
        {$ckr_noi.ckr_date|date_format:#date_short#|escape|default:"&nbsp;"}
      </td>
      <td class="t_border" style="text-align: center;">
        {assign var='file_info' value=$ckr_noi.noi_file}
        {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
          {if !$file_info->get('not_exist')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=viewfile&amp;viewfile={$var.model_id}&amp;file={$file_info->get('id')}" target="_blank"><img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" title="{#open#|escape}" /></a>
          {else}
            <img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" class="pointer dimmed" />
          {/if}
        {else}
          &nbsp;
        {/if}
      </td>
      <td class="hcenter">
        {$ckr_noi.noi_date|date_format:#date_short#|escape|default:"&nbsp;"}
      </td>
    </tr>
  {else}
    <tr class="t_odd1 t_odd2">
      <td class="error" colspan="4">{#no_items_found#|escape}</td>
    </tr>
  {/if}
  <tr>
    <td class="t_footer" colspan="4"></td>
  </tr>
</table>
<button type="button" class="button" style="float: left;" onclick="activateCkrNoiAddPanel('ckr', '{$customer_id}'); return false;">{#reports_report_add_ckr_report#|escape}</button>
<button type="button" class="button" style="float: right; margin-right: 0;" onclick="activateCkrNoiAddPanel('noi', '{$customer_id}'); return false;">{#reports_report_add_noi_report#|escape}</button>
