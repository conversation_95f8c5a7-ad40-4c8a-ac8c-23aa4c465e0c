<?php

use setasign\Fpdi\PdfParser\CrossReference\CrossReferenceException;
use setasign\Fpdi\PdfParser\Filter\FilterException;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfParser\Type\PdfTypeException;
use setasign\Fpdi\PdfReader\PdfReaderException;

/**
 * Trait for PDF processing
 */
trait PdfTrait
{
    /**
     * Check if there are missing files
     *
     * @var bool
     */
    private bool $hasMissingFiles = false;

    /**
     * Check if there are broken files
     *
     * @var bool
     */
    private bool $hasBrokenFiles = false;
    /**
     * Comparative table analog types
     * 33 - market
     * 36 - revenue
     * 41 - cost
     *
     * @var array|int[]
     */
    private array $comparativeTableTypes = [41, 33, 36];

    /**
     * Array of PDF files
     *
     * @var array
     */
    private array $collectedFilesForMerging = [];

    /**
     * @var array|array[]
     */
    private array $files = [
        41 => [],
        33 => [],
        36 => [],

    ];

    /**
     * Array with missing files
     *
     * @var array
     */
    private array $missingFiles = [];

    /**
     * Array with broken files
     *
     * @var array
     */
    private array $brokenFiles = [];

    /**
     * Merge pdf files into one
     *
     * @param string $fileName
     *
     * @return void
     * @throws Exception
     */
    private function merge(string $fileName): void
    {
        if (empty($this->collectedFilesForMerging)) {
            return;
        }

        $pdfMerge = new Pdf_Processor();
        $pdfMerge->setFiles($this->collectedFilesForMerging);

        try {
            $pdfMerge->merge();
        } catch (PdfReaderException $e) {
            echo json_encode([
                'error' => [
                    'type' => 'pdf_reader_error',
                    'message' => $e->getMessage(),
                    'code' => $e->getCode()
                ]
            ]);
            exit;
        } catch (CrossReferenceException $e) {
            echo json_encode([
                'error' => [
                    'type' => 'cross_reference_error',
                    'message' => $e->getMessage(),
                    'code' => $e->getCode()
                ]
            ]);
            exit;
        } catch (PdfParserException $e) {
            echo json_encode([
                'error' => [
                    'type' => 'pdf_parser_error',
                    'message' => $e->getMessage(),
                    'code' => $e->getCode()
                ]
            ]);
            exit;
        }

        $filePath = PH_RESOURCES_DIR . 'reports/' . basename($fileName);

        if (strpos(basename($fileName), 'final') !== false) {
            $this->addCertificateToPdf($pdfMerge);
        }

        // TODO: Fix this commented code so it works. Add watermark to draft files
        // if (strpos(basename($fileName), 'draft') !== false) {
        //     try {
        //         $this->mergeDraftAnalogFiles($fileName);
        //     } catch (PdfParserException | PdfReaderException $e) {
        //         echo $e->getMessage();
        //         return;
        //     }
        // }

        $pdfMerge->Output('F', $filePath);

        unset($pdfMerge);
        unset($this->collectedFilesForMerging[0]);
    }

    /**
     * @param string $fileName
     *
     * @return void
     */
    private function mergeDraftAnalogFiles(string $fileName): void
    {
        $pdfFile = new Pdf_Processor();

        foreach ($this->collectedFilesForMerging as $pdf) {
            if (strpos(basename($pdf), 'draft') !== false) {
                continue;
            }
            // $pageCount = $pdfFile->setSourceFile($pdf);
            // for ($pageNumber = 1; $pageNumber <= $pageCount; $pageNumber++) {
            //     $pdfFile->AddPage();
            //     $templateId = $pdfFile->importPage($pageNumber);
            //     $pdfFile->useTemplate($templateId);
            //     $pdfFile->SetFont('Times', 'B', 100);
            //     $pdfFile->SetTextColor(255, 0, 0);
            //     $pdfFile->SetXY(400, 30);
            //     $this->addWatermarkToPdf(55, 600, 100, 'DRAFT', $pdfFile);
            // }
        }

        $pdfFile->Output('F', $fileName);

        unset($pdfFile);
        unset($this->collectedFilesForMerging);
        $this->collectedFilesForMerging = [];
    }

    /**
     * Adds watermark to PDF
     *
     * @param int                $angle
     * @param int                $x
     * @param int                $y
     * @param string             $text
     * @param Pdf_Processor|null $pdfFile
     *
     * @return void
     */
    private function addWatermarkToPdf(
        int $angle = 55,
        int $x = -1,
        int $y = -1,
        string $text = '',
        ?Pdf_Processor $pdfFile = null
    ): void {
        if ($x == -1) {
            $x = $pdfFile->x;
        }
        if ($y == -1) {
            $y = $pdfFile->y;
        }
        if ($pdfFile->angle != 0) {
            $pdfFile->_out('Q');
        }
        $pdfFile->angle = $angle;
        if ($angle != 0) {
            $angle *= M_PI / 180;
            $c = cos($angle);
            $s = sin($angle);
            $cx = $x * $pdfFile->k;
            $cy = ($pdfFile->h - $y) * $pdfFile->k;
            $pdfFile->_out(
                sprintf(
                    'q %.5f %.5f %.5f %.5f %.2f %.2f cm 1 0 0 1 %.2f %.2f cm',
                    $c,
                    $s,
                    -$s,
                    $c,
                    $cx,
                    $cy,
                    -$cx,
                    -$cy
                )
            );
        }
        $pdfFile->Text(0, 0, $text);
    }

    /**
     * Append a customer certificate to the end of a final version PDF
     *
     * @param Pdf_Processor $pdfMerge
     *
     * @return void
     */
    private function addCertificateToPdf(Pdf_Processor $pdfMerge): void
    {
        $certificate_advance = $this->getAdvanceCertificate();
        $image = realpath($certificate_advance->get('path'));

        $pdfMerge->AddPage();
        $pdfMerge->Image($image, 0, 0, $pdfMerge->GetPageWidth(), $pdfMerge->GetPageHeight());
    }
}
