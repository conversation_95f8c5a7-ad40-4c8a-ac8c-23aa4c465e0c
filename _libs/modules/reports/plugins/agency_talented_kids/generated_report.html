<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="70%">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_full_num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_name#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_sex#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_age#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_area#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_area_other#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_other_area#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_test_name#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_test_count1#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_test_count2#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_test_count3#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_test_count4#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_status#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_harm#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_house#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_help#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_help_other#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_sponsor#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#reports_kid_sponsor_other#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}{if !$result.active} t_inactive{/if}">
            <td class="t_border hright" nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.document_id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
            </td>
            <td class="t_border" nowrap="nowrap">
              {$result.kid_name|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_sex|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_age|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_area|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_other_area|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_area_other|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.test_name|default:"&nbsp;"}
            </td>
            <td class="t_border" align="right">
              {$result.test_count1|default:"0"}
            </td>
            <td class="t_border" align="right">
              {$result.test_count2|default:"0"}
            </td>
            <td class="t_border" align="right">
              {$result.test_count3|default:"0"}
            </td>
            <td class="t_border" align="right">
              {$result.test_count4|default:"0"}
            </td>
            <td class="t_border">
              {$result.kid_status|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_harm|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_house|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_help|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_help_other|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.kid_sponsor|default:"&nbsp;"}
            </td>
            <td>
              {$result.kid_sponsor_other|default:"&nbsp;"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="20">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="20"></td>
        </tr>
        {if $reports_additional_options.count}
          <tr class="t_even">
            <td colspan="20"><strong>{#reports_total#|escape}:</strong> {$reports_additional_options.count}</td>
          </tr>
          <tr>
            <td class="t_footer" colspan="20"></td>
          </tr>
        {/if}
      </table>
    </td>
  </tr>
</table>