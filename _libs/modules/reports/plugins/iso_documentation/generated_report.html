<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      {if $reports_additional_options.filter_value ne 'schedules'}
        <table border="0" cellpadding="5" cellspacing="0" class="t_table">
          <tr class="reports_title_row hcenter vmiddle">
            <td class="t_border"><div style="width: 20px;">{#num#|escape}</div></td>
            <td class="t_border"><div style="width: 110px;">{#reports_identification#|escape}</div></td>
            <td class="t_border"><div style="width: 60px;">{#reports_active_since#|escape}</div></td>
            <td class="t_border"><div style="width: 210px;">{#reports_name#|escape}</div></td>
            <td class="t_border"><div style="width: 75px;">{#reports_change_num#|escape}</div></td>
            <td class="t_border"><div style="width: 60px;">{#reports_date_from#|escape}</div></td>
            <td class="t_border"><div style="width: 100px;">{#reports_forms_count#|escape}</div></td>
            <td><div style="width: 75px;">{#reports_files#|escape}</div></td>
          </tr>
          {counter start=0 name='item_counter' print=false}
          {foreach from=$reports_results key=k name=doc item=result}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border hright" width="25" style="padding: 5px;">
                {counter name='item_counter' print=true}
              </td>
              <td class="t_border" style="padding: 5px;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
              </td>
              <td class="t_border" style="padding: 5px;">
                {if $result.actual_version_date}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}#layout_{$result.actual_version_date_layout}">{$result.actual_version_date|date_format:#date_short#|default:"&nbsp;"}</a>
                {else}
                  &nbsp;
                {/if}
              </td>
              <td class="t_border" style="padding: 5px;">
                {$result.name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" style="padding: 5px;">
                {if $result.actual_change_num}
                   <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}#layout_{$result.actual_change_num_layout}">{$result.actual_change_num|escape|default:"&nbsp;"}
                {else}
                  &nbsp;
                {/if}
              </td>
              <td class="t_border" style="padding: 5px;">
                {$result.actual_change_date|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td class="t_border hright" style="padding: 5px;">
                {if $result.form_num}
                   <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}#layout_{$result.form_num_layout}">{$result.form_num|escape|default:"&nbsp;"}
                {else}
                  &nbsp;
                {/if}
              </td>
              <td style="padding: 5px;">
                {if $result.actual_version_link}
                  <img src="{$theme->imagesUrl}attachments.png" border="0" alt="" style="cursor: pointer!important;" onclick="window.open('{$result.actual_version_link}', '_blank');" />
                {else}
                  &nbsp;
                {/if}
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="8">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="8"></td>
          </tr>
        </table>
      {else}
        <table border="0" cellpadding="5" cellspacing="0" class="t_table">
          <tr class="reports_title_row hcenter vmiddle">
            <td class="t_border"><div style="width: 20px;">{#num#|escape}</div></td>
            <td class="t_border"><div style="width: 110px;">{#reports_identification#|escape}</div></td>
            <td class="t_border"><div style="width: 60px;">{#reports_active_since#|escape}</div></td>
            <td class="t_border"><div style="width: 210px;">{#reports_name#|escape}</div></td>
            <td class="t_border"><div style="width: 75px;">{#reports_creator#|escape}</div></td>
            <td class="t_border"><div style="width: 75px;">{#reports_keeper#|escape}</div></td>
            <td class="t_border"><div style="width: 100px;">{#reports_keeping_term#|escape}</div></td>
            <td><div style="width: 75px;">{#reports_files#|escape}</div></td>
          </tr>
          {counter start=0 name='item_counter' print=false}
          {foreach from=$reports_results key=k name=doc item=result}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border hright" width="25" style="padding: 5px;">
                {counter name='item_counter' print=true}
              </td>
              <td class="t_border" style="padding: 5px;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}">{$result.full_num|numerate:$result.direction|default:"&nbsp;"}</a>
              </td>
              <td class="t_border" style="padding: 5px;">
                {$result.aform_date|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td class="t_border" style="padding: 5px;">
                {$result.name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" style="padding: 5px;">
                {$result.aform_creator|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" style="padding: 5px;">
                {$result.aform_protector|escape|default:"&nbsp;"}
              </td>
              <td class="t_border hright" style="padding: 5px;">
                {$result.aform_term_prot|date_format:#date_short#|default:"&nbsp;"}
              </td>
              <td style="padding: 5px;">
                {if $result.aform_link}
                  <img src="{$theme->imagesUrl}attachments.png" border="0" alt="" style="cursor: pointer!important;" onclick="window.open('{$result.aform_link}', '_blank');" />
                {else}
                  &nbsp;
                {/if}
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="8">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="8"></td>
          </tr>
        </table>
      {/if}
    </td>
  </tr>
</table>