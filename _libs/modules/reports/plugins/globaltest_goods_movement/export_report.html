{if !$prepare_placeholder}
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
{/if}
{if !$prepare_placeholder || $prepare_placeholder eq 'good_availability_table'}
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: left; font-weight: bold; background-color: #DFDFDF;"><div style="width: 130px;">{#reports_code#|escape}</div></td>
        <td style="text-align: right;" width="298"><div style="width: 298px;">{$reports_results.good_availability.code|escape|default:"&nbsp;"}</div></td>
      </tr>
      <tr>
        <td style="text-align: left; font-weight: bold; background-color: #DFDFDF;">{#reports_good#|escape}</td>
        <td style="text-align: right;">{$reports_results.good_availability.name|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="text-align: left; font-weight: bold; background-color: #DFDFDF;">{#reports_measure#|escape}</td>
        <td style="text-align: right;">{$reports_results.good_availability.measure|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td style="text-align: left; font-weight: bold; background-color: #DFDFDF;">{#reports_availability#|escape}</td>
        <td style="text-align: right;">{$reports_results.good_availability.quantity|escape|default:"&nbsp;"}</td>
      </tr>
    </table>
{/if}
{if !$prepare_placeholder || $prepare_placeholder eq 'good_documents_table'}
    <br />
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="198"><div style="width: 198px;">{#reports_action#|escape}</div></td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="198"><div style="width: 198px;">{#reports_about#|escape}</div></td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="98"><div style="width: 98px;">{#reports_date_added#|escape}</div></td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="298"><div style="width: 298px;">{#reports_document#|escape}</div></td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="103"><div style="width: 103px;">{#reports_quantity#|escape}</div></td>
        <td style="text-align: center; vertical-align: middle; font-weight: bold; background-color: #DFDFDF;" width="78"><div style="width: 78px;">{#reports_measure#|escape}</div></td>
      </tr>
      {foreach from=$reports_results.good_documents item=good_document}
        {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
        <tr>
          <td style="text-align: left;">{$good_document.action|escape|default:"&nbsp;"}</td>
          <td style="text-align: left;">{$good_document.about|escape|default:"&nbsp;"}</td>
          <td style="text-align: center;">{$good_document.date_added|escape|default:"&nbsp;"}</td>
          <td style="text-align: left;"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=warehouses_documents&amp;warehouses_documents=view&amp;view={$good_document.document_id}" target="_blank">{$good_document.document_type_name|escape|default:"&nbsp;"}&nbsp;{#num#} {$good_document.document_num|escape|default:"&nbsp;"}</a></td>
          <td style="text-align: right;"><strong><span style="color: {if $good_document.nomenclature_direction eq 'plus'}green{else}red{/if};">{if $good_document.nomenclature_direction eq 'plus'}+{else}-{/if}</span></strong> {$good_document.nomenclature_quantity|escape|default:"&nbsp;"}</td>
          <td style="text-align: center;">{$good_document.nomenclature_measure_name|escape|default:"0.00"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="6"><span color="red">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
{/if}
{if !$prepare_placeholder}
  </body>
</html>
{/if}