<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row hcenter">
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_insured#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_insurance_type#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_department#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_premium_bgn#|escape}</div></td>
    <td style="vertical-align: middle;"><div style="">{#reports_commission_bgn#|escape}</div></td>
  </tr>
  {foreach from=$reports_results item=result name=results}
    {cycle values='t_odd1 t_odd2,t_even1 t_even2' assign=row_class}
    {foreach from=$result.insurances item=insurance name=ins}
      {foreach from=$insurance.departments item=department name=dep}
        <tr class="{$row_class}">
          {if $smarty.foreach.ins.first && $smarty.foreach.dep.first}
            <td class="t_border vmiddle" rowspan="{$result.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.id}">{$result.name|escape|default:"&nbsp;"}</a>
            </td>
          {/if}
          {if $smarty.foreach.dep.first}
            <td class="t_border vmiddle" rowspan="{$insurance.rowspan}">
              {$insurance.name|escape|default:"&nbsp;"}
            </td>
          {/if}
          <td class="t_border">
            {$department.name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright">
            {$department.bonus|string_format:"%.2f"|escape|default:"0.00"}
          </td>
          <td class="hright">
            {$department.commission|string_format:"%.2f"|escape|default:"0.00"}
          </td>
        </tr>
      {/foreach}
    {/foreach}
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="5">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td class="t_footer" colspan="5"></td>
  </tr>
</table>

