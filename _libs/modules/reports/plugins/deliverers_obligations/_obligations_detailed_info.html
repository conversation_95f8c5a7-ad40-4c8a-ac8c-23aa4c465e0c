  <h1 id="obligations_title">{$table_title|escape}</h1>
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
    <tr class="reports_title_row hcenter">
      <td class="t_border" style="vertical-align: middle;"><div style="width: 474px;">{#reports_document_type#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_document_num#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_document_issue_date#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_document_deadline#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_document_currency#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 100px;">{#reports_document_sum_org#|escape}</div></td>
      <td style="vertical-align: middle;"><div style="width: 100px;">{#reports_document_sum#|escape} ({$main_currency})</div></td>
    </tr>
    {foreach from=$documents_list item=expense name=doc}
      {capture assign='current_row_class'}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
      <tr class="{$current_row_class}">
        <td class="pointer t_border" style="width: 97%;" onclick="toggleDocumentsGT2(this)" id="report_document_{$expense.id}_switch" width="484">
          <div class="switch_expand"></div>{$expense.type_name|escape|default:"&nbsp;"}
        </td>
        <td class="t_border hcenter" width="104">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=expenses_reasons&amp;expenses_reasons=view&amp;view={$expense.id}" target="blank">{if $expense.num}{$expense.num|escape|default:"&nbsp;"}{else}{#reports_no_number#|escape}{/if}</a>
        </td>
        <td class="t_border hcenter" width="104">
          {$expense.issue_date|date_format:#date_short#|escape}
        </td>
        <td class="t_border hcenter" width="104">
          {$expense.date_of_payment|date_format:#date_short#|escape}
        </td>
        <td class="t_border hcenter" width="102">
          {$expense.currency|escape|default:"&nbsp;"}
        </td>
        <td class="t_border hright" width="102">
          {if $expense.sum_org}{$expense.sum_org|string_format:"%.2f"|default:"0.00"}{else}-{/if}
        </td>
        <td class="hright" width="102">
          {if $expense.sum}{$expense.sum|string_format:"%.2f"|default:"0.00"}{else}-{/if}
        </td>
      </tr>
      <tr id="report_document_{$expense.id}_box" style="display: none" class="{$current_row_class}">
        <td colspan="7">
          {include file=`$theme->templatesDir`_gt2_view.html table=$expense.gt2_var}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="7">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr class="row_blue">
      <td class="t_border hright" colspan="6"><strong>{#reports_document_sum_total#}</strong></td>
      <td class="hright">
        <strong>{if $total_obligations}{$total_obligations|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong>
      </td>
    </tr>
    <tr>
      <td class="t_footer" colspan="7"></td>
    </tr>
  </table>
