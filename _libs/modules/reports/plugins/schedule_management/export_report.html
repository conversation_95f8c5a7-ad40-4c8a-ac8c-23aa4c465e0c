<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_employee#|escape}</strong></td>
        {foreach from=$reports_results.days_list item=dl name=d_lst}
          <td style="text-align: center; vertical-align: bottom;{if !$dl.working} background-color: #FFFDC0 !important;{/if}"><strong>{$dl.label|escape}</strong></td>
        {/foreach}
      </tr>
      {foreach from=$reports_results.employees item=employee}
        <tr>
          <td style="vertical-align: middle; white-space: nowrap;">
            {$employee.name|escape|default:"&nbsp;"}
          </td>
          {foreach from=$employee.schedule item=day key=date_formattted name=emp_dat}
            <td class="{if !$smarty.foreach.emp_dat.last}t_border{/if}{if !$reports_results.days_list[$date_formattted].working} presence_form_holiday{/if}" style="text-align: center; vertical-align: bottom;">
              <div style="">
                {$day.show_label|escape}
              </div>
            </td>
          {/foreach}
        </tr>
      {foreachelse}
        <tr>
          <td colspan="{$reports_results.total_colspan}"><span style="color: red">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>

    <h3 style="text-decoration: underline; margin-bottom: 5px;">{#reports_legend#|escape}</h3>
    {foreach from=$reports_results.legend item=workshift name=wrkshft}
      <strong>{$workshift.code|escape}</strong> - <i>{$workshift.legend|escape}</i>{if !$smarty.foreach.wrkshft.last}<br />{/if}
    {/foreach}
  </body>
</html>
