reports_from_date = From
reports_to_date = To
reports_payment_type = Payment type
reports_document_type = Operation type
reports_office_name = Branch
reports_employees = Employee
reports_for_employee = Advance/salary payed to
reports_customers = Customer
reports_currency = Currency
reports_type_income = Type income
reports_type_expense = Type expense
reports_money_given = Obligation

reports_payment_type_cash = cash
reports_payment_type_bank = bank
reports_payment_type_card = card
reports_document_type_income = income
reports_document_type_expense = expense
reports_money_given_yes = paid
reports_money_given_no = unpaid

reports_legend = Legend:
reports_expence = Expence
reports_no_introduce_bank_paying = No introduce bank paying
reports_expence_no_introduce_bank_paying = Expence with no introduce bank paying
reports_income = Income
reports_outcome = Outcome
reports_lost_money = Lost money

reports_office = Branch
reports_employee = Employee
reports_document_num = №
reports_customer = Customer
reports_type = Type
reports_paying_reason = Paying Reason
reports_type_paying = Paying Type
reports_paid_to = Paid to
reports_paying_value = Value
reports_paying_currency = Currency
reports_take_money = Received
reports_money_date = Paying Date

reports_incomings = Incomings (cash)
reports_bank_incomings = Incomings (bank)
reports_expences = Expences (cash)
reports_bank_expences = Expences (bank)
reports_incomes = Incomes
reports_outcomes = Outdomes
reports_total = TOTAL Incomings
