<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <strong>{#reports_t1_name#|escape}</strong>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="20"><div style="width: 20px">&nbsp;</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="220"><div style="width: 220px">{#reports_t1_h1#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="105"><div style="width: 105px">{#reports_t1_h2#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="105"><div style="width: 105px">{#reports_t1_h3#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="220"><div style="width: 220px">{#reports_t1_h4#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" width="220"><div style="width: 220px">{#reports_t1_h5#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.tsii_kalibs item=tsii_kalib key=nomenclature_id}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border"><input type="checkbox" name="included_tsii_kalibs_rows[]" id="row_{$nomenclature_id}" value="{$tsii_kalib.encoded_data|escape}" checked="checked" /></td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 220px;">{$tsii_kalib.name|escape|default:"--"}, {$tsii_kalib.tech_ident_num|escape|default:"--"}, {$tsii_kalib.tech_producer|escape|default:"--"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 105px;">{$tsii_kalib.tech_range|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 105px;">{$tsii_kalib.tech_borders|escape|default:"&nbsp;"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 220px;">{$tsii_kalib.document_full_num|escape|default:"--"} / {$tsii_kalib.document_date|escape|default:"--"}, <br />{$tsii_kalib.document_customer|escape|default:"--"}</td>
            <td class="t_v_border" style="text-align: left; max-width: 220px;">{$tsii_kalib.tech_charact|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td colspan="6" style="text-align: center;">--</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
      <br />
      <strong>{#reports_t2_name#|escape}</strong>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="20"><div style="width: 20px">&nbsp;</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="220"><div style="width: 220px">{#reports_t2_h1#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="220"><div style="width: 220px">{#reports_t2_h2#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="220"><div style="width: 220px">{#reports_t2_h3#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" width="220"><div style="width: 220px">{#reports_t2_h4#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.tsii_no_kalibs item=tsii_no_kalib key=nomenclature_id}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border">
              <input type="checkbox" name="included_tsii_no_kalibs_rows[]" id="row_{$nomenclature_id}" value="{$tsii_no_kalib.encoded_data|escape}" checked="checked" />
            </td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 220px;">{$tsii_no_kalib.name|escape|default:"--"}, {$tsii_no_kalib.tech_ident_num|escape|default:"--"}, {$tsii_no_kalib.tech_producer|escape|default:"--"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 220px;">{$tsii_no_kalib.document_full_num|escape|default:"--"} / {$tsii_no_kalib.document_date|escape|default:"--"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 220px;">{$tsii_no_kalib.tech_interval|escape|default:"&nbsp;"}</td>
            <td class="t_v_border" style="text-align: left; max-width: 220px;">{$tsii_no_kalib.tech_charact|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td colspan="5" style="text-align: center;">--</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="5"></td>
        </tr>
      </table>
      <br />
      <strong>{#reports_t3_name#|escape}</strong>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="20"><div style="width: 20px">&nbsp;</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="350"><div style="width: 350px">{#reports_t3_h1#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" class="t_border" width="321"><div style="width: 321px">{#reports_t3_h2#|escape}</div></td>
          <td style="text-align: center; vertical-align: middle;" width="220"><div style="width: 220px">{#reports_t3_h3#|escape}</div></td>
        </tr>
        {foreach from=$reports_results.equipments item=equipment key=nomenclature_id}
          {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
          <tr class="{$current_row_class}">
            <td class="t_v_border t_border">
              <input type="checkbox" name="included_equipments_rows[]" id="row_{$nomenclature_id}" value="{$equipment.encoded_data|escape}" checked="checked" />
            </td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 350px;">{$equipment.name|escape|default:"--"}, {$equipment.tech_ident_num|escape|default:"--"}, {$equipment.tech_producer|escape|default:"--"}</td>
            <td class="t_v_border t_border" style="text-align: left; max-width: 321px;">{$equipment.document_full_num|escape|default:"--"} / {$equipment.document_date|escape|default:"--"}</td>
            <td class="t_v_border" style="text-align: left; max-width: 220px;">{$equipment.tech_charact|escape|default:"&nbsp;"}</td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd1 t_odd2,t_even1 t_even2'}">
            <td colspan="4" style="text-align: center;">--</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="4"></td>
        </tr>
      </table>
      <br />
      {if $export_permission}
        <button type="submit" name="export_selected_records" class="button reports_export_button" onclick="exportSelectedRecords(this);">{#export_report#|escape}</button>
      {/if}
    </td>
  </tr>
</table>
