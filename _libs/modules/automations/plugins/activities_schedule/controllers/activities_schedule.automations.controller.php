<?php

/**
 * Plugin automations for additional functionality related to
 * Activities_Schedule report
 *
 * @package Automations
 * @subpackage Activities_Schedule
 * @see Activities_Schedule
 * @uses Activities_Schedule - report factory class
 */
class Activities_Schedule_Automations_Controller extends Automations_Controller {

    /**
     * Displays notification that source record has a schedule that should be
     * updated.
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the execution of the method
     */
    public function warnSourceRecord(array $params) {

        /** @var Registry $registry */
        $registry = &$this->registry;
        /** @var ADODB_mysqli $db */
        $db = &$registry['db'];

        $report_name = 'activities_schedule';
        /** @var Activities_Schedule $report_class */
        $report_class = Reports::getPluginFactory($report_name);
        $settings = $report_class::getReportSettings($registry, $report_name);

        // validate that module matches those used in report
        if (!array_key_exists($params['module'], $settings['module_params'])) {
            return false;
        }

        // new model for contract on edittopic is a total mess (it is taken from request)
        // so we need to use all sorts of hacks to get needed data
        $model_id =
            $params['model']->modelName == 'Contract' && $params['model']->get('old_model')->get('subtype') != 'contract' ?
            $params['model']->get('parent_record') :
            $params['model']->get('id');

        // 1. check that schedule exists
        $schedule_id = $db->GetOne("
            SELECT d.id
            FROM " . DB_TABLE_DOCUMENTS . " AS d
            JOIN " . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr
              ON dr.parent_id = d.id
                AND dr.parent_model_name = 'Document'
                AND dr.link_to = '{$model_id}'
                AND dr.link_to_model_name = '{$params['model']->modelName}'
            WHERE d.type = '{$settings['document_schedule_type']}'
              AND d.active = 1
              AND d.deleted_by = 0
            ORDER BY d.id DESC");
        if (!$schedule_id) {
            return false;
        }

        $source_params = $settings['module_params'][$params['module']];
        $model = $params['model'];
        $old_model = $params['model']->get('old_model');

        // 2. check that relevant changes were made
        $made_changes = false;
        if ($this->action == 'edit' && $params['model']->get('id') == $model_id) {
            // check basic data (dates) for changes
            // define earlier star date
            $start_dates = array();
            foreach (array($old_model, $model, ) as $idx => $m) {
                $start = array($m->get($source_params['start']));
                if (!empty($source_params['start2'])) {
                    $start[] = $m->get($source_params['start2']);
                }
                $start = array_filter($start);
                $start_dates[$idx] = reset($start) ?: '';
            }
            if ($old_model && (
                $start_dates[1] > $start_dates[0] ||
                $model->get($source_params['end']) < $old_model->get($source_params['end'])
            )) {
                $made_changes = true;
            }
        }
        if (!$made_changes && $this->action == 'edit' && $params['module'] != 'contracts' || $this->action == 'edittopic' && $params['module'] == 'contracts') {
            // check GT2 for changes
            $gt2_qtys = array();
            foreach (array($old_model, $model, ) as $idx => $m) {
                $gt2_qtys[$idx] = array();
                if ($m->get('vars')) {
                    foreach ($m->get('vars') as $var) {
                        if ($var['type'] == 'gt2') {
                            if (!empty($var['values'])) {
                                // accumulate GT2 quantities grouped by article
                                foreach ($var['values'] as $row) {
                                    if (empty($row) || !empty($row['deleted'])) {
                                        continue;
                                    }
                                    if (!array_key_exists($row['article_id'], $gt2_qtys[$idx])) {
                                        $gt2_qtys[$idx][$row['article_id']] = 0;
                                    }
                                    $gt2_qtys[$idx][$row['article_id']] += $row['quantity'];
                                }
                            }
                            break;
                        }
                    }
                }
            }
            if (array_diff_assoc($gt2_qtys[0], $gt2_qtys[1]) || array_diff_assoc($gt2_qtys[1], $gt2_qtys[0])) {
                $made_changes = true;
            }
        }
        if ($params['module'] == 'contracts' && $model->get('subtype') == 'annex' &&
        ($model->get('subtype_status') == 'started' && $old_model->get('subtype_status') != 'started' ||
        $model->get('subtype_status') != 'started' && $old_model->get('subtype_status') == 'started')) {
            // annex entered into force or is terminated
            // we assume that changes are made because exploring them will be way too lengthy
            $made_changes = true;
        }

        if ($made_changes) {
            $this->registry['messages']->setWarning(
                $this->i18n(
                    'plugin_warning_activities_schedule_out_of_date',
                    array(
                        sprintf(
                            '<a class="strong" href="%s?%s=reports&reports=generate_report&report_type=%s&source_module=%s&model_id=%d">',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'],
                            $report_name,
                            $params['module'],
                            $model_id
                        ),
                        '</a>'
                    )
                )
            );
            $this->registry['messages']->insertInSession($this->registry);
        }
        return true;
    }
}
