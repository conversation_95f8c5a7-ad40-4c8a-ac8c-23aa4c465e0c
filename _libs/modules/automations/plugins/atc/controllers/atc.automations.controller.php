<?php

class Atc_Automations_Controller extends Automations_Controller
{

    const DOCUMENT_TYPE_SAP_SALE = 6;
    const DOCUMENT_TYPE_STORE_SALE = 1;

    private $customer;
    private $nomenclature;
    private $stats = array();

    /**
     * Automation to change the statuses of the related
     */
    public function changeRelatedStatusDocument($params)
    {
        $result = true;
        $settings = $this->settings;

        $model_assoc_vars = $params['model']->getAssocVars();
        $old_model_assoc_vars = $params['model']->get('old_model')->getAssocVars();

        $model_reports = $model_assoc_vars[$settings['document_var_case']]['value'];
        $old_model_reports = $old_model_assoc_vars[$settings['document_var_case']]['value'];

        $new_selected_models = array_filter($model_reports);
        $old_selected_models = array_filter($old_model_reports);

        $to_status = array();
        $to_status_sold = array_diff($new_selected_models, $old_selected_models);
        $to_status_returned = array_diff($old_selected_models, $new_selected_models);

        foreach ($to_status_sold as $tss) {
            $to_status[$tss] = $settings['status_sold'];
        }
        foreach ($to_status_returned as $tsr) {
            $to_status[$tsr] = $settings['status_returned'];
        }
        unset($to_status_sold);
        unset($to_status_returned);

        if (!empty($to_status)) {
            $this->registry['db']->StartTrans();
            require_once(PH_MODULES_DIR . 'documents/models/documents.factory.php');
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

            $machine_reports = Documents::search($this->registry, array('where' => array('d.id IN ("' . implode('","', array_keys($to_status)) . '")')));
            foreach ($machine_reports as $machine_report) {
                $old_machine_report = clone $machine_report;

                $machine_report->set('status', 'closed', true);
                $machine_report->set('substatus', 'closed_' . $to_status[$machine_report->get('id')], true);
                if ($machine_report->setStatus()) {
                    $filters = array('where' => array('d.id = ' . $machine_report->get('id')),
                        'model_lang' => $machine_report->get('model_lang'),
                        'skip_assignments' => true,
                        'skip_permissions_check' => true);
                    $new_machine_report = Documents::searchOne($this->registry, $filters);

                    $audit_parent = Documents_History::saveData($this->registry, array('model' => $machine_report, 'action_type' => 'status', 'new_model' => $new_machine_report, 'old_model' => $old_machine_report));
                } else {
                    $this->registry['db']->FailTrans();
                }
            }
            $result = !$this->registry['db']->HasFailedTrans();

            $this->registry['db']->CompleteTrans();

            if (!$result) {
                $this->registry['messages']->setError($this->i18n('error_status_change_failed'));
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        return $result;
    }

    /**
     * Update document of type "Machine file" after adding documents of type "Machine release" or "Warranty Card"
     *
     * @param array $params - params for the automation
     * @return boolean
     */
    public function updateMachineFileAfterMachineReleaseAndWarrantyCard($params)
    {
        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];
        $messages = &$registry['messages'];
        $document = $params['model'];
        $model_lang = $document->get('model_lang');

        // Unsanitize the model
        $sanitize_after = false;
        if ($document->isSanitized()) {
            $document->unsanitize();
            $sanitize_after = true;
        }

        // Check required settings
        $required_settings = array(
            'machine_file_status',
            'machine_file_substatus',
            'machine_files_var',
            'doc_type_machine_release',
            'doc_type_warranty_card',
        );
        if (count(array_intersect_key(array_flip($required_settings), array_filter($this->settings))) != count($required_settings)) {
            $messages->setError($this->i18n('automation_updatemachinefileaftermachinereleaseandwarrantycard_error_missing_required_settings'));
            $messages->insertInSession($registry);
            return false;
        }

        // Validate the document type for which the automation is executed
        $document_type = $document->get('type');
        if (!in_array($document_type, array($this->settings['doc_type_machine_release'], $this->settings['doc_type_warranty_card']))) {
            $messages->setError($this->i18n('automation_updatemachinefileaftermachinereleaseandwarrantycard_error_unknown_document_type'));
            $messages->insertInSession($registry);
            return false;
        }

        // Get the machine files
        $machine_files = $document->getPlainVarValue($this->settings['machine_files_var']);
        if (empty($machine_files)) {
            $messages->setError($this->i18n('automation_updatemachinefileaftermachinereleaseandwarrantycard_error_no_machines_selected'));
            $messages->insertInSession($registry);
            return false;
        }

        // Start a transaction
        $db->StartTrans();

        // Prepare for assignments
        $document_customer = $document->get('customer');
        $query = "
            SELECT id
              FROM " . DB_TABLE_USERS . "
              WHERE deleted_by = 0
                AND active = 1
                AND default_customer = '{$document_customer}'";
        $assignments_observer = $db->GetCol($query);
        if (!in_array($registry['originalUser']->get('id'), $assignments_observer)) {
            $assignments_observer[] = $registry['originalUser']->get('id');
        }
        $document->getAssignments('responsible');
        $document->getAssignments('decision');
        $document->getAssignments('observer');
        $document->getAssignments();
        $document_old = clone $document;
        $document_old->sanitize();
        $document->set('assignments_observer', $assignments_observer, true);

        // Try to make the assignments
        if ($document->assign()) {
            // Write history
            $filters = array(
                'where' => array("d.id = '{$document->get('id')}'"),
                'model_lang' => $model_lang,
                'skip_assignments' => true,
                'skip_permissions_check' => true
            );
            $document_new = Documents::searchOne($registry, $filters);
            $document_new->getAssignments('responsible');
            $document_new->getAssignments('decision');
            $document_new->getAssignments('observer');
            $document_new->getAssignments();
            Documents_History::saveData(
                $registry, array(
                'old_model' => $document_old,
                'model' => $document,
                'new_model' => $document_new,
                'action_type' => 'assign'
                )
            );

            // If the transaction has not failed
            if (!$db->HasFailedTrans()) {
                // Get the machine files models
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $filters = array(
                    'where' => array("d.id IN ('" . implode("', '", $machine_files) . "')"),
                    'sanitize' => true,
                    'model_lang' => $model_lang,
                    'skip_assignments' => true,
                    'skip_permissions_check' => true
                );
                $machine_files = Documents::search($registry, $filters);
                if (empty($machine_files)) {
                    $messages->setError($this->i18n('automation_updatemachinefileaftermachinereleaseandwarrantycard_error_no_machines_found'));
                    $messages->insertInSession($registry);
                    return false;
                }

                // Include some required files
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

                // Go through each machine
                foreach ($machine_files as $machine_file) {
                    // If the document is machine release
                    if ($document_type == $this->settings['doc_type_machine_release']) {
                        // Change the customer
                        $machine_file_old = clone $machine_file;
                        $machine_file->unsanitize();
                        $machine_file->set('customer', $document_customer, true);
                        if ($machine_file->save()) {
                            // Write history
                            $filters = array(
                                'where' => array("d.id = '{$machine_file->get('id')}'"),
                                'model_lang' => $model_lang,
                                'sanitize' => false,
                                'skip_assignments' => true,
                                'skip_permissions_check' => true
                            );
                            $machine_file_new = Documents::searchOne($registry, $filters);
                            Documents_History::saveData(
                                $registry, array(
                                'old_model' => $machine_file_old,
                                'model' => $machine_file,
                                'new_model' => $machine_file_new,
                                'action_type' => 'edit'
                                )
                            );
                        } else {
                            $db->FailTrans();
                            break;
                        }
                    }

                    // Change the status
                    if ($document_type == $this->settings['doc_type_machine_release']) {
                        $machine_file = clone $machine_file_new;
                    }
                    $machine_file_old = clone $machine_file;
                    $machine_file_old->sanitize();
                    $machine_file->set('status', $this->settings['machine_file_status'], true);
                    $machine_file->set('substatus', "{$this->settings['machine_file_status']}_{$this->settings['machine_file_substatus']}", true);
                    if ($machine_file->setStatus()) {
                        // Write history
                        $filters = array(
                            'where' => array("d.id = '{$machine_file->get('id')}'"),
                            'model_lang' => $model_lang,
                            'sanitize' => false,
                            'skip_assignments' => true,
                            'skip_permissions_check' => true
                        );
                        $machine_file_new = Documents::searchOne($registry, $filters);
                        Documents_History::saveData(
                            $registry, array(
                            'old_model' => $machine_file_old,
                            'model' => $machine_file,
                            'new_model' => $machine_file_new,
                            'action_type' => 'status'
                            )
                        );

                        // Make the assignments
                        $machine_file = clone $machine_file_new;
                        $machine_file->getAssignments('responsible');
                        $machine_file->getAssignments('decision');
                        $machine_file->getAssignments('observer');
                        $machine_file->getAssignments();
                        $machine_file_old = clone $machine_file;
                        $machine_file_old->sanitize();
                        // If the document is machine release
                        if ($document_type == $this->settings['doc_type_machine_release']) {
                            // Renew the observer assignments
                            $assignments_observer = array_diff($assignments_observer, array($registry['originalUser']->get('id')));
                        } else {
                            // If the document is warranty card, then add observer assignments
                            $assignments_observer = array_merge(array_keys($machine_file->get('assignments_observer')), $assignments_observer);
                        }
                        $machine_file->set('assignments_observer', $assignments_observer, true);
                        if ($machine_file->assign(false, false)) {
                            // Write history
                            $filters = array(
                                'where' => array("d.id = '{$machine_file->get('id')}'"),
                                'model_lang' => $model_lang,
                                'sanitize' => false,
                                'skip_assignments' => true,
                                'skip_permissions_check' => true
                            );
                            $machine_file_new = Documents::searchOne($registry, $filters);
                            $machine_file_new->getAssignments('responsible');
                            $machine_file_new->getAssignments('decision');
                            $machine_file_new->getAssignments('observer');
                            $machine_file_new->getAssignments();
                            Documents_History::saveData(
                                $registry, array(
                                'old_model' => $machine_file_old,
                                'model' => $machine_file,
                                'new_model' => $machine_file_new,
                                'action_type' => 'assign'
                                )
                            );

                            // Make relation between the machine file and the machine release
                            $machine_file_new->set('origin_id', $document->get('id'), true);
                            $machine_file_new->set('origin_model', 'Document', true);
                            $machine_file_new->set('clone_transform', 'inherited', true);
                            $machine_file_new->insertRelatives();
                        } else {
                            $db->FailTrans();
                            break;
                        }
                    } else {
                        $db->FailTrans();
                        break;
                    }
                }
            }
        } else {
            $db->FailTrans();
        }

        // Sanitize the model
        if ($sanitize_after) {
            $document->sanitize();
        }

        // Complete the transaction
        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        // Message for success or failure
        if ($result) {
            $messages->setMessage($this->i18n('automation_updatemachinefileaftermachinereleaseandwarrantycard_machines_edit_success'));
        } else {
            $messages->setError($this->i18n('automation_updatemachinefileaftermachinereleaseandwarrantycard_error_machines_edit_failed'));
        }
        $messages->insertInSession($registry);

        return $result;
    }

    /**
     * Set warning if protocol custom_num is duplicated
     * @param array $params
     * @return boolean
     */
    public function duplicateProtocols($params)
    {
        // Prepare some basics
        $registry = &$this->registry;
        $messages = &$registry['messages'];
        $document = $params['model'];
        $custom_num = $document->get('custom_num');
        $query = "SELECT id, full_num
                    FROM " . DB_TABLE_DOCUMENTS . "
                      WHERE id != {$document->get('id')}
                        AND custom_num = '{$custom_num}'
                        AND substatus != 3
                        AND `type` = 1
                        AND deleted_by = 0
                        AND active = 1";
        // Gets ducplicate documents
        $duplicate_documents = $registry['db']->GetAssoc($query);
        if (count($duplicate_documents) == 0) {
            return true;
        }
        $duplicate_document_msg = $this->i18n('automation_protocol_duplicate_number');
        $duplicate_urls = array();
        foreach ($duplicate_documents as $duplicate_id => $duplicate_full_num) {
            $url = "{$_SERVER['PHP_SELF']}?{$registry['module_param']}=documents&amp;documents=view&amp;view=" . $duplicate_id . "&amp;model_lang={$registry['lang']}";
            $duplicate_urls[] = "<a href='{$url}' target='_blank'>" . $duplicate_full_num . "</a>";
        }
        $messages->setWarning(sprintf($this->i18n('automation_protocol_duplicate_number'), implode(', ', $duplicate_urls)));
        $messages->insertInSession($registry);
        return true;
    }

    /**
     * Change the status of a document of type 2 if deleted from a warranty card
     *
     * @param array $params
     * @return boolean
     */
    public function changeStatusIfDeleted($params)
    {
        $model = $params['model'];
        $old_model = $params['model']->get('old_model');

        $model_engine_ids = $model->getPlainVarValue('dosie_engine_id');
        $old_engine_ids = $old_model->getPlainVarValue('dosie_engine_id');

        $documents_ids = array_diff($old_engine_ids, $model_engine_ids);
        $result = true;
        if (!empty($documents_ids) && is_array($documents_ids)) {
            $documents = Documents::search($this->registry, array('where' => array('d.id IN ("' . implode('","', $documents_ids) . '")')));
            $result = $this->_changeDocumentsStatus($documents, 'opened', 4);
        }

        return $result;
    }

    /**
     * Changes status of all documents of type 2 from all deactivated warranty cards documents
     *
     * @param array $params
     * @return boolean
     */
    public function changeStatusWhenDeactivate($params)
    {
        $result = true;

        $query = "
        SELECT GROUP_CONCAT(DISTINCT d.id) AS documents_ids, GROUP_CONCAT(DISTINCT dc2.value) AS subdocuments_ids
        FROM " . DB_TABLE_DOCUMENTS . " as d
        JOIN " . DB_TABLE_FIELDS_META . " as fm2
          ON(d.active=0
            AND d.deleted_by=0
            AND d.`type`=3
            AND fm2.model_type=d.`type`
            AND fm2.name='dosie_engine_id'
            AND fm2.model='Document')
        JOIN " . DB_TABLE_DOCUMENTS_CSTM . " as dc2
          ON(dc2.model_id=d.id
            AND dc2.value != ''
            AND dc2.var_id=fm2.id )
        JOIN " . DB_TABLE_FIELDS_META . " as fm
          ON(fm.model='Document'
            AND fm.model_type=d.`type`
            AND fm.name='dosie_deactivated_flag'
          )
        LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " as dc
          ON(dc.model_id=d.id
            AND dc.var_id=fm.id
            AND dc2.num=dc.num
          )
        WHERE dc.value!=1 OR dc.value IS NULL";

        $documents_result = $this->registry['db']->getRow($query);

        if (empty($documents_result['subdocuments_ids']) || empty($documents_result['documents_ids'])) {
            return $result;
        }

        $documents = Documents::search(
                $this->registry, array(
                'where' => array("d.id IN ({$documents_result['subdocuments_ids']})"),
                'skip_assignments' => true,
                'skip_permissions_check' => true
                )
        );

        $result = $this->_changeDocumentsStatus($documents, 'opened', 4, $documents_result['documents_ids']);
        return $result;
    }

    /**
     * Changes status of documents and saves history of the actions
     *
     * @param array $documents
     * @param string $new_status
     * @param int $new_substatus
     * @return boolean
     */
    private function _changeDocumentsStatus($documents, $new_status, $new_substatus, $documents_ids = false)
    {
        $result = true;
        if (!empty($documents)) {
            $failed_documents_ids = array();
            foreach ($documents as $document) {
                $document->unsanitize();
                // Preparing the required parameters
                $status_params = array(
                    'model_id' => $document->get('id'),
                    'module' => 'documents',
                    'model' => $document,
                    'new_status' => $new_status,
                    'new_substatus' => $new_substatus
                );
                // Saves the new status of the document
                if (!$this->status($status_params)) {
                    $failed_documents_ids[] = $document->get('id');
                    $this->executionErrors[] = "Failed to set new status for document with id: {$document->get('id')}!";
                    // Returns false if even one operaion failed
                    $result = false;
                }
            }

            if ($documents_ids) {
                $query = "INSERT INTO " . DB_TABLE_DOCUMENTS_CSTM . " (model_id, var_id, num, value, added, added_by, modified, modified_by, lang)
                            SELECT d.id, fm.id, dc2.num, '1', NOW(), 1, NOW(), 1, dc2.lang
                              FROM " . DB_TABLE_DOCUMENTS . " as d
                              JOIN " . DB_TABLE_FIELDS_META . " as fm2
                                ON(d.active=0
                                  AND d.deleted_by=0
                                  AND d.`type`=3
                                  AND fm2.model_type=d.`type`
                                  AND fm2.name='dosie_engine_id'
                                  AND fm2.model='Document'
                                  AND d.id IN({$documents_ids})
                                )
                              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " as dc2
                                ON(dc2.model_id=d.id
                                  AND dc2.value != ''
                                  AND dc2.var_id=fm2.id" .
                    (!empty($failed_documents_ids) ? "
                                  AND dc2.value NOT IN(" . implode(', ', $failed_documents_ids) . ")" : '') . "
                                )
                              JOIN " . DB_TABLE_FIELDS_META . " as fm
                                ON(fm.model='Document'
                                  AND fm.model_type=d.`type`
                                  AND fm.name='dosie_deactivated_flag'
                                )
                              LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " as dc
                                ON(dc.model_id=d.id
                                  AND dc.var_id=fm.id
                                  AND dc2.num=dc.num
                                )
                              WHERE dc.value!=1 OR dc.value IS NULL
                              ON DUPLICATE KEY UPDATE
                                value = VALUES(value),
                                modified = VALUES(modified),
                                modified_by = VALUES(modified_by)";
                $this->registry['db']->Execute($query);
                if ($this->registry['db']->ErrorMsg()) {
                    $this->executionErrors[] = $this->registry['db']->ErrorMsg();
                    $result = false;
                }
            }
        } else {
            $result = false;
        }
        return $result;
    }

    /**
     * Add relation between document of type "Service order" (id 5) and document of type "Machine file" (id 2)
     *
     * @param array $params - automation params
     * @return boolean
     */
    public function addRelationServiceOrders($params)
    {
        // Prepare some basics
        $registry = $this->registry;
        $messages = $registry['messages'];
        $db = $registry['db'];

        // Prepare to collect messages
        $messages_success = array();

        // Start a transaction
        $db->StartTrans();

        // Get the current model, which should be of type "Service order"
        $service_order = $params['model'];

        // Unsanitize the model
        if ($sanitize_after = $service_order->isSanitized()) {
            $service_order->unsanitize();
        }

        // Get the current machine serial number
        $mash_air_serial_num = $service_order->getPlainVarValue('mash_air_serial_num');

        // Define "Machine file" documents type id
        $doc_type_machine_file = 2;
        // Define tag "Repeated service"
        $tag_repeated_service_id = 4;

        // Remove all relations between the current service order and the machine files
        $query = "
            DELETE dr
              FROM " . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr
              JOIN " . DB_TABLE_DOCUMENTS . " AS d
                ON (dr.link_to_model_name = '{$service_order->modelName}'
                  AND dr.link_to = {$service_order->get('id')}
                  AND dr.parent_model_name = 'Document'
                  AND dr.parent_id = d.id
                  AND d.type = {$doc_type_machine_file})";
        $db->Execute($query);

        /**
         * Update relations between current service order and the machine files
         */
        if (!empty($mash_air_serial_num)) {
            // Get all machine files for the current machine
            $query = "
            SELECT d.id,
                d.full_num,
                di.description
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                ON (!d.deleted
                  AND d.active
                  AND d.type = {$doc_type_machine_file}
                  AND di.parent_id = d.id
                  AND di.lang = '{$service_order->get('model_lang')}')
              JOIN " . DB_TABLE_FIELDS_META . " AS fm
                ON (fm.model = 'Document'
                  AND fm.model_type = d.type
                  AND fm.name = 'air_serial_num')
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON (dc.model_id = d.id
                  AND dc.var_id = fm.id
                  AND dc.num = 1
                  AND dc.lang = ''
                  AND dc.value = '{$mash_air_serial_num}')
              ORDER BY id ASC";
            $machine_files = $db->GetAll($query);

            // If there are machine files
            if (!empty($machine_files)) {
                // Add relation between the current service order and the machine file
                $insert_values = array();
                foreach ($machine_files as $machine_file) {
                    $insert_values[] = "({$machine_file['id']}, 'Document', {$service_order->get('id')}, 'Document', 'inherited')";
                    $url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%s&amp;model_lang=%s', $_SERVER['PHP_SELF'], $registry['module_param'], $machine_file['id'], $service_order->get('model_lang'));
                    $messages_success[] = sprintf($this->i18n('automation_addrelationserviceorders_add_relation_success'), $url, $machine_file['full_num']);
                }
                $query = "
                  INSERT IGNORE INTO " . DB_TABLE_DOCUMENTS_RELATIVES . " (parent_id, parent_model_name, link_to, link_to_model_name, origin) VALUES
                    " . implode(",
                    ", $insert_values);
                $db->Execute($query);

                // Fill the autocompleter for machine file with the one with the biggest id
                $vars_to_edit = array(
                    'mash_file_id'   => 'id',
                    'mash_file_name' => 'description'
                );
                $get_old_vars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                $service_order->getVars();
                $registry->set('get_old_vars', $get_old_vars, true);
                $old_service_order = clone $service_order;
                $old_service_order->sanitize();
                $vars = $service_order->get('vars');
                foreach ($vars as $k => $var) {
                    if (array_key_exists($var['name'], $vars_to_edit)) {
                        $vars[$k]['value'] = $machine_file[$vars_to_edit[$var['name']]];
                    }
                }
                $service_order->set('vars', $vars, true);
                if ($service_order->save()) {
                    $new_service_order_filters = array(
                        'where'                  => array("d.id = '{$service_order->get('id')}'"),
                        'model_lang'             => $service_order->get('model_lang'),
                        'skip_assignments'       => true,
                        'skip_permissions_check' => true
                    );
                    $new_service_order = Documents::searchOne($registry, $new_service_order_filters);
                    $get_old_vars = $registry->get('get_old_vars');
                    $registry->set('get_old_vars', true, true);
                    $new_service_order->getVars();
                    $registry->set('get_old_vars', $get_old_vars, true);
                    Documents_History::saveData(
                        $registry,
                        array(
                            'action_type' => 'edit',
                            'old_model'   => $old_service_order,
                            'model'       => $service_order,
                            'new_model'   => $new_service_order
                        )
                    );
                } else {
                    // Set error: failed to fill machine file autocompleter
                    $messages->setError($this->i18n('automation_addrelationserviceorders_fill_machine_file_failed'));
                }
            } else {
                // If there is no machine file: show error
                $messages->setError($this->i18n('automation_addrelationserviceorders_add_relation_failed'));
            }
        }

        /**
         * Add/Remove tag "Repeated service"
         */
        $has_other_service_orders = false;
        if (!empty($mash_air_serial_num)) {
            $query = "
            SELECT COUNT(*)
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN _fields_meta AS fm
                ON (!d.deleted
                  AND d.active
                  AND d.type = {$service_order->get('type')}
                  AND d.id != {$service_order->get('id')}
                  AND fm.model = '{$service_order->modelName}')
                  AND fm.model_type = {$service_order->get('type')}
                  AND fm.name = 'mash_air_serial_num'
              JOIN documents_cstm AS dc
                ON (dc.model_id = d.id
                  AND dc.var_id = fm.id
                  AND dc.num = 1
                  AND dc.lang = ''
                  AND dc.value = '{$mash_air_serial_num}')";
            $has_other_service_orders = $db->GetOne($query);
        }
        $service_order->getTags();
        $service_order_tags = $service_order->get('tags');
        if (!in_array($tag_repeated_service_id, $service_order_tags) && $has_other_service_orders) {
            // Add the tag
            $tag_params = array(
                'id' => $params['id'],
                'module' => 'documents',
                'model' => $new_service_order,
                'model_id' => $new_service_order->get('id'),
                'new_tags' => $tag_repeated_service_id
            );
            if (!$this->tag($tag_params)) {
                $db->FailTrans();
            }
        } else if (in_array($tag_repeated_service_id, $service_order_tags) && !$has_other_service_orders) {
            // Remove the tag
            $tag_params = array(
                'id' => $params['id'],
                'module' => 'documents',
                'model' => $new_service_order,
                'model_id' => $new_service_order->get('id'),
                'tags' => $tag_repeated_service_id
            );
            if (!$this->removeTags($tag_params)) {
                $db->FailTrans();
            }
        }

        // Sanitize the model
        if ($sanitize_after) {
            $service_order->sanitize();
        }

        // If there are errors
        if ($messages->getErrors()) {
            // Fail the transaction
            $db->FailTrans();
        } else if ($db->HasFailedTrans()) {
            // If there are no error, bu transaction is failed, then set an error message for that
            $messages->setError($this->i18n('error_technical_error_please_contact_nzoom_support'));
        }

        // Get the result of the transaction
        $result = !$db->HasFailedTrans();

        // If the result is successful and there are success messages
        if ($result && !empty($messages_success)) {
            // Set the success messages
            foreach ($messages_success as $message_success) {
                $messages->setMessage($message_success);
            }
        }

        // Insert messages in session
        $messages->insertInSession($registry);

        // Complete the transaction
        $db->CompleteTrans();

        return $result;
    }

    /**
     * Validate documents of type "Warranty card"
     *
     * @param array automation params
     * @return boolean
     */
    public function validateWarrantyCard($params)
    {
        $result = true;

        $warranty = &$params['model'];

        if ($warranty->isSanitized()) {
            $warranty->unsanitize();
            $sanitize_after = true;
        }

        $model_lang = $warranty->get('model_lang');
        $query = "
            SELECT id
              FROM " . DB_TABLE_FIELDS_META . "
              WHERE model = 'Document'
                AND model_type = '{$warranty->get('type')}'
                AND name = '{$this->settings['field_mash_air_serial_num']}'";
        $field_mash_air_serial_num = $this->registry['db']->GetOne($query);
        $query = "
            SELECT d.id    AS id,
                d.full_num AS num,
                dc.value   AS serial
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON (d.type = '{$warranty->get('type')}'
                  AND d.deleted_by = 0
                  AND d.active = 1
                  AND d.id != '{$warranty->get('id')}'
                  AND dc.model_id = d.id
                  AND dc.var_id = '{$field_mash_air_serial_num}'
                  AND dc.lang IN ('', '{$model_lang}')
                  AND dc.value IN ('" . implode("', '", $warranty->getPlainVarValue($this->settings['field_mash_air_serial_num'])) . "'))";
        $results_array = $this->registry['db']->GetAll($query);
        if ($results_array) {
            $this->registry['messages']->setError($this->i18n('automations_validatewarrantycard_failed'));
            $results = array();
            foreach ($results_array as $result) {
                if (!isset($results[$result['id']])) {
                    $results[$result['id']] = array(
                        'num' => $result['num'],
                        'serials' => array()
                    );
                }
                $results[$result['id']]['serials'][] = $result['serial'];
            }
            foreach ($results as $id => $card) {
                $url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%s&amp;model_lang=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], $id, $model_lang);
                $this->registry['messages']->setError(sprintf($this->i18n('automations_validatewarrantycard_failed_card_serials'), $url, $card['num'], implode(', ', $card['serials'])));
            }
            $result = false;
        }

        if (isset($sanitize_after)) {
            $warranty->sanitize();
        }

        return $result;
    }

    /**
     * Forbid adding of warranty cards if it didn't' came from dashlet atc_unmounted_machines
     * or hide the "Add" tab
     */
    public function beforeWarrantyCard($params) {
        // Forbid adding of warranty cards if it didn't' came from dashlet atc_unmounted_machines
        if (in_array($this->registry['action'], array('add', 'addquick')) && !$params['model']->getVarValue('warranty_all')) {
            $this->registry['messages']->setError($this->i18n('automations_beforewarrantycard_use_the_dashlet'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect('index');
        }
//         echo "<script>
//                   window.onload = function () {
//                       $('zpMenu1Item4').style.display = 'none';
//                   };
//               </script>";

//         echo '<style>
//                 [id="zpMenu1Item4"] {
//                   display: none;
//                 }
//                 [id="after_action_add"] {
//                   display: none;
//                 }
//                 label[for="after_action_add"] {
//                   display: none;
//                 }
//                 [id="after_action_add"] + label + br {
//                   display: none;
//                 }
//               </style>';

        /**
         * Temporary forbid add permissions for documents of type 3
         */
        $rights = $this->registry['originalUser']->get('rights');
        $rights['documents3']['add'] = 'none';
//         $rights['documents3']['add'] = 'all';
//         $rights['documents']['add'] = 'all';
        $this->registry['originalUser']->set('rights', $rights, true);
    }

    /**
     * Check whether the Warranty Generation fits all requirements.
     * Warranty MUST NOT generate when pattern = 2 (60 months) AND it contains any warranty_max set to 1 (24 months)
     *
     * @param $params
     * @return bool
     */
    public function shouldWarrantyGenerate($params)
    {
        $settings = $this->settings;
        $pattern = $settings['pattern'];
        $warranty_max = $settings['warranty_max'];
        $model_id = $params['model']->get('id');

        // If request's pattern ID matches the one from AD's settings
        if ($this->registry['request']->get('pattern') == $pattern) {
            // Get warranty_max's field ID
            $query = "
            SELECT id
              FROM " . DB_TABLE_FIELDS_META . "
              WHERE model_type = 3
                AND model = 'Document'
                AND name = 'warranty_max'";
            $field_id = $this->registry['db']->GetOne($query);

            // Returns all warranty_max fields set to 1 (24 months)
            $query = "
               SELECT value
                 FROM " . DB_TABLE_DOCUMENTS_CSTM . "
                 WHERE var_id = {$field_id}
                   AND model_id = {$model_id}
                   AND value != '{$warranty_max}'";

            // Fail action if query returns any data
            if ($this->registry['db']->GetAll($query)) {
                $this->registry['messages']->setError($this->i18n('automations_generation_not_allowed'));
                $this->registry['messages']->insertInSession($this->registry);

                $referer = $_SERVER['HTTP_REFERER'];
                if (isset($referer)) {
                    header('Location: ' . $referer);
                    exit;
                }

                $this->redirect('documents', 'view',['view'=>$model_id]);
                return false;
            }
        }

        return true;
    }

    /**
     * Compose file path for the iDoc SAP file
     * @param string $type
     * @return string $filename
     */
    public function composeiDocSapFilename(string $type)
    {
        switch($type) {
            case 'debmas':
                $table = 'sap_debmas';
                $query = "SELECT COUNT(`id`) FROM `{$table}` WHERE direction='outgoing'";
                $count = (int) $this->registry['db']->getOne($query);
                $filename = sprintf('01_DEBMAS_%08d.xml', ++$count);
                break;
            case 'iorder':
                $table = 'sap_iorder';
                $query = "SELECT COUNT(`id`) FROM `{$table}` WHERE direction='outgoing'";
                $count = (int) $this->registry['db']->getOne($query);
                $filename = sprintf('02_IORDER_%08d.xml', ++$count);
                break;
            case 'conf':
                $table = 'sap_conf';
                $query = "SELECT COUNT(`id`) FROM `{$table}` WHERE direction='outgoing'";
                $count = (int) $this->registry['db']->getOne($query);
                $filename = sprintf('03_CONF_%08d.xml', ++$count);
                break;
            case 'wmmbid':
                $table = 'sap_wmmbid';
                $query = "SELECT COUNT(`id`) FROM `{$table}` WHERE direction='outgoing'";
                $count = (int) $this->registry['db']->getOne($query);
                $filename = sprintf('04_WMMBID_%08d.xml', ++$count);
                break;
        }

        return $filename;
    }

    /**
     * Logs incoming/outgoing SAP files in a DB table
     *
     * @param string $type
     * @param object|null $record
     * @param string $filepath
     * @param string $direction
     * @return bool $result
     */

    public function logiDocSap(string $type, $record, string $filepath, string $direction)
    {
        $filename = basename($filepath);
        //$xmlStr = $this->beautifyXML(file_get_contents($filepath));
        $xmlStr = file_get_contents($filepath);

        $recordId = $record ? $record->get('id') : '';

        switch($type) {
            case 'debmas':
                $table = 'sap_debmas';
                $query = "INSERT INTO `{$table}` (`direction`, `customer_id`, `filename`, `path`, `xml`) VALUES\n" .
                    "('{$direction}', '{$recordId}', '" . General::slashesEscape($filename) . "', '" .
                    General::slashesEscape($filepath) . "', '" . General::slashesEscape($xmlStr) . "')";
                $this->registry['db']->Execute($query);
                break;
            case 'iorder':
                $table = 'sap_iorder';
                $query = "INSERT INTO `{$table}` (`direction`, `order_id`, `filename`, `path`, `xml`) VALUES\n" .
                    "('{$direction}', '{$recordId}', '" . General::slashesEscape($filename) . "', '" .
                    General::slashesEscape($filepath) . "', '" . General::slashesEscape($xmlStr) . "')";
                $this->registry['db']->Execute($query);
                break;
            case 'conf':
                $table = 'sap_conf';
                $query = "INSERT INTO `{$table}` (`direction`, `confirmation_id`, `filename`, `path`, `xml`) VALUES\n" .
                    "('{$direction}', '{$recordId}', '" . General::slashesEscape($filename) . "', '" .
                    General::slashesEscape($filepath) . "', '" . General::slashesEscape($xmlStr) . "')";
                $this->registry['db']->Execute($query);
                break;
            case 'wmmbid':
                $table = 'sap_wmmbid';
                $query = "INSERT INTO `{$table}` (`direction`, `material_id`, `filename`, `path`, `xml`) VALUES\n" .
                    "('{$direction}', '{$recordId}', '" . General::slashesEscape($filename) . "', '" .
                    General::slashesEscape($filepath) . "', '" . General::slashesEscape($xmlStr) . "')";
                $this->registry['db']->Execute($query);
                break;
            case 'matmas':
                $table = 'sap_matmas';
                $query = "INSERT INTO `{$table}` (`direction`, `material_id`, `filename`, `path`, `xml`) VALUES\n" .
                    "('{$direction}', '{$recordId}', '" . General::slashesEscape($filename) . "', '" .
                    General::slashesEscape($filepath) . "', '" . General::slashesEscape($xmlStr) . "')";
                $this->registry['db']->Execute($query);
                break;
            case 'desadv':
                $table = 'sap_desadv';
                $query = "INSERT INTO `{$table}` (`direction`, `delivery_id`, `filename`, `path`, `xml`) VALUES\n" .
                    "('{$direction}', '{$recordId}', '" . General::slashesEscape($filename) . "', '" .
                    General::slashesEscape($filepath) . "', '" . General::slashesEscape($xmlStr) . "')";
                $this->registry['db']->Execute($query);
                break;
        }

        $result = true;
        if ($this->registry['db']->ErrorMsg()) {
            $result = false;
        }

        return $result;
    }

    /**
     * Automation to create SAP iDOC file for order (document 7),
     * store it in certain folder and log it in database
     */
    private function _sendToSAP($type, $object)
    {
        switch($type) {
            case 'debmas':
                $xmlStr = $this->createiDocCustomer($object);
                break;
            case 'iorder':
                $xmlStr = $this->createiDocOrder($object);
                break;
            case 'conf':
                $xmlStr = $this->createiDocConfirmation($object);
                break;
            case 'wmmbid':
                $xmlStr = $this->createiDocMaterial($object);
                break;
            default:
                $this->registry['messages']->setError($this->i18n('error_type_not_defined'));
                $this->registry['messages']->insertInSession($this->registry);
                return false;
        }
        //output XML for debug
        //header('Content-type: text/xml');
        //echo $this->beautifyXML($xmlStr);exit;

        $filename = $this->composeiDocSapFilename($type);
        $path = realpath($this->settings['sap_path']);

        if (empty($this->settings['sap_path'])) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_defined'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        } elseif (!file_exists($path) || !is_dir($path) || !is_writable($path)) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_writable'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        } elseif (file_exists($path . '/' . $filename)) {
            $this->registry['messages']->setError(sprintf($this->i18n('error_sap_filename_already_exists'), $filename));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        if (!file_put_contents($path . '/' . $filename, $xmlStr)) {
            $this->registry['messages']->setError($this->i18n('error_could_not_write_sap_file'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        if (!$this->logiDocSap($type, $object, $path . '/' . $filename, 'outgoing')) {
            $this->registry['messages']->setWarning($this->i18n('warning_could_not_log_sap_message'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        $this->registry['messages']->setMessage(sprintf($this->i18n('message_write_sap_file_successful'), $filename));
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Automation to create SAP iDOC file for confirmation of orders (document 8),
     * store it in certain folder and log it in database
     */
    public function nzoom2SAPMaterialSync($params)
    {
        /**
         * @var $materialsDoc Document
         */
        $materialsDoc = $params['model'];

        //prepares and sends all information to SAP
        $this->_sendToSAP('wmmbid', $materialsDoc);

        $this->updateAutomationHistory($params, $materialsDoc, 1);

        return true;
    }

    /**
     * Automation to create SAP iDOC file for confirmation of orders (document 8),
     * store it in certain folder and log it in database
     */
    public function nzoom2SAPConfirmationSync($params)
    {
        /**
         * @var $confirmation Document
         */
        $confirmation = $params['model'];

        //prepares and sends all information to SAP
        $this->_sendToSAP('conf', $confirmation);

        $this->updateAutomationHistory($params, $confirmation, 1);

        return true;
    }

    /**
     * Automation to create SAP iDOC file for order (document 7),
     * store it in certain folder and log it in database
     */
    public function nzoom2SAPOrderSync($params)
    {
        /**
         * @var $order Document
         */
        $order = $params['model'];

        //prepares and sends all information to SAP
        $this->_sendToSAP('iorder', $order);

        $this->updateAutomationHistory($params, $order, 1);

        return true;
    }

    /**
     * Automation to create SAP iDOC file for dealers/installers (customer 2),
     * store it in certain folder and log it in database
     */
    public function nzoom2SAPCustomerSync($params)
    {
        /**
         * @var $customer Customer
         */
        $customer = $params['model'];

        //prepares and sends all information to SAP
        $this->_sendToSAP('debmas', $customer);

        $this->updateAutomationHistory($params, $customer, 1);

        return true;
    }

    /**
     * Creates customer iDoc xml file
     * @param Customer $customer
     * @return string $xml
     */
    public function createiDocCustomer(Customer $customer)
    {
        $date = new DateTime('NOW');

        $xml = new SimpleXMLElement('<DEBMAS03/>');

        $iDoc = $xml->addChild('IDOC');
        $iDoc->addAttribute('BEGIN', 1);

        //EDI -> Electronic Data Interchange
        $edi = $iDoc->addChild('EDI_DC40');
        $edi->addAttribute('SEGMENT', 1);
        $edi->addChild('TABNAM', 'EDI_DC40');

        //ToDo: define whether to use the following elements
        $edi->addChild('MANDT');
        $edi->addChild('DOCNUM');
        $edi->addChild('DOCREL');
        $edi->addChild('STATUS');
        $edi->addChild('DIRECT');
        $edi->addChild('OUTMOD');

        $edi->addChild('IDOCTYP', 'DEBMAS03');
        $edi->addChild('MESTYP', 'DEBMAS');
        $edi->addChild('STDMES', 'DEBMAS');


        //sender
        //ToDo: define sender/receiver
        $edi->addChild('SNDPOR', 'NZOOM-IN');
        $edi->addChild('SNDPRT', 'LS');
        $edi->addChild('SNDPRN', 'NZOOM');
        //receiver
        $edi->addChild('RCVPOR', 'SAPTST');
        $edi->addChild('RCVPRT', 'LS');
        $edi->addChild('RCVPRN', 'NZOOM');
        //created date and time
        $edi->addChild('CREDAT', $date->format('Ymd'));
        $edi->addChild('CRETIM', $date->format('His'));
        //ToDo: define serial?!?!?
        $edi->addChild('SERIAL', $date->format('YmdHis'));

        //E1KNA1M - basic customer data
        $e1kna1m = $iDoc->addChild('E1KNA1M');
        $e1kna1m->addAttribute('SEGMENT', 1);

        $e1kna1m->addChild('MSGFN', '005');
        $e1kna1m->addChild('KUNNR');
        $e1kna1m->addChild('ANRED');
        $e1kna1m->addChild('BAHNS', $customer->get('id'));
        $e1kna1m->addChild('BBBNR');
        $e1kna1m->addChild('BBSNR');
        $e1kna1m->addChild('BUBKZ');
        $e1kna1m->addChild('KTOKD', '1000');
        //ToDo: define whether the customer is Bulgarian
        $e1kna1m->addChild('LAND1', 'BG');
        $e1kna1m->addChild('NAME1', trim($customer->get('name') . ' ' . $customer->get('lastname')));
        $e1kna1m->addChild('NAME2', $customer->get('is_company') ? $customer->get('company_name') : '');
        $e1kna1m->addChild('ORT01', $customer->get('city'));
        $e1kna1m->addChild('PSTLZ', $customer->get('postal_code'));
        $e1kna1m->addChild('STRAS', $customer->get('address'));

        $e1kna1m->addChild('REGIO', $customer->getVarValue('sap_region'));

        $sortl = $customer->get('ucn');
        if ($customer->get('is_company')) {
            $sortl = $customer->get('eik');
            if ($customer->get('in_dds')) {
                $sortl = $customer->get('in_dds');
            }
        }
        $e1kna1m->addChild('SORTL', $sortl);
        $e1kna1m->addChild('SPRAS');
        $e1kna1m->addChild('LZONE');
        $e1kna1m->addChild('UMJAH');
        $e1kna1m->addChild('JMZAH');
        $e1kna1m->addChild('JMJAH');
        $e1kna1m->addChild('UMSA1');
        $e1kna1m->addChild('HZUOR');
        $e1kna1m->addChild('CIVVE');
        //communication language
        $e1kna1m->addChild('SPRAS_ISO', 'BG');


        $phones = $customer->get('phone');
        if (!$phones) {
            $phones = array();
        }
        $mobiles = $customer->get('gsm');
        if (!$mobiles) {
            $mobiles = array();
        }
        $allPhones = array_merge($phones, $mobiles);
        if ($allPhones) {
            $phoneCnt = 0;
            foreach ($allPhones as $phone) {
                $phoneCnt++;
                $e1kna1m->addChild('TELF' . $phoneCnt, $phone);
            }
        }

        //VAT
        $e1kna1m->addChild('STKZU', $customer->get('in_dds') ? 'X':'');
        $e1kna1m->addChild('STCD1', $customer->get('eik'));
        $e1kna1m->addChild('STCEG', $customer->get('in_dds'));

        //Additional customer data KNA1 (add it empty)
        $e1kna11 = $e1kna1m->addChild('E1KNA11');
        $e1kna11->addAttribute('SEGMENT', 1);
        $emails = $customer->get('emaiil');
        $e1kna11->addChild('KNURL', $emails[0] ?? '');

        //Customer sales KNVV
        $e1knvvm = $e1kna1m->addChild('E1KNVVM');
        $e1knvvm->addAttribute('SEGMENT', 1);

        $e1knvvm->addChild('MSGFN', '005');
        $e1knvvm->addChild('VKORG', '1000');
        $e1knvvm->addChild('VTWEG', '00');
        $e1knvvm->addChild('SPART', '00');
        $e1knvvm->addChild('VERSG', '1');
        $e1knvvm->addChild('KALKS', '1');
        $e1knvvm->addChild('AWAHR');
        $e1knvvm->addChild('INCO1', 'EXW');
        $e1knvvm->addChild('INCO2', 'ФРАНКО ЗАВОД-ПРОИЗВОДИТЕЛ');
        $e1knvvm->addChild('ANTLF');
        $e1knvvm->addChild('KZAZU');
        $e1knvvm->addChild('LPRIO');
        $e1knvvm->addChild('VSBED', '01');
        $e1knvvm->addChild('PERFK', 'BG');
        $e1knvvm->addChild('PERRL', 'BG');
        $e1knvvm->addChild('WAERS', 'BGN');
        $e1knvvm->addChild('KTGRD', '01');
        $e1knvvm->addChild('ZTERM', '1000');
        $e1knvvm->addChild('VWERK', '1000');
        $e1knvvm->addChild('VKBUR', '1000');
        $e1knvvm->addChild('UEBTO');
        $e1knvvm->addChild('UNTTO');
        $e1knvvm->addChild('PODTG');

        //Customer base tax indicator KNVI (1)
        $e1knvim1 = $e1kna1m->addChild('E1KNVIM');
        $e1knvim1->addAttribute('SEGMENT', 1);

        $e1knvim1->addChild('MSGFN', '005');
        $e1knvim1->addChild('ALAND', 'BG');
        $e1knvim1->addChild('TATYP', 'MWST');
        $e1knvim1->addChild('TAXKD', '1');

        //Customer base tax indicator KNVI (2)
        $e1knvim2 = $e1kna1m->addChild('E1KNVIM');
        $e1knvim2->addAttribute('SEGMENT', 1);

        $e1knvim2->addChild('MSGFN', '005');
        $e1knvim2->addChild('ALAND', 'BG');
        $e1knvim2->addChild('TATYP', 'ZMWS');
        $e1knvim2->addChild('TAXKD', '1');

        //Customer base company code KNB1
        $e1knb1m = $e1kna1m->addChild('E1KNB1M');
        $e1knb1m->addAttribute('SEGMENT', 1);

        $e1knb1m->addChild('MSGFN', '005');
        $e1knb1m->addChild('BUKRS', '1000');
        $e1knb1m->addChild('AKONT', '0000041101');
        $e1knb1m->addChild('ZTERM', '1000');
        $e1knb1m->addChild('ZINDT');
        $e1knb1m->addChild('ZINRT');
        $e1knb1m->addChild('VLIBB');
        $e1knb1m->addChild('VRSZL');
        $e1knb1m->addChild('VRSPR');
        $e1knb1m->addChild('VERDT');
        $e1knb1m->addChild('WEBTR');
        $e1knb1m->addChild('DATLZ');
        $e1knb1m->addChild('XZVER', 'X');
        $e1knb1m->addChild('KULTG');
        $e1knb1m->addChild('PERNR');

        //Customer contact persons KNVK
        if ($customer->get('is_company')) {
            $mol = trim($customer->get('mol'));
            $molNames = preg_split('#\s+#', $mol);
            $molFirst = array_shift($molNames);
            $molLast = implode(' ', $molNames);
            $e1knvkm = $e1kna1m->addChild('E1KNVKM');
            $e1knvkm->addAttribute('SEGMENT', 1);
            $e1knvkm->addChild('NAME1', $molFirst);
            $e1knvkm->addChild('NAMEV', $molLast);
            $e1knvkm->addChild('PAFKT', 'BG');

            $filters = array(
                'where' => array(
                    'bn.parent_customer = ' . $customer->get('id'),
                    'c.subtype = \'contact\''
                )
            );
            $contactPersons = Customers_Contactpersons::search($this->registry, $filters);
            if ($contactPersons) {
                foreach($contactPersons as $contactPerson) {
                    $e1knvkm = $e1kna1m->addChild('E1KNVKM');
                    $e1knvkm->addAttribute('SEGMENT', 1);
                    $e1knvkm->addChild('NAME1', trim($contactPerson->get('name')));
                    $e1knvkm->addChild('NAMEV', trim($contactPerson->get('lastname')));
                    $e1knvkm->addChild('PAFKT', $contactPerson->get('admit_VAT_credit') ? 'BG' : '');
                }
            }
        }

        return $xml->asXML();
    }

    /**
     * Creates order iDoc xml file
     * @param Document $order
     * @return string $xml
     */
    public function createiDocOrder(Document $order)
    {
        $date = new DateTime('NOW');

        $xml = new SimpleXMLElement('<IORDER01/>');

        $iDoc = $xml->addChild('IDOC');
        $iDoc->addAttribute('BEGIN', 1);

        //EDI -> Electronic Data Interchange
        $edi = $iDoc->addChild('EDI_DC40');
        $edi->addAttribute('SEGMENT', 1);
        $edi->addChild('TABNAM', 'EDI_DC40');

        //ToDo: define whether to use the following elements
        $edi->addChild('MANDT');
        $edi->addChild('DOCNUM');
        $edi->addChild('DOCREL');
        $edi->addChild('STATUS');
        $edi->addChild('DIRECT');
        $edi->addChild('OUTMOD');

        $edi->addChild('IDOCTYP', 'IORDER01');
        $edi->addChild('MESTYP', 'IORDER');
        $edi->addChild('STDMES', 'IORDER');


        //sender
        //ToDo: define sender/receiver
        $edi->addChild('SNDPOR', 'NZOOM-IN');
        $edi->addChild('SNDPRT', 'LS');
        $edi->addChild('SNDPRN', 'NZOOM');
        //receiver
        $edi->addChild('RCVPOR', 'SAPTST');
        $edi->addChild('RCVPRT', 'LS');
        $edi->addChild('RCVPRN', 'NZOOM');
        //created date and time
        $edi->addChild('CREDAT', $date->format('Ymd'));
        $edi->addChild('CRETIM', $date->format('His'));
        //ToDo: define serial?!?!?
        $edi->addChild('SERIAL', $date->format('YmdHis'));

        $e1orhdr = $iDoc->addChild('E1ORHDR');
        $e1orhdr->addAttribute('SEGMENT', 1);

        $e1orhdr->addChild('AUFNR', $order->get('full_num'));
        $e1orhdr->addChild('STRNO', 'ATC');
        $e1orhdr->addChild('KTEXT', mb_substr($order->get('description'), 0, 40));
        $e1orhdr->addChild('EQUNR');

        $sapCodeType = '';
        $serviceOrderTypeId = $order->getPlainVarValue('service_order_type');
        if ($serviceOrderTypeId) {
            $nom = Nomenclatures::searchOne($this->registry,
                                            array('where' => array('n.id = ' . $serviceOrderTypeId)));
            if ($nom) {
                $sapCodeType = $nom->getVarValue('sap_code');
            }
        }
        $e1orhdr->addChild('AUART', $sapCodeType);
        $e1orhdr->addChild('STTXT');
        $e1orhdr->addChild('USTXT');
        $e1orhdr->addChild('BUKRS');
        $e1orhdr->addChild('KOKRS');
        $e1orhdr->addChild('WAERS');
        $e1orhdr->addChild('TERKZ');
        $e1orhdr->addChild('GSBER');

        $plannedStartDate = new DateTime($order->getVarValue('planned_start_date'));
        $e1orhdr->addChild('GSTRS', $plannedStartDate->format('Ymd'));
        $e1orhdr->addChild('GSUZS', $plannedStartDate->format('His'));
        $plannedEndDate = new DateTime($order->getVarValue('planned_end_date'));
        $e1orhdr->addChild('GLTRS', $plannedEndDate->format('Ymd'));
        $e1orhdr->addChild('GLUZS', $plannedEndDate->format('His'));
        $e1orhdr->addChild('GSTRP');
        $e1orhdr->addChild('GSUZP');
        $e1orhdr->addChild('GLTRP');
        $e1orhdr->addChild('GLUZP');

        $sapCodeWorkCenter = '';
        $workCenterId = $order->getPlainVarValue('work_center');
        if ($workCenterId) {
            $nom = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $workCenterId)));
            if ($nom) {
                $sapCodeWorkCenter = $nom->getVarValue('sap_code');
            }
        }
        $e1orhdr->addChild('ARBPL', $sapCodeWorkCenter);

        $sapCodeActivity = '';
        $activityTypeId = $order->getPlainVarValue('activity_type');
        if ($activityTypeId) {
            $nom = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $activityTypeId)));
            if ($nom) {
                $sapCodeActivity = $nom->getVarValue('sap_code');
            }
        }
        $e1orhdr->addChild('ILART', $sapCodeActivity);
        $e1orhdr->addChild('WERKS');
        $e1orhdr->addChild('LANGU_ISO');
        $e1orhdr->addChild('SERVICE');


        //Customer number
        $e1orpar = $e1orhdr->addChild('E1ORPAR');
        $e1orpar->addAttribute('SEGMENT', 1);
        $customerId = $order->getVarValue('dealer_name_id');
        $sapCodeCustomer = '';
        if ($customerId) {
            $customer = Customers::searchOne($this->registry, array('where' => array('c.id = ' . $customerId)));
            if ($customer) {
                $sapCodeCustomer = $customer->getVarValue('sap_code');
            }
        }
        $e1orpar->addChild('PARNR', $sapCodeCustomer);

        $e1oropr = $e1orhdr->addChild('E1OROPR');
        $e1oropr->addAttribute('SEGMENT', 1);
        $e1oropr->addChild('VORNR', '0010');

        return $xml->asXML();
    }

    /**
     * Creates order iDoc xml file
     * @param Document $confirmation
     * @return string $xml
     */
    public function createiDocMaterial(Document $confirmation)
    {
        $date = new DateTime('NOW');

        $xml = new SimpleXMLElement('<WMMBID02/>');

        $iDoc = $xml->addChild('IDOC');
        $iDoc->addAttribute('BEGIN', 1);

        //EDI -> Electronic Data Interchange
        $edi = $iDoc->addChild('EDI_DC40');
        $edi->addAttribute('SEGMENT', 1);
        $edi->addChild('TABNAM', 'EDI_DC40');

        //ToDo: define whether to use the following elements
        $edi->addChild('MANDT');
        $edi->addChild('DOCNUM');
        $edi->addChild('DOCREL');
        $edi->addChild('STATUS');
        $edi->addChild('DIRECT');
        $edi->addChild('OUTMOD');

        $edi->addChild('IDOCTYP', 'WMMBID02');
        $edi->addChild('MESTYP', 'WMMBXY');
        $edi->addChild('STDMES', 'WMMBID02');

        //sender
        //ToDo: define sender/receiver
        $edi->addChild('SNDPOR', 'NZOOM-IN');
        $edi->addChild('SNDPRT', 'LS');
        $edi->addChild('SNDPRN', 'NZOOM');
        //receiver
        $edi->addChild('RCVPOR', 'SAPTST');
        $edi->addChild('RCVPRT', 'LS');
        $edi->addChild('RCVPRN', 'NZOOM');
        //created date and time
        $edi->addChild('CREDAT', $date->format('Ymd'));
        $edi->addChild('CRETIM', $date->format('His'));
        //ToDo: define serial?!?!?
        $edi->addChild('SERIAL', $date->format('YmdHis'));

        $docDate = new DateTime($confirmation->get('date'));

        //E1MBXYH - basic material data
        $e1mbxyh = $iDoc->addChild('E1MBXYH');
        $e1mbxyh->addAttribute('SEGMENT', 1);

        $e1mbxyh->addChild('BLDAT', $docDate->format('Ymd'));
        $e1mbxyh->addChild('BUDAT', $date->format('Ymd'));
        $e1mbxyh->addChild('TCODE', 'MB1A');
        //ToDo: define the username
        $e1mbxyh->addChild('USNAM');

        $assocVars = $confirmation->getAssocVars();

        foreach($assocVars['material_sap']['value'] as $idx => $materialSapCode) {
            //E1MBXYI for each row of the grouping table
            $e1mbxyi = $e1mbxyh->addChild('E1MBXYI');
            $e1mbxyi->addAttribute('SEGMENT', 1);

            $e1mbxyi->addChild('MATNR', sprintf('%018d', $materialSapCode));
            $e1mbxyi->addChild('WERKS', '1000');
            $e1mbxyi->addChild('LGORT', '1003');
            $e1mbxyi->addChild('BWART', '261');
            $e1mbxyi->addChild('ERFMG', $assocVars['material_quantity']['value'][$idx]);
            //$e1mbxyi->addChild('ERFME', $this->convertMaterialMeasureNz2Sap($assocVars['material_measure']['value'][$idx]));
            //measure should be empty (Bug 5472, comment 5)
            $e1mbxyi->addChild('ERFME', '');
            $e1mbxyi->addChild('AUFNR', $assocVars['service_order_num']['value']);
        }

        return $xml->asXML();
    }

     /**
     * Converts nzoom option value to material measure in SAP
     * @param int $nzoomMeasureId
     * @return string $sapMesure
     */
    private function convertMaterialMeasureNz2Sap(int $nzoomMeasureId) {
        $sapMesure = '';

        if (array_key_exists('measurement_' .$nzoomMeasureId, $this->settings)) {
            $sapMesure = $this->settings['measurement_' .$nzoomMeasureId];
        }

        return $sapMesure;
    }

    /**
     * Converts SAP measurement into nzoom measure
     * @param string $sapMeasure
     * @return string $nzoomMeasureId
     */
    private function convertMeasureSap2Nz($sapMeasure) {
        $nzoomMeasureId = '';
        $sapMeasure = (string)$sapMeasure;

        //measurements map
        $measurements = array();
        foreach($this->settings as $setting => $value) {
            if (preg_match('#^measurement_(\d+)#', $setting, $matches)) {
                $measurements[$matches[1]] = $value;
            }
        }
        $measurementsReverse = array_flip($measurements);
        if (array_key_exists($sapMeasure, $measurementsReverse)) {
            $nzoomMeasureId = $measurementsReverse[$sapMeasure];
        }

        return $nzoomMeasureId;
    }

    /**
     * Creates order iDoc xml file
     * @param Document $confirmation
     * @return string $xml
     */
    public function createiDocConfirmation(Document $confirmation) {

        $date = new DateTime('NOW');

        $xml = new SimpleXMLElement('<CONF32/>');

        $iDoc = $xml->addChild('IDOC');
        $iDoc->addAttribute('BEGIN', 1);

        //EDI -> Electronic Data Interchange
        $edi = $iDoc->addChild('EDI_DC40');
        $edi->addAttribute('SEGMENT', 1);
        $edi->addChild('TABNAM', 'EDI_DC40');

        //ToDo: define whether to use the following elements
        $edi->addChild('MANDT');
        $edi->addChild('DOCNUM');
        $edi->addChild('DOCREL');
        $edi->addChild('STATUS');
        $edi->addChild('DIRECT');
        $edi->addChild('OUTMOD');

        $edi->addChild('IDOCTYP', 'CONF32');
        $edi->addChild('MESTYP', 'CONF32');
        $edi->addChild('STDMES', 'CONF32');

        //sender
        //ToDo: define sender/receiver
        $edi->addChild('SNDPOR', 'NZOOM-IN');
        $edi->addChild('SNDPRT', 'LS');
        $edi->addChild('SNDPRN', 'NZOOM');
        //receiver
        $edi->addChild('RCVPOR', 'SAPTST');
        $edi->addChild('RCVPRT', 'LS');
        $edi->addChild('RCVPRN', 'NZOOM');
        //created date and time
        $edi->addChild('CREDAT', $date->format('Ymd'));
        $edi->addChild('CRETIM', $date->format('His'));
        //ToDo: define serial?!?!?
        $edi->addChild('SERIAL', $date->format('YmdHis'));

        $sapCodeWorkCenter = '';
        $workCenterId = $confirmation->getPlainVarValue('confirm_work_center');
        if ($workCenterId) {
            $nom = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $workCenterId)));
            if ($nom) {
                $sapCodeWorkCenter = $nom->getVarValue('sap_code');
            }
        }
        $sapCodeActivity = '';
        $activityTypeId = $confirmation->getPlainVarValue('confirm_activity_type');
        if ($activityTypeId) {
            $nom = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $activityTypeId)));
            if ($nom) {
                $sapCodeActivity = $nom->getVarValue('sap_code');
            }
        }

        $confirmationAssocVars = $confirmation->getAssocVars();

        $e1conf5Segments = array();
        $confirmWorkHours = $confirmationAssocVars['confirm_work']['value'];
        $measureUnitsLearrWork = $confirmationAssocVars['learr_work']['value'];
        if ($confirmWorkHours) {
            foreach($confirmWorkHours as $idx => $confirmWorkHour) {
                //report by hours
                $e1conf5Segments[] = array(
                    'ISMNW' => $confirmWorkHour,
                    'ISMNE' => $this->settings['ismne_work'] ?? '',
                    'LEARR' => $measureUnitsLearrWork[$idx] ?? '',
                );
            }
        }

        $confirmDistance = $confirmation->getVarValue('confirm_distance_traveled');
        if ($confirmDistance) {
            //report by hours
            $e1conf5Segments[] = array(
                'ISMNW' => $confirmDistance,
                'ISMNE' => $this->settings['ismne_distance_traveled'] ?? '',
                'LEARR' => $confirmation->getPlainVarValue('learr_distance_traveled'),
            );
        }
        if (empty($e1conf5Segments)) {
            $e1conf5Segments[] = array(
                'ISMNW' => '',
                'ISMNE' => '',
                'LEARR' => '',
            );
        }
        $confirmStartDate = new DateTime($confirmation->getVarValue('confirm_start_date'));
        $confirmEndDate = new DateTime($confirmation->getVarValue('confirm_start_date'));

        foreach($e1conf5Segments as $idx => $e1conf5Segment) {
            $e1conf5 = $iDoc->addChild('E1CONF5');
            $e1conf5->addAttribute('SEGMENT', 1);

            $e1conf5->addChild('SATZA', 'I20');
            $e1conf5->addChild('LDATE', $confirmStartDate->format('Ymd'));
            $e1conf5->addChild('LTIME');
            $e1conf5->addChild('ERDAT', $confirmEndDate->format('Ymd'));
            $e1conf5->addChild('ERTIM');
            $e1conf5->addChild('BUDAT', $date->format('Ymd'));
            $e1conf5->addChild('ARBPL', $sapCodeWorkCenter);
            $e1conf5->addChild('WERKS', '1000');
            $e1conf5->addChild('AUFNR', $confirmation->getVarValue('service_order_num'));
            $e1conf5->addChild('VORNR', '010');
            $e1conf5->addChild('LTXA1', $confirmation->getPlainVarValue('confirm_description'));
            $e1conf5->addChild('ISMNW', $e1conf5Segment['ISMNW']);
            $e1conf5->addChild('ISMNE', $e1conf5Segment['ISMNE']);
            $e1conf5->addChild('LEARR', $e1conf5Segment['LEARR']);
        }

        //header('Content-type: application/xml');
        //echo $xml->asXML();exit;

        return $xml->asXML();
    }

    /**
     *
     * Automation to read SAP iDOC files and import customers
     */
    public function sap2NzoomCustomerSync($params) {
        $this->stats = array(
            'added' => array(),
            'modified' => array(),
            'failed' => array(),
        );
        $path = realpath($this->settings['sap_path']);

        if (empty($this->settings['sap_path'])) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_defined'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        } elseif (!file_exists($path) || !is_dir($path) || !is_writable($path)) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_writable'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        //load customer i18n files
        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers.ini');

        //get the files from the shared path
        $sapFiles = $this->_getSapFiles($path, 'DEBMAS_');
        $this->registry->set('dont_check_vat_num', true, true);
        foreach($sapFiles as $sapFile) {
            $result = $this->processCustomerFromSapFile("{$path}/{$sapFile}");
            $this->logiDocSap('debmas', $this->customer, "{$path}/{$sapFile}", 'incoming');
            $this->archiveSapFile("{$path}/{$sapFile}");
            $this->updateAutomationHistory($params, $this->customer, $result);
        }
        $this->registry->remove('dont_check_vat_num');

        //trace($this->stats);
        //trace($this->executionErrors);
        if (!empty($this->settings['log_stats'])) {
            $log_message = "STATS:\n " . json_encode($this->stats, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
        }

        if (!empty($this->executionErrors)) {
            $log_message = "ERRORS:\n " . implode("\n", $this->executionErrors);
            General::log($this->registry, __METHOD__, $log_message);
        }

        return true;
    }

    /**
     *
     * Automation to read SAP iDOC files and import materials
     */
    public function sap2NzoomMaterialSync($params) {
        $this->stats = array(
            'added' => array(),
            'modified' => array(),
            'failed' => array(),
            'skipped' => array(),
        );
        $path = realpath($this->settings['sap_path']);

        if (empty($this->settings['sap_path'])) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_defined'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        } elseif (!file_exists($path) || !is_dir($path) || !is_writable($path)) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_writable'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        //load nomenclature i18n files
        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'nomenclatures/i18n/' . $this->registry['lang'] . '/nomenclatures.ini');

        //get the files from the shared path
        $sapFiles = $this->_getSapFiles($path, 'MATMAS_');
        foreach($sapFiles as $sapFile) {
            $result = $this->processMaterialFromSapFile("{$path}/{$sapFile}");
            $this->logiDocSap('matmas', $this->nomenclature, "{$path}/{$sapFile}", 'incoming');
            $this->archiveSapFile("{$path}/{$sapFile}");
            $this->updateAutomationHistory($params, $this->nomenclature, $result);
        }

        //trace($this->stats);
        if (!empty($this->settings['log_stats'])) {
            $log_message = "STATS:\n " . json_encode($this->stats, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
        }

        if (!empty($this->executionErrors)) {
            $log_message = "ERRORS:\n " . implode("\n", $this->executionErrors);
            General::log($this->registry, __METHOD__, $log_message);
        }

        return true;
    }

    /**
     *
     * Automation to read SAP iDOC files and import deliveries
     */
    public function sap2NzoomDeliverySync($params) {
        $this->stats = array(
            'added' => array(),
            'modified' => array(),
            'warning' => array(),
            'failed' => array(),
        );
        $path = realpath($this->settings['sap_path']);

        if (empty($this->settings['sap_path'])) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_defined'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        } elseif (!file_exists($path) || !is_dir($path) || !is_writable($path)) {
            $this->registry['messages']->setError($this->i18n('error_sap_path_not_writable'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        //load document i18n files
        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');

        //get the files from the shared path
        $sapFiles = $this->_getSapFiles($path, 'DESADV_');
        foreach($sapFiles as $sapFile) {
            $result = $this->processDeliveryFromSapFile("{$path}/{$sapFile}");
            $this->logiDocSap('desadv', $this->document ?? null, "{$path}/{$sapFile}", 'incoming');
            $this->archiveSapFile("{$path}/{$sapFile}");
            $this->updateAutomationHistory($params, $this->document ?? null, $result);
        }

        //trace($this->stats);
        //trace($this->executionErrors);
        //trace($this->executionWarnings);
        if (!empty($this->settings['log_stats'])) {
            $log_message = "STATS:\n " . json_encode($this->stats, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
        }

        if (!empty($this->executionErrors)) {
            $log_message = "ERRORS:\n " . implode("\n", $this->executionErrors);
            General::log($this->registry, __METHOD__, $log_message);
        }

        if (!empty($this->executionWarnings)) {
            $log_message = "WARNINGS:\n " . implode("\n", $this->executionWarnings);
            General::log($this->registry, __METHOD__, $log_message);
        }

        return true;
    }


    /**
     *
     * Automation to verify SAP handed quantity with all Sales documents
     */
    public function checkSapDocumentHandedQuantity($params)
    {
        $tagId_quantitiesDontMatch = $this->settings['document_tag_id'];

        $model = $params['model'];
        $filters = array('where'      => array('d.id="' . $model->get('id') . '"'),
            'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $filters);

        $document->getModelTags();
        $tags = $document->get('tags')?:[];

        // If the document is already tagged, do nothing
        if (in_array($tagId_quantitiesDontMatch, $tags)) {
            return true;
        }

        // If quantities match - nothing to do
        if ($this-> checkActualQuantitiesForSapSale($model->get('id'))) {
            $this->updateAutomationHistory($params, null, 1);
            return true;
        }

        // If quantities don't match - tag the document

        $old_document = clone $document;
        $old_document->getModelTagsForAudit();
        $old_document->sanitize();

        $tags[] = $tagId_quantitiesDontMatch;
        $document->set('tags', $tags, true);

        if ($document->updateTags(array('skip_permissions' => true))) {
            // get the updated document
            $filters = array('where' => array('d.id="' . $document->get('id') . '"'),
                'model_lang' => $this->registry['lang']);
            $document = Documents::searchOne($this->registry, $filters);
            $document->getModelTagsForAudit();
            $document->sanitize();
            Documents_History::saveData($this->registry, array('model' => $document, 'action_type' => 'tag', 'new_model' => $document, 'old_model' => $old_document));
        }

        $this->registry['messages']->setWarning(sprintf($this->i18n('sap_doc_incorrect_quantities'), $document->getAdditionalVarValue('sap_code')));
        $this->registry['messages']->insertInSession($this->registry);
        return true;
    }

    private function getSapSaleHandedQuantitiesByMachineId (int $sapSaleId) : array
    {
        /** @var ADODB_pdo_mysql $db */
        $db = $this->registry['db'];
        $sapSaleType = self::DOCUMENT_TYPE_SAP_SALE;

        $sql = <<<SQL
            SELECT dc_sap_article_id.value as machineId, dc_sap_handed_quantity.value as quantity
                FROM documents d
                    JOIN documents_cstm dc_sap_article_id      ON dc_sap_article_id.model_id = d.id AND dc_sap_article_id.`var_id` = 605  AND dc_sap_article_id.lang = ''
                    JOIN documents_cstm dc_sap_handed_quantity ON dc_sap_handed_quantity.model_id = dc_sap_article_id.model_id AND dc_sap_handed_quantity.`var_id` = 608 AND dc_sap_article_id.num = dc_sap_handed_quantity.num  AND dc_sap_handed_quantity.lang = ''
                WHERE d.type={$sapSaleType} AND d.id={$sapSaleId}
                GROUP BY dc_sap_article_id.value;
            SQL;

        return $db->getAssoc($sql,false,false,true);
    }

    private function getStoreSalesQuantitiesByMachineId (int $sapSaleId) : array
    {
        /** @var ADODB_pdo_mysql $db */
        $db = $this->registry['db'];
        $storeSaleType = self::DOCUMENT_TYPE_STORE_SALE;

        $sql = <<<SQL
            SELECT dc_type_engine_id.`value` as machineId, count(dc_type_engine_id.`value`) as quantity
                FROM documents d
                     JOIN documents_cstm dc_protocol_id ON dc_protocol_id.`model_id` = d.id AND dc_protocol_id.`var_id` = 119 AND dc_protocol_id.`lang` = '' AND dc_protocol_id.`num` = 1
                     JOIN documents_cstm dc_type_engine_id ON dc_type_engine_id.`model_id` = d.id AND dc_type_engine_id.`var_id` = 110  AND dc_type_engine_id.`lang` = ''
                WHERE d.`type`={$storeSaleType} AND dc_protocol_id.`value`={$sapSaleId}
                GROUP BY dc_type_engine_id.`value`;
            SQL;

        return $db->getAssoc($sql,false,false,true);
    }

    /**
     * Checks quantities from the Sap Sale and from storage sales and returns true if they match
     * @param int $sapSaleId
     * @return bool
     */
    private function checkActualQuantitiesForSapSale(int $sapSaleId) : bool
    {
        $handedQuantities = $this->getSapSaleHandedQuantitiesByMachineId($sapSaleId);
        $actualQuantities = $this->getStoreSalesQuantitiesByMachineId($sapSaleId);

        foreach ($handedQuantities as $machineId => $hq) {
            if ((int)$hq !== (int)($actualQuantities[$machineId]??0)) {
                return false;
            }
        }

        return true;
    }

    /** Archive the XML file
     * @param $filePath
     */
    private function archiveSapFile($filePath) {
        unlink($filePath);
    }

    /** Adds or edits customer from XML file
     *
     * @param $filePath - xml file path
     * @return bool
     */
    private function processCustomerFromSapFile($filePath) {

        $xmlStr = file_get_contents($filePath);

        $xml = new SimpleXMLElement($xmlStr);
        $baseData = $xml->IDOC->E1KNA1M;
        $contactPersonData = $xml->IDOC->E1KNA1M->E1KNVKM;
        $sapCode = (string) $baseData->KUNNR;

        $id = (string) $xml->IDOC->E1KNA1M->BAHNS;

        $this->customer = null;

        if ($id) {
            $filters = array('where' => array('c.id = ' . $id));
            $customer = Customers::searchOne($this->registry, $filters);
            if (!$customer) {
                $this->executionErrors[] = "Cannot find customer with id: {$id} while trying to sync from " . basename($filePath);
                return false;
            }
        } elseif ($sapCode) {
            //check if there is a customer with such sap_code
            $action = $this->registry->get('action');
            $this->registry->set('action', 'search', true);
            $filters = array(
                'where' => array(
                    'c.active = 1',
                    'c.type = 2',
                    'a__sap_code = "' . General::slashesEscape($sapCode) . '"'
                )
            );
            $customers = Customers::search($this->registry, $filters);
            $this->registry->set('action', $action, true);
            if ($customers) {
                $customer = $customers[0];
                //customer found
                $id = $customer->get('id');
            }
        }
        if (empty($customer)) {
            //no customer found, add it
            $customer = new Customer($this->registry, array(
                'type' => 2,
                //ToDo: define whether the customer is added as company ot person
                'is_company' => 1
            ));
        }

        $assocVars = $customer->getAssocVars();
        $old_customer = clone $customer;

        $customer->set('name', (string) $baseData->NAME1, true);
        $customer->set('company_name', (string) $baseData->NAME2, true);
        $customer->set('address', (string) $baseData->STRAS, true);
        $customer->set('postal_code', (string) $baseData->PSTLZ, true);
        $customer->set('city', (string) $baseData->ORT01, true);
        $customer->set('eik', (string) $baseData->STCD1, true);
        $customer->set('in_dds', (string) $baseData->STCEG, true);

        $phones = array();
        if (!empty($baseData->TELF1)) {
            $phones[] = (string) $baseData->TELF1;
        }
        if (!empty($baseData->TELF2)) {
            $phones[] = (string) $baseData->TELF2;
        }
        if (!empty($baseData->TELF3)) {
            $phones[] = (string) $baseData->TELF3;
        }
        $phones = array_filter(array_map('trim', $phones));
        if ($phones) {
            $customer->set('gsm', $phones, true);
        }

        //set region variables
        $regionSapCode = $baseData->REGIO;
        $regionID = '';
        $regionName = '';

        //check if there is a nomenclature with such sap_code
        $action = $this->registry->get('action');
        $this->registry->set('action', 'search', true);
        $filters = array(
            'where' => array(
                'n.active = 1',
                'n.type = 13',
                'a__sap_code = "' . General::slashesEscape($regionSapCode) . '"'
            )
        );
        $regionNom = Nomenclatures::searchOne($this->registry, $filters);
        $this->registry->set('action', $action, true);
        if ($regionNom) {
            $regionID = $regionNom->get('id');
            $regionName = $regionNom->get('name');
            unset($regionNom);
        }

        $assocVars['sap_region']['value'] = $regionSapCode;
        $assocVars['sap_region_id']['value'] = $regionID;
        $assocVars['sap_region_name']['value'] = $regionName;

        $e1kna11 = $baseData->E1KNA11;
        $email = (string) $e1kna11->KNURL;
        $emails = $email_notes = array();;
        if ($customer->get('email')) {
            $emails = $customer->get('email');
            $email_notes = $customer->get('email_note');
        }

        if ($email && !in_array($email, $emails)) {
            //add the email at first place
            array_unshift($emails, $email);
            array_unshift($email_notes, '');
            $customer->set('email', $emails, true);
            $customer->set('email_note', $email_notes, true);
        }

        $assocVars['sap_code']['value'] = $sapCode;
        $assocVars['type_contragent']['value'] = array(1);

        $customer->set('vars', array_values($assocVars), true);
        $customer->set('assoc_vars', $assocVars, true);

        if ($customer->get('is_company') && !empty($contactPersonData)) {
            $mol = '';
            $cP = $contactPersonData[0] ?? null;
            if (!empty($cP) && is_object($cP)) {
                $mol = trim((string)$cP->NAME1 . " " . (string)$cP->NAMEV);
            }
            $customer->set('mol', $mol, true);
        }

        $this->registry['db']->StartTrans();

        if ($customer->save()) {
            $filters = array('where' => array('c.id = ' . $customer->get('id')),
                'model_lang' => $customer->get('model_lang'));
            $new_customer = Customers::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_customer->getVars();

            Customers_History::saveData($this->registry,
                                                        array('model' => $customer,
                                                            'action_type' => ($id) ? 'edit' : 'add',
                                                            'new_model' => $new_customer,
                                                            'old_model' => $old_customer));

            if ($contactPersonData) {
                $filters = array(
                    'where' => array(
                        'c.is_main = 1',
                        'c.parent_customer = ' . $customer->get('id'),
                        'c.subtype = \'branch\''
                    )
                );
                $mainBranch = Customers_Branches::searchOne($this->registry, $filters);
                $branchId = $mainBranch->get('id');

                $contactPersonsAssoc = array();
                if ($id) {
                    $filters = array(
                        'where' => array(
                            'bn.parent_customer = ' . $id,
                            'c.subtype = \'contact\''
                        )
                    );
                    $contactPersons = Customers_Contactpersons::search($this->registry, $filters);
                    foreach ($contactPersons as $contactPerson) {
                        $fullName = trim($contactPerson->get('name') . " " . $contactPerson->get('lastname'));
                        $contactPersonsAssoc[$fullName] = $contactPerson;
                    }
                }

                foreach($contactPersonData as $cP) {
                    $fN = trim((string) $cP->NAME1 . " " . (string) $cP->NAMEV);
                    $financial_person = (string) $cP->PAFKT == 'BG';

                    if (!array_key_exists($fN, $contactPersonsAssoc)) {
                        //add new contact person
                        $oldContactPerson = new Customers_Contactperson($this->registry);
                        $contactPerson = new Customers_Contactperson($this->registry, array(
                            'parent_customer_id' => $customer->get('id'),
                            'parent_branch' => $branchId,
                            'name' => (string) $cP->NAME1,
                            'lastname' => (string) $cP->NAMEV,
                            'financial_person' => $financial_person,
                        ));
                        $contactPerson->save();

                        Customers_History::saveData($this->registry, array(
                            'model'               => $customer,
                            'new_model'           => $contactPerson,
                            'old_model'           => $oldContactPerson,
                            'action_type'         => 'add_contact_person',
                            'contact_person_name' => trim($contactPerson->get('name') . ' ' . $contactPerson->get('lastname')
                        )));

                    } else {
                        $contactPerson = $contactPersonsAssoc[$fN];
                        //change only the financial person flag
                        if ($contactPerson->get('financial_person') != $financial_person) {
                            $oldContactPerson = clone $contactPerson;
                            $contactPerson->set('parent_customer_id', $customer->get('id'), true);
                            $contactPerson->set('parent_branch', $branchId, true);
                            $contactPerson->set('financial_person', $financial_person, true);
                            $contactPerson->unsanitize();
                            $contactPerson->save();

                            Customers_History::saveData($this->registry, array(
                                'model'               => $customer,
                                'new_model'           => $contactPerson,
                                'old_model'           => $oldContactPerson,
                                'action_type'         => 'edit_contact_person',
                                'contact_person_name' => trim($contactPerson->get('name') . ' ' . $contactPerson->get('lastname')
                            )));
                        }
                    }
                }
            }
        } else {
            //the validation fails
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        $filename = basename($filePath);
        $name = (string) $baseData->NAME1;

        if ($result) {
            if ($id) {
                $this->stats['modified'][] = "{$name} (ID: {$id}, file: {$filename})";
            } else {
                $id = $customer->get('id');
                $this->stats['added'][] = "{$name} (ID: {$id}, file: {$filename})";
            }
        } else {
            $errors = implode("\n" , $this->registry['messages']->getErrors());
            $this->registry['messages']->flush();

            $this->stats['failed'][] = "{$name} (file: {$filename})" ;
            $this->executionErrors[] = "Failed to save customer \"{$name}\" from file: {$filename}\n{$errors}";
        }

        $this->customer = $customer;

        return $result;
    }

    /** Adds or edits nomenclature from XML file
     *
     * @param $filePath - xml file path
     * @return bool
     */
    private function processMaterialFromSapFile($filePath) {

        $filename = basename($filePath);
        $xmlStr = file_get_contents($filePath);

        $xml = new SimpleXMLElement($xmlStr);
        $baseData = $xml->IDOC->E1MARAM;

        $sapCode = (string) $baseData->MATNR;
        $prefixes = $this->settings['material_code_valid_prefixes'] ?
            implode('|', preg_split('#\s*,\s*#', $this->settings['material_code_valid_prefixes'])):'';
        if (!empty($sapCode) && $prefixes && !preg_match("#^({$prefixes})#", $sapCode)) {
            $this->stats['skipped'][] = "{$sapCode} (file: {$filename})" ;
            return false;
        }

        $this->nomenclature = null;

        //get the bulgarian data
        //get the element E1MAKTM for which the SPRAS_ISO is BG
        $additionalData = $xml->xpath('//E1MAKTM[SPRAS_ISO=\'BG\']')[0] ?? null;
        if (is_null($additionalData) || !is_object($additionalData)) {
            $this->stats['failed'][] = "{$sapCode} (file: {$filename})" ;
            $this->executionErrors[] = "Failed to save nomenclature \"{$sapCode}\" from file: {$filename}\nNo data in Bulgarian for this material!";
            return false;
        }

        //check if there is a nomenclature with such sap_code
        $action = $this->registry->get('action');
        $this->registry->set('action', 'search', true);
        $filters = array(
            'where' => array(
                'n.active = 1',
                'n.type = 10',
                'a__sap_code = "' . General::slashesEscape($sapCode) . '"'
            )
        );
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        $this->registry->set('action', $action, true);

        if (empty($nomenclature)) {
            //no nomenclature found, add it
            $nomenclature = new Nomenclature($this->registry, array(
                'type' => 10,
            ));
        }
        $id = $nomenclature->get('id');

        $assocVars = $nomenclature->getAssocVars();
        $old_nomenclature = clone $nomenclature;

        $nomenclature->set('code', (string) $baseData->MFRPN, true);
        $nomenclature->set('name', (string) $additionalData->MAKTX, true);

        $assocVars['sap_code']['value'] = $sapCode;
        $assocVars['gross_weight']['value'] = (string) $baseData->BRGEW;
        $assocVars['net_weight']['value'] = (string) $baseData->NTGEW;
        $assocVars['mesure_weight']['value'] = (string) $baseData->GEWEI;
        $assocVars['volume']['value'] = (string) $baseData->VOLUM;
        $assocVars['mesure_volume']['value'] = (string) $baseData->VOLEH;
        $assocVars['measure']['value'] = $this->convertMeasureSap2Nz((string) $baseData->MEINS);

        $nomenclature->set('vars', array_values($assocVars), true);
        $nomenclature->set('assoc_vars', $assocVars, true);

        $this->registry['db']->StartTrans();

        if ($nomenclature->save()) {
            $filters = array('where' => array('n.id = ' . $nomenclature->get('id')),
                'model_lang' => $nomenclature->get('model_lang'));
            $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_nomenclature->getVars();

            Nomenclatures_History::saveData($this->registry,
                                        array('model' => $nomenclature,
                                            'action_type' => ($id) ? 'edit' : 'add',
                                            'new_model' => $new_nomenclature,
                                            'old_model' => $old_nomenclature));
        } else {
            //the validation fails
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        $filename = basename($filePath);

        if ($result) {
            if ($id) {
                $this->stats['modified'][] = "{$sapCode} (ID: {$id}, file: {$filename})";
            } else {
                $id = $new_nomenclature->get('id');
                $this->stats['added'][] = "{$sapCode} (ID: {$id}, file: {$filename})";
            }
        } else {
            $errors = implode("\n" , $this->registry['messages']->getErrors());
            $this->registry['messages']->flush();

            $this->stats['failed'][] = "{$sapCode} (file: {$filename})" ;
            $this->executionErrors[] = "Failed to save nomenclature \"{$sapCode}\" from file: {$filename}\n{$errors}";
        }

        $this->nomenclature = $nomenclature;

        return $result;
    }

    /** Adds or edits document from XML file
     *
     * @param $filePath - xml file path
     * @return bool
     */
    private function processDeliveryFromSapFile($filePath) {

        $xmlStr = file_get_contents($filePath);

        $xml = new SimpleXMLElement($xmlStr);
        $baseData = $xml->IDOC->E1EDL20;
        $additionalData = $baseData->E1ADRM1;
        $tableData = $baseData->E1EDL24;
        $sapCode = (string) $baseData->VBELN;

        $filename = basename($filePath);

        $this->document = null;

        //check if there is a document with such sap_code
        $action = $this->registry->get('action');
        $this->registry->set('action', 'search', true);
        $filters = array(
            'where' => array(
                'd.active = 1',
                'd.type = 6',
                'a__sap_code = "' . General::slashesEscape($sapCode) . '"'
            )
        );
        $documents = Documents::search($this->registry, $filters);

        //search customer by SAP code
        $customerSapCode = (string) $additionalData->PARTNER_ID;
        $checkCode = ltrim($customerSapCode, '0');
        $filters = array(
            'where' => array(
                'c.active = 1',
                'c.type = 2',
                'a__sap_code REGEXP "^0*' . General::slashesEscape($checkCode) . '$"'
            )
        );
        $customers = Customers::search($this->registry, $filters);
        $this->registry->set('action', $action, true);

        if (empty($customers)) {
            //no customer
            $this->stats['failed'][] = "{$sapCode} (file: {$filename})" ;
            $this->executionErrors[] = "Failed to save document \"{$sapCode}\" from file: {$filename}\nCustomer with sap_code {$customerSapCode} not found!";
            return false;
        } else {
            $customer = $customers[0];
        }

        if (empty($documents)) {
            //no document found, add it
            $document = new Document($this->registry, array(
                'type' => 6,
            ));
        } else {
            $document = $documents[0]->unsanitize();
            //IMPORTANT: Don't edit documents that have already been processed (substatus changed)
            if ($document->get('substatus') != 0) {
                $this->stats['warning'][] = "{$sapCode} (file: {$filename})";
                $this->executionWarnings[] = "Skipped document \"{$sapCode}\" from file: {$filename}\nThe document (id: {$document->get('id')}) has already been added and processed in nZoom!";
                return false;
            }
        }
        $id = $document->get('id');

//        $filters = array(
//            'where' => array(
//                'n.active = 1',
//                'n.id = ' . $this->settings['nom_unknown_id']
//            )
//        );
        $this->registry->set('action', 'search', true);
        /**
         * @var $nomUknown Nomenclature
         */
//        $nomUnknown = Nomenclatures::searchOne($this->registry, $filters);

        $filters = array(
            'where' => array(
                'dt.id = 6',
                'dt.active = 1'
            ),
            'sanitize' => true
        );
        $docType = Documents_Types::searchOne($this->registry, $filters);

        $assocVars = $document->getAssocVars();
        $old_document = clone $document;

        $document->set('name', $docType->get('name'), true);
        $document->set('customer', $customer->get('id'), true);
        $document->set('group', $docType->getDefaultGroup(), true);
        $document->set('department', $docType->getDefaultDepartment(), true);

        $dateSap = (string) $baseData->PODAT;
        $date = substr($dateSap, 0, 4) . '-' .
                substr($dateSap, 4, 2) . '-' .
                substr($dateSap, 6, 2);
        $document->set('date', $date, true);

        $assocVars['sap_code']['value'] = $sapCode;
        $assocVars['expedition']['value'] = (string) $baseData->VSTEL;

        //IMPORTANT: initialize the table vars as they may be changed
        //the import will create them from scratch
        //grouing 7: sap_article_id, sap_article_name, sap_article_quantity,
        // sap_remaining_quantity, sap_handed_quantity, sap_article_measure, etc.
        foreach($assocVars as $varName => $var) {
            $groupNum = $var['grouping'] ?? '';
            if ($groupNum == 7) {
                $assocVars[$varName]['value'] = array();
            }
        }

        $rowNum = 0;
        foreach($tableData as $idx => $row) {
            $position = (string) $row->POSNR;
            if (substr($position, 0, 1) != '9') {
                continue;
            }
            //remove the leading zeros from customer code
            $nomSapCode = preg_replace("#^0*#", "", (string) $row->MATNR);

            //check if the type machine is available
            $filters = array(
                'where' => array(
                    'n.active = 1',
                    'n.type = 5',
                    'a__sap_code LIKE "%' . General::slashesEscape($nomSapCode) . '%"'
                )
            );
            $this->registry->set('action', 'search', true);
            $nomenclatures = Nomenclatures::search($this->registry, $filters);
            $this->registry->set('action', $action, true);
            if (empty($nomenclatures)) {
                //no such nomenclature, but just warn, do not stop the iteration
                //$nomenclatures = [$nomUnknown];
                if (preg_match('#^1#', $nomSapCode)) {
                    //Report warning only if the nomenclature sapCode begins with "1" sa per Bug 5531, comment 17
                    $this->stats['warning'][] = "{$sapCode} (file: {$filename})";
                    $this->executionWarnings[] = "Type machine with sap_code {$nomSapCode} not found!";
                }
            }

            foreach($nomenclatures as $nomenclature) {
                $nomId = $nomenclature->get('id');
                $quantity = (float)$row->LFIMG;

                //check if the nomenclature has already been placed
                if (in_array($nomId, $assocVars['sap_article_id']['value'])) {
                    $rNum = array_search($nomId, $assocVars['sap_article_id']['value'], true);
                    if ($rNum !== false) {
                        //it is already in: sum up the quantity
                        $assocVars['sap_article_quantity']['value'][$rNum] += $quantity;
                        $assocVars['sap_remaining_quantity']['value'][$rNum] += $quantity;
                        continue;
                    }
                }

                //IMPORTANT: group tables start numbering from 1 (not 0)
                $rowNum++;

                $assocVars['sap_number']['value'][$rowNum] = $nomSapCode;
                $assocVars['sap_article_id']['value'][$rowNum] = $nomId;
                $assocVars['sap_article_name']['value'][$rowNum] = $nomenclature->get('name');
                $assocVars['sap_article_quantity']['value'][$rowNum] = $quantity;
                $assocVars['sap_remaining_quantity']['value'][$rowNum] = $quantity;
                $assocVars['sap_handed_quantity']['value'][$rowNum] = 0;
                $assocVars['sap_article_measure']['value'][$rowNum] = $this->convertMeasureSap2Nz(
                    (string)$row->VRKME
                );

            }
        }

        if (empty($assocVars['sap_number']['value'])) {
            $this->stats['failed'][] = "{$sapCode} (file: {$filename})" ;
            $this->executionErrors[] = "Failed to save document \"{$sapCode}\" from file: {$filename} (No machines found!)";
            return true;
        }

        $document->set('vars', array_values($assocVars), true);
        $document->set('assoc_vars', $assocVars, true);

        $this->registry['db']->StartTrans();

        if ($document->save()) {
            $filters = array('where' => array('d.id = ' . $document->get('id')),
                'model_lang' => $document->get('model_lang'));
            $new_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();

            Documents_History::saveData($this->registry,
                                            array('model' => $document,
                                                'action_type' => ($id) ? 'edit' : 'add',
                                                'new_model' => $new_document,
                                                'old_model' => $old_document));
        } else {
            //the validation fails
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        if ($result) {
            if ($id) {
                $this->stats['modified'][] = "{$sapCode} (ID: {$id}, file: {$filename})";
            } else {
                $id = $new_document->get('id');
                $this->stats['added'][] = "{$sapCode} (ID: {$id}, file: {$filename})";
            }
        } else {
            $errors = implode("\n" , $this->registry['messages']->getErrors());
            $this->registry['messages']->flush();

            $this->stats['failed'][] = "{$sapCode} (file: {$filename})" ;
            $this->executionErrors[] = "Failed to save document \"{$sapCode}\" from file: {$filename}\n{$errors}";
        }

        $this->document = $document;

        return $result;
    }

    /**
     * Gets files that are not processed
     *
     * @param $path
     * @param $prefix
     * @return array $files
     */
    private function _getSapFiles($path, $prefix) {
        //get the files from the shared path
        $files = array_map('basename', glob("{$path}/{$prefix}*.xml"));
        sort($files);

        //exclude already processed files
        $filesCSV = '"' . implode('", "', $files) . '"';
        switch($prefix) {
            case 'DEBMAS_':
                $table = 'sap_debmas';
                break;
            case 'MATMAS_':
                $table = 'sap_matmas';
                break;
            case 'DESADV_':
                $table = 'sap_desadv';
                break;
        }
        $query = "SELECT `filename` FROM `{$table}` WHERE filename IN ({$filesCSV}) ORDER BY `filename`";
        $alreadyProcessed = $this->registry['db']->getCol($query);
        if (!empty($alreadyProcessed)) {
            $files = array_diff($files, $alreadyProcessed);
        }

        return $files;
    }

    /**
     * Beautify xml
     *
     * @param $xml
     * @return $beautified false|string
     */
    public function beautifyXML($xml) {
        $dom = new DOMDocument("1.0");
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        $dom->loadXML($xml);
        $beautifiedXml = $dom->saveXML();

        return $beautifiedXml;
    }

    /**
     * Automation to create
     */
    public function createSapDocuments($params) {
        $tagId = $this->settings['document_tag_id'];
        $substatusId = $this->settings['substatus_id'];
        $docTypeId = $params['start_model_type'];
        $docTable = DB_TABLE_DOCUMENTS;
        $docVarsTable = DB_TABLE_DOCUMENTS_CSTM;
        $fieldsTable = DB_TABLE_FIELDS_META;
        $tagsModelsTable = DB_TABLE_TAGS_MODELS;
        $query = "SELECT d.id, d.full_num
                 FROM {$docTable} d
                 JOIN {$fieldsTable} fm1
                   ON d.substatus={$substatusId} AND d.active=1 AND d.deleted=0 AND d.type={$docTypeId} AND d.type=fm1.model_type AND fm1.name='service_order_id'
                 JOIN {$fieldsTable} fm2
                   ON d.substatus={$substatusId} AND d.active=1 AND d.deleted=0 AND d.type={$docTypeId} AND d.type=fm2.model_type AND fm2.name='service_protocol_id'
                 JOIN {$fieldsTable} fm3
                   ON d.substatus={$substatusId} AND d.active=1 AND d.deleted=0 AND d.type={$docTypeId} AND d.type=fm3.model_type AND fm3.name='part_id'
                 JOIN {$docVarsTable} dc3
                   ON d.id=dc3.model_id AND fm3.id=dc3.var_id AND dc3.num=1 AND dc3.value!=''
                 LEFT JOIN {$docVarsTable} dc1
                   ON d.id=dc1.model_id AND fm1.id=dc1.var_id AND dc1.num=1 AND dc1.lang=''
                 LEFT JOIN {$docVarsTable} dc2
                   ON d.id=dc2.model_id AND fm2.id=dc2.var_id AND dc2.num=1 AND dc2.lang=''
                 LEFT JOIN {$tagsModelsTable} tm
                   ON d.id=tm.model_id AND tm.model='Document' AND tm.tag_id={$tagId}
                 WHERE d.type={$docTypeId}
                     AND d.substatus={$substatusId}
                     AND (dc1.value IS NULL or dc1.value='')
                     AND (dc2.value IS NULL or dc2.value='')
                     AND tm.tag_id IS NULL";
        $docData = $this->registry['db']->getAssoc($query);
        if (empty($docData)) {
            return true;
        }

        $this->registry['db']->StartTrans();

        try {
            $serviceOrder = $this->addServiceOrder($docData);
            $visitProtocol = $this->addVisitProtocol($docData, $serviceOrder);
            $this->updateOrders($params, $docData, $serviceOrder, $visitProtocol);
        } catch (\Exception $e){
            $this->registry['db']->FailTrans();
            $this->executionErrors[] = $e->getMessage();
        }

        $result = !$this->registry['db']->HasFailedTrans();

        $this->registry['db']->CompleteTrans();

        return $result;
    }

/**
     * @param array $docData
     * @return Document
     * @throws Exception
     */
    public function addServiceOrder(array $docData): Document
    {
        $filters = array(
            'where' => array(
                'c.id = ' . $this->settings['default_customer_id'],
            ),
            'sanitize' => true
        );
        $defaultCustomer = Customers::searchOne($this->registry, $filters);

        //new document
        $document = new Document($this->registry, array(
            'type' => 7,
        ));
        $filters = array(
            'where' => array(
                'dt.id = 7',
                'dt.active = 1'
            ),
        );
        $docType = Documents_Types::searchOne($this->registry, $filters);

        $assocVars = $document->getAssocVars();
        $old_document = clone $document;

        $document->set('date', General::strftime('%Y-%m-%d'), true);
        $document->set('name', $this->settings['default_service_order_name'], true);
        $document->set('customer', $this->settings['default_customer_id'], true);
        $document->set('group', $docType->getDefaultGroup(), true);
        $document->set('department', $docType->getDefaultDepartment(), true);

        $description = sprintf($this->settings['description'], implode(', ', array_values($docData)));
        $document->set('description', $description, true);

        $assocVars['dealer_name']['value'] = $defaultCustomer->get('name');
        $assocVars['dealer_name_id']['value'] = $defaultCustomer->get('id');
        $assocVars['admission_date']['value'] = General::strftime('%Y-%m-%d %H:%M:00');
        $assocVars['planned_start_date']['value'] = General::strftime('%Y-%m-%d %H:%M:00');
        $assocVars['planned_end_date']['value'] = General::strftime('%Y-%m-%d %H:%M:00');
        $assocVars['service_order_type']['value'] = $this->settings['service_order_type'];
        $assocVars['work_center']['value'] = $this->settings['work_center'];
        $assocVars['activity_type']['value'] = $this->settings['activity_type'];

        $document->set('vars', array_values($assocVars), true);
        $document->set('assoc_vars', $assocVars, true);

        if ($document->save()) {
            $filters = array(
                'where' => array('d.id = ' . $document->get('id')),
                'model_lang' => $document->get('model_lang')
            );
            $new_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $document,
                    'action_type' => 'add',
                    'new_model' => $new_document,
                    'old_model' => $old_document
                )
            );
            return $document;
        }

        throw new \Exception('Error saving service order! ' .
             implode('! ', $this->registry['messages']->getErrors())
        );
    }

    /**
     * @param array $params
     * @param array $docData
     * @param Document $serviceOrder
     * @param Document $visitProtocol
     * @throws Exception
     */
    public function updateOrders(array $params, array $docData, Document $serviceOrder, Document $visitProtocol): void
    {

        foreach($docData as $docId => $docFullNum) {

            //new document
            $document = Documents::searchOne($this->registry, array(
                'where' => array(
                    'd.id = ' . $docId,
                ),
            ));

            if (empty($document)) {
                throw new \Exception('Error updating order ' . $docId .
                                     implode('! ', $this->registry['messages']->getErrors()));
            }

            $assocVars = $document->getAssocVars();
            $old_document = clone $document;

            $assocVars['service_order_id']['value'] = $serviceOrder->get('id');
            $assocVars['service_order_name']['value'] = $serviceOrder->get('full_num');

            $assocVars['service_protocol_id']['value'] = $visitProtocol->get('id');
            $assocVars['service_protocol_name']['value'] = $visitProtocol->get('full_num');

            $document->set('vars', array_values($assocVars), true);
            $document->set('assoc_vars', $assocVars, true);

            if ($document->save()) {
                $filters = array(
                    'where' => array('d.id = ' . $document->get('id')),
                    'model_lang' => $document->get('model_lang')
                );
                $new_document = Documents::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_document->getVars();

                Documents_History::saveData(
                    $this->registry,
                    array(
                        'model' => $document,
                        'action_type' => 'edit',
                        'new_model' => $new_document,
                        'old_model' => $old_document
                    )
                );

                //add the tag
                $old_document = clone $new_document;
                $old_document->getModelTagsForAudit();

                $new_document->unsanitize();
                // call addTags method directly (skip validation)
                $result = $new_document->addTags(array($this->settings['document_tag_id']));
                if ($result) {
                    // get all new tags for audit
                    $new_document->getModelTagsForAudit();

                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $new_document,
                            'action_type' => 'tag',
                            'new_model' => $new_document,
                            'old_model' => $old_document
                        )
                    );
                } else {
                    throw new \Exception('Error updating tagging ' . $docId .
                                         implode('! ', $this->registry['messages']->getErrors()));
                }

                $this->updateAutomationHistory($params, $document, 1);
            } else {
                throw new \Exception('Error updating order ' . $docId  .
                                     implode('! ', $this->registry['messages']->getErrors()));
            }
        }
    }

    /**
     * @param array $docData
     * @return Document
     * @throws Exception
     */
    public function addVisitProtocol(array $docData, Document $serviceOrder): Document
    {
        $docTypeId = 5;
        $nomTypeId = 10;
        $substatusId = $this->settings['substatus_id'];
        $docTable = DB_TABLE_DOCUMENTS;
        $docVarsTable = DB_TABLE_DOCUMENTS_CSTM;
        $nomVarsTable = DB_TABLE_NOMENCLATURES_CSTM;
        $fieldsTable = DB_TABLE_FIELDS_META;
        $docIdsCSV = implode(',', array_keys($docData));
        //get all documents data
        $query = "SELECT dc1.value as part_id, dc2.value as part_name, nc1.value as part_sap_code, count(dc1.value) as part_count
                 FROM {$docTable} d
                 JOIN {$fieldsTable} fm1
                   ON d.substatus={$substatusId} AND d.active=1 AND d.deleted=0 AND fm1.model_type={$docTypeId} AND fm1.model='Document' AND fm1.name='part_id'
                 JOIN {$fieldsTable} fm2
                   ON fm2.model='Document' AND fm2.model_type={$docTypeId} AND fm2.name='part_name'
                 JOIN {$fieldsTable} fm3
                   ON fm3.model='Nomenclature' AND fm3.model_type={$nomTypeId} AND fm3.name='sap_code'
                 JOIN {$docVarsTable} dc1
                   ON d.id=dc1.model_id AND fm1.id=dc1.var_id AND dc1.lang='' AND dc1.value!=''
                 JOIN {$docVarsTable} dc2
                   ON d.id=dc2.model_id AND fm2.id=dc2.var_id AND dc1.num=dc2.num AND dc2.lang=''
                 LEFT JOIN {$nomVarsTable} nc1
                   ON dc1.value=nc1.model_id AND fm3.id=nc1.var_id AND nc1.num=1
                 WHERE d.id IN ({$docIdsCSV})
                 GROUP by dc1.value";
        $partsData = $this->registry['db']->getAll($query);

        //new document
        $document = new Document($this->registry, array(
            'type' => 8,
        ));
        $filters = array(
            'where' => array(
                'dt.id = 8',
                'dt.active = 1'
            ),
            'sanitize' => true
        );
        $docType = Documents_Types::searchOne($this->registry, $filters);

        $assocVars = $document->getAssocVars();
        $old_document = clone $document;

        $document->set('date', General::strftime('%Y-%m-%d'), true);
        $document->set('name', $this->settings['default_visit_protocol_name'], true);
        $document->set('customer', $this->settings['default_customer_id'], true);
        $document->set('group', $docType->getDefaultGroup(), true);
        $document->set('department', $docType->getDefaultDepartment(), true);

        $description = sprintf($this->settings['description'], implode(', ', array_values($docData)));
        $document->set('description', $description, true);

        $assocVars['service_order_id']['value'] = $serviceOrder->get('id');
        $assocVars['service_order_num']['value'] = $serviceOrder->get('full_num');

        foreach($partsData as $idx => $row) {
            $rIdx = $idx+1;
            $assocVars['material_id']['value'][$rIdx] = $row['part_id'];
            $assocVars['material_name']['value'][$rIdx] = $row['part_name'];
            $assocVars['material_sap']['value'][$rIdx] = $row['part_sap_code'];
            $assocVars['material_quantity']['value'][$rIdx] = $row['part_count'];
        }

        $document->set('vars', array_values($assocVars), true);
        $document->set('assoc_vars', $assocVars, true);

        if ($document->save()) {
            $filters = array('where' => array('d.id = ' . $document->get('id')),
                'model_lang' => $document->get('model_lang'));
            $new_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();

            Documents_History::saveData($this->registry,
                                        array('model' => $document,
                                              'action_type' => 'add',
                                              'new_model' => $new_document,
                                              'old_model' => $old_document));

            //create link between the visit protocol and the service order
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_DOCUMENTS_RELATIVES . ' SET' . "\n" .
                     'parent_id = ' . $document->get('id') . ",\n" .
                     'link_to = ' . $serviceOrder->get('id') . ",\n" .
                     'link_to_model_name = "Document"' . ",\n" .
                     'origin = "inherited"';
            $this->registry['db']->Execute($query);

            return $document;
        }

        throw new \Exception('Error saving visit protocol' .
                             implode('! ', $this->registry['messages']->getErrors()));

    }
}

?>
