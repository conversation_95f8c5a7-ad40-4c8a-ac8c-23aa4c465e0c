<?php

trait syncFileServer
{
    private $fileErrors = [];
    private $modelsFiles = [];
    private $filesTypes = [];
    private $searchedPathsList = [
        'archive'      => PH_FILES_DIR . 'remote/архив/',
        'archive_data' => PH_FILES_DIR . 'remote/Архив-данни за АС/',
        'invoices'     => PH_FILES_DIR . 'remote/Fakturi/',
    ];
    private $automationParams = [];
    private $stationsDataPrepared = false;
    private $stationsData = [];

    /**
     * Method that will sync the files from the VEOLIA server with the currently recorded files in nZoom
     */
    public function syncFiles($params)
    {
        set_time_limit(0);
        ini_set('memory_limit', '4G');
        $execution_time = date('Y-m-d H:i:s');
        $this->automationParams = $params;

        $server_files = array_fill_keys(array_keys($this->searchedPathsList), array());
        $nzoom_files = [];
        $all_server_files = [];
        $time_match = '';
        if ($this->settings['use_time_matching']) {
            $time_match = $this->getPreviousAutomationTime();
        } else {
            $nzoom_files = $this->getExistingNzoomFiles();
        }

        foreach ($this->searchedPathsList as $key => $path) {
            $paths_to_read = array($path);
            if ($key == 'invoices') {
                $paths_to_read = array();
                $year = date('Y');
                for ($yr = $year; $yr >= ($year - 5); $yr--) {
                    $paths_to_read[] = $path . $yr . DIRECTORY_SEPARATOR;
                }
            }

            $tmp = array();
            foreach ($paths_to_read as $pth) {
                $tmp[] = $this->getServerFiles($pth, $time_match, boolval($this->settings['use_console_search']));
            }
            $server_files[$key] = array_merge([], ...$tmp);
        }
        $all_server_files = array_merge([], ...array_values($server_files));
        $server_files = array_filter($server_files);

        $this->processArchiveFiles(array_diff($server_files['archive']??[], $nzoom_files));
        $this->processArchiveDataFiles(array_diff($server_files['archive_data']??[], $nzoom_files));
        $this->processInvoiceFiles(array_diff($server_files['invoices']??[], $nzoom_files));

        if (!$this->settings['use_time_matching']) {
            $remove_files = array_diff($nzoom_files, $all_server_files);
            $this->processFilesForRemoving($remove_files);
        }
        $this->getFilesTypes();

        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('edit_all', true, true);
        foreach ($this->modelsFiles as $module => $models_included) {
            foreach ($models_included as $model_id => $model_files) {
                $this->registry['db']->StartTrans();
                $this->manageFilesToModel($module, $model_id, $model_files);
                $res = !$this->registry['db']->HasFailedTrans();
                $this->registry['db']->CompleteTrans();
                if (!$res) {
                    $this->executionErrors[] = "Failed on sync of {$model_id}!";
                    break;
                }
            }
        }

        $this->registry->set('edit_all', $edit_all, true);
        $this->processErrorMessages();
        $this->automationParams['log_date'] = $execution_time;
        $this->updateAutomationHistory($this->automationParams, 0, true);

        return true;
    }

    /**
     * Get full list of the existing nZoom files
     *
     * @return array
     */
    private function getExistingNzoomFiles() : array
    {
        $sql = 'SELECT `path` FROM ' . DB_TABLE_FILES . ' WHERE `deleted_by`=0';
        $paths_clauses = [];
        foreach ($this->searchedPathsList as $search_path) {
            $paths_clauses[] = '`path` LIKE "%' . $search_path . '%"';
        }
        if (!empty($paths_clauses)) {
            $sql .= ' AND (' . implode(' OR ', $paths_clauses) . ')';
        }
        $files_list = $this->registry['db']->GetCol($sql);
        return $files_list;
    }

    /**
     * Function to get the server files list
     *
     * @param string $path - the path which we will take the files from using recursive walk
     * @param string $time_match - time string to match the time with time of the files
     * @param $use_console_method - defines if a console method will be used or FilesLib. FilesLib by default
     *
     * @return array
     */
    private function getServerFiles($path, $time_match = '', $use_console_method = false) : array
    {
        if ($use_console_method) {
            $server_files = $this->getServerFilesConsole($path, $time_match);
        } else {
            $server_files = $this->getServerFilesFilesLib($path, $time_match);
        }

        return $server_files;
    }

    /**
     * Get server files with console methods
     *
     * @param string $path - the path which we will take the files from using recursive walk
     * @param string $time_match - time string to match the time with time of the files
     *
     * @return array
     */
    private function getServerFilesConsole($path, $time_match = '') : array
    {
        $files_list = array();
        $os = General::detectOS('server');
        if (preg_match('#win#i', $os)) {
            $command = "PowerShell -Command \"[Console]::OutputEncoding = [System.Text.Encoding]::UTF8; Get-ChildItem '$path' -Recurse -File" . (!empty($time_match) ? " | Where-Object { \$_.LastWriteTime -gt [datetime]::Parse('$time_match') }" : "") . " | Select-Object -ExpandProperty FullName\"";
            $output = shell_exec($command);
            $files_list = array_filter(explode("\n", $output));
        } elseif (preg_match('#linux#i', $os)) {
            $command = "find '$path' -type f" . (!empty($time_match) ? " -newermt '$time_match'" : "");
            $output = shell_exec($command);
            $files_list = array_filter(explode("\n", trim($output)));
        }

        return $files_list;
    }

    /**
     * Get server files with FilesLib methods
     *
     * @param string $path - the path which we will take the files from using recursive walk
     * @param string $time_match - time string to match the time with time of the files
     *
     * @return array
     */
    private function getServerFilesFilesLib($path, $time_match = '') : array
    {
        $files_list = FilesLib::readDir($path, true, 'files_only', '', true);
        if ($time_match) {
            foreach ($files_list as $k => $fl) {
                // Compare the modification time with the current time
                if (filemtime($fl) <= $time_match) {
                    unset($files_list[$k]);
                }
            }
        }

        return array_values($files_list);
    }

    /**
     * @param array $filters
     * @return object of type Customer
     */
    private function getCustomerModel(array $filters): object
    {
        $search_filters = array(
            'model_lang' => $this->registry->get('model_lang'),
            'where' => $filters,
            'sanitize' => true
        );
        return Customers::searchOne($this->registry, $search_filters);
    }

    /**
     * Function to handle attaching and deleting files to a single model
     *
     * @param string $module - the name of the module (lowercase)
     * @param integer $model_id - the id of the model from that module
     * @param array $model_files - list of files to be attached to that model
     *
     * @return void
     */
    private function manageFilesToModel($module, $model_id, $model_files = array()): void
    {
        $factory_class = ucfirst($module);
        $history_class = $factory_class . '_History';
        $model = $factory_class::$modelName;
        $alias = $factory_class::getAlias($factory_class::$modelName, '');

        $filters = array(
            'where' => array($alias . ".id = '{$model_id}'"),
            'model_lang' => $this->registry['lang'],
            'skip_permissions_check' => true,
            'sanitize' => false
        );
        $old_model = $factory_class::searchOne($this->registry, $filters);
        $old_model->getAttachments();

        $new_files = [];
        $removed_files = [];
        // PROCESS ADDED FILES AS ATTACHMENTS
        foreach ($model_files['add'] as $file_path) {
            $path_element = array_reverse(explode(DIRECTORY_SEPARATOR, $file_path));
            $file_name = $path_element[0];

            $file = new File($this->registry, [
                'model'    => $model,
                'model_id' => $old_model->get('id'),
                'filename' => $file_name,
                'name'     => $file_name,
                'path'     => $file_path,
                'origin'   => 'attached',
                'revision' => Files::getLatestRevision($this->registry, [
                    'model_id' => $old_model->get('id'),
                    'filename' => $file_name,
                    'origin'   => 'attached'
                ])
            ]);

            if (!$file->save()) {
                // TODO: not uploaded file handle
                $this->fileErrors[] = sprintf('Attaching file %s failed during file save!', $file_path);
                continue;
            }

            // define the prefix
            preg_match_all('#^([A-Za-z0-9а-яА-Я]+)(\-| ).*#', $file_name, $matches);
            $prefix_file = $matches[1][0] ?? '';
            $new_file = [
                'id'          => $file->get('id'),
                'type'        => '',
                'type_name'   => '',
                'description' => $file_name
            ];
            if (!empty($prefix_file) && array_key_exists($prefix_file, $this->filesTypes)) {
                $new_file['type'] = $this->filesTypes[$prefix_file]['id'];
                $new_file['type_name'] = $this->filesTypes[$prefix_file]['name'];
            }
            $new_files[] = $new_file;
        }

        if (!empty($new_files)) {
            // Write history for the new files attachment
            $new_model = $factory_class::searchOne($this->registry, $filters);
            $new_model->getAttachments();
            $history_class::saveData(
                $this->registry,
                array(
                    'action_type' => 'add_attachments',
                    'old_model'   => $old_model,
                    'model'       => $new_model,
                    'new_model'   => $new_model
                )
            );
            $old_model = clone $new_model;
        }

        // PROCESS THE FILES FOR THE GROUPING TABLE
        // find all the vars in the grouping table
        $assoc_vars = $old_model->getAssocVars();
        $group_index = 0;
        if (isset($assoc_vars[$this->settings['nomenclature_var_file']])) {
            $group_index = $assoc_vars[$this->settings['nomenclature_var_file']]['grouping'];
        }

        $group_vars = [];
        if ($group_index) {
            foreach ($assoc_vars as $var) {
                if ($var['grouping'] == $group_index && $var['type'] != 'group') {
                    $group_vars[] = $var['name'];
                }
            }
        }

        $group_table_files_added = false;
        $group_table_files_removed = false;
        if (!empty($group_vars)) {
            if (!empty($new_files)) {
                // find the last free index
                $last_completed_row = 0;
                foreach ($group_vars as $grp_var) {
                    $values = !empty($assoc_vars[$grp_var]['value']) ? $assoc_vars[$grp_var]['value'] : array();
                    $values = array_filter($values);
                    $reverted_array = array_reverse(array_keys($values));
                    $last_row = reset($reverted_array);
                    if ($last_row !== false && $last_completed_row<$last_row) {
                        $last_completed_row = $last_row;
                    }
                }

                foreach ($group_vars as $grp_var) {
                    foreach ($new_files as $k => $nf) {
                        $new_value = '';
                        switch($grp_var) {
                            case $this->settings['nomenclature_var_file']: $new_value = $nf['id']; break;
                            case $this->settings['nomenclature_var_file_type_id']: $new_value = $nf['type']; break;
                            case $this->settings['nomenclature_var_file_type_name']: $new_value = $nf['type_name']; break;
                            case $this->settings['nomenclature_var_file_desc']: $new_value = $nf['description']; break;
                        }
                        $assoc_vars[$grp_var]['value'][$last_completed_row+$k+1] = $new_value;
                        $group_table_files_added = true;
                    }
                }
            }
            if (!empty($model_files['delete'])) {
                foreach ($assoc_vars[$this->settings['nomenclature_var_file']]['value'] as $row => $file_data) {
                    $current_file_id = $file_data;
                    if (is_object($file_data)) {
                        $current_file_id = $file_data->get('id');
                    }
                    if (!in_array($current_file_id, $model_files['delete'])) {
                        continue;
                    }
                    foreach ($group_vars as $grp_var) {
                        unset($assoc_vars[$grp_var]['value'][$row]);
                        $group_table_files_removed = true;
                        $removed_files[] = $current_file_id;
                    }
                }
            }
        }

        if ($group_table_files_added || $group_table_files_removed) {
            $new_model = clone $old_model;
            $new_model->set('vars', array_values($assoc_vars), true);
            $new_model->set('assoc_vars', $assoc_vars, true);

            if ($new_model->save()) {
                $new_model = $factory_class::searchOne($this->registry, $filters);
                $new_model->getVars();
                $new_model->sanitize();
                $history_class::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'edit',
                        'old_model'   => $old_model,
                        'model'       => $new_model,
                        'new_model'   => $new_model
                    )
                );
            }
            $old_model = clone $new_model;
        }

        if ($removed_files) {
            // write history for removed files
            $old_model->getAttachments();
            Files::delete($this->registry, $removed_files);
            $new_model = $factory_class::searchOne($this->registry, $filters);
            $new_model->getAttachments();
            $history_class::saveData(
                $this->registry,
                array(
                    'action_type' => 'del_attachments',
                    'old_model'   => $old_model,
                    'model'       => $new_model,
                    'new_model'   => $new_model
                )
            );
        }
    }

    /**
     * Process the invoices files
     *
     * @param $invoice_files
     * @return void
     */
    private function processInvoiceFiles($invoice_files): void
    {
        if (empty($invoice_files)) {
            return;
        }

        $invoice_files_lots = [];
        foreach ($invoice_files as $full_file_path) {

            // get only the path below the source path
            $path_cut = preg_replace('#' . General::slashesEscape($this->searchedPathsList['invoices']) . '#', '', $full_file_path);
            $path_elements = explode(DIRECTORY_SEPARATOR, $path_cut);

            // if the first folder below the source folder is not with the name of a year, skip this element
            if (!preg_match('#^[0-9]{4}$#', $path_elements[0])) {
                continue;
            }

            // check if the file is pdf
            $file_name = end($path_elements);
            $file_name_elements = explode('.', $file_name);
            $extension = end($file_name_elements);
            if ($extension != 'pdf') {
                continue;
            }

            // process the file name and find its consisting elements and group them by lot
            unset($file_name_elements[count($file_name_elements) - 1]);
            $name_only = implode('.', $file_name_elements);
            $name_elements = preg_split('#\s*\-\s*#', $name_only);
            $lot_num = reset($name_elements);
            $lot_num = ltrim($lot_num, '0');
            if (!isset($invoice_files_lots[$lot_num])) {
                $invoice_files_lots[$lot_num] = array();
            }
            $invoice_files_lots[$lot_num][] = $full_file_path;
        }

        // search the noms which have the required lots
        if (!empty($invoice_files_lots)) {
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $this->settings['invoice_nomenclature_type'] . '" AND `name`="' . $this->settings['invoice_nomenclature_lot'] . '"';
            $var_lot_id = $this->registry['db']->GetOne($sql);

            $sql = 'SELECT n.id, nl.name' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                   ' ON (n_cstm.model_id=n.id AND n_cstm.var_id="' . $var_lot_id . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nl' . "\n" .
                   ' ON (nl.parent_id=n_cstm.value AND nl.lang="' . $this->registry['lang'] . '" AND nl.name IN ("' . implode('","',
                       General::slashesEscape(array_keys($invoice_files_lots))) . '"))' . "\n";
            $noms_lots_relation = $this->registry['db']->GetAssoc($sql);

            foreach ($noms_lots_relation as $nom_id => $lot_num) {
                $this->completeFilesInModelsList('nomenclatures', $nom_id, 'add', $invoice_files_lots[$lot_num]);
            }
        }
    }

    /**
     * Process the archive data files
     *
     * @param $archive_data_files
     * @return void
     */
    private function processArchiveDataFiles($archive_data_files): void
    {
        if (empty($archive_data_files)) {
            return;
        }

        $psiro_code_files = [];
        foreach ($archive_data_files as $full_file_path) {

            // get only the path below the source path
            $path_cut = preg_replace('#' . General::slashesEscape($this->searchedPathsList['archive_data']) . '#', '', $full_file_path);
            $path_elements = explode(DIRECTORY_SEPARATOR, $path_cut);

            // if the first folder below the source folder is not with the name of a code containing only numbers
            $path_elements[0] = ltrim($path_elements[0], '0');
            if (empty($path_elements[0]) || !preg_match('#^[0-9]*$#', $path_elements[0])) {
                continue;
            }

            // check if the file is pdf
            $file_name = end($path_elements);
            $file_name_elements = explode('.', $file_name);
            $extension = end($file_name_elements);
            if ($extension != 'pdf') {
                continue;
            }
            $psiro_code_files[$path_elements[0]][] = $full_file_path;
        }

        // search the noms which have the required lots
        if (!empty($psiro_code_files)) {
            $stations_data = $this->getStationsData();
            $noms_psiro_relations = array_combine(array_keys($stations_data), array_column($stations_data, 'code'));
            $noms_psiro_relations = array_intersect($noms_psiro_relations, array_keys($psiro_code_files));

            foreach ($noms_psiro_relations as $nom_id => $psiro_num) {
                $this->completeFilesInModelsList('nomenclatures', $nom_id, 'add', $psiro_code_files[$psiro_num]);
            }
        }
    }

    /**
     * Function to get the full list of available files types
     *
     * @return void
     */
    private function getFilesTypes() : void
    {
        $sql = 'SELECT n.`code`, n.`id`, ni18n.`name` FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE n.`type`="' . $this->settings['nomenclature_file_type'] . '" AND n.`active`=1 AND n.`deleted_by`=0';
        $this->filesTypes = $this->registry['db']->GetAssoc($sql);
    }

    /**
     * Process the archive data files
     *
     * @param $archive_files
     * @return void
     */
    private function processArchiveFiles($archive_files): void
    {
        if (empty($archive_files)) {
            return;
        }
        $psiro_code_reg_exp = '#^\d{1,4}$#';
        $name_subelements_regexp = [
            'prefix'     => '#^([a-zA-Zа-яА-Я0-9])+$#',
            'psiro_code' => '#^\d{1,4}$#',
            'building'   => '#^([a-zA-Zа-яА-Я0-9])*$#',
            'entrance'   => '#^([a-zA-Zа-яА-Я0-9\,])*$#',
            'object'     => '#^([a-zA-Zа-яА-Я0-9\.])*$#',
        ];
        $stations_data = $this->getStationsData();
        $station_codes = array_combine(array_keys($stations_data), array_column($stations_data, 'code'));

        // process archive files
        $models_search_list = ['partners' => [], 'investors' => []];
        foreach ($archive_files as $full_file_path) {
            $path_element = array_reverse(explode(DIRECTORY_SEPARATOR, $full_file_path));
            $file_name = $path_element[0];
            $parent_dir_name = $path_element[1];

            // check if the file is pdf
            $file_name_elements = explode('.', $file_name);
            $extension = end($file_name_elements);
            if ($extension != 'pdf') {
                continue;
            }

            // process the file name and find its consisting elements and group them by lot
            unset($file_name_elements[count($file_name_elements) - 1]);
            $file_name_only = implode('.', $file_name_elements);

            if (preg_match($psiro_code_reg_exp, $parent_dir_name)) {
                // split the name to its subelements
                $name_subelements = explode('-', $file_name_only);
                $name_subelements_parsed = [
                    'prefix'     => '',
                    'psiro_code' => '',
                    'building'   => '',
                    'entrance'   => '',
                    'object'     => '',
                ];

                foreach ($name_subelements_regexp as $key => $reg_exp) {
                    if (!isset($name_subelements[0])) {
                        continue;
                    }
                    if (preg_match($reg_exp, trim($name_subelements[0]))) {
                        $name_subelements_parsed[$key] = trim(strval($name_subelements[0]));
                    }
                    array_shift($name_subelements);
                }
                $name_subelements_parsed['entrance'] = array_filter(preg_split('/\s*\,\s*/', $name_subelements_parsed['entrance']));

                // PROCESS THE ELEMENTS FOUND
                // remove leading zeroes from the psiro code
                $name_subelements_parsed['psiro_code'] = ltrim($name_subelements_parsed['psiro_code'], '0');

                // find the station
                if (empty($name_subelements_parsed['psiro_code'])) {
                    $this->fileErrors[] = sprintf('File %s can not be attached - no psiro code parsed!', $full_file_path);
                    continue;
                }
                $station_id = array_search($name_subelements_parsed['psiro_code'], $station_codes);
                if ($station_id === false) {
                    $this->fileErrors[] = sprintf('File %s can not be attached - no station matched!', $full_file_path);
                    continue;
                }

                if (empty($name_subelements_parsed['object'])) {
                    // if no object is defined then assign the file to the station
                    $this->completeFilesInModelsList('nomenclatures', $station_id, 'add', array($full_file_path));
                    continue;
                }

                // find the entrances
                $current_station = $stations_data[$station_id];
                $entrances_found = array_intersect(array_combine(array_keys($current_station['entrance']), array_column($current_station['entrance'], 'name')), $name_subelements_parsed['entrance']);

                if (count($entrances_found) != count($name_subelements_parsed['entrance'])) {
                    $this->completeFilesInModelsList('nomenclatures', $station_id, 'add', array($full_file_path));
                    continue;
                }

                // check all entrances and try to find unique object
                $object_found = 0;
                foreach ($entrances_found as $entr_id => $entr_name) {
                    $objects_match = array_keys($current_station['entrance'][$entr_id]['objects'], $name_subelements_parsed['object']);
                    if (empty($objects_match)) {
                        // no object match found - continue to the next one
                        continue;
                    }
                    if (count($objects_match) > 1) {
                        // more than one object found - the file will be attached to the station
                        break;
                    }
                    if ($object_found) {
                        // this will be the second found object - the file will be attached to the station
                        break;
                    }

                    // if none of the above happened - mark the object as the one found
                    $object_found = reset($objects_match);
                }

                $this->completeFilesInModelsList('nomenclatures', ($object_found ?: $station_id), 'add', array($full_file_path));
            } elseif (in_array($this->settings['subfolder_other_clients'], $path_element)) {
                $this->completeFilesInModelsList('customers', $this->settings['customer_other_client'], 'add', array($full_file_path));
            } elseif (in_array($this->settings['subfolder_partners'], $path_element)) {
                if (!isset($models_search_list['partners'][$parent_dir_name])) {
                    $models_search_list['partners'][$parent_dir_name] = array();
                }
                $models_search_list['partners'][$parent_dir_name][] = $full_file_path;
            } elseif (in_array($this->settings['subfolder_institutions'], $path_element)) {
                if (!isset($models_search_list['investors'][$parent_dir_name])) {
                    $models_search_list['investors'][$parent_dir_name] = array();
                }
                $models_search_list['investors'][$parent_dir_name][] = $full_file_path;
            }
        }

        if (!empty($models_search_list['investors'])) {
            $sql = 'SELECT `code`, `id` FROM ' . DB_TABLE_CUSTOMERS . '
                    WHERE `type` IN (' . $this->settings['customer_institution_ids'] . ') AND
                          `code` IN ("' . implode('","', array_keys($models_search_list['investors'])) . '") AND
                          `subtype`="normal" AND `deleted_by`=0';
            $customers_list = $this->registry['db']->GetAssoc($sql);

            foreach ($customers_list as $cust_code => $cust_id) {
                $this->completeFilesInModelsList('customers', $cust_id, 'add', $models_search_list['investors'][$cust_code]);
            }
        }

        if (!empty($models_search_list['partners'])) {
            $sql = 'SELECT `code`, `id` FROM ' . DB_TABLE_CUSTOMERS . '
                    WHERE `type` IN (' . $this->settings['customer_partners_ids'] . ') AND
                          `code` IN ("' . implode('","', array_keys($models_search_list['partners'])) . '") AND
                          `subtype`="normal" AND `deleted_by`=0';
            $customers_list = $this->registry['db']->GetAssoc($sql);

            foreach ($customers_list as $cust_code => $cust_id) {
                $this->completeFilesInModelsList('customers', $cust_id, 'add', $models_search_list['partners'][$cust_code]);
            }
        }
    }

    /**
     * Function to prepare
     *
     * @return void
     */
    private function processFilesForRemoving($remove_files) : void
    {
        // find the information for the files
        $sql = 'SELECT `id`, `model`, `model_id`, `path` FROM '. DB_TABLE_FILES . ' WHERE `deleted_by`=0 AND `path` IN ("' . implode('","', General::slashesEscape($remove_files)) . '")';
        $remove_files_list = $this->registry['db']->GetAssoc($sql);

        foreach ($remove_files_list as $fl_id => $rem_fl) {
            // define module
            $module = General::singular2plural(strtolower($rem_fl['model']));
            $this->completeFilesInModelsList($module, $rem_fl['model_id'], 'delete', array($fl_id));
        }
    }

    /**
     * Function that will return the last execution time of the automation
     *
     * @return string - the time of the last execution or empty string if this is the first execution
     */
    private function getPreviousAutomationTime() : string
    {
        $sql = 'SELECT `added` FROM ' . DB_TABLE_AUTOMATIONS_HISTORY . ' WHERE `parent_id`="' . $this->automationParams['id'] . '" AND `model_id`=0';
        $last_execution_time = $this->registry['db']->GetOne($sql);

        return strval($last_execution_time);
    }

    /**
     * Function to manage the execution errors
     */
    private function processErrorMessages() : void
    {
        foreach ($this->fileErrors as $errors) {
            $this->executionErrors[] = $errors;
        }
    }

    /**
     * Function to get the stations data
     *
     * @return string - array with stations data
     */
    private function getStationsData() : array
    {
        if ($this->stationsDataPrepared) {
            return $this->stationsData;
        }

        $vars = [
            $this->settings['nomenclature_station_var_psiro'],
            $this->settings['nomenclature_station_var_entrance'],
        ];

        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $this->settings['nomenclature_stations_type'] . '" AND `name` IN ("' . implode('","', $vars) . '")';
        $vars = $this->registry['db']->GetAssoc($sql);

        $sql = 'SELECT n.id, n_cstm_psiro.value as code, n_cstm_entr.value as entrance, ni18n_entr.name as entrance_name' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
               'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_psiro' . "\n" .
               ' ON (n_cstm_psiro.model_id=n.id AND n_cstm_psiro.var_id="' . ($vars[$this->settings['nomenclature_station_var_psiro']] ?? '') . '" AND n.type="' . $this->settings['nomenclature_stations_type'] . '" AND n.active=1 AND n.deleted_by=0)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_entr' . "\n" .
               ' ON (n_cstm_entr.model_id=n.id AND n_cstm_entr.var_id="' . ($vars[$this->settings['nomenclature_station_var_entrance']] ?? '') . '")' . "\n" .
               'LEFT JOIN '. DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_entr' . "\n" .
               ' ON (ni18n_entr.parent_id=n_cstm_entr.value AND ni18n_entr.lang="' . $this->registry['lang'] . '")' . "\n";
        $stations_list = $this->registry['db']->GetAssoc($sql);

        $objects_by_entrance = array();
        $entrance_list = array_unique(array_column($stations_list, 'entrance'));
        if (!empty($entrance_list)) {
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $this->settings['nomenclature_object_type'] . '" AND `name`="' . $this->settings['nomenclature_object_var_entrance'] . '"';
            $entrance_var = $this->registry['db']->GetOne($sql);

            $sql = 'SELECT n.id as object, ni18n.name as object_name, n_cstm_entr.value as entrance' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_entr' . "\n" .
                   ' ON (n_cstm_entr.model_id=n.id AND n_cstm_entr.var_id="' . $entrance_var . '" AND n.type="' . $this->settings['nomenclature_object_type'] . '" AND n.active=1 AND n.deleted_by=0 AND n_cstm_entr.value IN ("' . implode('","', $entrance_list) . '"))' . "\n" .
                   'LEFT JOIN '. DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '")' . "\n";
            $objects_list = $this->registry['db']->GetAll($sql);

            foreach ($objects_list as $obj) {
                if (!isset($objects_by_entrance[$obj['entrance']])) {
                    $objects_by_entrance[$obj['entrance']] = array();
                }
                $objects_by_entrance[$obj['entrance']][$obj['object']] = $obj['object_name'];
            }
        }

        foreach ($stations_list as $station_id => $station_data) {
            if (!isset($this->stationsData[$station_id])) {
                $this->stationsData[$station_id] = [
                    'id'       => $station_id,
                    'code'     => $station_data['code'],
                    'entrance' => []
                ];
            }
            $this->stationsData[$station_id]['entrance'][$station_data['entrance']] = [
                'name'    => $station_data['entrance_name'],
                'objects' => (!empty($objects_by_entrance[$station_data['entrance']]) ? $objects_by_entrance[$station_data['entrance']] : [])
            ];
        }

        $this->stationsDataPrepared = true;
        return $this->stationsData;
    }

    /**
     * Function that puts the file in the right place in the working files array
     *
     * @param string $module - the name of the module
     * @param integer $model_id - the model id
     * @param string $file_action - the action which will be done with the file ('add' or 'delete')
     * @param array $files - list of files data (paths for ADD or ids for DELETE)
     *
     * @return void
     */
    private function completeFilesInModelsList($module, $model_id, $file_action, $files): void
    {
        if (!isset($this->modelsFiles[$module])) {
            $this->modelsFiles[$module] = array();
        }
        if (!isset($this->modelsFiles[$module][$model_id])) {
            $this->modelsFiles[$module][$model_id] = array(
                'add'    => [],
                'delete' => []
            );
        }
        $this->modelsFiles[$module][$model_id][$file_action] = array_merge($this->modelsFiles[$module][$model_id][$file_action], $files);
    }
}
