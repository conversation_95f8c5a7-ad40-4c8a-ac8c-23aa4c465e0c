<?php
include_once PH_MODULES_DIR . 'callbacks/models/callbacks.model.php';
include_once PH_MODULES_DIR . 'eurotrust/models/eurotrust.logger.class.php';
include_once PH_MODULES_DIR . 'eurotrust/models/eurotrust.operations.factory.php';

use Nzoom\Eurotrust\Model\ClientConfig;
use Nzoom\Eurotrust\Model\EurotrustAnnotation;
use Nzoom\Eurotrust\Model\EurotrustClient;
use Nzoom\Eurotrust\Model\EurotrustSendFileData;
use Nzoom\Eurotrust\Model\EurotrustSendFileDataDocument;
use Nzoom\Eurotrust\Model\EurotrustFile;
use Nzoom\Eurotrust\Model\EurotrustResponse;
use Nzoom\Eurotrust\Model\EurotrustSendFileDataSignInfo;
use Nzoom\Eurotrust\Model\EurotrustSendGroupFilesData;
use Nzoom\Eurotrust\Model\EurotrustSendGroupFilesDataDocument;
use Nzoom\Eurotrust\Model\EurotrustUsers;
use Nzoom\Eurotrust\Model\Eurotrust;
use Nzoom\Eurotrust\Model\KeyPair;

trait Eurotrust_Trait {
    private static $signVarPrefix = 'sign_from_var_';
    private static $eurotrustDefaults = [
        'DOCUMENT_DESCRIPTION' => 'Nzoom document',
        //timestamp, 2 hours
        'DOCUMENT_EXPIRE_INTERVAL' => '+2 hours',
        //The amount is in euro. 0 stands for unlimited.
        'DOCUMENT_COVERAGE' => 20000,
        //whether the document requires previewing before signing
        'DOCUMENT_PREVIEW' => 0,
        // "PDF1" (PDF Baseline),
        // "PDF2" (PDF Baseline + Long Term),
        // "PDF3" (PDF Baseline + Timestamp),
        // "XML1" (XML Baseline),
        // "XML2" (XML Baseline + Long Term),
        // "XML3" (XML Baseline),
        // "XML4" (XML Baseline + Long Term),
        // "CAD1" (Sign any file type),
        // "CAD2" (Sign any file type with timestamp)
        'SIGN_INFO_TYPE' => 'PDF2',
        //BIOrequired: 0/1 Optional parameter. Default value 0.
        'BIOREQUIRED' => 0,
        //for group files only
        //"optional": 0/1 Whether the document is optional for signing.
        // The first document must always be optional=0
        'DOCUMENT_OPTIONAL' => 0,

    ];

    public array $automation_params;
    private array $files;

    /**
     * Send document to be Eurotrust for sign
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function signEurotrust(array $params): bool {
        if (!empty($this->before_action) && $this->registry['db']->HasFailedTrans()) {
            //IMPORTANT: this checks for previously failed befora action automations
            return false;
        }

        $this->automation_params = $params;

        $params['model']->unsanitize();
        $model = $params['model'];

        $this->getAllFiles($model);

        if (empty($this->files)) {
            $this->registry['messages']->setError($this->i18n('error_sending_files_to_sign'), '', -2);
            $this->registry['messages']->setError($this->i18n('error_no_files_sign'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }
        if (!$this->validateFiles()) {
            $this->registry['messages']->setError($this->i18n('error_sending_files_to_sign'), '', -2);
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        try {
            $etClientConfig = $this->getEurotrustClientConfig();
            if (count($this->files) == 1) {
                $this->sendFileToEurotrust($etClientConfig, $model);
                $msg = 'msg_file_sent_to_eurotrust';
            } else {
                $this->sendGroupFilesToEurotrust($etClientConfig, $model);
                $msg = 'msg_group_files_sent_to_eurotrust';
            }

            $this->registry['messages']->setMessage($this->i18n($msg));
            $result = true;
        } catch (\Exception $e) {
            //ToDo: error reporting
            $this->registry['messages']->setError($this->i18n('error_cannot_send_file_to_eurotrust'));
            $log_message = "Automation:{$this->automation_params['id']}\nModel: {$model->modelName}\nModelId: {$model->get('id')}\nException:{$e->getMessage()}";
            General::log($this->registry, __METHOD__, $log_message);

            $result = false;
        }

        $this->registry['messages']->insertInSession($this->registry);

        return $result;
    }


    /**
     * @param ClientConfig $etClientConfig
     * @param Model $model
     * @return void
     * @throws Exception
     */
    private function sendFileToEurotrust(ClientConfig $etClientConfig, Model $model): void
    {
        $etClient = new EurotrustClient($etClientConfig);

        $callback = $this->getNewCallback();
        $callbackUrl = $callback->getUrl();
        //ToDo: define test callback
        //$callbackUrl = 'https://nzoom.ocenki.bg/test/callback/';

        //it is certain at this point that the files are only one
        $file = $this->files[0];
        $etFile = new EurotrustFile($file['filename'], $file['path']);

        $keyPair = KeyPair::generateNew();
        $etData = new EurotrustSendFileData(
            $etClientConfig->getVendorNumber(),
            $keyPair->getPublicKey(),
            $callbackUrl
        );
        $etData->setDocument($this->getEurotrustDocument($this->settings));
        $etData->setSignInfo($this->getEurotrustSignInfo($this->settings));
        $this->addEurotrustUsers($etData, $this->settings);

        $context = [
            'automation_id' => $this->automation_params['id'],
            'model_name' => $this->automation_params['model']->modelName,
            'model_id' => $this->automation_params['model']->get('id'),
            'files' => $this->files,
        ];
        $etClient->setLogger(new EurotrustLogger($this->registry, $context));

        $etResponse = $etClient->sendDocument($etFile, $etData);
        try {
            $this->saveOperation($etResponse, $model, $keyPair);
        } catch (\Exception $e) {
            //ToDo: IMPLEMENT withdraw
            //$etClient->withdrawDocument();
            throw $e;
        }
    }

    /**
     * @param ClientConfig $etClientConfig
     * @param Model $model
     * @return void
     * @throws Exception
     */
    private function sendGroupFilesToEurotrust(ClientConfig $etClientConfig, Model $model): void
    {
        $etClient = new EurotrustClient($etClientConfig);

        $callback = $this->getNewCallback();
        $callbackUrl = $callback->getUrl();
        //ToDo: remove me
        $callbackUrl = 'https://nzoom.ocenki.bg/test/callback/';

        //it is certain that the files have been proof valid
        $etFiles = array();
        foreach($this->files as $file) {
            $etFiles[] = new EurotrustFile($file['filename'], $file['path']);
        }

        $keyPair = KeyPair::generateNew();
        $etData = new EurotrustSendGroupFilesData(
            $etClientConfig->getVendorNumber(),
            $keyPair->getPublicKey(),
            new \DateTime($settings['document_expire_interval'] ?? self::$eurotrustDefaults['DOCUMENT_EXPIRE_INTERVAL']),
            (int)($this->settings['document_coverage'] ?? self::$eurotrustDefaults['DOCUMENT_COVERAGE']),
            $callbackUrl
        );
        $etData->setGroupDescription($this->settings['document_description'] ?? self::$eurotrustDefaults['DOCUMENT_DESCRIPTION']);
        $etData->setDocuments($this->getEurotrustDocuments($this->settings));
        $etData->setSignInfo($this->getEurotrustSignInfo($this->settings));
        $this->addEurotrustUsers($etData, $this->settings);

        $context = [
            'automation_id' => $this->automation_params['id'],
            'model_name' => $this->automation_params['model']->modelName,
            'model_id' => $this->automation_params['model']->get('id'),
            'files' => $this->files,
        ];
        $etClient->setLogger(new EurotrustLogger($this->registry, $context));

        $etResponse = $etClient->sendGroupDocuments($etFiles, $etData);
        $this->saveOperation($etResponse, $model, $keyPair);
    }

    private function getAllFiles($model):void {
        $files = array();

        //get files from specified additional vars (excl. GT2)
        $vars = array();
        foreach($this->settings as $k => $v) {
            if (preg_match('#^' . preg_quote(self::$signVarPrefix) . '(.*)#', $k, $matches)) {
                $vars[$matches[1]] = $v;
            }
        }
        if (!empty($vars)) {
            $files = $this->getFilesFromAdditionalVars($model, $vars);
        }
        //ToDo: get files from other sources

        $this->files = $files;
    }

    private function getFilesFromAdditionalVars($model, $vars):array {
        $files = array();

        foreach($vars as $sourceVarName => $destinationVarName) {
            $fileIds = $model->getValueByName($sourceVarName);
            if (empty($fileIds)) {
                continue;
            }
            if (!is_array($fileIds)) {
                //single variable
                $fileIds = array($fileIds);
            }
            $fileSearchIds = array_filter(array_values($fileIds));
            if (empty($fileSearchIds)) {
                continue;
            }
            $filesAssoc = $this->getFilesAssoc($fileSearchIds);
            foreach($fileIds as $idx => $fileId) {
                if (!array_key_exists($fileId, $filesAssoc)) {
                    continue;
                }
                $file = $filesAssoc[$fileId];
                $files[] = array(
                    'id' => $fileId,
                    'filename' => $file->get('filename'),
                    'path' => $file->get('path'),
                    'origin' => 'additional',
                    'source' => $sourceVarName,
                    'destination' => $destinationVarName,
                    'revision' => $file->get('revision'),
                    'description' => $file->get('description'),
                    //IMPORTANT: single variables should have row null
                    'row' => $idx > 0 ? $idx:null,
                );
            }
        }

        return $files;
    }

    private function getFilesAssoc($ids) {
        $files = Files::search($this->registry, array(
            'where' => array(
                'f.id IN (' . implode(',', $ids) . ')'
            ),
            'skip_permissions_check' => true
        ));
        $filesAssocByIds = array();
        foreach($files as $file) {
            $filesAssocByIds[$file->get('id')] = $file;
        }

        return $filesAssocByIds;
    }

    private function validateFiles():bool {
        $valid = true;
        foreach($this->files as $fileId => $fileRow) {
            if (!file_exists($fileRow['path']) || !is_file($fileRow['path'])) {
                $this->registry['messages']->setError($this->i18n('error_file_doesnt_exist', $fileRow['filename']));
                $valid = false;
            }
            if (!EurotrustClient::validateFilename($fileRow['filename'])) {
                $this->registry['messages']->setError(
                    $this->i18n(
                        'error_file_invalid_filename',
                        [
                            $fileRow['filename'],
                            EurotrustClient::$fileNameForbiddenSymbols,
                            implode(', ', EurotrustClient::$fileNameAllowedExtensions)
                        ]
                    )
                );
                $valid = false;
            }
        }

        //filenames should be unique
        if (count($this->files) > count(array_unique(array_column($this->files, 'filename')))) {
            $this->registry['messages']->setError(
                $this->i18n('error_filenames_not_unique',
                    [implode(', ', array_column($this->files, 'filename'))]
                )
            );
            $valid = false;
        }

        if (count($this->files) > EurotrustClient::$maxGroupFilesCount) {
            $this->registry['messages']->setError(
                $this->i18n('error_too_many_files',
                    [EurotrustClient::$maxGroupFilesCount, count($this->files)]
                )
            );
            $valid = false;
        }

        return $valid;
    }
    /**
    * @return EurotrustSendFileDataDocument
    * @throws Exception
    */
    public function getEurotrustDocumentExpirationDate(array $settings): DateTime
    {
        return new \DateTime($settings['document_expire_interval']
                                 ?? self::$eurotrustDefaults['DOCUMENT_EXPIRE_INTERVAL']);
    }

    /**
     * @return EurotrustSendFileDataDocument
     * @throws Exception
     */
    public function getEurotrustDocument(array $settings): EurotrustSendFileDataDocument
    {
        $etDocument = new EurotrustSendFileDataDocument(
            $settings['document_description'] ?? self::$eurotrustDefaults['DOCUMENT_DESCRIPTION'],
            $this->getEurotrustDocumentExpirationDate($settings),
            (int)($settings['document_coverage'] ?? self::$eurotrustDefaults['DOCUMENT_COVERAGE']),
            (int)($settings['document_preview'] ?? self::$eurotrustDefaults['DOCUMENT_PREVIEW']),
        );
        if (!empty($settings['document_annotation_text'])) {
            $annotation[] = new EurotrustAnnotation(
                $settings['document_annotation_text'],
                $settings['document_annotation_font_name'] ?? 'Verdana',
                $settings['document_annotation_font_size'] ?? '12',
                $settings['document_annotation_font_color'] ?? '#000000',
                $settings['document_annotation_axis_x'],
                $settings['document_annotation_axis_y'],
                //ToDo: research what should be the argument for last page
                $settings['document_annotation_page']
            );
            $etDocument->setAnnotationParameters($annotation);
        }

        return $etDocument;
    }

    /**
     * @return Array
     * @throws Exception
     */
    public function getEurotrustDocuments(array $settings): Array
    {
        $etDocuments = array();
        foreach($this->files as $idx => $file) {
            $etDocument = new EurotrustSendGroupFilesDataDocument(
                //first is always NOT optional
                $idx === 0 ? 0 : $settings['document_optional'] ?? self::$eurotrustDefaults['DOCUMENT_OPTIONAL'],
                $file['description'] ? $file['description'] : self::$eurotrustDefaults['DOCUMENT_DESCRIPTION'],
                (int)($settings['document_preview'] ?? self::$eurotrustDefaults['DOCUMENT_PREVIEW'])
            );
            if (!empty($settings['document_annotation_text'])) {
                $annotation[] = new EurotrustAnnotation(
                    $settings['document_annotation_text'],
                    $settings['document_annotation_font_name'] ?? 'Verdana',
                    $settings['document_annotation_font_size'] ?? '12',
                    $settings['document_annotation_font_color'] ?? '#000000',
                    $settings['document_annotation_axis_x'],
                    $settings['document_annotation_axis_y'],
                    //ToDo: research what should be the argument for last page
                    $settings['document_annotation_page']
                );
                $etDocument->setAnnotationParameters($annotation);
            }
            $etDocuments[] = $etDocument;
        }

        return $etDocuments;
    }


    /**
     * @return EurotrustSendFileDataSignInfo
     * @throws Exception
     */
    public function getEurotrustSignInfo(array $settings): EurotrustSendFileDataSignInfo
    {
        return new EurotrustSendFileDataSignInfo(
            $settings['sign_info_type'] ?? self::$eurotrustDefaults['SIGN_INFO_TYPE']
        );
    }

    /**
     * @throws Exception
     */
    public function addEurotrustUsers($data, array $settings): void
    {
        $etUsers = $data->getUsers();
        if (!empty($settings['users_by_email'])) {
            $emails = preg_split('#\s*,\s*#', $settings['users_by_email']);
            foreach($emails as $email) {
                $etUsers->addByEmail($email);
            }
        }
        //ToDo: expand possibilities to allow from assigned users or users in a variable(s)
    }


    /**
     * Save files within the nzoom variables
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     * @throws Exception
     */
    public function saveEurotrustSignedFiles(array $params): bool {
        /** @var Callbacks_Request $callbackRequest */
        $callbackRequest = $params['model'];

        $callbackData = $callbackRequest->getBodyData();
        if (empty($callbackData->transactionID ?? null)) {
            throw new \Exception('Could not obtain transactionId!');
        }

        /** @var Eurotrust_Transaction $transaction */
        $transaction = Eurotrust_Transactions::getTransaction($this->registry, $callbackData->transactionID);
        $operation = Eurotrust_Operations::getOperation($this->registry, $transaction->get('thread_id'));


        $this->registry->set('get_old_vars', true, true);

        $filters = ['where' => [Eurotrust_Files::$alias . ".parent_id = '{$operation->get('id')}'"]];
        $operationFilesData = Eurotrust_Files::search($this->registry, $filters);

        $oldModels = array();
        $models = array();
        $modifiedVars = array();
        /** @var Eurotrust_File $etFile */
        foreach($operationFilesData as $etFile) {
            $modelKey = $operation->get('model') . $operation->get('model_id');
            if (array_key_exists($modelKey, $models)) {
                $model = $models[$modelKey];
            } else {
                $model = $this->getModelById($operation->get('model'), $operation->get('model_id'));
                $model->unsanitize();
                $models[$modelKey] = $model;
                $oldModels[$modelKey] = clone $model;
            }
            $vars = $model->getAssocVars();
            $sourceData = json_decode($etFile->get('source'), true);
            $row = $sourceData['row'];
            $destinationVar = $etFile->get('destination');
            $modifiedVars[$modelKey][] = $destinationVar;
            if (!array_key_exists($destinationVar, $vars)) {
                throw new \Exception('Invalid destination variable!');
            }
            if ($vars[$destinationVar]['grouping']) {
                //destination is grouping table
                if ($row) {
                    //the file is from table, store it in the same row to the destination table
                    $vars[$destinationVar]['value'][$row] = $etFile->get('signed_file_id');
                } else {
                    $lastRow = $model->getVariableLastRowNum($destinationVar);
                    //the file is from single variable, store it in the LAST row of the destination table
                    $vars[$destinationVar]['value'][$lastRow] = $etFile->get('signed_file_id');
                }
            } elseif ($vars[$destinationVar]['gt2']) {
                    //destination is GT2
                    if ($row) {
                        //the file is from table, store it in the same row to the destination table
                        $vars[$destinationVar]['values'][$row] = $etFile->get('signed_file_id');
                    } else {
                        $lastRow = $model->getVariableLastRowNum($destinationVar);
                        //the file is from single variable, store it in the LAST row of the destination table
                        $vars[$destinationVar]['values'][$lastRow] = $etFile->get('signed_file_id');
                    }
            } else {
                //destination is a single variable
                $vars[$destinationVar]['value'] = $etFile->get('signed_file_id');
            }
            $model->set('vars', array_values($vars), true);
            $model->set('assoc_vars', $vars, true);
        }

        foreach($models as $idx => $model) {
            $oldModel = $oldModels[$idx];
            $oldModel->getAssocVars();

            if ($model->save()) {
                // write history
                $historyClass = ucfirst(strtolower(General::singular2plural($model->modelName))) . '_History';
                $historyClass::saveData($this->registry, array('model' => $model, 'action_type' => 'edit', 'new_model' => $model, 'old_model' => $oldModel));

                $launchAutomations = $this->settings['launch_automations_upon_success'] ?? false;
                if ($launchAutomations) {
                    $modelKey = $operation->get('model') . $operation->get('model_id');

                    //use this condition to launch automations
                    $model->set('signed_file_vars', $modifiedVars[$modelKey], true);

                    //execute all the automations with action add on documents
                    $worker = new Automations_Controller($this->registry);
                    $worker->executeActionAutomations($oldModel, $model, 'sign');
                }
            }
        }

        return true;
    }

    private function getModelById($modelName, $modelId) {
        $factory = ucfirst(General::singular2plural($modelName));

        //get table alias
        $alias = $factory::$alias;

        //prepare some filters
        $filters = array(
            'where' => array($alias . '.id = \'' . $modelId . '\''),
            'sanitize' => true
        );

        return $factory::searchOne($this->registry, $filters);
    }

    /**
     * @return ClientConfig
     */
    public function getEurotrustClientConfig(): ClientConfig
    {
        $etSettings = $this->registry['config']->getSectionParams('eurotrust');
        $testMode = $etSettings['testMode'] ?? false;
        return new ClientConfig(
            $etSettings['vendorNumber'],
            $etSettings['vendorKey'],
            $testMode ? $etSettings['baseUrlTest'] : $etSettings['baseUrl'],
            (bool) ($etSettings['logRequest']??false)
        );
    }

    /**
     * Saves operation and its transactions and files
     *
     * @param EurotrustResponse $etResponse
     * @param Model $model
     * @param KeyPair $keyPair
     * @return void
     * @throws Exception
     */
    public function saveOperation(EurotrustResponse $etResponse, Model $model, KeyPair $keyPair): void
    {
        $responseData = json_decode($etResponse->getResponse(), true);
        if (empty($responseData['transactions'])) {
            throw new \Exception('Invalid data for transactions!');
        }

        try {
            $db = $this->registry['db'];
            /**
             * @var ADOConnection $db
             */
            $db->StartTrans();

            $operation = new Eurotrust_Operation($this->registry);
            $operation->set('thread_id', $responseData['threadID']);
            $operation->set('model', $model->modelName);
            $operation->set('model_id', $model->get('id'));
            $operation->set('context', json_encode(['automation_id' => $this->automation_params['id']]));
            $operation->set('publicKey', $keyPair->getPublicKey());
            $operation->set('privateKey', Eurotrust_Operation::encryptKey($keyPair->getPrivateKey()));
            if (!$operation->save()) {
                throw new \Exception('Failed to save operation!');
            }

            foreach ($responseData['transactions'] as $transactionData) {
                $identity = Eurotrust_Transactions::getTransactionIdentity($transactionData);
                $transaction = new Eurotrust_Transaction($this->registry);
                $transaction->set('parent_id', $operation->get('id'));
                $transaction->set('thread_id', $operation->get('thread_id'));
                $transaction->set('transaction_id', $transactionData['transactionID']);
                $transaction->set('user_identified_by', $identity['identifiedBy']);
                $transaction->set('user_identity', $identity['identity']);
                if (!$transaction->save('add')) {
                    throw new Exception("Cannot save transaction with id {$transactionData['transactionID']}!");
                }
            }

            foreach ($this->files as $fileData) {
                $etFile = new Eurotrust_File($this->registry);
                $etFile->set('parent_id', $operation->get('id'));
                $etFile->set('thread_id', $operation->get('thread_id'));
                $etFile->set('file_id', $fileData['id']);
                $etFile->set('source', json_encode($fileData));
                $etFile->set('destination', $fileData['destination']);
                if (!$etFile->save('add')) {
                    throw new Exception("Cannot save file with id {$fileData['id']}!");
                }
            }
            $db->CompleteTrans();
        } catch(\Exception $e) {
            $db->CompleteTrans(false);
            throw $e;
        }
    }

    /**
     * @return callable
     * @throws Exception
     */
    public function getNewCallback(): Callback
    {
        return Callbacks::initNewCallback(
            $this->registry,
            [
                'vendor' => 'Eurotrust',
                'expires' => $this->getEurotrustDocumentExpirationDate($this->settings)->format('Y-m-d H:i:s'),
                'method' => "module := eurotrust\nmethod := processCallbackSignDocument"
            ]
        );
    }
}
