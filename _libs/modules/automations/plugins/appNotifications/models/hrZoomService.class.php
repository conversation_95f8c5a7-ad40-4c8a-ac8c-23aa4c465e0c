<?php

use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Client;
use GuzzleHttp\Cookie\CookieJar;

class hrZoomService {


    private string $host;
    private string $userAgent;
    private CookieJar $cookieJar;
    private Client $client;
    private const POST_METHOD = 'POST';


    public function __construct(string $host, string $userAgent, CookieJar $cookieJar)
    {
        $this->userAgent = $userAgent;
        $headers = [
            'User-Agent' => $this->userAgent,
            'Accept' => 'application/json',
        ];
        $this->host = $host;
        $this->cookieJar = $cookieJar;
        $this->client = new Client([
            'base_uri' => $this->host,
            'http_errors' => false,
            'cookies' => $this->cookieJar,
            'headers' => $headers,
        ]);
    }

    public function getCookieJar()
    {
        return $this->cookieJar;
    }

    public function authenticate($user, $pass)
    {
        $endpoint = 'nauth/apiLogin';
        $body['form_params'] = [
            'username' => $user,
            'password' => $pass,
        ];

        return $this->execute(self::POST_METHOD, $endpoint, [], $body);
    }

    public  function sendPost($endpoint, $body, $params = [])
    {
        return $this->execute(self::POST_METHOD, $endpoint, $params, $body);
    }

    /**
     * Execute a request with the specified parameters
     * @param string $method METHOD_* constant value
     * @param array $params any Query (GET) parameters
     * @param array $headers any headers
     * @param array $options any GuzzleHttp\Request options
     * @return ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function execute(string $method,
                            string  $endpoint,
                            array  $params = [],
                            array  $options = []): ResponseInterface
    {
        $compiledOptions = $options;
        if (!empty($params)) {
            $endpoint .= "?" . http_build_query($params);
        }
        $rawResponse = $this->getClient()->request($method, $endpoint, $compiledOptions);
        $this->lastResponse = $rawResponse;
        if ($rawResponse->getStatusCode() > 299) {
            throw new Exception(
                "Mzoom responded with abnormal status code {$rawResponse->getStatusCode()} ({$rawResponse->getBody()->getContents()})\n"
                . "Intended URI: {$endpoint}",
                $rawResponse->getStatusCode());
        }

        return $this->lastResponse;
    }

    private function getClient() : Client
    {
        return $this->client;
    }


}
