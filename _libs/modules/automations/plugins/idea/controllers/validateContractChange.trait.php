<?php

trait validateContractChange {
    public $automation_params;

    /**
     * Function to validate the sum change of a repayment plan
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function validateActiveContractChange(array $params): bool
    {
        $this->automation_params = $params;

        // get the data from the registry
        $new_model = Documents::buildModel($this->registry);
        $new_gt2 = $new_model->getGT2Vars();

        // get the data from the DB
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $old_gt2 = $new_model->getGT2Vars();

        $old_tbl_total = 0;
        $new_tbl_total = 0;
        if (!empty($old_gt2['values'])) {
            $old_tbl_total = array_sum(array_column($old_gt2['values'], 'price'));
        }
        if (!empty($new_gt2['values'])) {
            $new_tbl_total = array_sum(array_column($new_gt2['values'], 'price'));
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);

        // compare the two values and throw error if the new one IS LESS than the old one
        $result = true;
        if ($new_tbl_total < $old_tbl_total) {
            $this->registry['messages']->setError(sprintf($this->i18n('error_automation_old_sum_greater_than_new_sum'), $new_tbl_total, $old_tbl_total));
            $this->registry['messages']->insertInSession($this->registry);
            $result = false;
        }

        return $result;
    }

}
