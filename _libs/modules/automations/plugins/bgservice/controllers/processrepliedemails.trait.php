<?php

use Illuminate\Pagination\LengthAwarePaginator;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Nzoom\Email\Authentication\Exception\ProviderNotSupported;
use Nzoom\Email\Imap\Box;
use Nzoom\Email\Imap\BoxFactory;
use Nzoom\Email\Imap\Message;
use Nzoom\Email\Imap\MessageListPaginated;
use Webklex\PHPIMAP\Exceptions\ConnectionFailedException;
use Webklex\PHPIMAP\Exceptions\FolderFetchingException;
use Webklex\PHPIMAP\Exceptions\GetMessagesFailedException;
use Webklex\PHPIMAP\Support\MessageCollection;

trait ProcessRepliedEmails_Trait
{
    static private $timeLimit = 60 * 60; // 1 hour
    private $oldTimeLimit;

    static private $IMAP_PASSWORD_KEY = 'process_emails';
    static private $IMAP_PASSWORD_ALGO = 'xtea';

    private Box $box;
    private $params;
    private $customersCache = [];

    private $db;
    private $attachmentsCache = [];

    private $settingsParsed;


    /**
     * Crontab automation to process the replies in the selected inbox
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of operation
     */
    public function processRepliedEmails($params)
    {
        $this->params = $params;
        $this->setTimeLimit();

        try {
            $box = $this->getMailBox();

            try {
                // Check if the folder for moving messages to, exists. Create it if not.
                // This check is important to happen before reading messages because of the IMAP state!
                $processedEmailsFolder = $this->setting('operation')['processedMailsFolder'];
                if ($processedEmailsFolder) {
                    $processedEmailsFolder = \mb_convert_encoding($processedEmailsFolder, "UTF7-IMAP");
                    if (!$box->getClient()->checkFolder($processedEmailsFolder)) {
                        $box->getClient()->createFolder($processedEmailsFolder, true);
                    }
                }
            } catch (\Exception|\Error $e) {
                throw new Exception("BGS->processRepliedEmails() - processMessage [Error] {Error while creating folder in box '{$this->setting('operation')['processedMailsFolder']}'}", 0, $e);
            }
            $messagePages = $this->getMessagePages($box);
            $messagePages->setReverse(true);

            $processedMessages = [];
            foreach ($messagePages ?? [] as $page) {
                if (!$page) {
                    continue;
                }

                foreach ($page->reverse() as $message) {
                    try {
                        /** @var Message $mask */
                        $mask = $message->mask();
                        $this->processMessage($mask);

                        // Collect all properly processed messages in an array
                        // (any errors will be cached and this code is not reached)
                        if ($processedEmailsFolder) {
                            $processedMessages[] = $mask;
                        }

                        $mask = null;
                    } catch (\Exception|\Error $e) {
                        $subject = isset($mask)?mb_substr($mask->getSubject(),0, 100):'-';
                        $from =  isset($mask)?((string) $mask->getFrom()):'-';
                        $this->logError("BGS->processRepliedEmails() - processMessage [Error] {Error while processing message '{$subject}', from: {$from}", $e);
                        $this->executionErrors[] = $this->getErrorString($e->getMessage(), $subject, $from);
                    }
                }
            }

            foreach ($processedMessages as $msg) {
                try {
                    // Use mask for move to support fallback to copy-delete if the move command doesn't work
                    $msg->move($processedEmailsFolder);
                    $msg = null;
                } catch (\Exception|\Error $e) {
                    $subject = isset($msg)?mb_substr($msg->getSubject(),0, 100):'-';
                    $from =  isset($msg)?((string) $msg->getFrom()):'-';
                    $this->logError("BGS->processRepliedEmails() - processMessage [Error] {Error while moving message '{$subject}', from: {$from} (message is processed correctly, but not moved)", $e);
                    $this->executionErrors[] = $this->getErrorString($e->getMessage(), $subject, $from);;
                }
            }

            // Writes history
            $this->updateAutomationHistory($this->params, 0, 1);

            // Send error report if there are any errors
            if (!empty($this->executionErrors) && $this->setting('operation')['error_reporting_email']??false) {
                $executionErrors = array_map(function ($error) {
                    return htmlspecialchars($error);
                }, $this->executionErrors);

                $divider = "\n\n--------------------------------\n\n";
                $errors = $divider . implode($divider, $executionErrors) . $divider;

                $errorLogString = $this->i18n('warning_automation_bgservice_processrepliedemails_operational_errors_pattern', [$errors]);
                $this->sendErrorReport($this->setting('operation')['error_reporting_email'], $errorLogString);
            }
        } catch (\Exception|\Error $e) {
            $subject = '';
            $sender = '';
            if(isset($mask)) {
                $subject = $mask->getSubject();
                $sender = implode(',', $mask->getMails());
            }
            // Flattern the error chain.
            $exceptions = [];
            $err = $e;
            do {
                $traces = [];
                foreach ($err->getTrace() as $n=>$data) {
                    $traces[] = "#{$n} {$data['file']}({$data['line']}): " . ($data['class']??'').($data['type']??'') . "{$data['function']}()";
                }
                $exceptions[] = "Message: {$err->getMessage()}"
                    . "\n    File: {$err->getFile()}:{$err->getLine()}"
                    . "\n    Stack trace: " . implode("\n", $traces);

                $this->executionErrors[] = $err->getMessage()
                    . "\n\nid: " . $this->params['id']
                    . "\nemail.subject: {$subject}"
                    . "\nemail.sender: {$sender}";
            } while ($err = $err->getPrevious());

            $errorLogString = "Exceptions:\n" . implode("\n\n", $exceptions)
                . "\n\nautomation.id: " . $this->params['id']
                . "\nautomation.start_model_type: " . $this->params['start_model_type']
                . "\nautomation.settings: " . var_export($this->settings, true)
                . "\nemail.subject: {$subject}"
                . "\nemail.sender: {$sender}";

            General::log($this->registry, "BGS->processRepliedEmails() - Try to process email [Error]", $errorLogString);

            $this->updateAutomationHistory($this->params, 0, 0);

            if ($this->setting('operation')['error_reporting_email']??false) {
                $this->sendErrorReport($this->setting('operation')['error_reporting_email'], $errorLogString);
            }
        } finally {
            if(isset($box)) {
                try {
                    $box->expunge();
                    $box->disconnect();
                } catch (\Exception|\Error $e) {
                    $this->logError("BGS->processRepliedEmails() - processMessage [Error] {Error while expunge and disconnect}", $e);
                }
            }

            $this->revertTimeLimit();
        }
        //AuthFailedException|ConnectionFailedException|FolderFetchingException|GetMessagesFailedException|\Webklex\PHPIMAP\Exceptions\RuntimeException
        return true;
    }

    /**
     * Process the email
     * @param Message $message
     * @return void
     * @throws Exception
     */
    private function processMessage(Message $message): void
    {
        $emailCode = $message->getCode();
        $mails = $message->getMails();

        if (!empty($emailCode) && $message->isBounced() && !empty($mails)) {
            $this->processBouncedMessage($message);
            return;
        }
        if (preg_match('#^Out of office#i', $message->getSubject())) {
            // 'Out of office' auto-response, can't be processed as bounced, because no code, but it is not new ticket!
            // Silently do nothing
            return;
        }

        foreach ($mails as $email) {
            $this->processMessageFromSender($email, $message);
        }
    }

    /**
     * Process as bounced
     * @param Message $message
     * @return void
     * @throws Exception
     */
    private function processBouncedMessage(Message $message)
    {
        $emailCode = $message->getCode();
        $mails = $message->getMails();
        foreach ($mails as $key => $email) {
            $emailRecord = $this->findEmailRecord($emailCode, $email);
            // get the mail from emails_sentbox taken by code and recipient
            if (!$emailRecord) {
                // if no mail is found it shouldn't be deleted
                throw new \Exception("Email record not found in emails_sentbox while processing "
                    . "bounced message! Code: '{$emailCode}', email: '{$email}', Subject: '{$message->getSubject()}'");
            }

            // if mail in emails_sentbox is found the status in emails_sentbox is updated
            $this->updateBouncedEmailRecord($message, $key, $emailRecord);
            return;
        }
    }

    /**
     * Processes the message
     * @param string $email
     * @param Message $message
     * @return true
     * @throws Exception
     */
    private function processMessageFromSender(string $email, Message $message)
    {
        $emailCode = $message->getCode();
        $plainTextBody = $this->clearHTMLBody($this->clearHTMLBody($message->getBetterBody()), true);

        if (!empty($emailCode)) {
            // get the mail from emails_sentbox taken by code and recipient
            $sentboxData = $this->fetchEmailRecord($emailCode, $email);
            if (!empty($sentboxData)) {
                //remove reply content
                $plainTextBody = $message->removeOriginalEmail($plainTextBody, $sentboxData['body']);
                // if mail is found and model id is set
                if (!empty($sentboxData['model_id'])) {
                    $parentId = $this->fetchEmailParentId($sentboxData['model'], $sentboxData['model_id']);
                }
            }
        }

        $timeFromCloselog = (int) $this->setting('ignore_status_closed_for_minutes', 0);

        if (empty($parentId) && !empty($this->setting('hashtag_field_match'))) {
            // check for hashtag
            if (preg_match('#\#([a-zA-Z0-9\-\/\.\(\)]*)(\:| |$)#', $message->getSubject(), $matches)) {
                $parentId = $this->checkForExistingDocument(
                    $this->setting('hashtag_field_match'),
                    $matches[1],
                    $this->params['start_model_type'],
                    $timeFromCloselog);
            }
        }

        if (!empty($parentId)) {
            $parent_model = $this->getDocumentSkipPermissions($parentId, $timeFromCloselog);
            // TODO: field to search for transferred ticket if the current ticket
            // status - settings
            // field name - settings
        }

        $this->getDb()->StartTrans();
        if (empty($parent_model) && !$this->setting('skip_add_new_model')) {
            //set email user for current user
            $current_user = $this->registry->get('currentUser');
            $this->registry->set('currentUser', $this->getAutomationUser(), true);
            try {
                $new_document = $this->addNewDocument($email, $message, $plainTextBody);
            } catch (\Exception $e) {
                // Revert the transaction started above if error ocures
                $this->getDb()->CompleteTrans(false);
                throw $e;
            } finally {
                // No matter the outcome return the  currentUser!
                $this->registry->set('currentUser', $current_user, true);
            }

            $parent_model = clone $new_document;
        }

        try {
            // Add communication record if the document reference is found or it is created
            if (isset($parent_model) && is_object($parent_model)) {
                switch ($this->setting('create_model_name_on_model_match')) {
                    case 'comment':
                        // meke sure to add comment only if the document already existed
                        if (empty($new_document)) {
                            $this->addReceivedComment($message, $parent_model);
                        }
                        break;
                    case 'email':
                    default:
                        $this->addReceivedEmail($message, $parent_model, empty($new_document));

                        // Make sure to send notifications only if the document already existed, as if it is created
                        // notifications are sent automatically
                        if (empty($new_document)) {
                            $this->sendNotificationEmail($parent_model);
                        }
                        break;
                }
            }
        } catch (\Exception $e) {
            // Revert the transaction started above if error ocures
            $this->getDb()->FailTrans();
            throw $e;
        } finally {
            $this->getDb()->CompleteTrans();
        }

        return true;
    }

    /**
     * @return ADODB_mysqli
     */
    private function getDb(): ADODB_mysqli
    {
        if (!is_null($this->db)) {
            return $this->db;
        }
        return $this->db = $this->registry['db'];
    }

    private function findEmailRecord($emailCode, $destinationEmail)
    {
        // get the mail from emails_sentbox taken by code and recipient
        $query = "SELECT id FROM " . DB_TABLE_EMAILS_SENTBOX . ' WHERE code = \'' . $emailCode . '\' AND recipient=\'' . $destinationEmail . '\'';
        return $this->getDb()->GetOne($query);
    }

    private function fetchEmailRecord(string $emailCode, string $destinationEmail)
    {
        // IMPORTANT!!! The LIKE search by e-mail is because of the possibility the installation to have end-point e-mail
        $query = "SELECT *, UNCOMPRESS(`body`) as body FROM " . DB_TABLE_EMAILS_SENTBOX
            . " WHERE code = '{$emailCode}' AND recipient LIKE '%{$destinationEmail}%'";
        return $this->getDb()->GetRow($query);
    }


    /**
     * Returns a string to use as a time filter of messages.
     * The specific value depends on automation setting 'operation/messageMaxAge'
     * @return array|null
     */
    private function getMsgAgeFilter(): ?array
    {
        $maxAgeFilter = null;
        $maxAgeSetting = $this->setting('operation')['messageMaxAge'] ?? false;
        if ($maxAgeSetting) {
            $time_filter = new DateTime('NOW');
            $time_filter->modify('-' . $maxAgeSetting);
            $maxAgeFilter = ['SINCE' => $time_filter->format('Y-m-d')];
        }
        return $maxAgeFilter;
    }

    /*
     * Function used from processReplaiedEmails to clear an html e-mail its main tags
     * (body, script, style, head, noscript)
     *
     * @param string $email_body - the body of the e-mail which will be processed
     * @param boolean $strip_tags - flag to mark if the e-mail will be cleared from all tags (false by default)
     * @return bool - result of operation
     */
    private function clearHTMLBody($email_body = '', $strip_tags = false)
    {
        // Search for charset in the HTML, and convert to UTF-8, before removing the meta header
        if (preg_match('/<meta[^>]+charset=([^"\s]+)"/', $email_body, $matches)) {
            $detectedEncoding = mb_detect_encoding($email_body);
            if ($detectedEncoding !== 'UTF-8') {
                $email_body = mb_convert_encoding($email_body, 'UTF-8', $detectedEncoding);
            }
            if (mb_detect_encoding($email_body) !== $matches[1] || $detectedEncoding !== 'UTF-8') {
                $email_body = preg_replace('/<meta[^>]+charset=[^>]*>/', '', $email_body);
            }
        }

        // clear the body
        $cleared_body = preg_replace('#.*<body>(.*)</body>.*#s', '$1', $email_body);
        $cleared_body = preg_replace(
            array(// Remove invisible content
                '@<head[^>]*?>.*?</head>@siu',
                '@<style[^>]*?>.*?</style>@siu',
                '@<script[^>]*?.*?</script>@siu',
                '@<noscript[^>]*?.*?</noscript>@siu',
            ),
            "",
            $cleared_body);

        if ($strip_tags) {
            // Replace td with tab
            $cleared_body = preg_replace('#(</td>)#', "$1\t", $cleared_body);
            // Replace br, tr, p, div with new lines
            $cleared_body = preg_replace('#(<br\s?/?>|</tr>|</p>|</div>)#', "$1\n", $cleared_body);
            $cleared_body = strip_tags($cleared_body);
            $cleared_body = preg_replace("#&nbsp;#", " ", $cleared_body);

            // Replace multiple new lines with maximum of 2
            $cleared_body = preg_replace("#\n(\s|&nbsp;)+\n#", "\n\n", $cleared_body);
            $cleared_body = preg_replace("#\s*\n{2,}\s*#", "\n\n", $cleared_body);
        }

        return $cleared_body;
    }

    /**
     * @param Message $email
     * @param $key
     * @param $found
     * @param $db
     * @return void
     */
    private function updateBouncedEmailRecord(Message $email, $key, $found): void
    {
        $action = $email->getAction();
        $diagnostic = $email->getAction();
        $action[$key] = !empty($action[$key]) ? $action[$key] : 'failed';
        $diagnostic[$key] = !empty($diagnostic[$key]) ? $diagnostic[$key] : 'Diagnostic string was not found';
        $set = [];
        $set[] = sprintf('`status` = "%s"', $action[$key]);
        $set[] = sprintf('`bounced` = now()');
        $set[] = sprintf('`diagnostic` = "%s"', $diagnostic[$key]);
        $query = "UPDATE " . DB_TABLE_EMAILS_SENTBOX . " SET \n" . implode(",\n", $set) . "\n" .
            "WHERE id = " . $found;
        $db = $this->getDb();
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            throw new \Exception("Can not process bounced mail: {$db->ErrorMsg()}");
        }

        require_once PH_MODULES_DIR . 'crontab/models/emails.crontabs.model.php';
        $crontab = new Emails_Crontab($this->registry, array('action' => 'inbox_emails'));
        $crontab->updateParentEmail($found);
    }

    /**
     * @param $model
     * @param $modelId
     * @return int|null
     */
    private function fetchEmailParentId(string $model, int $modelId): ?int
    {
        if ($model === 'Comment') {
            $sql = 'SELECT `model_id` FROM ' . DB_TABLE_COMMENTS . ' WHERE `model`="Document" AND `id`="' . $modelId . '";' . "\n";
            return $this->getDb()->GetOne($sql);
        }
        if ($model === 'Document') {
            return $modelId;
        }
        return null;
    }

    /**
     * @param $hashtagFieldName
     * @param $hashtagValue
     * @param $modelType
     * @param int $timeFromClosing
     * @return ?string
     */
    private function checkForExistingDocument($hashtagFieldName, $hashtagValue, $modelType, $timeFromClosing = 0)
    {
        $tbl = [
            'd' => DB_TABLE_DOCUMENTS,
            'di' => DB_TABLE_DOCUMENTS_I18N
        ];
        $lang = $this->registry['lang'];
        $sql = <<<SQL
            SELECT `id` FROM {$tbl['d']} as d
            INNER JOIN {$tbl['di']} as di ON (di.parent_id=d.id AND di.lang="{$lang}")
            WHERE `{$hashtagFieldName}`='{$hashtagValue}' AND 
                  `type`='{$modelType}' AND 
                  `active`=1 AND 
                  `deleted_by`=0 AND 
                  (
                      `status`!="closed" OR
                      DATE_ADD(d.`status_modified`, INTERVAL {$timeFromClosing} MINUTE) > NOW() OR 
                      d.`status_modified` = '0000-00-00 00:00:00'
                  )
            SQL;

        return $this->getDb()->GetOne($sql);
    }

    /**
     * @param string $email
     * @return array
     */
    private function getTheCustomerBasedOnTheEMail(string $email): array
    {
        $sql = 'SELECT c.id, c.parent_customer as parent, c1.parent_customer as grandparent' . "\n" .
            'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
            'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c1' . "\n" .
            ' ON (c1.id=c.parent_customer)' . "\n" .
            'WHERE c.email LIKE \'%' . $email . '%\' AND c.active=1 AND c.deleted_by=0' . "\n";
        $customer_match_list = $this->getDb()->GetAll($sql);

        $customers_list = [];
        foreach ($customer_match_list as $cml) {
            $key = implode('_', array_reverse(array_filter($cml)));
            if (!empty($cml['grandparent'])) {
                $customers_list[$key] = $cml['grandparent'];
            } elseif (!empty($cml['parent'])) {
                $customers_list[$key] = $cml['parent'];
            } else {
                $customers_list[$key] = $cml['id'];
            }
        }
        return $customers_list;
    }

    /**
     * @param $customer_domain
     * @param $email
     * @param $customer_id
     * @return int|null
     */
    private function getCustomerIdByEmailDomain($domainVarName, $email)
    {
        $db = $this->getDb();

        $domain = substr($email, strpos($email, '@'));

        $sql = "SELECT c.id
  FROM `" . DB_TABLE_CUSTOMERS . "` AS `c`
  JOIN `" . DB_TABLE_CUSTOMERS_CSTM . "` AS `cc`
    ON (`cc`.`model_id` = `c`.`id`
      AND `cc`.`var_id` IN (
        SELECT `id` FROM " . DB_TABLE_FIELDS_META . " WHERE `model`='Customer' AND `name`='{$domainVarName}')
      AND c.active=1 AND c.deleted_by=0
      AND `cc`.`value`='{$domain}')
  GROUP BY c.id";
        $customersIncluded = $db->GetCol($sql);

        if (count($customersIncluded) !== 1) {
            return null;
        }

        return (int)reset($customersIncluded);
    }

    /**
     * @param array $recipients
     * @param string $mailCode
     * @param Message $message
     * @param Document $parent_model
     * @param string $received_date
     * @param string $body
     * @param array $attachments_list
     * @return void
     */
    private function insertToEmailsSendBox(array $recipients,
                                           string $mailCode,
                                           Message $message,
                                           Document $parent_model,
                                           string $received_date,
                                           string $body,
                                           array $attachments_list): void
    {
        $from = $message->getFrom()->first();
        $fromaddr = $from->mail ?? ($from->mailbox . "@" . $from->host);
        $subject = General::slashesEscape($message->getSubject());
        $body = General::slashesEscape($body);
        $attachments_list = implode(',', $attachments_list);
        $values = array();
        foreach ($recipients as $origin => $origin_list) {
            foreach ($origin_list as $recipient_email => $recipient_name) {
                $values[] = sprintf("(NULL, '%s', '%s', '%s', '%s', '%s', '%s', %s, '%s', '%s', %s, '%s', '%s', '%s', '%s', '%s', %s, '%s', '%s')",
                    $mailCode,
                    $fromaddr,
                    $origin,
                    $recipient_email,
                    General::slashesEscape($recipient_name),
                    $subject,
                    "compress('{$body}')",
                    $attachments_list,
                    'received',
                    'NULL', $parent_model->modelName, $parent_model->get('id'), 0, '', '', 0, $received_date, PH_AUTOMATION_USER);
            }
        }
        if (!empty($values)) {
            $query = 'INSERT INTO ' . DB_TABLE_EMAILS_SENTBOX .
                ' (`id`, `code`, `from`, `origin`, `recipient`, `recipient_name`, `subject`, `body`, `attachments`, `status`, `diagnostic`, `model`, `model_id`, `system`, `receipt_to`, `extra`, `resent_mail_id`, `sent`, `sent_by`)' .
                'VALUES ' . implode(",\n", $values) . "\n";
            // Supress any error and convert it to \Exception, so we can catch and proccess it upstream
            if (!@$this->getDb()->Execute($query)) {
                throw new \Exception("Unable to insert to the sentbox (code: {$mailCode}, from: {$fromaddr}, origin: {$origin}, recipient: {$recipient_email}, subject: {$subject})! Original Msg: '{$this->getDb()->errorMsg()}'");
            }
        }
    }

    /**
     * @return Box
     * @throws Exception
     */
    private function getMailBox(): Box
    {
        try {
            $emailConfig = new \Nzoom\Email\Authentication\Config($this->setting('IMAP'));
            $boxFactory = new BoxFactory();
            return $this->box = $boxFactory($emailConfig);
        } catch (IdentityProviderException $e) {
            throw new \Exception('Unable to connect to the IMAP server! Check authentication provider and credentials!', null, $e);
        } catch (ProviderNotSupported $e) {
            throw new \Exception('Unable to connect to the IMAP server! Check provider value in configuration \'IMAP/providerConfig/provider\'. Supported Values are \'Azure\' and \'Google\'', null, $e);
        } catch (\Exception $e) {
            throw new \Exception('Unable to connect to the IMAP server! (Reason unknown)', null, $e);
        }
    }

    /**
     * @param $parent_model
     * @return void
     */
    private function sendNotificationEmail($parent_model): void
    {
        $notifyIncomeEmail = $this->setting('notify_income_email', false);
        if (!$notifyIncomeEmail) {
            return;
        }
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $filters = ['where' => ['e.id = ' . $notifyIncomeEmail], 'sanitize' => true];
        $template = Emails::searchOne($this->registry, $filters);

        $notified_users = [];
        if (!empty($template)) {
            $aTypes = ['owner', 'responsible', 'observer', 'decision'];
            foreach ($aTypes as $t) {
                $assignments_list = $parent_model->getAssignments($t);
                foreach ($assignments_list as $assign_id => $assignment_info) {
                    if ($assignment_info['is_portal']) {
                        continue;
                    }
                    if (in_array($assign_id, $notified_users)) {
                        continue;
                    }

                    $mailer = new Mailer($this->registry);
                    $mailer->templateName = 'custom_template';
                    $mailer->template['subject'] = $template->get('subject');
                    $mailer->template['body'] = $template->get('body');

                    $email_recipient = $this->getDb()->GetOne('SELECT `email` FROM `' . DB_TABLE_USERS . '` WHERE `id`="' . $assign_id . '"');
                    $mailer->placeholder->add('to_email', $email_recipient);
                    $mailer->placeholder->add('recipient_name', $assignment_info['assigned_to_name']);
                    $mailer->placeholder->merge($parent_model->getEmailsVars());

                    $mailer->template['sender'] = $this->registry['config']->getParam('emails', 'from_email');
                    $mailer->template['from_name'] = $this->registry['config']->getParam('emails', 'from_name');
                    $mailer->template['replyto_email'] = $this->registry['config']->getParam('emails', 'replyto_email') ?: $this->registry['config']->getParam('emails', 'from_email');
                    $mailer->template['replyto_name'] = $this->registry['config']->getParam('emails', 'replyto_name') ?: $this->registry['config']->getParam('emails', 'from_name');
                    $mailer->template['recipient'] = $email_recipient;
                    $mailer->template['recipient_name'] = $assignment_info['assigned_to_name'];

                    //send email
                    $mailer->send();
                    $notified_users[] = $assign_id;
                }
            }
        }
    }


    /**
     * @param Message $message
     * @param Document $parentModel
     * @return void
     * @throws Exception
     */
    private function addReceivedComment(Message $message, Document $parentModel): void
    {
        require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';

        $cleanedBody = $this->clearHTMLBody($message->hasHTMLBody() ? $message->getHTMLBody() : $message->getTextBody(), true);

        //add new comment
        $comment = Comments::buildModel($this->registry);
        $comment->set('model', $parentModel->modelName, true);
        $comment->set('model_id', $parentModel->get('id'), true);
        $comment->set('subject', $message->getSubject(), true);
        $comment->set('content', $cleanedBody, true);

        //save comment
        if ($comment->save()) {
            $comment->saveHistory();
        } else {
            throw new \Exception("Comment not saved: {$this->getDb()->errorMsg()}");
        }
        $attachments = $message->getAttachments();

        //add attachments if not already added
        if (!empty($attachments)) {
            // get the old model
            $oldParentModel = clone $parentModel;
            $oldParentModel->getAttachments();
            $parentModel->getAttachments();
            $this->attachFilesIfNotAttachedAndGetIds($message, $oldParentModel);
            $this->saveDocHistory('add_attachments', $parentModel, $parentModel, $oldParentModel);
        }
    }

    /**
     * @param Message $message
     * @param Document $parentModel
     * @param bool $attachFiles
     * @return void
     * @throws Exception
     */
    private function addReceivedEmail(Message $message, Document $parentModel, bool $attachFiles = false): void
    {
        $body = $this->clearHTMLBody($message->hasHTMLBody() ? $message->getHTMLBody() : $message->getTextBody());
        $from = $message->getFrom()->first();
        $fromaddr = $from->mail ?? ($from->mailbox . "@" . $from->host);
        $subject = General::slashesEscape($message->getSubject());

        $attachmentsList = [];
        if ($attachFiles) {
            $attachments = $message->getAttachments();
            if (!empty($attachments)) {
                $old_parent_model = clone $parentModel;
                $old_parent_model->getAttachments();

                $attachmentsList = $this->attachFilesIfNotAttachedAndGetIds($message, $parentModel);
                $parentModel->getAttachments();
                $this->saveDocHistory('add_attachments', $parentModel, $parentModel, $old_parent_model);
            }
        }

        $recipients = [
            'to' => [],
            'cc' => []
        ];

        $toValues = $message->get('to');
        if (!empty($toValues)) {
            foreach ($toValues->all() as $to) {
                $recipients['to'][strtolower($to->mail)] = strtolower($to->personal);
            }
        }

        $ccValues = $message->get('cc');
        if (!empty($ccValues)) {
            foreach ($ccValues->all() as $cc) {
                $recipients['cc'][strtolower($cc->mail)] = strtolower($cc->personal);
            }
        }
        $settingsEmail = strtolower($this->setting('operation')['email']);
        if (!array_key_exists($settingsEmail, $recipients['to']) && !array_key_exists($settingsEmail, $recipients['cc'])) {
            // if the current e-mail is not in TO or CC, fill it in TO
            $recipients['to'][$settingsEmail] = '';
        }

        // complete the e-mail data
        $mailCode = uniqid('', true);
        $parentModel->set('mail_code', $mailCode, true);
        $parentModel->set('mail_from', $fromaddr, true);
        $parentModel->set('mail_to', array_keys($recipients['to']), true);
        $parentModel->set('mail_cc', array_keys($recipients['cc']), true);
        $parentModel->set('email_subject', $subject, true);
        $parentModel->set('body_formated', $body, true);
        $parentModel->set('attached_files', $attachmentsList, true);
        $this->saveDocHistory('receive_email', $parentModel, $parentModel, $parentModel);

        $this->insertToEmailsSendBox(
            $recipients,
            $mailCode,
            $message,
            $parentModel,
            $this->date2Iso($message->getDate()),
            $body,
            $attachmentsList);
    }

    public function saveDocHistory($actionType, $model, $new_model, $old_model)
    {
        Documents_History::saveData($this->registry, [
            'model' => $model,
            'action_type' => $actionType,
            'new_model' => $new_model,
            'old_model' => $old_model]);
    }

    /**
     * @param $attachment
     * @param $attfilename
     * @param $model
     * @return bool|mixed
     * @throws Exception
     */
    public function attachFileToModel($attachment, $attfilename, $model)
    {
        $result = true;
        // if the attachment is message and the name is not detected properlly from the subject, we set a default name
        if ($attfilename === 'attached_message.eml') {
            if (!isset($model->attachedMessages)) {
                $model->attachedMessages = 0;
            }

            $attfilename = 'attached_message_' . ++$model->attachedMessages . '.eml';
        }

        $tmpfname = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $attfilename;
        $handle = fopen($tmpfname, 'wb');
        fwrite($handle, $attachment);
        fclose($handle);
        $file = array(
            'name' => $attfilename,
            'type' => mime_content_type($tmpfname),
            'tmp_name' => $tmpfname,
            'error' => '',
            'size' => filesize($tmpfname));
        $file_params = array(
            'id' => '',
            'name' => $attfilename,
            'filename' => $attfilename,
            'description' => $attfilename,
            'revision' => '',
            'permission' => 'all');
        $rev_params = array('model_id' => $model->get('id'),
            'filename' => $file['name'],
            'origin' => 'attached');
        $file_params['revision'] = Files::getLatestRevision($this->registry, $rev_params);
        $file_path_function_name = 'define' . str_replace('_', '', $model->modelName) . 'FilePath';
        $file_path = Files::$file_path_function_name($file, $file_params, $model);

        $destination = $file_path['destination'];
        $filename = FilesLib::removeSpecialChars($file_path['new_filename']);
        if (substr($destination, -1) != '/') {
            //put a slash at the end of the destination folder
            $destination .= '/';
        }

        if (!is_dir($destination)) {
            //create destination folder
            FilesLib::createDir($destination);
        }

        //check for restrictions
        if (empty($restrictions['upload_max_filesize'])) {
            $restrictions['upload_max_filesize'] = ini_get('upload_max_filesize');
        }
        $maxfilesize = General::convertBytes($restrictions['upload_max_filesize']);
        if ($file['size'] > $maxfilesize) {
            $result = false;
        }

        if (!empty($restrictions['upload_min_filesize']) && $file['size'] < General::convertBytes($restrictions['upload_min_filesize'])) {
            $result = false;
        }

        //check for extension restrictions
        if (!empty($restrictions['allowed_extensions']) && is_string($restrictions['allowed_extensions'])) {
            $restrictions['allowed_extensions'] = preg_split('#\s*(,|;)\s*#', $restrictions['allowed_extensions']);
        }

        if (empty($restrictions['forbidden_extensions'])) {
            $restrictions['forbidden_extensions'] = array('php', 'js', 'vbs', 'exe', 'com');
        } elseif (is_string($restrictions['forbidden_extensions'])) {
            $restrictions['forbidden_extensions'] = preg_split('#\s*(,|;)\s*#', $restrictions['forbidden_extensions']);
        }

        $fileinfo = pathinfo($file['name']);

        //check for allowed extensions
        if (!empty($restrictions['allowed_extensions']) && !in_array(strtolower($fileinfo['extension']??''), array_map("strtolower", $restrictions['allowed_extensions']))) {
            $result = false;
        }

        //check for not allowed extensions
        if (!empty($restrictions['forbidden_extensions']) && in_array(strtolower($fileinfo['extension']??''), array_map("strtolower", $restrictions['forbidden_extensions']))) {
            $result = false;
        }

        if ($result) {
            // If different files have the same name, or the name conflicts after transliteration - add random chars on the end of the filename
            $orig_filename = $filename;
            while (file_exists($destination . $filename)) {
                $fileNameParts = explode('.', $orig_filename);
                $fileNameParts[count($fileNameParts) - 2] .= '_' . bin2hex(random_bytes('2'));
                $filename = implode('.', $fileNameParts);
            }
            if (@copy($file['tmp_name'], $destination . $filename)) {
                @chmod($destination . $filename, 0777);
                $file_params['model_id'] = $model->get('id');
                $file_params['model'] = $model->modelName;
                $file_params['model_lang'] = $model->get('model_lang');
                $file_params['filename'] = $file['name'];
                $file_params['path'] = $destination . $filename;
                $file_params['origin'] = 'attached';

                $oAttachment = new File($this->registry, $file_params);
                $attachment_saved = $oAttachment->save();
                if (!$attachment_saved) {
                    //remove the file if the save procedure fails
                    //the failure could occur only due to SQL error
                    @unlink($destination . $filename);
                    $result = false;
                } else {
                    $result = $oAttachment->get('id');
                }
            } else {
                $result = false;
            }
        }
        unlink($tmpfname);

        return $result;
    }

    /**
     * Returns data from the document type
     * @return array|false
     */
    private function getTypeData($modelType)
    {
        $db = $this->getDb();
        $sql = 'SELECT dt.*, dti18n.*' . "\n" .
            'FROM ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . " as dti18n" . "\n" .
            '  ON (dti18n.parent_id=dt.id AND dti18n.lang="' . $this->registry['lang'] . '")' . "\n" .
            'WHERE dt.id=' . $modelType;
        return $db->GetRow($sql);
    }

    /**
     * @param Document $document
     * @param string $email
     * @param Customer $customer
     * @param array $defaultDocVars
     * @return void
     */
    private function fillDocumentVars(Document $document, string $email, Customer $customer, array $defaultDocVars): void
    {
        $custToDocVars = $this->getCustomerDocVarRelations();
        $document->set('plain_vars', null, true);
        $document->getVars();
        $tmpVars = $document->get('vars');

        $customerAssocVars = array();

        foreach ($tmpVars as $idx => $var) {
            if ($var['name'] == 'client_mail') {
                $tmpVars[$idx]['value'] = $email;
            } elseif ($var['name'] == 'ticket_priority') {
                $tmpVars[$idx]['value'] = $this->setting('ticket_priority');
            } elseif (in_array($var['name'], $custToDocVars)) {
                if (empty($customerAssocVars)) {
                    $customerAssocVars = $customer->getAssocVars();
                }
                $customerVar = array_search($var['name'], $custToDocVars);
                if (!empty($customerAssocVars[$customerVar]['value'])) {
                    $tmpVars[$idx]['value'] = $customerAssocVars[$customerVar]['value'];
                } else {
                    $tmpVars[$idx]['value'] = (!empty($defaultDocVars[$var['name']]) ? $defaultDocVars[$var['name']] : '');
                }
            } elseif (!empty($defaultDocVars[$var['name']])) {
                $tmpVars[$idx]['value'] = $defaultDocVars[$var['name']];
            }
        }

        $document->set('vars', $tmpVars, true);
        $document->saveVars();
    }

    /**
     * @param array $default_doc_basic_vars
     * @param array $default_doc_vars
     * @return array
     */
    private function getDefaultDocVars(): array
    {
        $basicVars = [];
        $vars = [];
        $defaultDocVars = array_filter(preg_split('#\s*,\s*#', $this->setting('default_doc_vars')));
        foreach ($defaultDocVars as $rel) {
            if (strpos($rel, '=>') === false) {
                continue;
            }

            list($name, $value) = preg_split('#\s*=\>\s*#', $rel);
            $name = trim($name);
            $value = trim($value);
            if (empty($name)) {
                continue;
            }

            if (preg_match('#^b_#', $name) && !empty($value)) {
                $name = preg_replace('#^b_#', '', $name);
                $basicVars[$name] = $value;
                continue;
            }

            $vars[$name] = $value;
        }
        return array($basicVars, $vars);
    }

    /**
     * Rturns vars from customer to be set in the document
     * @return array|false|string[]
     */
    private function getCustomerDocVarSettings()
    {
        $custToDocVars = $this->setting('cust_to_doc_vars', false);

        if (!$custToDocVars) {
            return [];
        }

        return array_filter(preg_split('#\s*,\s*#', $custToDocVars));
    }

    /**
     * @return array
     */
    private function getCustomerDocVarRelations(): array
    {
        $custToDocVars = array();

        foreach ($this->getCustomerDocVarSettings() as $rel) {
            if (strpos($rel, '=>') === false) {
                continue;
            }
            list($custVar, $docVar) = preg_split('#\s*=\>\s*#', $rel);
            if (!empty($custVar) && !empty($docVar)) {
                $custToDocVars[$custVar] = $docVar;
            }
        }
        return $custToDocVars;
    }

    /**
     * @param Document $document
     * @return Document|false
     */
    private function assignObserversIfNeeded(Document $document)
    {
        if (!$this->setting('assign_observers', false)) {
            return $document;
        }

        $assignUsers = preg_split('#\s*,\s*#', $this->setting('assign_observers'));
        $assignUsers = array_filter($assignUsers);

        if (empty($assignUsers)) {
            return $document;
        }

        // Update the observers
        $assignmentsParams = array(
            'model_id' => $document->get('id'),
            'module' => 'documents',
            'model' => $document,
            'new_assign_observer' => implode(',', $assignUsers)
        );

        if ($this->assign($assignmentsParams)) {
            ;
            $assignDocument = $this->getDocumentWithModelLang($document->get('id'), $document->get('model_lang'));
            return clone $assignDocument;
        }

        $this->getDb()->FailTrans();
        return false;
    }

    /**
     * @param $id
     * @return Document|false
     */
    private function getDocument($id)
    {
        $filters = ['where' => ["d.id = '{$id}'"]];
        return Documents::searchOne($this->registry, $filters);
    }

    /**
     * @param $id
     * @return Document|false
     */
    private function getDocumentWithModelLang($id, $modelLang)
    {
        $filters = ['where' => ["d.id = '{$id}'"], 'model_lang' => $modelLang];
        return Documents::searchOne($this->registry, $filters);
    }

    /**
     * @param $id
     * @param $onlyOpenedDocuments
     * @return Document|false
     */
    private function getDocumentSkipPermissions($id, int $timeFromClosing = 0)
    {
        $filters = [
            'where' => [
                "d.id = '{$id}'",
                'd.`active`=1',
                'd.`deleted_by`=0',
            ],
            'skip_permissions_check' => true
        ];

        $filters['where'][] = <<<WHERE
            (
                d.`status`!="closed" OR
                DATE_ADD(d.`status_modified`, INTERVAL {$timeFromClosing} MINUTE) > NOW() OR
                d.`status_modified`= '0000-00-00 00:00:00'
            )
            WHERE;

        return Documents::searchOne($this->registry, $filters);
    }


    /**
     * @param $customer
     * @param array $customersList
     * @param Document $document
     * @return void
     */
    private function documentSetCustomer($customer, array $customersList, Document $document): void
    {
        if ($customer->get('id') != $this->setting('default_customer') && $customer->get('is_company')) {
            // get the branch and contact person
            $branch = '';
            $contactPerson = '';
            foreach ($customersList as $k_cl => $cl) {
                $customerElements = explode('_', $k_cl);
                if (isset($customerElements[2])) {
                    // give priority to contact persons with lower ids
                    if ($contactPerson == '' || $contactPerson < $customerElements[2]) {
                        $branch = $customerElements[1];
                        $contactPerson = $customerElements[2];
                    }
                } elseif (isset($customerElements[1])) {
                    // check branch and set new one but only if contact person is not already set
                    if (!$contactPerson && ($branch == '' || $branch < $customerElements[1])) {
                        $branch = $customerElements[1];
                    }
                }
            }

            $branches = $customer->getBranches();
            if ($branch) {
                foreach ($branches as $k => $br) {
                    if ($br->get('id') != $branch) {
                        unset($branches[$k]);
                    }
                }
                $document->set('branch', $branch, true);
            } else {
                // check if main branch is selected
                foreach ($branches as $k => $br) {
                    if ($br->get('is_main')) {
                        $document->set('branch', $br->get('id'), true);
                    } else {
                        unset($branches[$k]);
                    }
                }
            }

            // get contact person
            if ($contactPerson) {
                $document->set('contact_person', $contactPerson, true);
            } elseif ($document->get('branch')) {
                $contact_persons = $customer->getContactPersons(array($document->get('branch')), true);
                $main_cp = reset($contact_persons[$document->get('branch')]);
                if ($main_cp) {
                    $document->set('contact_person', $main_cp->get('id'), true);
                }
            }
        }
    }

    /**
     * @param $customer
     * @param Document $document
     * @return void
     */
    private function documentSetTrademark($customer, Document $document): void
    {
        $trademarks = $customer->getTrademarks();
        $trademark = reset($trademarks);
        $document->set('trademark', ($trademark['id'] ?? ''), true);
    }

    /**
     * @param array $customersList
     * @param string $email
     * @return int
     * @throws Exception
     */
    private function getCustomerId(array $customersList, string $email): int
    {
        $customersIds = array_unique(array_values($customersList));

        if (count($customersIds) === 1) {
            return (int) reset($customersIds);
        }

        $customerDomainVarName = $this->setting('customer_domain', false);
        if ($customerDomainVarName) {
            $customer = $this->getCustomerIdByEmailDomain($customerDomainVarName, $email);
            if ($customer) {
                return $customer;
            }
        }

        $defaultCustomer = $this->setting('default_customer', false);
        if (!$defaultCustomer) {
            throw new \Exception("'default_customer' setting is not provided in automation settings!");
        }

        return (int) $defaultCustomer;
    }

    /**
     * @param string $email
     * @param Message $message
     * @param string $cleanedPlainTextBody
     * @return Document|null
     * @throws Exception
     */
    private function addNewDocument(string $email, Message $message, string $cleanedPlainTextBody): ?Document
    {
        $customersList = $this->getTheCustomerBasedOnTheEMail($email);
        $customerId = $this->getCustomerId($customersList, $email);

        if (!$customerId) {
            return null;
        }

        $customer = $this->getCustomer($customerId);
        $this->registry->set('edit_all', true, true);

        //add new document
        $document = Documents::buildModel($this->registry);
        $type_data = $this->getTypeData($this->setting('create_model_type_id'));

        // get the name from the subject
        $subject = preg_replace('/[^a-zA-Z\dа-яА-Я\-_.,!@#$%^&*()+=\[\]{}\\\|;:\'"„“<>?\s\/]+/u', '', trim($message->getSubject()));
        if (!$subject) {
            // if subject is empty, get from the type. Otherwise - get from the settings
            if ($type_data['default_name']) {
                $subject = $type_data['default_name'];
            } else {
                $subject = ' ';
            }
        }

        $document->set('type', $type_data['id'], true);
        $document->set('date', $this->date2Iso($message->getDate()), true);
        $document->set('department', $type_data['default_department'], true);
        $document->set('name', $subject, true);
        $document->set('description', $cleanedPlainTextBody, true);
        $document->set('group', $type_data['default_group'], true);
        $document->set('employee', $this->setting('employee'), true);
        $document->set($this->setting('validity_term_field', false), General::strftime('%Y-%m-%d', strtotime($this->setting('validity_term_calc_period'))), true);
        $document->set('customer', $customer->get('id'), true);
        if ($type_data['generate_system_task']) {
            $document->set('generate_system_task', 1, true);
        }

        $this->documentSetCustomer($customer, $customersList, $document);
        $this->documentSetTrademark($customer, $document);

        list($defaultDocBasicVars, $defaultDocVars) = $this->getDefaultDocVars();

        // complete the basic vars
        foreach ($defaultDocBasicVars as $vn => $vv) {
            $document->set($vn, $vv, true);
        }

        try {
            // save document
            if (!$document->save()) {
                throw new \Exception("Unable to save document: '{$this->getDb()->errorMsg()}'");
            }

            $this->fillDocumentVars($document, $email, $customer, $defaultDocVars);

            $newDocument = $this->getDocument($document->get('id'));

            $this->attachFilesIfNotAttachedAndGetIds($message, $newDocument);

            if ($this->getDb()->HasFailedTrans()) {
                throw new \Exception("Unable to save document additional vars: '{$this->getDb()->errorMsg()}'");
            }

            $oldModel = new Document($this->registry);
            $oldModel->sanitize();

            $this->saveDocHistory('add', $newDocument, $newDocument, $oldModel);

            $newDocument = $this->assignObserversIfNeeded($newDocument);

            if (!$this->executeActionAutomations($oldModel, $newDocument, 'add')) {
                throw new \Exception("Faild Automations after Document add: '{$this->getDb()->errorMsg()}'");
            }
        } catch (\Exception $e) {
            throw new \Exception('Document not added', null, $e);
        }

        return $newDocument ?: null;
    }

    /**
     * @param int $customerId
     * @return mixed
     */
    private function getCustomer(int $customerId)
    {
        if (array_key_exists($customerId, $this->customersCache)) {
            return $this->customersCache[$customerId];
        }

        $filters = [
            'where' => array('c.id = ' . $customerId),
            'skip_permissions_check' => true
        ];
        $customer = Customers::searchOne($this->registry, $filters);

        return $this->customersCache[$customerId] = $customer;
    }

    /**
     * @param string $dateStr
     * @return string
     */
    private function date2Iso(string $dateStr): string
    {
        return General::strftime($this->i18n('date_iso'), strtotime($dateStr));

    }

    /**
     * @param Exception|Error $e
     * @return void
     */
    private function logError($label, $e = null): void
    {
        General::log($this->registry,
            $label,
            ($e ? "Exceptions:\n {$e->getMessage()}\n on {$e->getFile()}:{$e->getLine()}": '')
            . "\n\nautomation.id: " . $this->params['id']
            . "\nautomation.start_model_type: " . $this->params['start_model_type']
            . "\nautomation.settings: " . var_export($this->settings, true));
    }

    /**
     * @param Message $message
     * @param Document $newDocument
     * @return void
     */
    private function attachFilesIfNotAttachedAndGetIds(Message $message, Document $newDocument): array
    {
        $key = "{$message->getSequenceId()}";
        if (array_key_exists($key, $this->attachmentsCache)) {
            return $this->attachmentsCache[$key];
        }
        $attachments = $message->getAttachments();
        $list = [];
        foreach ($attachments ?? [] as $att) {
            $attachment = $att->mask();
            $attach_res = $this->attachFileToModel($attachment->getContent(), $attachment->getName(), $newDocument);
            if ($attach_res) {
                $list[] = $attach_res;
            }
        }
        return $this->attachmentsCache[$key] = $list;
    }

    /**
     * @param Box $box
     * @return MessageListPaginated|MessageCollection|LengthAwarePaginator|Iterator
     * @throws ConnectionFailedException
     * @throws FolderFetchingException
     * @throws GetMessagesFailedException
     * @throws \Webklex\PHPIMAP\Exceptions\InvalidWhereQueryCriteriaException
     * @throws \Webklex\PHPIMAP\Exceptions\RuntimeException
     */
    private function getMessagePages(Box $box)
    {
        $operation = $this->setting('operation');
        $path = $operation['readFolder'] ?? 'INBOX';
        $chunkSize = $operation['chunkSize'] ?? '10';
        $ageFilter = $this->getMsgAgeFilter();
        try {
            $pathEncoded = \mb_convert_encoding($path, "UTF7-IMAP");
            $messages = $box->getMessagesFromPath($pathEncoded, $chunkSize, $ageFilter, 'desc');
        } catch (\Exception | \Error $e) {
            $this->executionErrors[] = $e->getMessage();
            throw new Exception("BGS->processRepliedEmails() - getMessagesFromPath [Error] {Cant retrieve the messages}! Path: '{$path}', ChunkSize: '{$chunkSize}', ageFilter: '".var_export($ageFilter, true)."'", null, $e);
        }
        return $messages ?? [];
    }

    private static function decryptSecretValue($encryptedValue) {
        return General::decrypt($encryptedValue, self::$IMAP_PASSWORD_KEY, self::$IMAP_PASSWORD_ALGO);
    }

    private static function decryptSecretSettings($settings) {
        $imapConfig = &$settings['IMAP'];
        if (array_key_exists('password', $imapConfig) && $imapConfig['password'] !== '') {
            $imapConfig['password'] = self::decryptSecretValue($imapConfig['password']);
        }
        if (array_key_exists('providerConfig', $imapConfig)
            && array_key_exists('refreshToken', $imapConfig['providerConfig']) && $imapConfig['providerConfig']['refreshToken'] !== '') {
            $imapConfig['providerConfig']['refreshToken'] = self::decryptSecretValue($imapConfig['providerConfig']['refreshToken']);
        }
        return $settings;
    }

    private function parseSettings() {
        if(array_key_exists('IMAP', $this->settings)) {
            // Modern settings mode!
            $this->settingsParsed = self::decryptSecretSettings($this->settings);
            return;
        }
        // Backward compatibility mode
        $parsed = [];
        foreach ($this->settings as $k=>$v) {
            $parts = explode('__', $k);
            if (!$parts) {
                continue;
            }
            $item = &$parsed;
            foreach ($parts as $p) {
                if ($item && !array_key_exists($p, $item)) {
                    $item[$p] = [];
                }
                $item = &$item[$p];
            }
            if (is_string($v)) {
                switch ($v) {
                    case 'false': $v = false; break;
                    case 'true': $v = true; break;
                    default:
                        if (is_numeric($v)) {
                            $v = $v + 0;
                        }
                }
            }
            $item = $v;
        }
        $this->settingsParsed = self::decryptSecretSettings($parsed);
    }

    /**
     * @param $name
     * @param $default
     * @return string|array|int|Object|null
     */
    private function setting($name, $default = null)
    {
        if (is_null($this->settingsParsed)) {
            $this->parseSettings();
        }
        return $this->settingsParsed[$name] ?? $default;
    }

    /**
     * @return void
     */
    private function setTimeLimit(): void
    {
        $this->oldTimeLimit = ini_get('max_execution_time');
        set_time_limit(self::$timeLimit);
    }

    public function revertTimeLimit()
    {
        set_time_limit($this->oldTimeLimit);
    }

    /**
     * @param string $errorLogString
     * @param $mails
     * @return true|void
     */
    private function sendErrorReport(string $toEmails, string $errorLogString)
    {
        $template = 'crontab_errors_notification';
        $model = new Model($this->registry);

        if (!$model->shouldSendEmail($template)) {
            return false;
        }

        $mailer = new Mailer($this->registry, $template);
        $mailer->placeholder->add('action', 'processRepliedEmails:' . $this->params['id']);
        $mailer->placeholder->add('errors', "<pre>$errorLogString</pre>");
        $mailer->placeholder->add('to_email', $toEmails);
        $mailer->placeholder->add('installation', $this->registry['config']->getParam('sys', 'code'));

        //send email
        return $mailer->send();
    }

    /**
     * @param string $message
     * @param string $subject
     * @param string $from
     * @return string
     */
    private function getErrorString(string $message, string $subject, string $from): string
    {
        return $message . "\n  в '{$subject}'\n  от {$from}";
    }
}
