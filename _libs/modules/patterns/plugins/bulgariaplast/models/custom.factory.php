<?php

use Nzoom\I18n\I18n;

/**
 * Custom plugin to prepare data for print/generation for BULGARIPLAST
 */
Class Custom_Factory extends Model_Factory
{
    public $folderName = 'bulgariaplast';
    public static $registry;
    public static $model;
    public static $pattern;
    public static $settings;
    public static $filePath;
    public static $data;

    /**
     * Translates custom labels defined in the i18n of the plugin
     */
    public static function i18n($param, $lang = '')
    {
        if (empty($lang)) {
            $lang = self::$model->get('model_lang');
        }
        $custom_file = realpath(dirname(__FILE__) . '/../i18n/' . $lang) . '/print.ini';
        $i18n = new I18n($lang, $custom_file);
        return $i18n->translate($param);
    }


    /**
     * Prepare order labels
     *
     * @param object $registry - registry object
     * @param object $model - document
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function prepareOrder($registry, $model, $pattern, &$params = array())
    {

        self::$registry = $registry;
        $db = $registry['db'];
        $lang = $registry['lang'];

        $assocVars = $model->getAssocVars();
        $accessory_ids = array_column(
            $assocVars['accessories_services_group']['values']?:[],
            array_flip($assocVars['accessories_services_group']['names'])['accessories_services_id']
        );
        $glazing_ids = array_column(
            $assocVars['glazing_group']['values']?:[],
            array_flip($assocVars['glazing_group']['names'])['glazing_id']
        );
        $allNomIds = array_filter(array_unique(array_merge($accessory_ids, $glazing_ids)));
        $allNomIdsCSV = implode(', ', $allNomIds);

        if (empty($allNomIds)) {
            //no data, nothing to do here
            return true;

        }

        //ToDo: define nom types in a setting
        $query = "SELECT nc.model_id, nc.value as form_id, fo.label as name
        FROM " . DB_TABLE_NOMENCLATURES . " n
        JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " nc
          ON n.id=nc.model_id AND n.type IN (8,11,13)
        JOIN " . DB_TABLE_FIELDS_META . " fm
          ON fm.name='form_type' AND fm.id=nc.var_id AND nc.model_id IN ({$allNomIdsCSV}) AND nc.num!=0
        JOIN " . DB_TABLE_FIELDS_OPTIONS . " fo
          ON nc.value=fo.option_value AND fo.parent_name='form_type' AND fo.lang='{$lang}'";
        $noms2formsAll = $db->getAll($query);
        
        $noms2forms = array();
        foreach($noms2formsAll as $n2f) {
            $noms2forms[$n2f['model_id']][$n2f['form_id']] = $n2f['name'];
        }

        $formNames = array_combine(
            array_column($noms2formsAll, 'form_id'),
            array_column($noms2formsAll, 'name')
        );

        $contentAllForms = array();
        $originalContent = $pattern->get('content');
        $originalAssocVars = $assocVars;
        foreach($formNames as $formId => $formName) {
            //each form on a separate page

            $assocVars = $originalAssocVars;
            $formContent = $originalContent;

            //remove the group items that are not in the form
            //glazing_group
            $gIDX = array_flip($assocVars['glazing_group']['names'])['glazing_id'];
            foreach($assocVars['glazing_group']['values'] as $ridx => $rowValues) {
                //glazing Id
                $glazingId = $rowValues[$gIDX];
                //corresponding form of the glazing
                $glazingFormIds = $noms2forms[$glazingId]??[];
                // remove the items that are not in the form id
                if (empty($glazingFormIds) || !array_key_exists($formId, $glazingFormIds)) {
                    unset($assocVars['glazing_group']['values'][$ridx]);
                }
            }

            //accessories_services_group
            $aIDX = array_flip($assocVars['accessories_services_group']['names'])['accessories_services_id'];
            foreach($assocVars['accessories_services_group']['values'] as $ridx => $rowValues) {
                //glazing Id
                $accessoryId = $rowValues[$aIDX];
                //corresponding form of the glazing
                $accessoryFormIds = $noms2forms[$accessoryId]??[];
                // remove the items that are not in the form id
                if (empty($accessoryFormIds) || !array_key_exists($formId, $accessoryFormIds)) {
                    unset($assocVars['accessories_services_group']['values'][$ridx]);
                }
            }

            $model->extender->add('a_accessories_services_group', self::prepareGroup($assocVars['accessories_services_group'], $pattern->get('id')));
            $model->extender->add('a_glazing_group', self::prepareGroup($assocVars['glazing_group'], $pattern->get('id')));
            $model->extender->add('form_type', $formNames[$formId]);
            $formContent = $model->extender->expand($formContent);
            $contentAllForms[] = $formContent;
        }

        $pattern->set('content', implode('[system_pagebreak]', $contentAllForms), true);

        return true;

    }

    /**
     * Prepares grouping table for PDF
     *
     * @param $agroupVar
     * @param $patternId
     */
    private static function prepareGroup($agroupVar, $patternId)
    {
        $registry = self::$registry;

        // Skip this grouping table if it has no rows or it has only one row which is empty
        if (empty($agroupVar['values']) || count($agroupVar['values']) == 1 && count(
                array_filter(reset($agroupVar['values']), function ($a) {
                    return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));
                })
            ) == 0) {
            return '';
        }

        $template_file = 'grouping_vars.html';

        $groupingViewer = new Viewer($registry);
        $groupingViewer->setFrameset($registry['theme']->templatesDir . $template_file);

        // checks if source contains 'replace_value' params and
        // changes the name content with the required replacements
        foreach ($agroupVar['names'] as $idx_var => $var_name) {
            $agroupVar['width'] = $agroupVar['width_print'];

            if (!empty($agroupVar['source'][$var_name])) {
                // parse the params
                $source_fields = General::parseSettings($agroupVar['source'][$var_name]);

                // checks if 'replace_value' param is set
                if (!empty($source_fields['replace_value'])) {
                    // find the replacement variable name
                    $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                    // check if there is a var with the required name and replace it
                    if ($replaced_var && in_array($replaced_var, $agroupVar['names'])) {
                        $column_key_idx = array_search($replaced_var, $agroupVar['names']);
                        foreach ($agroupVar['values'] as $row => $row_content) {
                            $agroupVar['values'][$row][$column_key_idx] =
                                str_replace(
                                    '[a_' . $replaced_var . ']',
                                    $row_content[$column_key_idx],
                                    $source_fields['replace_value']
                                );
                        }

                        // sets the option to overwrite a value and use it
                        // but not searching for its corresponding label
                        if (in_array($agroupVar['types'][$column_key_idx], array('dropdown', 'radio', 'checkbox'))) {
                            $agroupVar[$var_name]['overwrite_value'] = true;
                        }
                    }
                }
            }
        }

        //check if there are empty rows in the table and remove them
        if (isset($agroupVar['values']) && is_array($agroupVar['values'])) {
            $row_is_empty = true;
            foreach ($agroupVar['values'] as $row_index => $row_content) {
                foreach ($row_content as $cell_index => $cell_content) {
                    if ($cell_content || $cell_content === '0') {
                        $row_is_empty = false;
                    }
                }
                if ($row_is_empty) {
                    unset($agroupVar['values'][$row_index]);
                } else {
                    $row_is_empty = true;
                }
            }
        }

        foreach ($agroupVar['types'] as $key_column => $var_type) {
            if ($var_type == 'file_upload') {
                $group_var_name = $agroupVar['names'][$key_column];
                if (!empty($agroupVar['values'])) {
                    foreach ($agroupVar['values'] as $row => $row_values) {
                        if (!empty($row_values[$key_column]) && is_object(
                                $row_values[$key_column]
                            ) && !$row_values[$key_column]->get('not_exist') && !$row_values[$key_column]->get(
                                'deleted_by'
                            )) {
                            $file = $row_values[$key_column];
                            if (isset($agroupVar[$group_var_name]['view_mode']) && $agroupVar[$group_var_name]['view_mode'] == 'thumbnail' && $row_values[$key_column]->isImage(
                                )) {
                                $file_upload_properties = $agroupVar[$group_var_name];
                                $value = sprintf(
                                    '<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $registry['module_param'],
                                    rawurlencode(General::encrypt($file->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($file_upload_properties['thumb_width']) ? ("&maxwidth=" . $file_upload_properties['thumb_width']) : ''),
                                    (!empty($file_upload_properties['thumb_height']) ? ("&maxheight=" . $file_upload_properties['thumb_height']) : '')
                                );
                                $agroupVar['values'][$row][$key_column] = $value;
                            } else {
                                $agroupVar['values'][$row][$key_column] = $file->get('name');
                            }
                        } else {
                            $agroupVar['values'][$row][$key_column] = '';
                        }
                    }
                }
            }
        }

        $groupingViewer->data['var'] = $agroupVar;
        $groupingViewer->data['pattern_id'] = $patternId;

        return $groupingViewer->fetch();
    }

    /**
     * Prepare waybill
     *
     * @param object $registry - registry object
     * @param object $model - document
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function prepareWaybill($registry, $model, $pattern, &$params = array()) {
        $assocVars = $model->getAssocVars();
        $lang = $model->get('model_lang');
        $db = $registry['db'];

        //get the route
        $transportRoute = array_combine(
            array_column($assocVars['transport_route_group']['assoc_values'] ?? array(), 'transport_route_id'),
            array_column($assocVars['transport_route_group']['assoc_values'] ?? array(), 'transport_route')
        );

        $transportRequests = $assocVars['transport_group']['assoc_values'];
        $requestIds = array_column($assocVars['transport_group']['assoc_values'], 'request_id');
        $requestIdsCSV = implode(',', $requestIds);

        $query = "
        SELECT d.id, d.office as office_id,
               oi.name as office_name, 
               oi.description as office_description, 
               dc.value as kind_production,
               dc2.value as production_article_id,
               nc.value as schedule_position
        FROM " . DB_TABLE_DOCUMENTS . " d
        JOIN " . DB_TABLE_OFFICES_I18N . " oi
          ON d.office = oi.parent_id AND oi.lang='{$lang}'
        JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc
          ON d.id=dc.model_id AND dc.var_id=4814 AND dc.num=1
        JOIN " . DB_TABLE_DOCUMENTS_CSTM . " dc2
          ON d.id=dc2.model_id AND dc2.var_id=4815 AND dc.num=1
        LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " nc
          ON dc2.value=nc.model_id AND nc.var_id=1503 AND nc.num=1
        WHERE d.id IN ({$requestIdsCSV})";
        $requestData = $db->GetAssoc($query);

        //add request data
        foreach($transportRequests as $idx => $transportRequest) {
            $transportRequests[$idx] = array_merge($transportRequest, $requestData[$transportRequest['request_id']]??[]);
        }

        //sort
        usort($transportRequests, function ($a, $b) use ($transportRoute) {
            return self::_sortTransportRequest($a, $b, array_keys($transportRoute));
        });

        //sum the payment_amount
        $prevOrderNum = '';
        $prevOrderIdx = '';
        foreach($transportRequests as $idx => $transportRequest) {
            $orderNum = $transportRequest['order_full_num'];
            if ($orderNum != $prevOrderNum) {
                $prevOrderNum = $orderNum;
                $prevOrderIdx = $idx;
                $transportRequests[$idx]['payment_amount_rowspan'] = 1;
                $transportRequests[$idx]['payment_amount'] = (float) $transportRequest['payment_amount'];
            } else {
                $transportRequests[$prevOrderIdx]['payment_amount'] += (float) $transportRequest['payment_amount'];
                $transportRequests[$prevOrderIdx]['payment_amount_rowspan']++;
                unset($transportRequests[$idx]['payment_amount']);
            }
        }

        //highlight order description
        foreach($transportRequests as $idx => $transportRequest) {
            $description = $transportRequest['order_description'];
            $description = preg_replace('#(Брой:[^\r\n]*)#ui', '<span style="font-weight: bold">$1</span>', $description);
            $description = preg_replace_callback('#(Аксесоари:([^\r\n]*[^:]*(\r\n|$)))#umi',
                function($match) { return '<span style="font-weight: bold">' . trim($match[1]) . '</span>'; },
                $description
            );
            $transportRequests[$idx]['order_description'] = $description;
        }

        //replace the file upload
        foreach($transportRequests as $idx => $transportRequest) {
            if (!empty($transportRequest['file_panel']) && is_a($transportRequest['file_panel'], 'File')) {
                $file_upload_properties = $assocVars['transport_group']['file_panel'];
                $file = $transportRequest['file_panel'];
                $transportRequests[$idx]['file_panel'] = sprintf(
                    '<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                    $_SERVER['SCRIPT_NAME'],
                    $registry['module_param'],
                    rawurlencode(General::encrypt($file->get('id'), '_viewfile_', 'xtea')),
                    (!empty($file_upload_properties['thumb_width']) ? ("&maxwidth=" . $file_upload_properties['thumb_width']) : ''),
                    (!empty($file_upload_properties['thumb_height']) ? ("&maxheight=" . $file_upload_properties['thumb_height']) : '')
                );
            }
        }


        //group by offices and calculate totals;
        $transportRequestsByOffices = array();
        $transportTotals = array();
        foreach($transportRequests as $transportRequest) {
            $transportRequestsByOffices[$transportRequest['office_id']][] = $transportRequest;
            if (!array_key_exists($transportRequest['office_id'], $transportTotals)) {
                $transportTotals[$transportRequest['office_id']]['office_name'] = $transportRequest['office_name'];
                $transportTotals[$transportRequest['office_id']]['office_description'] = $transportRequest['office_description'];
                $transportTotals[$transportRequest['office_id']]['module_handle'] = (float) $transportRequest['module_handle'];
                $transportTotals[$transportRequest['office_id']]['module_handle_desc'] = $transportRequest['module_handle_desc'];
                $transportTotals[$transportRequest['office_id']]['module_cartridge'] = (float) $transportRequest['module_cartridge'];
                $transportTotals[$transportRequest['office_id']]['module_cartridge_desc'] = $transportRequest['module_cartridge_desc'];
                $transportTotals[$transportRequest['office_id']]['meters_req'] = (float) $transportRequest['meters_req'];
                $transportTotals[$transportRequest['office_id']]['num_req'] = (float) $transportRequest['num_req'];
                $transportTotals[$transportRequest['office_id']]['payment_amount'] = (float) $transportRequest['payment_amount']??0;
            } else {
                $transportTotals[$transportRequest['office_id']]['module_handle'] += (float) $transportRequest['module_handle'];
                $transportTotals[$transportRequest['office_id']]['module_cartridge'] += (float) $transportRequest['module_cartridge'];
                $transportTotals[$transportRequest['office_id']]['meters_req'] += (float) $transportRequest['meters_req'];
                $transportTotals[$transportRequest['office_id']]['num_req'] += (float) $transportRequest['num_req'];
                $transportTotals[$transportRequest['office_id']]['payment_amount'] += $transportRequest['payment_amount']??0;

                $transportTotals[$transportRequest['office_id']]['module_handle_desc'] = self::sumMultilineDesc(
                    $transportTotals[$transportRequest['office_id']]['module_handle_desc'],
                    $transportRequest['module_handle_desc']
                );

                $transportTotals[$transportRequest['office_id']]['module_cartridge_desc'] = self::sumMultilineDesc(
                    $transportTotals[$transportRequest['office_id']]['module_cartridge_desc'],
                    $transportRequest['module_cartridge_desc']
                );
            }
        }

        //group by offices and orders;
        $transportOrders = array();
        foreach($transportRequests as $transportRequest) {
            if (!array_key_exists($transportRequest['order_full_num'], $transportOrders)) {
                $transportOrders[$transportRequest['order_full_num']]['num_req'] = (float) $transportRequest['num_req'];
                $transportOrders[$transportRequest['order_full_num']]['meters_req'] = (float) $transportRequest['meters_req'];
                $transportOrders[$transportRequest['order_full_num']]['payment_amount'] = sprintf('%.2F', $transportRequest['payment_amount']);
            } else {
                $transportOrders[$transportRequest['order_full_num']]['num_req'] += (float) $transportRequest['num_req'];
                $transportOrders[$transportRequest['order_full_num']]['meters_req'] += (float) $transportRequest['meters_req'];
            }
        }
        ksort($transportOrders);

        $viewer = new Viewer($registry);
        $viewer->setFrameset(dirname(__DIR__) . '/templates/transport_group.html');
        $viewer->data['pattern_id'] = $pattern->get('id');
        $viewer->data['var'] = $assocVars['transport_group'];
        $viewer->data['keys'] = array_flip($assocVars['transport_group']['names']);
        $viewer->data['labels'] = array_combine($assocVars['transport_group']['names'], $assocVars['transport_group']['labels']);
        $viewer->data['transportRequestsByOffices'] = $transportRequestsByOffices;
        $viewer->templatesDir = __DIR__ . '/../templates/';
        $model->extender->add('transport_requests', $viewer->fetch());

        $viewer = new Viewer($registry);
        $viewer->setFrameset(dirname(__DIR__) . '/templates/transport_totals.html');
        $viewer->data['labels'] = array_combine($assocVars['transport_group']['names'], $assocVars['transport_group']['labels']);
        if (!empty($assocVars['transport_group']['backlabels'])) {
            $viewer->data['backlabels'] = array_combine(
                $assocVars['transport_group']['names'],
                $assocVars['transport_group']['backlabels']
            );
        }
        $viewer->data['transportTotals'] = $transportTotals;
        $model->extender->add('transport_totals', $viewer->fetch());

        $viewer = new Viewer($registry);
        $viewer->setFrameset(dirname(__DIR__) . '/templates/transport_orders.html');
        $viewer->data['labels'] = array_combine($assocVars['transport_group']['names'], $assocVars['transport_group']['labels']);
        if (!empty($assocVars['transport_group']['backlabels'])) {
            $viewer->data['backlabels'] = array_combine(
                $assocVars['transport_group']['names'],
                $assocVars['transport_group']['backlabels']
            );
        }
        $viewer->data['transportOrders'] = $transportOrders;
        $model->extender->add('transport_orders', $viewer->fetch());

        return true;
    }

    private static function parseMultilineDesc($desc) {
        $parsed = array();
        $rows = array_filter(preg_split('#\r\n|\n#', $desc));
        foreach($rows as $row) {
            preg_match('#^([^\|]*)\s*(\|\s*)?\|\s*([0-9\.]*)$#', $row, $matches);
            $item = $matches[1] ?? '';
            $count = $matches[3] ?? 0;
            if (array_key_exists($item, $parsed)) {
                $parsed[$item] += (float) $count;
            } else {
                $parsed[$item] = (float) $count;
            }
        }

        return $parsed;
    }

    private static function sumMultilineDesc($desc1, $desc2) {
        $items1 = self::parseMultilineDesc($desc1);
        $items2 = self::parseMultilineDesc($desc2);

        //sum into items1
        foreach($items2 as $item => $count) {
            if (array_key_exists($item, $items1)) {
                $items1[$item] += $count;
            } else {
                $items1[$item] = $count;
            }
        }

        $desc = array();
        foreach($items1 as $item => $count) {
            $desc[] = "{$item}| {$count}";
        }

        return implode("\r\n", $desc);
    }

    private function _sortTransportRequest($a, $b, $transportRouteIds) {
        //1. sort by transport route
        $atr = array_search($a['delivery_cityid'], $transportRouteIds);
        $btr = array_search($b['delivery_cityid'], $transportRouteIds);
        if ($atr !== false || $btr !== false) {
            if ($atr !== false && $btr !== false && $atr != $btr) {
                return $atr < $btr ? -1 : 1;
            } elseif ($atr === false) {
                return 1;
            } elseif ($btr === false) {
                return -1;
            }
        }

        //2. sort by kind_production (1 - Dograma is first, 3 - Accessories second, so ascending)
        if ($a['kind_production'] != $b['kind_production']) {
            return $a['kind_production'] < $b['kind_production'] ? -1:1;
        }

        //3. sort by schedule_position (ascending)
        if ($a['kind_production'] == $b['kind_production'] && $a['kind_production'] == 1 && $a['schedule_position'] != $b['schedule_position']) {
            return (int) $a['schedule_position'] < (int) $b['schedule_position'] ? -1:1;
        }

        //4. sort by date_order ASC
        if ($a['date_order'] != $b['date_order']) {
            return $a['date_order'] < $b['date_order'] ? -1:1;
        }

        //5. sort by order_full_num
        if ($a['order_full_num'] != $b['order_full_num']) {
            return strcmp($a['order_full_num'], $b['order_full_num']);
        }

        //6. sort by description (order_description)
        if ($a['order_description'] != $b['order_description']) {
            return strcmp($a['order_description'], $b['order_description']);
        }

        return 0;
    }
}
