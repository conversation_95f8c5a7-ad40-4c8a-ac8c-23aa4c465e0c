<?php
/**
 * Custom plugin to prepare data for print/genaration for BDZ
 */
Class Custom_Factory extends Model_Factory {
    public $folderName = 'bdz';
    public static $registry;

    /**
     * Prepare recipients data ("То", "With respect", "Аgreement with" and "Prepared") for all type of documents
     *
     * @param object $registry - registry object
     * @param object $model    - Telegram type document
     * @param object $pattern  - the pattern to print/generate with
     * @param array $params    - some custom params that might be used
     */
    public static function prepareRecipientsData(&$registry, &$model, &$pattern, &$params = array()) {
        // Get all grouping vars of the model
        $grouping_vars = $model->get('group_vars');


        // Prepare an array for the recipients data
        $recipients = array();
        // Prepare an array for the recipients customers ids
        $recipients_customers_ids = array();

        // Prepare an array for the respects data
        $respects = array();

        // Prepare an array for the agreements data
        $agreements = array();

        // Go through all grouping vars and get the data we need
        foreach ($grouping_vars as $var) {
            if ($var['name'] == 'recipient_id') {
                foreach ($var['value'] as $recipient_key => $recipient_id) {
                    $recipients[$recipient_key]['id'] = $recipient_id;
                    $recipients_customers_ids[] = $recipient_id;
                }
            } elseif ($var['name'] == 'recipient_name') {
                foreach ($var['value'] as $recipient_key => $recipient_name) {
                    $recipients[$recipient_key]['name'] = $recipient_name;
                }
            } elseif ($var['name'] == 'recipient_position') {
                foreach ($var['value'] as $recipient_key => $recipient_position) {
                    $recipients[$recipient_key]['position'] = $recipient_position;
                }
            } elseif ($var['name'] == 'recipient_department') {
                foreach ($var['value'] as $recipient_key => $recipient_department_value) {
                    $recipients[$recipient_key]['department_value'] = $recipient_department_value;
                }
                $recipient_department_options = array();
                foreach ($var['options'] as $recipient_department_option) {
                    $recipient_department_options[$recipient_department_option['option_value']] = $recipient_department_option['label'];
                }
                foreach ($recipients as $recipient_key => $recipient) {
                    $recipients[$recipient_key]['department_label'] = (empty($recipient_department_options[$recipient['department_value']]) ? '' : $recipient_department_options[$recipient['department_value']]);
                }
            } elseif ($var['name'] == 'respect_name') {
                foreach ($var['value'] as $respect_key => $respect_name) {
                    $respects[$respect_key]['name'] = $respect_name;
                }
            } elseif ($var['name'] == 'respect_position') {
                foreach ($var['value'] as $respect_key => $respect_position) {
                    $respects[$respect_key]['position'] = $respect_position;
                }
            } elseif ($var['name'] == 'respect_department') {
                foreach ($var['value'] as $respect_key => $respect_department_value) {
                    $respects[$respect_key]['department_value'] = $respect_department_value;
                }
                $respect_department_options = array();
                foreach ($var['options'] as $respect_department_option) {
                    $respect_department_options[$respect_department_option['option_value']] = $respect_department_option['label'];
                }
                foreach ($respects as $respect_key => $respect) {
                    $respects[$respect_key]['department_label'] = (empty($respect_department_options[$respect['department_value']]) ? '' : $respect_department_options[$respect['department_value']]);
                }
            } elseif ($var['name'] == 'prepared_name') {
                foreach ($var['value'] as $agreement_key => $agreement_name) {
                    $agreements[$agreement_key]['name'] = $agreement_name;
                }
            } elseif ($var['name'] == 'prepared_position') {
                foreach ($var['value'] as $agreement_key => $agreement_position) {
                    $agreements[$agreement_key]['position'] = $agreement_position;
                }
            } elseif ($var['name'] == 'prepared_department') {
/* !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! */
/* !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! */
/* !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! IMPORTANT !!! */
                // TODO: The additional var prepared_department is changed to be text instead of dropdown
                // The current plugin is currently not in use, but if it will be used in future, this peace of code
                // should be changed
                foreach ($var['value'] as $agreement_key => $agreement_department_value) {
                    $agreements[$agreement_key]['department_value'] = $agreement_department_value;
                }
                $agreement_department_options = array();
                foreach ($var['options'] as $agreement_department_option) {
                    $agreement_department_options[$agreement_department_option['option_value']] = $agreement_department_option['label'];
                }
                foreach ($agreements as $agreement_key => $agreement) {
                    $agreements[$agreement_key]['department_label'] = (empty($agreement_department_options[$agreement['department_value']]) ? '' : $agreement_department_options[$agreement['department_value']]);
                }
            } elseif ($var['name'] == 'agreement_answer') {
                foreach ($var['value'] as $agreement_key => $agreement_answer) {
                    $agreements[$agreement_key]['answer'] = $agreement_answer;
                }
            } elseif ($var['name'] == 'date_time') {
                foreach ($var['value'] as $agreement_key => $agreement_date_time) {
                    $agreements[$agreement_key]['date_time'] = $agreement_date_time;
                }
            }
        }

        // To:
        // Get the types of all recipient customers
        $query = 'SELECT `id`, `type`' . "\n" .
                 '  FROM `' . DB_TABLE_CUSTOMERS . '`' . "\n" .
                 '  WHERE `id` IN (\'' . implode('\', \'', $recipients_customers_ids) . '\')';
        $recipients_customers_types = $registry['db']->getAssoc($query);
        // Prepare the recipients HTML
        $recipient_html = '';
        $rows_count = 0;
        // Go through all recipients
        foreach ($recipients as $recipient) {
            $rows_count++;

            // If this is not the first row, then add a line break
            if ($rows_count > 1) {
                $recipient_html .= '<br />';
            }

            // If the recipient row have no customer id orthe customer is employee
            if (empty($recipient['id']) || (!empty($recipient['id']) && $recipients_customers_types[$recipient['id']] == PH_CUSTOMER_EMPLOYEE)) {
                // Set data to: Position, Department
                $recipient_html .= implode(', ', array_filter(array($recipient['position'], $recipient['department_label'])));
            } else {
                // Set data to: Name
                $recipient_html .= $recipient['name'];
            }
        }

        // With respect:
        $respect_html = '<table border="0" cellpadding="0" cellspacing="0" width="100%" style="width: 100%">';
        $rows_count = 0;
        $row_is_open = false;
        foreach ($respects as $respect) {
            $rows_count++;

            // Put an empty row between every two rows
            if ($rows_count > 1 && $rows_count%2 != 0) {
                $respect_html .= '<tr><td>&nbsp;</td><td>&nbsp;</td></tr>';
            }

            // Prepare the data for the cell
            $cell_data = (empty($respect['name']) ? '&nbsp;' : $respect['name']);
            $position_and_department = implode(', ', array_filter(array($respect['position'], $respect['department_label'])));
            $cell_data .= (empty($position_and_department) ? '' : '<br />' . $position_and_department);

            // If this is odd row
            if ($rows_count%2 != 0) {
                $respect_html .= '<tr><td>' . $cell_data . '</td>';
                $row_is_open = true;
            } else {
                $respect_html .= '<td>' . $cell_data . '</td></tr>';
            }
        }
        // If the row is open, then close it with an empty cell
        if ($row_is_open) {
            $respect_html .= '<td>&nbsp;</td></tr>';
        }
        $respect_html .= '</table>';

        // Agreement with:
        $agreement_html = '<table border="0" cellpadding="5" cellspacing="0">';
        $rows_count = 0;
        foreach ($agreements as $agreement) {
            $rows_count++;

            // Put an empty row between every two rows
            if ($rows_count > 1) {
                $agreement_html .= '<tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>';
            }

            // Prepare the data for the first cell at this row
            $cell1_data = (empty($agreement['name']) ? '&nbsp;' : $agreement['name']);

            // Prepare the data for the third cell at this row (the second cell is just a separator)
            $cell3_data = $cell1_data;

            $position_and_department = implode(', ', array_filter(array($agreement['position'], $agreement['department_label'])));
            $cell1_data .= (empty($position_and_department) ? '' : '<br />' . $position_and_department);

            // If the agreement answer is "Yes"
            if ($agreement['answer'] == 1) {
                // Add the date
                $cell3_data .= '<br />' . General::strftime('%d.%m.%Y, %H:%M', $agreement['date_time']);
            } else {
                // Empty the third cell
                $cell3_data = '';
            }

            // Set the HTML for the agreement
            $agreement_html .= '<tr><td style="width: 500px;">' . $cell1_data . '</td>';
            $agreement_html .= '<td style="width: 50px;">&nbsp;</td>';
            if (empty($cell3_data)) {
                $agreement_html .= '<td>&nbsp;</td></tr>';
            } else {
                $agreement_html .= '<td style="width: 250px; text-align: center; border: 3px double black;">' . $cell3_data . '</td></tr>';
            }
        }
        $agreement_html .= '</table>';

        // Prepared:
        // Get the customer model
        include_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
        $prepared = Customers::searchOne($registry, array('where'    => array('c.id = \'' . $model->get('employee') . '\''),
                                                          'sanitize' => true));
        // Prepare the HTML
        $prepared_html = '';
        if ($prepared) {
            $prepared_html = trim($prepared->get('name') . ' ' . $prepared->get('lastname'));
            $position_and_department = implode(', ', array_filter(array($prepared->get('type_name'), $prepared->get('department_name'))));
            $prepared_html .= (empty($position_and_department) ? '' : '<br />' . $position_and_department);
        }

        // Set the placeholders values
        $model->extender->add('telegram_recipient', $recipient_html);
        $model->extender->add('telegram_respect',   $respect_html);
        $model->extender->add('telegram_agreement', $agreement_html);
        $model->extender->add('telegram_prepared',  $prepared_html);
    }
}
?>