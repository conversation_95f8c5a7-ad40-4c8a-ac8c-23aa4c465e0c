<tr class="gt2_agregates">
  {if empty($table.hide_row_numbers)}
  <td>&nbsp;</td>
  {/if}
  {foreach name='j' key='key' from=$table.vars item='var'}
    {capture assign='class_name'}var_{$var.id}{/capture}
    {if !preg_match('#display\s*:\s*none#', $styles.$class_name)}
    <td class="{$class_name}">
      {strip}
        {if !empty($var.agregate)}
          {capture assign='ag_text'}gt2_tagregates_{$var.agregate}{/capture}
          {$smarty.config.$ag_text}:&nbsp;{$table.agregate_results_grouped[$group_by][$var.name]}
        {else}
          &nbsp;
        {/if}
      {/strip}
    </td>
    {/if}
  {/foreach}
</tr>
