{foreach from=$products item='product' key='k' name='p'}
  <div class="product">
    <div class="title">{$product.name} - {$product.weight} кг.</div>
    <table class="details">
    {foreach from=$product.details item='detail' key='kd'}
      {if $kd % 2 == 0}<tr>{/if}
        <td class="detail">
          <table>
            <tr>
              <td class="name"><div>{$detail.name|default:'&nbsp;'}</div></td>
              <td class="quantity">{$detail.quantity}</td>
              <td class="spacer">X</td>
              <td class="length">{$detail.length}</td>
              <td class="image">{if $detail.image}<img src="{$detail.image}" />{/if}</td>
            </tr>
          </table>
        <td/>
      {if $kd % 2 != 0}</tr>{/if}
    {/foreach}
    </table>
  </div>
  {if !$smarty.foreach.p.last}<div class="break">[system_pagebreak]</div>{/if}
{/foreach}

{if $is_html}
<style>
    {literal}
    @media screen {
        body {
            display: none;
        }
    }
    {/literal}
</style>
<script type="text/javascript">
    window.print();
    window.onafterprint = window.close;
</script>
{/if}
