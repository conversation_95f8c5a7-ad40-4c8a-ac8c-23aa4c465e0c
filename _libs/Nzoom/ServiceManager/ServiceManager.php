<?php

namespace Nzoom\ServiceManager;

class ServiceManager
{
private $services = [];
private $factories = [];

    public function __construct(array $factories=null)
    {
        if ($factories) {
            $this->setFactories($factories);
        }
    }

    public function get($serviceName)
    {
        if (isset($this->services[$serviceName]) ) {
            return $this->services[$serviceName];
        }

        try {
            return $this->services[$serviceName] = $this->build($serviceName);
        } catch (Exception\ServiceManagerException $e) {
            throw new Exception\ServiceNotFound("Service '$serviceName' not found!", 0, $e);
        }
    }

    public function set($name, $service)
    {
        $this->services[$name] = $service;
    }

    public function setFactories(array $factories) {
        $this->factories = array_merge($this->factories, $factories);
    }

    public function build(string $serviceName, array $options=null)
    {
        if (!isset($this->factories[$serviceName])) {
            throw new Exception\FactoryNotFound("Factory for service '$serviceName' not found!");
        }

        $factory = $this->factories[$serviceName];
        if (is_callable($factory)) {
            try {
                return call_user_func($factory, $this, $serviceName, $options);
            } catch (\Exception $e) {
                $exceptionType = get_class($e);
                throw new Exception\ServiceCantBeCreated("Factory for service '$serviceName' trown an '($exceptionType)' with message '{$e->getMessage()}'!", 0, $e);
            }
        }

        $factoryType = gettype($factory);
        throw new Exception\FactoryCantBeExecuted("Factory for service '$serviceName' can't be executed! The supplyed factory is of type: '$factoryType'");
    }


    public function setService(string $serviceName, $service)
    {
        $this->services[$serviceName] = $service;
    }
}
