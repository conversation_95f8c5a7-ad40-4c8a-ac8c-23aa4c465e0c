<?php

namespace Nzoom\Export\Entity;

/**
 * Class ExportData
 *
 * Main class for holding export data
 * Contains a header and multiple records
 * Supports both eager loading (traditional) and lazy loading (streaming) of records
 */
class ExportData implements \IteratorAggregate, \Countable
{
    /**
     * @var ExportHeader The export header
     */
    private $header;

    /**
     * @var ExportRecord[] Array of export records (for eager loading)
     */
    private $records = [];

    /**
     * @var array Additional metadata for the export data
     */
    private $metadata = [];

    /**
     * @var callable|null Record provider for lazy loading
     */
    private $recordProvider = null;

    /**
     * @var int Page size for pagination when using lazy loading
     */
    private $pageSize = 1000;

    /**
     * @var bool Whether this instance uses lazy loading
     */
    private $isLazy = false;

    /**
     * ExportData constructor
     *
     * @param ExportHeader|null $header The export header
     * @param array $metadata Additional metadata for the export data
     */
    public function __construct(ExportHeader $header = null, array $metadata = [])
    {
        $this->header = $header ?? new ExportHeader();
        $this->metadata = $metadata;
    }

    /**
     * Configure lazy loading with a record provider
     *
     * @param callable $recordProvider Function that takes (offset, limit) and returns ExportRecord[]
     * @param int|null $totalCount Total number of records available
     * @param int $pageSize Page size for pagination
     * @return $this
     */
    public function setRecordProvider(callable $recordProvider, int $pageSize = 1000): self
    {
        $this->recordProvider = $recordProvider;
        $this->pageSize = $pageSize;
        $this->isLazy = true;

        // Clear any existing records when switching to lazy mode
        $this->records = [];

        return $this;
    }

    /**
     * Check if this instance uses lazy loading
     *
     * @return bool True if using lazy loading
     */
    public function isLazy(): bool
    {
        return $this->isLazy;
    }

    /**
     * Get the page size for lazy loading
     *
     * @return int The page size
     */
    public function getPageSize(): int
    {
        return $this->pageSize;
    }

    /**
     * Set the page size for lazy loading
     *
     * @param int $pageSize The page size
     * @return $this
     */
    public function setPageSize(int $pageSize): self
    {
        $this->pageSize = $pageSize;
        return $this;
    }

    /**
     * Get the header
     *
     * @return ExportHeader The export header
     */
    public function getHeader(): ExportHeader
    {
        return $this->header;
    }

    /**
     * Set the header
     *
     * @param ExportHeader $header The export header
     */
    public function setHeader(ExportHeader $header): void
    {
        $this->header = $header;
    }

    /**
     * Get the metadata
     *
     * @return array The metadata
     */
    public function getMetadata(): array
    {
        return $this->metadata;
    }

    /**
     * Set the metadata
     *
     * @param array $metadata The metadata
     */
    public function setMetadata(array $metadata): void
    {
        $this->metadata = $metadata;
    }

    /**
     * Get a metadata value
     *
     * @param string $key The metadata key
     * @param mixed $default The default value to return if the key does not exist
     * @return mixed The metadata value or the default value
     */
    public function getMetadataValue(string $key, $default = null)
    {
        return $this->metadata[$key] ?? $default;
    }

    /**
     * Set a metadata value
     *
     * @param string $key The metadata key
     * @param mixed $value The metadata value
     */
    public function setMetadataValue(string $key, $value): void
    {
        $this->metadata[$key] = $value;
    }

    /**
     * Add a record to the export data
     * Note: This method only works in eager loading mode. In lazy loading mode, records are provided by the record provider.
     *
     * @param ExportRecord $record The record to add
     * @param bool $validate Whether to validate the record against the header
     * @throws \InvalidArgumentException If the record is invalid and validation is enabled
     * @throws \LogicException If called in lazy loading mode
     */
    public function addRecord(ExportRecord $record, bool $validate = false): void
    {
        if ($this->isLazy) {
            throw new \LogicException('Cannot add records directly when using lazy loading. Use the record provider instead.');
        }

        if ($validate && !$record->validate($this->header)) {
            throw new \InvalidArgumentException('Record does not match the header structure');
        }

        $this->records[] = $record;
    }

    /**
     * Get all records
     * Note: In lazy loading mode, this will load ALL records into memory. Use with caution for large datasets.
     *
     * @return ExportRecord[] Array of records
     */
    public function getRecords(): array
    {
        if ($this->isLazy) {
            // Load all records into memory (use with caution)
            $allRecords = [];
            foreach ($this->getLazyIterator() as $record) {
                $allRecords[] = $record;
            }
            return $allRecords;
        }

        return $this->records;
    }

    /**
     * Get record at specific index
     * Note: In lazy loading mode, this may be inefficient for large indices
     *
     * @param int $index The index of the record
     * @return ExportRecord|null The record or null if not found
     */
    public function getRecordAt(int $index): ?ExportRecord
    {
        if ($this->isLazy) {
            // For lazy loading, we need to iterate to the specific index
            $currentIndex = 0;
            foreach ($this->getLazyIterator() as $record) {
                if ($currentIndex === $index) {
                    return $record;
                }
                $currentIndex++;
            }
            return null;
        }

        return $this->records[$index] ?? null;
    }

    /**
     * Get record count
     *
     * @return int The number of records
     */
    public function count(): int
    {
        if ($this->isLazy) {
            return 1000;
        }

        return count($this->records);
    }

    /**
     * Create a new record, add it to the export data, and return it
     * Note: This method only works in eager loading mode.
     *
     * @param array $metadata Additional metadata for the record
     * @return ExportRecord The newly created record
     * @throws \LogicException If called in lazy loading mode
     */
    public function createRecord(array $metadata = []): ExportRecord
    {
        if ($this->isLazy) {
            throw new \LogicException('Cannot create records directly when using lazy loading. Use the record provider instead.');
        }

        $record = new ExportRecord($metadata);
        $this->addRecord($record);
        return $record;
    }

    /**
     * Check if the export data is empty (has no records)
     *
     * @return bool True if empty, false otherwise
     */
    public function isEmpty(): bool
    {
        if ($this->isLazy) {
            return $this->count() === 0;
        }

        return empty($this->records);
    }

    /**
     * Sort records by a column
     *
     * @param string $columnName The column name to sort by
     * @param bool $ascending Whether to sort in ascending order
     * @throws \InvalidArgumentException If the column name does not exist
     */
    public function sortByColumn(string $columnName, bool $ascending = true): void
    {
        if (!$this->header->hasColumn($columnName)) {
            throw new \InvalidArgumentException(sprintf(
                'Column with name "%s" does not exist',
                $columnName
            ));
        }

        usort($this->records, function (ExportRecord $a, ExportRecord $b) use ($columnName, $ascending) {
            $valueA = $a->getValueByColumnName($columnName);
            $valueB = $b->getValueByColumnName($columnName);

            if ($valueA === null && $valueB === null) {
                return 0;
            }

            if ($valueA === null) {
                return $ascending ? -1 : 1;
            }

            if ($valueB === null) {
                return $ascending ? 1 : -1;
            }

            $result = 0;
            $rawValueA = $valueA->getValue();
            $rawValueB = $valueB->getValue();

            if (is_string($rawValueA) && is_string($rawValueB)) {
                $result = strcasecmp($rawValueA, $rawValueB);
            } elseif (is_numeric($rawValueA) && is_numeric($rawValueB)) {
                $result = $rawValueA <=> $rawValueB;
            } elseif ($rawValueA instanceof \DateTimeInterface && $rawValueB instanceof \DateTimeInterface) {
                $result = $rawValueA <=> $rawValueB;
            } else {
                $result = strcasecmp((string) $rawValueA, (string) $rawValueB);
            }

            return $ascending ? $result : -$result;
        });
    }

    /**
     * Filter records by a callback
     *
     * @param callable $callback The callback to use for filtering
     */
    public function filter(callable $callback): void
    {
        $this->records = array_filter($this->records, $callback);
        $this->records = array_values($this->records); // Re-index the array
    }

    /**
     * Validate all records against the header
     *
     * @return bool True if all records are valid
     */
    public function validate(): bool
    {
        foreach ($this->records as $record) {
            if (!$record->validate($this->header)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the data as a two-dimensional array
     * First row contains headers, subsequent rows contain values
     *
     * @param bool $formatted Whether to use formatted values
     * @return array Two-dimensional array of data
     */
    public function toArray(bool $formatted = false): array
    {
        $result = [];

        // Add header row
        $result[] = $this->header->getLabels();

        // Add data rows
        foreach ($this->records as $record) {
            $result[] = $formatted ? $record->getFormattedValues() : $record->getRawValues();
        }

        return $result;
    }

    /**
     * Get an iterator for the records
     *
     * @return \Traversable Iterator for the records
     */
    public function getIterator(): \Traversable
    {
        if ($this->isLazy) {
            return $this->getLazyIterator();
        }

        return new \ArrayIterator($this->records);
    }

    /**
     * Get a lazy iterator that loads records on demand
     *
     * @return \Generator Generator that yields records page by page
     */
    private function getLazyIterator(): \Generator
    {
        if (!$this->recordProvider) {
            return;
        }

        $offset = 0;
        do {
            $records = ($this->recordProvider)($offset, $this->pageSize);

            if (empty($records)) {
                break;
            }

            foreach ($records as $record) {
                yield $record;
            }

            $offset += $this->pageSize;

        } while (count($records) === $this->pageSize);
    }
}
