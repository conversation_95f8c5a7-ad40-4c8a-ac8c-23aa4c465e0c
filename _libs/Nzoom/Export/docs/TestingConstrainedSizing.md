# Testing Excel Constrained Sizing Feature

## Quick Test

To quickly verify that the constrained sizing feature is working, you can run the test script:

```php
<?php
// Include your application bootstrap
require_once 'path/to/your/bootstrap.php';

use Nzoom\Export\Examples\QuickTest;

// Assuming you have a $registry object available
QuickTest::runAll($registry);
?>
```

## Manual Testing via Grid Export

### 1. Using the Grid Export Interface

1. **Navigate to any grid** (customers, invoices, etc.)
2. **Select some items** with long text content
3. **Click Export** and choose Excel format
4. **Look for the new sizing options** in the export dialog:
   - Maximum Column Width (default: 50.0)
   - Maximum Row Height (default: 100.0)

### 2. Testing Different Constraint Levels

Try these different settings to see the effect:

**Tight Constraints (Compact Layout):**
- Maximum Column Width: 25.0
- Maximum Row Height: 50.0

**Medium Constraints (Balanced):**
- Maximum Column Width: 40.0
- Maximum Row Height: 80.0

**Loose Constraints (Spacious):**
- Maximum Column Width: 70.0
- Maximum Row Height: 120.0

**No Constraints (Old Behavior):**
- Maximum Column Width: 255.0
- Maximum Row Height: 500.0

## What to Look For

### ✅ Expected Results

1. **Column Width Constraints:**
   - Columns should not exceed the specified maximum width
   - Text wrapping should be enabled for constrained columns
   - Long text should flow to multiple lines within cells

2. **Row Height Constraints:**
   - Rows should not exceed the specified maximum height
   - Multi-line content should be visible but constrained
   - Text should not be cut off (wrapping preserves content)

3. **Overall Layout:**
   - Excel file should be easier to navigate
   - No extremely wide columns that require horizontal scrolling
   - Consistent, predictable sizing

### ❌ Issues to Watch For

1. **Text Cut Off:** Content should never be lost
2. **No Wrapping:** Text should wrap in constrained columns
3. **Ignored Constraints:** Columns/rows exceeding limits
4. **Broken Layout:** Malformed Excel files

## Debugging

### Check if Feature is Active

1. **Verify adapter is being used:**
   ```php
   // In _export2 method, check if ExportService is being called
   // Look for: $exportService->export($filename, $data, $exportOptions);
   ```

2. **Check export options:**
   ```php
   // Add debug output in getExportOptions method
   var_dump($options); // Should show max_column_width and max_row_height
   ```

3. **Verify adapter receives options:**
   ```php
   // In ExcelExportFormatAdapter::export method
   var_dump($options); // Should contain sizing constraints
   ```

### Common Issues

**Issue:** Sizing constraints not applied
- **Check:** Is `_export2` method being called instead of `_exportExcel`?
- **Check:** Are export options being passed correctly?

**Issue:** Form fields not showing
- **Check:** JavaScript `toggleExportFormat` function
- **Check:** CSS classes for `.excel-option` elements

**Issue:** Options not reaching adapter
- **Check:** `export` method with options parameter in ExportService
- **Check:** Options parameter in adapter's export method

## Browser Developer Tools

Use browser dev tools to inspect the export form:

1. **Check form fields:**
   ```javascript
   // Should find these inputs when Excel format is selected
   document.querySelector('input[name="max_column_width"]');
   document.querySelector('input[name="max_row_height"]');
   ```

2. **Check form submission:**
   ```javascript
   // Monitor network tab for POST data including sizing options
   ```

## File Comparison

Create exports with different settings and compare:

1. **Export with tight constraints** → `export_tight.xlsx`
2. **Export with no constraints** → `export_unconstrained.xlsx`
3. **Open both files** and compare column widths and row heights

## Troubleshooting Steps

1. **Verify implementation:**
   - Check that `ExcelExportFormatAdapter` has the new methods
   - Verify `_export2` calls `exportWithOptions`
   - Confirm form includes sizing fields

2. **Test adapter directly:**
   ```php
   $adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');
   $options = ['max_column_width' => 30.0, 'max_row_height' => 60.0];
   $adapter->export('test.xlsx', 'xlsx', $exportData, $options);
   ```

3. **Check logs:**
   - Look for any error messages in application logs
   - Check for PhpSpreadsheet-related errors

4. **Verify PhpSpreadsheet version:**
   - Ensure compatible version is installed
   - Check for any missing dependencies

## Success Indicators

✅ **Feature is working correctly when:**
- Export form shows sizing options for Excel formats
- Generated Excel files respect column width limits
- Text wrapping is enabled for constrained columns
- Row heights are calculated and constrained appropriately
- All content remains visible and readable
- No PHP errors or exceptions occur during export
