<?php

namespace Nzoom\Export\Adapter;

use Exception;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Exception as WriterException;
use PhpOffice\PhpSpreadsheet\Writer\IWriter;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * Excel export format adapter
 *
 * Handles export to Excel formats (xlsx, xls)
 */
class ExcelExportFormatAdapter extends AbstractExportFormatAdapter
{
    /**
     * @var int Chunk size for processing records
     */
    private int $chunkSize = 1000;

    /**
     * @var float Maximum column width for auto-sizing (in Excel units)
     */
    private float $maxColumnWidth = 50.0;

    /**
     * @var float Maximum row height for auto-sizing (in points)
     */
    private float $maxRowHeight = 200.0;

    /**
     * Export data to a file on disk
     *
     * Saves the Excel file to a specified path or file pointer.
     *
     * @param string|resource $file The file path (string) or file pointer (resource) where the file should be saved
     * @param string $type The export type/format (xlsx, xls)
     * @param ExportData $exportData The export data containing records and headers
     * @param array $options Export options
     * @return void
     * @throws Exception If file cannot be created or written
     */
    public function export($file, string $type, ExportData $exportData, array $options = []): void
    {
        try {
            // Check if PhpSpreadsheet is available
            if (!class_exists(Spreadsheet::class)) {
                throw new Exception('PhpSpreadsheet library not available');
            }

            // Validate file parameter and determine save target
            $saveTarget = $this->validateAndPrepareSaveTarget($file);

            // Validate the export type
            $type = strtolower(trim($type));
            if (!in_array($type, $this->getSupportedExtensions())) {
                throw new Exception("Unsupported export type: {$type}. Supported types: " . implode(', ', $this->getSupportedExtensions()));
            }

            // Extract sizing options
            $this->extractSizingOptions($options);

            // Create and populate spreadsheet
            $spreadsheet = $this->createSpreadsheet($exportData);

            // Create appropriate writer based on type
            $writer = $this->createWriter($spreadsheet, $type);

            // Save to file or file pointer
            $writer->save($saveTarget);

            // Clean up
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);

        } catch (Exception $e) {
            // Log the error
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->error('Excel export error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            }

            // Re-throw the exception for caller to handle
            throw new Exception('Error generating Excel file: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Extract sizing options from export options
     *
     * @param array $options Export options
     * @return void
     */
    private function extractSizingOptions(array $options): void
    {
        if (isset($options['max_column_width']) && is_numeric($options['max_column_width'])) {
            $this->maxColumnWidth = (float) $options['max_column_width'];
        }

        if (isset($options['max_row_height']) && is_numeric($options['max_row_height'])) {
            $this->maxRowHeight = (float) $options['max_row_height'];
        }

        if (isset($options['chunk_size']) && is_numeric($options['chunk_size'])) {
            $this->chunkSize = (int) $options['chunk_size'];
        }

        // Allow override of locale via options
        if (isset($options['locale']) && is_string($options['locale'])) {
            try {
                \PhpOffice\PhpSpreadsheet\Settings::setLocale($options['locale']);
            } catch (\Exception $e) {
                if (isset($this->registry['logger'])) {
                    $this->registry['logger']->warning('Failed to set custom Excel locale: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * Create and populate a Spreadsheet object from ExportData
     *
     * @param ExportData $exportData The export data containing records and headers
     * @return Spreadsheet The populated spreadsheet
     */
    private function createSpreadsheet(ExportData $exportData): Spreadsheet
    {
        // Optimize memory for large exports
        $this->optimizeMemoryForExport();

        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Set document properties
        $this->setDocumentProperties($spreadsheet, 'Export');

        // Set locale for the spreadsheet
        $this->setSpreadsheetLocale();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Export');

        // Process ExportData records
        $this->processExportData($sheet, $exportData);

        return $spreadsheet;
    }

    /**
     * Set locale for the spreadsheet based on application locale
     *
     * @return void
     */
    private function setSpreadsheetLocale(): void
    {
        try {
            // Get the current application language/locale
            $locale = $this->getApplicationLocale();

            if ($locale) {
                // Set the locale for PhpSpreadsheet calculations and formatting
                \PhpOffice\PhpSpreadsheet\Settings::setLocale($locale);
            }
        } catch (\Exception $e) {
            // If locale setting fails, log it but continue with default locale
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warning('Failed to set Excel locale: ' . $e->getMessage());
            }
        }
    }

    /**
     * Get the application locale in a format suitable for PhpSpreadsheet
     *
     * @return string|null
     */
    private function getApplicationLocale(): ?string
    {
        // Try to get locale from registry (set by application)
        if (isset($this->registry['lang'])) {
            $appLang = $this->registry['lang'];

            // Map application language codes to PhpSpreadsheet locale codes
            $localeMap = [
                'en' => 'en_us',
                'bg' => 'bg',
                'de' => 'de',
                'es' => 'es',
                'fr' => 'fr',
                'it' => 'it',
                'nl' => 'nl',
                'pt' => 'pt_br',
                'ru' => 'ru',
                'sv' => 'sv',
                'no' => 'no',
                'da' => 'da',
                'fi' => 'fi',
                'pl' => 'pl',
                'cs' => 'cs',
                'hu' => 'hu',
                'tr' => 'tr'
            ];

            return $localeMap[$appLang] ?? 'en_us';
        }

        // Fallback: try to get from PHP locale
        if (isset($this->registry['locale'])) {
            $phpLocale = $this->registry['locale'];

            // Convert PHP locale format to PhpSpreadsheet format
            // e.g., 'en_US.UTF-8' -> 'en_us'
            if (preg_match('/^([a-z]{2})_([A-Z]{2})/', $phpLocale, $matches)) {
                return strtolower($matches[1] . '_' . $matches[2]);
            }
        }

        return null; // Use PhpSpreadsheet default
    }

    /**
     * Set document properties for the spreadsheet
     *
     * @param Spreadsheet $spreadsheet
     * @param string $filename
     * @return void
     */
    private function setDocumentProperties(Spreadsheet $spreadsheet, string $filename): void
    {
        $creator = "nZoom";
        if (isset($this->registry['currentUser'])) {
            $creator = $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname');
        }

        $spreadsheet->getProperties()->setCreator($creator)
            ->setLastModifiedBy("nZoom")
            ->setTitle($filename)
            ->setSubject($filename)
            ->setDescription("Export from nZoom");
    }

    /**
     * Process ExportData and add it to the sheet
     *
     * @param Worksheet $sheet
     * @param ExportData $exportData
     * @return void
     */
    private function processExportData(Worksheet $sheet, ExportData $exportData): void
    {
        // Get header from ExportData
        $header = $exportData->getHeader();
        $headers = $header->getLabels();

        // Add headers to sheet
        $this->addHeaders($sheet, $headers);

        // Style the header row
        $this->styleHeaderRow($sheet, count($headers));

        // Process records from ExportData
        $this->processExportDataRecords($sheet, $exportData);

        // Auto-size columns (no named ranges for ExportData)
        $this->finalizeExportDataColumns($sheet, $headers);

    }

    /**
     * Add headers to the sheet
     *
     * @param Worksheet $sheet
     * @param array $headers
     * @return void
     */
    private function addHeaders(Worksheet $sheet, array $headers): void
    {
        foreach ($headers as $col => $label) {
            $colLetter = Coordinate::stringFromColumnIndex($col + 1);
            $sheet->setCellValue("{$colLetter}1", $label);
        }
    }

    /**
     * Style the header row
     *
     * @param Worksheet $sheet
     * @param int $headerCount
     * @return void
     */
    private function styleHeaderRow(Worksheet $sheet, int $headerCount): void
    {
        $headerRange = 'A1:' . Coordinate::stringFromColumnIndex($headerCount) . '1';
        $sheet->getStyle($headerRange)->getFont()->setBold(true);
        $sheet->getStyle($headerRange)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('f6f9fc');
    }

    /**
     * Process ExportData records in chunks to avoid memory issues
     *
     * @param Worksheet $sheet
     * @param ExportData $exportData
     * @return void
     */
    private function processExportDataRecords(Worksheet $sheet, ExportData $exportData): void
    {
        $rowNumber = 2; // Start at row 2 (row 1 is headers)
        $recordCount = 0;
        $chunkCount = 0;

        // Iterate through ExportData records (supports streaming)
        foreach ($exportData as $record) {
            // Process the ExportRecord
            $this->processExportRecord($sheet, $record, $rowNumber);

            $recordCount++;
            $rowNumber++;

            // Perform garbage collection periodically for memory management
            if ($recordCount % $this->chunkSize === 0) {
                $chunkCount++;

                // Free up memory periodically
                if (gc_enabled()) {
                    gc_collect_cycles();
                }

                // Log progress for very large exports
                if (isset($this->registry['logger']) && $recordCount % 5000 === 0) {
                    $this->registry['logger']->info("Excel export progress: {$recordCount} records processed");
                }
            }
        }

        // Final cleanup
        if (gc_enabled()) {
            gc_collect_cycles();
        }

        // Log completion
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->info("Excel export completed: {$recordCount} records processed in {$chunkCount} chunks");
        }
    }

    /**
     * Process a single ExportRecord
     *
     * @param Worksheet $sheet
     * @param ExportRecord $record
     * @param int $row
     * @return void
     */
    private function processExportRecord(Worksheet $sheet, ExportRecord $record, int $row): void
    {
        $exportValues = $record->getValues();

        foreach ($exportValues as $colIndex => $exportValue) {
            $colLetter = Coordinate::stringFromColumnIndex($colIndex + 1);
            $cellAddress = "{$colLetter}{$row}";

            try {
                // Set cell value and formatting based on ExportValue
                $this->setCellValueWithFormatting($sheet, $cellAddress, $exportValue);
            } catch (Exception $e) {
                $this->handleExportRecordCellError($sheet, $colLetter, $row, $e, $colIndex, $record);
            }
        }
    }

    /**
     * Set cell value with proper formatting based on ExportValue
     *
     * @param Worksheet $sheet
     * @param string $cellAddress
     * @param ExportValue $exportValue
     * @return void
     */
    private function setCellValueWithFormatting(Worksheet $sheet, string $cellAddress, ExportValue $exportValue): void
    {
        $value = $exportValue->getValue();
        $type = $exportValue->getType();

        // Handle null values
        if ($value === null) {
            $sheet->setCellValue($cellAddress, '');
            return;
        }

        // Set cell value based on type
        switch ($type) {
            case ExportValue::TYPE_STRING:
            case 'text':
                $sheet->setCellValueExplicit($cellAddress, (string) $value, DataType::TYPE_STRING);
                break;

            case ExportValue::TYPE_INTEGER:
            case ExportValue::TYPE_FLOAT:
            case 'number':
                $sheet->setCellValue($cellAddress, is_numeric($value) ? $value : 0);
                break;

            case ExportValue::TYPE_BOOLEAN:
                $sheet->setCellValue($cellAddress, $value ? 1 : 0);
                break;

            case ExportValue::TYPE_DATE:
            case ExportValue::TYPE_DATETIME:
            case 'date':
            case 'datetime':
                $this->setCellDateValue($sheet, $cellAddress, $value, $type);
                break;

            default:
                // Default handling - let PhpSpreadsheet determine the best type
                if (is_string($value)) {
                    $sheet->setCellValueExplicit($cellAddress, $value, DataType::TYPE_STRING);
                } else {
                    $sheet->setCellValue($cellAddress, $value);
                }
                break;
        }

        // Apply number formatting
        $excelFormat = $this->getExcelFormatFromExportValue($exportValue);
        if ($excelFormat !== NumberFormat::FORMAT_GENERAL) {
            $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode($excelFormat);
        }
    }

    /**
     * Set cell value for date/datetime types
     *
     * @param Worksheet $sheet
     * @param string $cellAddress
     * @param mixed $value
     * @param string $type
     * @return void
     */
    private function setCellDateValue(Worksheet $sheet, string $cellAddress, $value, string $type): void
    {
        try {
            if ($value instanceof \DateTimeInterface) {
                // Convert DateTime to Excel date serial number
                $excelDate = \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($value);
                $sheet->setCellValue($cellAddress, $excelDate);
            } elseif (is_string($value) && strtotime($value) !== false) {
                // Parse string date and convert to Excel date serial number
                $dateTime = new \DateTime($value);
                $excelDate = \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($dateTime);
                $sheet->setCellValue($cellAddress, $excelDate);
            } else {
                // Fallback to string representation
                $sheet->setCellValueExplicit($cellAddress, (string) $value, DataType::TYPE_STRING);
            }
        } catch (\Exception $e) {
            // If date conversion fails, use string representation
            $sheet->setCellValueExplicit($cellAddress, (string) $value, DataType::TYPE_STRING);
        }
    }

    /**
     * Get Excel number format from ExportValue
     *
     * @param ExportValue $exportValue
     * @return string Excel format code
     */
    private function getExcelFormatFromExportValue(ExportValue $exportValue): string
    {
        $type = $exportValue->getType();
        $format = $exportValue->getFormat();

        switch ($type) {
            case ExportValue::TYPE_STRING:
            case 'text':
                return NumberFormat::FORMAT_TEXT;

            case ExportValue::TYPE_INTEGER:
            case 'number':
                return NumberFormat::FORMAT_NUMBER;

            case ExportValue::TYPE_FLOAT:
                // Use custom format if provided, otherwise default to 2 decimal places
                if ($format) {
                    return $this->convertCustomNumberFormat($format);
                }
                return NumberFormat::FORMAT_NUMBER_00;

            case ExportValue::TYPE_DATE:
            case 'date':
                // Use custom format if provided, otherwise default date format
                if ($format) {
                    return $this->convertCustomDateFormat($format);
                }
                return 'dd.mm.yyyy';

            case ExportValue::TYPE_DATETIME:
            case 'datetime':
                // Use custom format if provided, otherwise default datetime format
                if ($format) {
                    return $this->convertCustomDateFormat($format);
                }
                return 'dd.mm.yyyy hh:mm';

            case ExportValue::TYPE_BOOLEAN:
                return NumberFormat::FORMAT_GENERAL;

            default:
                return NumberFormat::FORMAT_GENERAL;
        }
    }

    /**
     * Convert custom number format to Excel format
     *
     * @param string $format
     * @return string Excel format code
     */
    private function convertCustomNumberFormat(string $format): string
    {
        // Handle common number format patterns
        if (preg_match('/^(\d+)$/', $format, $matches)) {
            // Format like "2" means 2 decimal places
            $decimals = (int) $matches[1];
            if ($decimals === 0) {
                return NumberFormat::FORMAT_NUMBER;
            } else {
                return '0.' . str_repeat('0', $decimals);
            }
        }

        // Return the format as-is if it looks like an Excel format
        return $format;
    }

    /**
     * Convert custom date format to Excel format
     *
     * @param string $format
     * @return string Excel format code
     */
    private function convertCustomDateFormat(string $format): string
    {
        // Convert common PHP date formats to Excel formats
        $formatMap = [
            'Y-m-d' => 'yyyy-mm-dd',
            'd.m.Y' => 'dd.mm.yyyy',
            'd/m/Y' => 'dd/mm/yyyy',
            'm/d/Y' => 'mm/dd/yyyy',
            'Y-m-d H:i' => 'yyyy-mm-dd hh:mm',
            'd.m.Y H:i' => 'dd.mm.yyyy hh:mm',
            'd/m/Y H:i' => 'dd/mm/yyyy hh:mm',
            'm/d/Y H:i' => 'mm/dd/yyyy hh:mm',
            'Y-m-d H:i:s' => 'yyyy-mm-dd hh:mm:ss',
            'd.m.Y H:i:s' => 'dd.mm.yyyy hh:mm:ss',
        ];

        // Return mapped format or original format if already Excel-compatible
        return $formatMap[$format] ?? $format;
    }

    /**
     * Handle ExportRecord cell processing error
     *
     * @param Worksheet $sheet
     * @param string $colLetter
     * @param int $row
     * @param Exception $e
     * @param int $colIndex
     * @param ExportRecord $record
     * @return void
     */
    private function handleExportRecordCellError(Worksheet $sheet, string $colLetter, int $row, Exception $e, int $colIndex, ExportRecord $record): void
    {
        // If a single cell fails, log the error but continue with the export
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->error('Excel export ExportRecord cell error: ' . $e->getMessage() .
                ' in column ' . $colIndex . ' for record at row ' . $row);
        }
        // Put an error placeholder in the cell
        $sheet->setCellValueExplicit("{$colLetter}{$row}", '[ERROR]', DataType::TYPE_STRING);
    }

    /**
     * Finalize columns for ExportData with constrained auto-sizing
     *
     * @param Worksheet $sheet
     * @param array $headers
     * @return void
     */
    private function finalizeExportDataColumns(Worksheet $sheet, array $headers): void
    {
        // First, enable auto-sizing for all columns
        for ($i = 0; $i < count($headers); $i++) {
            $colLetter = Coordinate::stringFromColumnIndex($i + 1);
            $sheet->getColumnDimension($colLetter)->setAutoSize(true);
        }

        // Calculate column widths to apply constraints
        $this->applyColumnWidthConstraints($sheet);

        // Apply row height constraints if needed
        $this->applyRowHeightConstraints($sheet);

        // Apply vertical alignment to all cells
        $this->applyVerticalAlignment($sheet);
    }

    /**
     * Apply column width constraints to prevent overly wide columns
     *
     * @param Worksheet $sheet
     * @return void
     */
    private function applyColumnWidthConstraints(Worksheet $sheet): void
    {
        // Calculate column widths first
        $sheet->calculateColumnWidths();

        // Apply maximum width constraints
        foreach ($sheet->getColumnDimensions() as $colDimension) {
            if (!$colDimension->getAutoSize()) {
                continue;
            }

            $colWidth = $colDimension->getWidth();
            if ($colWidth > $this->maxColumnWidth) {
                // Disable auto-size and set to maximum width
                $colDimension->setAutoSize(false);
                $colDimension->setWidth($this->maxColumnWidth);

                // Enable text wrapping for cells in this column to handle overflow
                $colLetter = $colDimension->getColumnIndex();
                $highestRow = $sheet->getHighestDataRow();
                if ($highestRow > 0) {
                    $range = "{$colLetter}1:{$colLetter}{$highestRow}";
                    $sheet->getStyle($range)->getAlignment()->setWrapText(true);
                }
            }
        }
    }

    /**
     * Apply row height constraints to prevent overly tall rows
     *
     * This method sets a fixed height for rows that contain multiline content
     * to indicate that the content is long without making rows extremely tall.
     *
     * @param Worksheet $sheet
     * @return void
     */
    private function applyRowHeightConstraints(Worksheet $sheet): void
    {
        $highestRow = $sheet->getHighestDataRow();
        $highestCol = $sheet->getHighestDataColumn();
        $highestCol = Coordinate::columnIndexFromString($highestCol);

        // Get default font for calculations
        $defaultFont = $sheet->getParent()->getDefaultStyle()->getFont();
        $defaultFontSize = $defaultFont->getSize();

        // Calculate heights for different line counts
        $singleLineHeight = $defaultFontSize * 1.2; // Default single line height

        for ($row = 1; $row <= $highestRow; $row++) {
            $hasMultilineContent = false;
            $longestLineCount = false;

            // Check each cell in this row for multiline content
            for ($col = 1; $col <= $highestCol; $col++) {
                $cell = $sheet->getCell(Coordinate::stringFromColumnIndex($col) . $row);
                $cellValue = (string) $cell->getCalculatedValue();

                // Skip empty cells
                if (empty($cellValue)) {
                    continue;
                }

                // Check if this cell has multiline content (explicit line breaks or text wrapping)
                $hasLineBreaks = strpos($cellValue, "\n") !== false;
                $hasTextWrap = $cell->getStyle()->getAlignment()->getWrapText();
                $isLongText = strlen($cellValue) > 100; // Consider text over 100 chars as potentially wrapping

                if ($hasLineBreaks || ($hasTextWrap && $isLongText)) {
                    $hasMultilineContent = true;
                    $longestLineCount = max($longestLineCount, strlen($cellValue));
                    break; // Found multiline content, no need to check other cells in this row
                }
            }

            // Set row height based on content
            if ($hasMultilineContent) {
                // Apply the maximum constraint to the multiline height
                $properHeight = $singleLineHeight * floor($longestLineCount / $this->maxColumnWidth);

                $finalHeight = min($properHeight, $this->maxRowHeight);

                $sheet->getRowDimension($row)->setRowHeight($finalHeight);
            }
        }
    }

    /**
     * Apply vertical alignment to all cells
     *
     * @param Worksheet $sheet
     * @return void
     */
    private function applyVerticalAlignment(Worksheet $sheet): void
    {
        $highestRow = $sheet->getHighestDataRow();
        $highestCol = $sheet->getHighestDataColumn();

        if ($highestRow > 0 && $highestCol) {
            // Apply vertical alignment to all data cells
            $dataRange = "A1:{$highestCol}{$highestRow}";
            $sheet->getStyle($dataRange)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_TOP);
        }
    }

    /**
     * Create appropriate writer based on file extension
     *
     * @param Spreadsheet $spreadsheet The spreadsheet to write
     * @param string $extension File extension (xlsx, xls)
     * @return IWriter
     * @throws \Exception If extension is not supported
     */
    private function createWriter(Spreadsheet $spreadsheet, string $extension): IWriter
    {
        $extension = strtolower($extension);

        switch ($extension) {
            case 'xlsx':
                return new Xlsx($spreadsheet);
            case 'xls':
                return new Xls($spreadsheet);
            default:
                throw new Exception("Unsupported Excel format: {$extension}");
        }
    }

    /**
     * Handle spreadsheet-specific errors
     *
     * @param WriterException $e
     * @param string $filename
     * @return void
     */
    private function handleSpreadsheetError(WriterException $e, string $filename): void
    {
        // Log the error
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->error('Excel export error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
        }

        // Handle the error for the user
        $errorMessage = 'Error generating Excel export: ' . $e->getMessage();
        $this->handleExportError($errorMessage);

        // Log export attempt
        if (class_exists('Exports')) {
            try {
                \Exports::exportLog($this->registry, [
                    'file_name' => $filename,
                    'log' => ['error' => $errorMessage]
                ]);
            } catch (Exception $logEx) {
                // Silently fail if logging fails
            }
        }
    }

    /**
     * Get Excel formatting for a field
     *
     * @param string $varName Field name
     * @return string Excel format code
     */
    private function getExcelFormatting(string $varName): string
    {
        switch ($varName) {
            case 'full_num':
            case 'num':
            case 'invoice_num':
            case 'name_full_num':
            case 'customer_name':
            case 'customer_name_code':
            case 'status':
            case 'tags':
            case 'type':
            case 'customer':
            case 'added_by':
            case 'modified_by':
            case 'deleted_by':
            case 'employee':
            case 'office':
            case 'media':
            case 'project':
            case 'project_name_code':
            case 'department':
            case 'group':
            case 'owner':
            case 'observer':
            case 'decision':
            case 'responsible':
            case 'relatives_children':
            case 'relatives_parent':
            case 'kind':
            case 'country':
            case 'country_name':
            case 'phone':
            case 'gsm':
            case 'fax':
            case 'web':
            case 'email':
            case 'skype':
            case 'othercontact':
            case 'priority':
            case 'visibility':
            case 'availability':
            case 'participants':
            case 'code':
            case 'name_code':
            case 'categories':
            case 'categories_names':
            case 'trademark':
            case 'trademark_name':
            case 'trademark_name_code':
            case 'history_activity':
            case 'comments':
            case 'emails':
            case 'recurrence_type':
            case 'eik':
            case 'postal_code':
            case 'in_dds':
            case 'iban':
            case 'ucn':
            case 'identity_num':
            case 'registration_volume':
            case 'registration_file':
                return NumberFormat::FORMAT_TEXT;
            case 'deadline':
            case 'validity_term':
            case 'planned_start_date':
            case 'planned_finish_date':
            case 'event_start':
            case 'event_end':
                // custom date formatting
                return 'dd.mm.yyyy';
            case 'added':
            case 'created':
            case 'modified':
            case 'deleted':
            case 'date':
            case 'identity_date':
            case 'identity_valid':
                // custom date/time formatting
                return 'dd.mm.yyyy hh:mm';
            case 'sell_price':
            case 'last_delivery_price':
            case 'average_weighted_delivery_price':
            case 'sell_price_and_currency':
            case 'last_delivery_price_and_currency':
            case 'average_weighted_delivery_price_and_currency':
                return NumberFormat::FORMAT_NUMBER_00;
            case 'exec_time':
            case 'age':
            case 'timesheet_time':
            case 'planned_time':
                return NumberFormat::FORMAT_NUMBER;
        }

        return NumberFormat::FORMAT_GENERAL;
    }

    /**
     * Optimize memory usage for large exports
     *
     * @return void
     */
    private function optimizeMemoryForExport(): void
    {
        // Perform garbage collection to free memory
        if (gc_enabled()) {
            gc_collect_cycles();
        }

        // For large exports, try to increase memory limit
        $currentLimit = ini_get('memory_limit');
        $currentLimitBytes = $this->convertToBytes($currentLimit);

        // If current limit is less than 512MB, try to increase it
        if ($currentLimitBytes < 536870912) { // 512MB in bytes
            @ini_set('memory_limit', '512M');
        }
    }

    /**
     * Convert PHP memory limit string to bytes
     *
     * @param string $memoryLimit Memory limit string (e.g., '128M')
     * @return int Memory limit in bytes
     */
    private function convertToBytes(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $last = strtolower($memoryLimit[strlen($memoryLimit) - 1]);
        $value = (int) $memoryLimit;

        switch ($last) {
            case 'g':
                $value *= 1024;
                // Fall through to next case
            case 'm':
                $value *= 1024;
                // Fall through to next case
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * {@inheritdoc}
     */
    public static function getSupportedExtensions(): array
    {
        return ['xlsx', 'xls'];
    }

    /**
     * {@inheritdoc}
     */
    public function getMimeType(string $format = null): string
    {
        $format = $format ?? $this->getDefaultExtension();

        switch (strtolower($format)) {
            case 'xlsx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            case 'xls':
                return 'application/vnd.ms-excel';
            default:
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        }
    }

    /**
     * {@inheritdoc}
     */
    public function getDefaultExtension(): string
    {
        return 'xlsx';
    }

    /**
     * {@inheritdoc}
     */
    public static function supportsFormat(string $format): bool
    {
        return in_array(strtolower($format), static::getSupportedExtensions());
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatName(): string
    {
        return 'excel';
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatOptions(): array
    {
        return [
            'extension' => [
                'type' => 'select',
                'label' => 'File Format',
                'options' => [
                    'xlsx' => 'Excel 2007+ (.xlsx)',
                    'xls' => 'Excel 97-2003 (.xls)'
                ],
                'default' => 'xlsx'
            ],
            'chunk_size' => [
                'type' => 'number',
                'label' => 'Processing Chunk Size',
                'default' => 1000,
                'min' => 100,
                'max' => 5000
            ],
            'max_column_width' => [
                'type' => 'number',
                'label' => 'Maximum Column Width',
                'default' => 50.0,
                'min' => 10.0,
                'max' => 255.0,
                'step' => 0.1,
                'description' => 'Maximum width for auto-sized columns (Excel units). Prevents extremely wide columns.'
            ],
            'max_row_height' => [
                'type' => 'number',
                'label' => 'Maximum Row Height',
                'default' => 100.0,
                'min' => 15.0,
                'max' => 500.0,
                'step' => 0.1,
                'description' => 'Maximum height for rows (points). Prevents extremely tall rows.'
            ],
            'locale' => [
                'type' => 'select',
                'label' => 'Language/Locale',
                'options' => [
                    '' => 'Auto (Application Language)',
                    'en_us' => 'English (US)',
                    'bg' => 'Bulgarian',
                    'de' => 'German',
                    'es' => 'Spanish',
                    'fr' => 'French',
                    'it' => 'Italian',
                    'nl' => 'Dutch',
                    'pt_br' => 'Portuguese (Brazil)',
                    'ru' => 'Russian',
                    'sv' => 'Swedish',
                    'no' => 'Norwegian',
                    'da' => 'Danish',
                    'fi' => 'Finnish',
                    'pl' => 'Polish',
                    'cs' => 'Czech',
                    'hu' => 'Hungarian',
                    'tr' => 'Turkish'
                ],
                'default' => '',
                'description' => 'Language for number formatting, date formatting, and function names. Auto uses application language.'
            ]
        ];
    }
}
