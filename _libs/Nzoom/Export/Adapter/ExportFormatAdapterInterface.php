<?php

namespace Nzoom\Export\Adapter;

use Nzoom\Export\Entity\ExportData;

/**
 * Interface for export format adapters
 *
 * This interface defines the contract that all export format adapters must implement.
 * Each adapter is responsible for handling the export logic for a specific format.
 */
interface ExportFormatAdapterInterface
{
    /**
     * Export data in the specific format
     *
     * @param string|resource $file The file path (string) or file pointer (resource) where the file should be saved
     * @param string $type The export type/format (xlsx, xls)
     * @param ExportData $exportData The export data containing records and headers
     * @param array $options Export options
     * @return void
     */
    public function export($file, string $type, ExportData $exportData, array $options = []): void;

    /**
     * Get the supported file extensions for this format
     *
     * @return array Array of supported file extensions (without dots)
     */
    public static function getSupportedExtensions(): array;

    /**
     * Get the MIME type for this format
     *
     * @param string|null $format Optional format to get MIME type for. If null, uses default format.
     * @return string The MIME type
     */
    public function getMimeType(string $format = null): string;

    /**
     * Get the default file extension for this format
     *
     * @return string The default file extension (without dot)
     */
    public function getDefaultExtension(): string;

    /**
     * Validate if the adapter can handle the given format
     *
     * @param string $format The format to validate
     * @return bool True if the format is supported, false otherwise
     */
    public static function supportsFormat(string $format): bool;

    /**
     * Get format-specific options that can be configured
     *
     * @return array Array of option definitions
     */
    public function getFormatOptions(): array;

    /**
     * Set format-specific configuration
     *
     * @param array $config Configuration array
     * @return self
     */
    public function setConfiguration(array $config): self;

    /**
     * Get the format name/identifier
     *
     * @return string The format identifier
     */
    public function getFormatName(): string;
}
