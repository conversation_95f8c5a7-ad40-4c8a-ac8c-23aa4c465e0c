<?php

namespace Nzoom\Export\Streamer;

/**
 * File pointer-based streamer decorator
 *
 * This decorator wraps FileStreamer and provides file pointer-based data streaming.
 * It reads data from a file pointer in chunks for memory efficiency.
 */
class PointerFileStreamer extends FileStreamer
{
    /**
     * @var resource|null File pointer resource
     */
    private $filePointer;

    /**
     * @var int Chunk size in bytes
     */
    private int $chunkSize;

    /**
     * @var int|null Total file size in bytes
     */
    private ?int $totalSize = null;

    /**
     * Constructor
     *
     * @param resource $filePointer File pointer resource
     * @param string $filename The filename to present to the browser
     * @param string $mimeType The MIME type for the content
     * @param int $chunkSize Chunk size in bytes (default: 8192)
     */
    public function __construct($filePointer, string $filename, string $mimeType = 'application/octet-stream', int $chunkSize = 8192)
    {
        if (!is_resource($filePointer)) {
            throw new \InvalidArgumentException('File pointer must be a valid resource');
        }

        parent::__construct($filename, $mimeType);

        $this->filePointer = $filePointer;
        $this->chunkSize = $chunkSize;

        // Try to get file size and stats for caching
        $stat = fstat($this->filePointer);
        if ($stat !== false) {
            if (isset($stat['size'])) {
                $this->totalSize = $stat['size'];
                $this->getHeaders()->addHeader('Content-Length', (string) $this->totalSize);
            }

            // Set cache headers based on file stats
            if (isset($stat['mtime'])) {
                $this->setLastModified($stat['mtime']);

                // Generate ETag based on file size and modification time
                if (isset($stat['size'])) {
                    $etag = md5($stat['size'] . '-' . $stat['mtime']);
                    $this->setETag($etag);
                }
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function performStreaming(): void
    {
        rewind($this->filePointer);
        while (is_resource($this->filePointer) && !feof($this->filePointer)) {
            $chunk = fread($this->filePointer, $this->chunkSize);

            if ($chunk === false || $chunk === '') {
                break;
            }

            $this->outputChunk($chunk);

            // Check if client disconnected
            if (!$this->isClientConnected()) {
                break;
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function cleanup(): void
    {
        // Clean up file pointer
        if (is_resource($this->filePointer)) {
            fclose($this->filePointer);
            $this->filePointer = null;
        }

        // Call parent cleanup
        parent::cleanup();
    }

    /**
     * Get the current chunk size
     *
     * @return int Chunk size in bytes
     */
    public function getChunkSize(): int
    {
        return $this->chunkSize;
    }

    /**
     * Set the chunk size
     *
     * @param int $chunkSize Chunk size in bytes
     * @return self
     */
    public function setChunkSize(int $chunkSize): self
    {
        if ($chunkSize <= 0) {
            throw new \InvalidArgumentException('Chunk size must be greater than 0');
        }

        $this->chunkSize = $chunkSize;
        return $this;
    }

    /**
     * Get the total file size
     *
     * @return int|null Total size in bytes, or null if unknown
     */
    public function getTotalSize(): ?int
    {
        return $this->totalSize;
    }
}
