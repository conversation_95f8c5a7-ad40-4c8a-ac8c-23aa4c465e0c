<?php

namespace Nzoom\Mvc\ControllerTrait;

use Nzoom\Navigation;

/**
 * @method getViewer(): Viewer
 * @property \Registry $registry
 * @property string $module
 * @property string $controller
 * @property string $modelName
 * @property string $modelFactoryName
 */
trait GridBasedListTrait
{
    private static array $SEARCH_PARAMS_PROTO = [
        'search_fields' => [],
        'search_fields_prev' => [],
        'compare_options' => [],
        'values' => [],
        'logical_operator' => []
    ];
    /**
     * listing of all models
     */
    private function _list() {
        if (!$this->registry['theme']->isModern()) {
            return;
        }
        // Redirect to a regular search if section_type link.
        $this->redirectTypeSection2ListForModernTheme();

        $this->viewer = $this->getViewer();
        $this->viewer->setTemplate('list.html');
        $this->viewer->data['db_col_alias'] = $this->modelFactoryName::getAlias($this->module, $this->controller);
        /** @var \Request $request */
        $request = $this->registry['request'];
        $get = $request->getGet();
        if (!($get['key'] ?? false)) {
            $request->set('key', '', true);
        }

        $url = $request->getBaseUrl(
            "{$request->getServer()['SCRIPT_NAME']}?" .
            \Router::MODULE_PARAM . "={$this->module}&controller={$this->controller}&"
        );
        $url .= 'use_ajax=1&';
        if ($request->getGet('type')) {
            $url .= "type={$request->getGet('type')}&";
        }
        $url .= "lang={$this->registry->get('lang')}&model_lang={$this->registry->get('lang')}&{$this->controller}=";


        $this->viewer->data['listData'] = $url . 'listData';
        $this->viewer->data['columnsDefinitions'] = $url . 'getListColumnsDefinitions';

        $rights = $this->registry['currentUser']->getRights();
        $this->viewer->data['manageOutlooksFeature'] = $rights[$this->module]['manage_outlooks'] != "none" ? '1' : '0';
        $this->viewer->data['showSearch'] = $rights[$this->module]['search'] != "none" ? '1' : '0';
    }

    /**
     * listing of all models
     */
    private function _getListColumnsDefinitions() {
        /** @var \Request $request */
        $request = $this->registry['request'];

        $searchValue = $request->get('value');

        $searchColumnsByFieldName = $this->getSearchColumnsByFieldName($searchValue);

        $user_id = (int) $this->registry['currentUser']->get('id');
        $role_id = (int) $this->registry['currentUser']->get('role');

        $name = $request->getGet('name');
        $value = (int) $request->getGet('value');
        $outlook = \Outlooks::getOutlook($this->registry, $name === 'section', $value, $user_id, $role_id, $this->module, $this->controller);

        if ($outlook) {
            $outlookFields = $outlook->get('current_custom_fields');
            $outlookFieldsByName = [];
            foreach ($outlookFields as $v) {
                if (!$v['position']) {
                    continue;
                }
                $outlookFieldsByName[$v['name']] = $v;
            }

            if($name === 'type') {
                try {
                    $meta = $this->getFieldsMetaData($value, array_keys($outlookFieldsByName));
                } catch (\Error $e) {
                    // TODO: log error
                }
            }

            $columnsDef = [];
            foreach ($outlookFieldsByName as $fieldName => $v) {
                $def = [
                    'fieldName' => $v['name'],
                    'type' => $v['field_type'],
                    'label' => $v['label'],
                    'origin' => $v['origin'],
                    'fieldPath' => "properties.{$v['name']}",
                    'sort' => null,
                    'model_type' => $v['model_type'],
                    'visible' => (bool)$v['position'],
                    'width' => $v['column_width'],
                ];

                if ($v['field_type'] === 'file_upload') {
                    $def = array_merge($def, $this->getListThumbnailSettings($v['source']));
                }

                $isBasic = $v['origin'] === 'basic';

                // Support sortable additional fields
                if (!$isBasic && $meta[$fieldName]['sortable'] ?? false) {
                    $def['sort'] = 'a__' . $fieldName;
                }

                $searchField = $searchColumnsByFieldName[$fieldName] ?? false;
                if ($searchField) {
                    $def['searchFieldName'] = $searchField['option_value'] ?? null;
                    $def['searchCompareOptions'] = $searchField['compare_options'] ?? null;

                    if ($isBasic && !in_array($fieldName, ['tags']) && ($searchField['option_value']??false)) {
                        $def['sort'] = $searchField['option_value'] ?? null;
                    }
                }

                $columnsDef[] = $def;
            }
            $output = [
                'outlook' => [
                    'id' => $outlook->get('id')
                ],
                'columns' => $columnsDef
            ];
            echo json_encode($output);
            exit;
        }

        $output = [
            'outlook' => [
                'id' => null,
                'module' => $this->module,
                'controller' => $this->controller,
                'model_id' => $name === 'type' ? $value: 0,
                'section' => $name === 'section' ? $value: 0,
                //'lang' => $this->registry['lang'],
                //'model_lang' => $this->model_lang,
                'model_type_id' => $name === 'type' ? $value: 0,
                'assignments_type' => 'User',
                'assignments' => [$user_id],
                'role_id' => $role_id,
            ],
            'columns' => []
        ];

        $this->registry->set('ajax_result', json_encode($output), true);
    }

    /**
     * @param $filters
     * @return array|string[]
     */
    private function generateFriendlyFilters($filters): array
    {
        /*$filtersForFriendly = $filters;
        $filtersForFriendly['params']['module'] = $this->registry->get('module');
        $filtersForFriendly['params']['controller'] = $this->registry->get('controller');*/
        return \Filters::getUserFriendlyFilters($this->registry, $filters['session_param']);
    }


    /**
     * @param $filters
     * @return \Outlook
     */
    private function getCurrentOutlook($filters): ?\Outlook
    {
        $name = 'type';
        if (!empty($filters['where'])) {
            foreach ($filters['where'] as $where) {
                $alias = $this->getDefaultModelAlias();
                $regexpModel = "/{$alias}\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu";
                if (preg_match($regexpModel, $where)) {
                    $val = trim(preg_replace($regexpModel, '$1', $where));
                    $name = 'type';
                    $value = $val;
                }
                $regexpType = "/{$alias}t\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu";
                if (preg_match($regexpType, $where)) {
                    $val = trim(preg_replace($regexpType, '$1', $where));
                    $name = 'section';
                    $value = $val;
                }
            }
        }

        $user_id = (int)$this->registry['currentUser']->get('id');
        $role_id = (int)$this->registry['currentUser']->get('role');

        return \Outlooks::getOutlook(
            $this->registry,
            $name === 'section',
            $value ?? 0,
            $user_id,
            $role_id,
            $this->module,
            $this->controller
        );
    }

    /**
     * @param $searchValue
     * @return array
     */
    private function getSearchColumnsByFieldName($searchValue): array
    {
        if ($searchValue) {
            $sesFilters = [
                'search_fields' => [
                    self::$searchAdditionalVarsSwitch??false,
                ],
                'compare_options' => ["= '%s'"],
                'values' => explode(',', $searchValue),
            ];
        } else {
            $sesFilters = [];
        }
        list($advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search) = $this->getSearchOptions($sesFilters);

        $searchColumnsByFieldName = [];
        foreach ($advanced_search as $v) {
            $searchColumnsByFieldName[$v['field_name']] = $v;
        }

        foreach ($additional_search ?: [] as $v) {
            $searchColumnsByFieldName[$v['field_name']] = $v;
        }

        foreach ($system_fields['sort']['basic_vars']??[] as $k=>$v) {
            $searchColumnsByFieldName[$v['field_name']] = $v;
        }
        return $searchColumnsByFieldName;
    }

    private function getSearchParamsForDashlet(int $dashletId): array
    {
        $dashletModel = \Dashlets::searchOne($this->registry, ['where' => ['id = ' . $dashletId]]);
        if (!$dashletModel) {
            return [];
        }
        return $dashletModel->get('filters');
    }

    /**
     * @return void
     */
    public function _listIds()
    {
        $filters = $this->prepFiltersFromRequest();

        $sql = array('select' => '',
            'from' => '',
            'where' => '',
            'group' => '',
            'order' => '',
            'limit' => '');

        $ids = $this->modelFactoryName::getIds($this->registry, $filters, $sql);

        $this->registry->set('ajax_result', json_encode($ids), true);
    }

    /**
     * Prep filters from request, bypasses the saved filters in session.
     * @param $filters
     * @return array
     */
    public function prepFiltersFromRequest($filters = []): array
    {
        $sessionParam = 'list_';
        $sessionParam = strtolower($sessionParam . $this->modelName);
        $this->registry['session']->remove($sessionParam);
        // This forces the saveSearchFilters to consider all params including sorting, event when no serch params are present.
        if (! $this->registry['request']->get('search_fields')) {
            $this->registry['request']->set('search_fields', []);
        }
        $filters = $this->modelFactoryName::saveSearchFilters($this->registry, $sessionParam, $filters);

        return $filters;
    }

    private function _getAdvancedSearchOptions(): void
    {
        $request = $this->registry['request'];

        $filters = [
            'search_fields' => $request->getGet('search_fields', []),
            'compare_options' => $request->getGet('compare_options', []),
            'values' => $request->getGet('values', []),
            'values_autocomplete' => $request->getGet('values_autocomplete', []),
            'date_period' => $request->getGet('date_period', []),
            'logical_operator' => $request->getGet('logical_operator', []),
        ];
        $valuesAK = $request->getGet('values_autocomplete', null);
        if ($valuesAK) {
            $filters['values_autocomplete'] = $valuesAK;
        }

        list($advanced_search, $system_fields, $saved_filters, $simple_search, $additional_search) = $this->getSearchOptions($filters);

        $alias = $this->getDefaultModelAlias();

        $this->viewer = new \Viewer($this->registry);
        $search_fields = array('basic_vars' => $advanced_search, 'additional_vars' => $additional_search);

        $this->viewer->data['session_filters'] = $filters;
        $this->viewer->data['saved_filters'] = $saved_filters;
        $this->viewer->data['simple_search_defs'] = $simple_search;
        $this->viewer->data['search_fields'] = $search_fields;
        $this->viewer->data['advanced_search_options'] = json_encode($advanced_search);
        $this->viewer->data['additional_search_options'] = json_encode($additional_search) . (isset($additional_columns) ? ', additional_columns = ' .json_encode($additional_columns) : '');
        $this->viewer->data['switch_additional'] = isset($this::$searchAdditionalVarsSwitch) ? $this::$searchAdditionalVarsSwitch : false;
        $this->viewer->data['alias'] = $alias;

        $this->viewer->data['additional_hidden_filters'] = !empty($additional_hidden_filters) ? $additional_hidden_filters : array();
        $this->viewer->data['system_fields'] = $system_fields;
        $this->viewer->setFrameset('_action_search_advanced_form.html');

        $this->registry->set('ajax_result', $this->viewer->fetch(), true);
    }

    /**
     * @param string $exclude coma separated list of actions to exclude
     * @param string $include coma separated list of actions to include
     * @return void
     * @throws \Exception
     */
    private function _getListMultiActionsPanel(string $exclude = '', string $include = '')
    {
        $type = $this->registry['request']->getGet('type');
        $typeSection = $this->registry['request']->getGet('type_section');

        $viewer = new \Viewer($this->registry);


        $typesArr = [];
        if ($type) {
            $typesArr = [(int)$type];
        } elseif ($typeSection) {
            $typesArr = $this->idListFromModels($this->fetchTypesByTypeSection((int) $typeSection));
        }


        $tags = $this->getTagOptions($typesArr);
        if (count($tags) && isset($tags[0])) {
            // Plain list of tags
            $viewer->data['tags_options'] = $tags;
        } else {
            // Tags are grouped by section
            if ($tags['contain_optgroups']??false) {
                unset($tags['contain_optgroups']);

                foreach ($tags as $optGroupName => $optGroupList) {
                    $optGroupListLiltered = array_filter($optGroupList, function ($option) {
                        return $option['active_option'] == 1;
                    });

                    if (empty($optGroupListLiltered)) {
                        unset($tags[$optGroupName]);
                    } else {
                        $tags[$optGroupName] = array_values($optGroupListLiltered);
                    }
                }
            }
            $viewer->data['tags_optgroups'] = $tags;
        }

        if (!empty($typesArr)) {
            if (in_array('setstatus', $this->actionDefinitions)) {
                $viewer->data['statuses'] = $this->getStatusOptions($typesArr);
            }

            // get print patterns
            $printPatterns = $this->getPrintPatternOptions($typesArr);
            $this->data['patterns_grouped'] = $printPatterns ? ['' => $printPatterns] : [];
        }

        if (preg_match('/export/', $include)) {
            $singleType = count($typesArr) == 1 ? $typesArr[0] : '';
            $viewer->data['exportAction'] = $this->createExportAction(
                "{$this->module}{$singleType}",
                    $typesArr??[],
                    $typeSection??[]
            );
        }

        $viewer->data['exclude'] = $exclude;
        $viewer->data['include'] = $include;
        $viewer->data['session_param'] = $session_param??'';
        $viewer->data['module'] = $this->module??'';
        $viewer->data['controller'] = $this->controller??'';
        $viewer->setFrameset('multiple_actions_list.html');

        $this->registry->set('ajax_result', $viewer->fetch(), true);
    }

    private function idListFromModels($models, $field = 'id'): array{
        $ids = [];
        array_walk($models, function (&$model) use(&$ids, $field) {
            $ids[] = (int) $model->get($field);
        });
        return $ids;
    }

    private function _getListTitle() {
        $viewer = new \Viewer($this->registry);
        $request = $this->registry['request'];
        $controllerName = $request->getGet('for_controller');

        if ($type = $request->getGet('type')) {
            $title = $viewer->getListTitleForType($type, $this->module, $controllerName);
        } elseif ($typeSection = $request->getGet('type_section')) {
            $title = $viewer->getListTitleForTypeSection($typeSection, $this->module, $controllerName);
        } else {
            $title = $this->i18n($this->module);
        }

        $this->actionCompleted = true;
        $this->registry->set('ajax_result',  $title, true);
    }

    private function _getListActions() {
        $viewer = new \Viewer($this->registry);
        $viewer->data['moduleTemplatesDir'] = PH_MODULES_DIR . $this->module . '/view/templates/';
        $viewer->data['available_actions'] = $this->getActions();
        $viewer->setFrameset('_list_actions.html');
        $this->actionCompleted = true;
        $this->registry->set('ajax_result', $viewer->fetch(), true);
    }

    /**
     * @param array $types
     * @return array
     */
    private function fetchStatusesByTypes(array $types): array
    {
        $substatusModelFactory = "\\{$this->modelFactoryName}_Substatuses";
        if (!class_exists($substatusModelFactory)) {
            return [];
        }

        $typeModel = $this->fetchTypesById($types);

        // Types may have inactive types ids
        $typeIds = $this->idListFromModels($typeModel);
        $typesIdsStr = "'" . implode("','", $typeIds) . "'";
        $moduleName = \General::plural2singular(strtolower($this->modelName));

        // task_type, contract_type, doc_type
        if ($moduleName === 'document') {
            $moduleName = 'doc';
        }

        $typeFieldName = $moduleName . '_type';
        $statusAlias = $substatusModelFactory::getAlias($this->modelFactoryName, 'status');

        $filtersSubstatuses = [
            'model_lang' => $this->registry->get('lang'),
            'sanitize' => true,
            'where' => [
                "{$statusAlias}.$typeFieldName IN ({$typesIdsStr})",
                "{$statusAlias}.active = 1",
            ]
        ];

        return $substatusModelFactory::search($this->registry, $filtersSubstatuses);
    }

    /**
     * @param array $type
     * @return array
     */
    private function getStatusOptions(array $types): array
    {
        $substatuses = $this->fetchStatusesByTypes($types);

        $statusOptions = [];
        if(empty($substatuses)) {
            return $statusOptions;
        }
        $typeModel = $this->fetchTypesById($types)[0];
        foreach ($substatuses as $key => $substatuses_by_status) {
            $statusOptions[] = [
                'id' => $key,
                'name' => $this->i18n("{$this->module}_status_{$key}"),
                'requires_comment' => $typeModel ? $typeModel->get($key . '_requires_comment') : 'without_comment'
            ];
            foreach ($substatuses_by_status as $substatus) {
                $statusOptions[] = [
                    'id' => $substatus['parent_status'] . '_' . $substatus['id'],
                    'name' => '-- ' . $substatus['name'],
                    'requires_comment' => $substatus['requires_comment']
                ];
            }
        }
        return $statusOptions;
    }

    /**
     * @param array $types
     * @return array
     */
    private function getTagOptions(array $types): array
    {
        return \Dropdown::getTags(
            [
                $this->registry,
                'model' => strtolower(\General::singular2plural($this->modelName)),
                'model_types' => $types,
            ]);
    }

    /**
     * @param array $typeSection
     * @return array
     */
    private function fetchTypesById(array $typeIds): array
    {
        $typeIdsStr = "'" . implode("','", $typeIds) . "'";
        $typeAlias = $this->getDefaultModelAlias().'t';
        $filters = [
            'where' => [
                "{$typeAlias}.id IN ({$typeIdsStr})",
                "{$typeAlias}.active = 1",
            ],
            'model_lang' => $this->registry->get('lang'),
            'sanitize' => true,
        ];

        return $this->searchForTypes($filters);
    }

    /**
     * @param int $typeSection
     * @return array
     */
    private function fetchTypesByTypeSection(int $typeSection): array
    {
        $typeAlias = $this->getDefaultModelAlias().'t';
        $filters = [
            'where' => [
                "{$typeAlias}.type_section = '{$typeSection}'",
                "{$typeAlias}.active = 1",
            ],
            'model_lang' => $this->registry->get('lang'),
            'sanitize' => true,
        ];

        return $this->searchForTypes($filters);
    }

    private function searchForTypes($filters): array
    {
        $factoryClass = "\\{$this->modelFactoryName}_Types";
        return $factoryClass::search($this->registry, $filters);
    }

    /**
     * @param array $type
     * @return array
     */
    private function getPrintPatterns(array $types): array
    {
        $typesStr = "'" . implode("','", $types) . "'";
        $filters = [
            'where' => [
                "p.model = '{$this->modelName}'",
                "p.model_type IN ({$typesStr})",
                'p.active = 1',
                'p.format = "pdf"',
                'p.list = 0'
            ],
            'sort' => ['p.position != 0 DESC', 'p.position ASC', 'p.id ASC'],
            'sanitize' => true
        ];
        if ($this->registry['currentUser']->get('is_portal')) {
            $filters['where'][] = 'p.is_portal = 1';
        }
        return \Patterns::search($this->registry, $filters);
    }

    private function getPrintPatternOptions(array $types): array
    {
        $patterns = $this->getPrintPatterns($types);

        $patternsOptions = [];
        if (empty($patterns)) {
            return $patternsOptions;
        }

        foreach ($patterns as $pattern) {
            $patternsOptions[] = [
                'id' => $pattern->get('id'),
                'name' => $pattern->get('name'),
            ];
        }
        return $patternsOptions;
    }

    /**
     * Redirect search to list for modern theme as there is no longer search page
     *
     * @return void
     */
    private function redirectSearch2ListForModernTheme(): void
    {
        /** @var \Request $request */
        $request = $this->registry['request'];

        // Prottect from redirecting to list page when the request accepts JSON, because of the REST API
        if (! $this->registry['theme']->isModern() || \Auth::$is_rest) {
            return;
        }

        // Support session_param_prefix
        $session_param_prefix = $request->getGet('session_param_prefix', false);
        if (
            $session_param_prefix
            && preg_match('/^dashlets_(?P<id>\d+)$/', $session_param_prefix, $matches)
        ) {
            $this->redirect(
                $this->module,
                'list',
                http_build_query($this->getSearchParamsForDashlet((int) $matches['id']))
            );
            return;
        }

        // Support standard search parameters
        $requestUri = $request->getServer()['REQUEST_URI'];
        $requestQuery = preg_replace("/^[^?]+\??(.*)$/", '$1', $requestUri);
        parse_str($requestQuery, $queryParams);
        unset($queryParams['launch'], $queryParams[$this->module]);


        if (($queryParams['filters_action'] ?? null) === 'loadfilter' && ($queryParams['filter_name'] ?? null)) {
            try {
                $queryParams = $this->getLoadedFilterParams((int)$queryParams['filter_name']);
            } catch (\Exception $e) {
                $this->registry['messages']->setError($this->i18n('error_filter_not_found'));
            }
        }

        if ($typeSection = ($queryParams['type_section'] ?? null)) {
            $queryParams = array_merge_recursive(
                $queryParams,
                $this->getSearchParamsForTypeSection((int) $typeSection)
            );
            unset($queryParams['type_section']);
        }

        $this->redirect($this->module, 'list', http_build_query($queryParams));
    }

    private function redirectTypeSection2ListForModernTheme(): void
    {
        /** @var \Request $request */
        $request = $this->registry['request'];
        // Protect from redirecting to list page when the request accepts JSON, because of the REST API
        if (!$this->registry['theme']->isModern() || \Auth::$is_rest || !$request->getGet('type_section')) {
            return;
        }
        $typeSection = $request->getGet('type_section');
        $queryParams = $this->getSearchParamsForTypeSection((int) $typeSection);
        $this->redirect($this->module, 'list', http_build_query($queryParams));
    }

    /**
     * @param int $filterId
     * @return array
     */
    private function getLoadedFilterParams(int $filterId): array
    {
        $filter = \Filters::searchOne($this->registry, ['where' => ["`id` = '{$filterId}'"]]);
        return \General::slashesStrip($filter->getParams());
    }

    /**
     * Generates params for search url - search by single type_section
     * @param int $typeSection
     * @return array
     */
    public function getSearchParamsForTypeSection(int $typeSection): array
    {
        $modelAlias = $this->getModelTypeAlias($this->modelFactoryName, $this->module, $this->controller);
        return $this->generateSearchFilterParams($modelAlias, 'type_section', $typeSection);
    }

    /**
     * Generates params for search url - search by single type
     * @param int $type
     * @return array
     */
    public function getSearchParamsForType(int $type): array
    {
        return $this->getSearchParamsForBasic('type', $type);
    }


    /**
     * Returns the db alias of model d for documents, n for nomenclatures, ...
     * @param string $factoryName
     * @param $module
     * @param $controller
     * @return string
     */
    function getModelAlias (string $factoryName, $module, $controller): string
    {
        $factoryName = "\\{$factoryName}";
        return $factoryName::getAlias($module, $controller);
    }

    /**
     * Returns the db alias of models type table - dt for documents, nt for nomenclatures, ...
     * @param string $factoryName
     * @param $module
     * @param $controller
     * @return string
     */
    function getModelTypeAlias (string $factoryName, $module, $controller): string
    {
        return $this->getModelAlias("{$factoryName}_Types", $module, $controller) . 't';
    }

    /**
     * Returns the db alias of model for assignments. da for documents, ta for tasks, ...
     * @param string $factoryName
     * @param $module
     * @param $controller
     * @return string
     */
    function getModelAssignAlias (string $factoryName, $module, $controller): string
    {
        return $this->getModelAlias($factoryName, $module, $controller) . 'a';
    }


    /**
     * Generates params for search url - basic var
     * @param string $varName
     * @param string $varValue
     * @param string $compareOptions
     * @return array
     */
    public function getSearchParamsForBasic(
        string $varName,
        string $varValue,
        string $compareOptions = "= '%s'"
    ): array {
        $modelAlias = $this->getModelAlias($this->modelFactoryName, $this->module, $this->controller);
        return $this->generateSearchFilterParams($modelAlias, $varName, $varValue, $compareOptions);
    }

    /**
     * Generates params for search url - search for assigned
     * @param string $assignmentName
     * @param string $assignmentUser
     * @return array
     */
    public function getSearchParamsForAssignment(
        string $assignmentName,
        string $assignmentUser
    ): array {
        $compareOptions = "assignment_{$assignmentName} = %s";
        $modelAlias = $this->getModelAssignAlias($this->modelFactoryName, $this->module, $this->controller);
        return $this->generateSearchFilterParams($modelAlias, 'assigned_to', $assignmentUser, $compareOptions);
    }

    /**
     * @param string $modelAlias
     * @param string $varName
     * @param string $varValue
     * @param string $compareOptions
     * @return array|array[]
     */
    private function generateSearchFilterParams(
        string $modelAlias,
        string $varName,
        string $varValue,
        string $compareOptions = "= '%s'"
    ): array {
        $queryParams = self::$SEARCH_PARAMS_PROTO;
        $queryParams['search_fields'][] = "{$modelAlias}.{$varName}";
        $queryParams['search_fields_prev'][] = "{$modelAlias}.{$varName}";
        $queryParams['compare_options'][] = $compareOptions;
        $queryParams['values'][] = $varValue;
        $queryParams['logical_operator'][] = 'AND';
        return $queryParams;
    }

    /**
     * @return mixed
     */
    private function getDefaultModelAlias()
    {
        $factory_name = $this->modelFactoryName;
        $alias_words = explode('_', $this->modelFactoryName, 2);
        return $factory_name::getAlias($alias_words[0], (isset($alias_words[1]) ? $alias_words[1] : $alias_words[0]));
    }

    public function _saveFilter() {
        /** @var \Registry $registry */
        $registry = $this->registry;

        /** @var \Request $request */
        $request = $registry['request'];

        $search_module = $request->getGet('search_module');
        $search_controller = $request->getGet('search_controller');
        $filterName = $request->getGet('save_filter_name');
        $isNewFilter = (bool) $request->getGet('save_filter_name_isCustom');
        $saveAsAction = $request->getGet('save_as_action');

        if (!$filterName) {
            $resp = ['error' => $registry['translater']->translate('error_no_filter_name_specified')];
            $this->actionCompleted = true;
            $registry->set('ajax_result', json_encode($resp), true);
            http_response_code(400); // Bad request (missing parameter)
            return;
        }

        if ($search_module) {
            if (!$search_controller) {
                $search_controller = $search_module;
            }
        } else {
            $search_module = $registry->get('module');
            $search_controller = $registry->get('controller');
        }

        $sessionPrefix = 'list_';
        $sessionParam = strtolower($sessionPrefix . $this->modelName);
        $registry['session']->remove($sessionParam);

        // exception for filter for contact persons
        if ($search_module == 'customers'
            && $search_module == $search_controller
            && $sessionParam == 'filter_customers_contactperson') {
            $search_controller = 'contactpersons';
        }

        $params = $request->getGet();

        try {
            if ($isNewFilter) {
                // insert
                $model = $this->addNewFilter($filterName, [
                    'module' => $search_module,
                    'controller' => $search_controller,
                    'module_from' => $search_module,
                    'controller_from' => $search_controller,
                    'action' => (bool) $saveAsAction,
                ], $params);
            } else {
                // update
                $model = $this->updateFilter($filterName, $params, (bool) $saveAsAction);
            }
        } catch (\Exception $e) {
            $resp = ['error' => $e->getMessage()];
            $this->actionCompleted = true;
            $registry->set('ajax_result', json_encode($resp), true);
            http_response_code(400); // Bad request (missing parameter)
            return;
        }

        $v = new \Viewer($registry);
        $v->setFrameset('actions_box_item.html');
        // Workaround the conventional action parameter (the requested action - string)
        $v->assign([]);
        $v->getRenderer()->assign('action', $this->generateFilterAction($model));
        $actionHtml = $v->fetch();

        $resp = [
            'success' => true,
            'filterId' => $model->get('id'),
            'actionHtml' => $actionHtml,
            'isAction' => (bool)(int) $model->get('action'),
        ];

        $this->actionCompleted = true;
        header('Content-type: application/json');
        $registry->set('ajax_result', json_encode($resp), true);
    }

    public function _loadFilter() {
        /** @var \Registry $registry */
        $registry = $this->registry;

        /** @var \Request $request */
        $request = $registry['request'];

        $filterId = $request->getGet('id');

        $model = \Filters::searchOne($this->registry, ['where' => ["f.id={$filterId}"]]);

        if (
            !$model
            || (int) $model->get('added_by') !== 1
            && (int) $model->get('added_by') !== (int) $registry['currentUser']->get('id')
        ) {
            $resp = ['error' => "Can't load filter!"];
            $this->actionCompleted = true;
            http_response_code(403); // Forbidden
            $registry->set('ajax_result', json_encode($resp), true);
            return;
        }
        $params = \General::slashesStrip($model->getParams());

        // TODO: When available in production use Navigation::buildNzoomUrl
        //  $url = Navigation::buildNzoomUrl($this->module, 'list', $this->controller, $params);
        $urlData = [
            'launch' => $model->get('module'),
            $model->get('module') => $model->get('controller'),
            $model->get('controller') => 'list',
        ];
        $urlData = array_merge($urlData, $params);
        $url = $_SERVER['PHP_SELF'] . "?" . http_build_query($urlData);

        $resp = [
            'success' => true,
            'filterId' => $filterId,
            'url' => $url,
        ];

        $this->actionCompleted = true;
        $registry->set('ajax_result', json_encode($resp), true);
    }

    public function _deleteFilter() {
        /** @var \Registry $registry */
        $registry = $this->registry;

        /** @var \Request $request */
        $request = $registry['request'];

        $filterId = $request->getGet('id');

        if (!$filterId) {
            $resp = ['error' => "No filter id specified!"];
            $this->actionCompleted = true;
            http_response_code(400); // Bad request (missing parameter)
            $registry->set('ajax_result', json_encode($resp), true);
            return;
        }

        $model = \Filters::searchOne($this->registry, ['where' => ["f.id={$filterId}"]]);
        if (!$model || (int) $model->get('added_by') !== (int) $registry['currentUser']->get('id')) {
            $resp = ['error' => "Can't delete filter!"];
            $this->actionCompleted = true;
            http_response_code(403); // Forbidden
            $registry->set('ajax_result', json_encode($resp), true);
            return;
        }

        \Filters::purge($registry, ['id' => $filterId]);

        $models = \Filters::search($registry, [
            'where' => [
                "f.module = '{$model->get('search_modul')}'",
                "f.controller = '{$model->get('search_controller')}'",
                "f.user_defined = 1",
                "f.added_by = '{$registry['currentUser']->get('id')}' OR",
                "f.added_by = 1",
            ],
            'sanitize' => 1
        ]);

        $saved_filters = array();
        foreach ($models as $model) {
            $saved_filters[] = array('option_value' => $model->get('id'),
                'label' => $model->get('name'));
        }

        $resp = [
            'success' => true,
            'filterId' => $filterId,
            'filters' => $saved_filters
        ];
        $this->actionCompleted = true;
        $registry->set('ajax_result', json_encode($resp), true);
    }

    private function addNewFilter($name, $filterData, $params) {
        // Add some moredata
        $paramsEncoded = \Filter::encodeParams(\Filter::extractFiltersToSave($params));
        $data = [];
        foreach ($filterData as $filterName => $filterValue) {
            $data[$filterName] = "{$filterName}='{$filterValue}'";
        }
        $data = array_merge($data, [
            'name' => "name='{$name}'",
            'added_by' => "added_by='{$this->registry['currentUser']->get('id')}'",
            'added' => "added=now()",
            'user_defined' => "user_defined=1",
            'active' => "active=1",
            'params' => "params='{$paramsEncoded}'",
        ]);
        $model = new \Filter($this->registry, ['name'=>$name]);
        $model->insert($data, $name, $this->registry['currentUser']->get('id'));
        if (!$model->get('id')) {
            throw new \Exception("Error saving filter");
        }

        return \Filters::searchOne($this->registry, ['where' => ["f.id={$model->get('id')}"]]);
    }

    private function updateFilter(int $id, array $params, bool $saveAsAction = false) {
        /** @var \Filter $model */
        $model = \Filters::searchOne($this->registry, ['where' => ["f.id={$id}"]]);
        $paramsEncoded = \Filter::encodeParams(\Filter::extractFiltersToSave($params));
        $data = [
            'action' => "action=" . (int) $saveAsAction,
            'modified' => "modified=now()",
            'modified_by' => "modified_by={$this->registry['currentUser']->get('id')}",
            'params' => "params='{$paramsEncoded}'",
        ];
        $model->update($data, $id, $this->registry['currentUser']->get('id'));
        foreach ($data as $name => $value) {
            $model->set($name, preg_replace("/^[^=]+=(.*)/", '$1', $value), true);
        }
        return $model;
    }

    private function getListThumbnailSettings(string $source): array
    {
        $config = $this->registry['config'];
        $source = \General::parseSettings($source);

        return [
            'list_image_thumbnail' => (bool) ($source['list_image_thumbnail'] ?? $config->getParam('files', 'list_image_thumbnail')),
            'list_image_thumbnail_width' => (int) ($source['list_image_thumbnail_width'] ?? $config->getParam('files', 'list_image_thumbnail_width')),
            'list_image_thumbnail_height' => (int) ($source['list_image_thumbnail_height'] ?? $config->getParam('files', 'list_image_thumbnail_height')),
        ];
    }

    private function prepListRecordFileuploadAttributes(\Model $record, array $additionalVars): void
    {
        foreach ($additionalVars as $k=>$v) {
            $record->getVarValue($k, 0);
            $var = $record->getVar($k);
            if (isset($var['type']) && $var['type'] === 'file_upload' && is_a($var['value'], \File::class)) {
                $var = array_merge($var, $this->getListThumbnailSettings($var['source']));
                $var['value']->set('viewUrl', $var['value']->getFileURL('viewfile') , true);
                $var['value']->set('getUrl', $var['value']->getFileURL() , true);
            }
            $record->properties[$k] = $var;
        }
    }

    /**
     * @param int $modelType
     * @param array $fieldNames
     * @return array
     * @throws \Exception|\Throwable
     */
    private function getFieldsMetaData(int $modelType, array $fieldNames): array
    {
        $fieldNamesStr = implode("','", $fieldNames);
        $sql = <<<SQL
            SELECT model, model_type, name, sortable, searchable
                FROM `_fields_meta`
                WHERE `model` = '{$this->modelName}'
                  AND `model_type` = '{$modelType}'
                  AND `name` IN ('{$fieldNamesStr}')
            SQL;

        set_error_handler(function($errno, $errstr) {
            throw new \Exception("Error fetching fields metadata", $errno, $errstr);
        });
        $meta = $this->registry['db']->getAll($sql);
        restore_error_handler();

        if(!is_array($meta)) {
            throw new \Exception("Error fetching fields metadata");
        }

        $metaMapped = [];
        foreach ($meta as $v) {
            $metaMapped[$v['name']] = $v;
        }
        return $metaMapped;
    }

    /**
     * @param \Filter $model
     * @return array
     */
    private function generateFilterAction(\Filter $model): array
    {
        /** @var \Registry $registry */
        $registry = $this->registry;

        $filterAction = 'search';

        $actionQuery = [
            'filters_action' => 'loadfilter',
            'filter_name' => $model->get('id')
        ];

        if ($registry['model_locked_by_me'] ?? false) {
            $actionQuery['unlock'] = $registry['model_locked_by_me'];
        }

        $url = Navigation::buildNzoomUrl(
            'tasks',
            'search',
            $this->module != $this->controller ? $registry['controller'] : null,
            $actionQuery
        );

        $theme = $registry['theme'];
        $modluleControler = $this->module . ($this->controller != $this->module ? '_' . $this->controller : '');
        return [
                'module_param' => $registry['module_param'],
                'controller_param' => ($this->controller != $this->module) ? $registry['controller_param'] : '',
                'action_param' => $registry['action_param'],
                'action' => $filterAction,
                'model_id' => '0',
                'name' => $filterAction . $model->get('id'),
                'label' => $model->get('name'),
                'selected' => false,
                'expanded' => false,
                'url' => $url,
                'img' => "menu/{$modluleControler}",
                'icon' => is_callable([$theme, 'getIconForAction']) ? $theme->getIconForRecord($modluleControler) : null
            ] + $model->getAll();
    }
}
