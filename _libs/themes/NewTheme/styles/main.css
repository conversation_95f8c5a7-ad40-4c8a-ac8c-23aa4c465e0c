/*Main*/
.m_container {
    height: 100%;
}
.m_main {
    height: 72%;
    width: 100%;
    vertical-align: top;
}
.m_title_bar td {
    background-color: #B2B2B2;
    border-bottom: 1px solid #222433;
}
.m_title_bar div {
    height: 14px;
    padding: 2px;
    float: left;
    font-size: 11px;
    font-family: Roboto, sans-serif;
    color: #FFF;
    font-style: italic;
    /*font-weight: bold;*/
}
.m_welcome {
    float: right !important;
    text-align: right;
    color: #FFF !important;
    padding-right: 5px !important;
    border-left: 1px solid #AAAAAA;
}
.m_logout {
    float: right !important;
    text-align: left;
    color: #222433 !important;
    padding-right: 5px !important;
    background-color: #B2B2B2 !important;
    border-left: 1px solid #222433;
}
.m_logout:hover {
    background-color: #CACACA !important;
}
.m_logout a:link, .m_logout a:visited, .m_logout a:active {
    display: block;
    font-family: <PERSON><PERSON>, sans-serif;
    font-size: 10px;
    text-decoration: none;
    color: #FFFFFF;
    background: url('../images/logout.png') transparent no-repeat -4px -1px;
    padding-left: 10px;
}
.m_logout a:hover {
    text-decoration: none;
    color: #666666;
    background: url('../images/logout2.png') transparent no-repeat -4px -1px;
}
.m_stopwatchbar {
    float: right !important;
    padding-right: 10px !important;
    text-align: right;
}
.m_lockbar {
    float: right !important;
    padding-right: 10px !important;
    text-align: right;
}
.m_navbar a:link, .m_navbar a:visited, .m_navbar a:active {
    font-family: Roboto, sans-serif;
    font-size: 11px;
    text-decoration: none;
    color: #FFFFFF;
}
.m_navbar a:hover {
    text-decoration: none;
    color: #222433;
}
#m_doc_sections {
    padding-left: 5px;
}
.m_announcement_bar {
    color: #FFFFFF;
    background-color: #04599A;
    border-top: 0px none;
    border-bottom: 1px solid #999999;
    font-weight: bold;
}
.m_announcement_bar a:link, .m_announcement_bar a:visited, .m_announcement_bar a:active, .m_announcement_bar a:hover {
    color: #FFFFFF;
}
.m_announcement_wrapper {
    height: 14px;
    padding: 2px;
    width: 96%;
    float: left;
    position: relative;
    overflow: hidden;
    font-size: 10px;
    font-family: Roboto, sans-serif;
}
.m_announcement_wrapper > div {
    position: relative;
    white-space: nowrap;
    width: 1000px;
    left: 1200px;
}
.m_announcement_wrapper > div span, .m_announcement_hidebar {
    margin: 0 5px;
}
/*Header*/
.m_header {
    vertical-align: top;
    height: 20px;
}
.m_header_logo {
    background-color: rgba(235, 235, 235, 0.761);
    height: 62px;
    padding-left: 25px;
}
.m_header_info {
    text-align: right;
    padding-right: 20px;
    font-family: Roboto, sans-serif;
    font-size: 11px;
    color: #FF6600;
    font-weight: bold;
    font-style: italic;
    padding-bottom: 5px;
}
.m_header_info a:link, .m_header_info a:active, .m_header_info a:visited {
    color: #FF6600;
}
.m_header_info a:hover {
    color: #222433;
}
.m_header_divider1 {
    background: url('../images/header_bg2.jpg') #E2E2E2 repeat-x 0 0;
    height: 10px;
}
.m_header_divider2 {
    background-color: #222433 !important;
    height: 5px;
    padding-top: 2px;
    text-align: right;
}
.m_header_body {
    background: 0 0 no-repeat #B2B2B2;
    height: 105px;
}
.m_lang_menu {
    height: 19px;
    text-align: right;
    padding-right: 20px;
    float: right !important;
    margin-left: 15px;
}
.m_lang_menu img {
    opacity: 0.30;
    filter: alpha(opacity=30);
    border: 1px solid #F1F1F1;
}
.m_lang_menu img:hover {
    opacity: 1;
    filter: alpha(opacity=100);
    border: 1px solid #999999;
}
.m_lang_menu a:hover img, .m_lang_menu a.selected img, .m_lang_menu a.selected img:hover {
    opacity: 1;
    filter: alpha(opacity=100);
    border: 1px solid #999999;
}
.m_lang_menu a:link, .m_lang_menu a:active, .m_lang_menu a:visited {
    color: #888888;
}
.m_lang_menu a:hover, .m_lang_menu a.selected {
    color: #666666;
}
.m_lang_menu a.selected {
    font-weight: bold;
}
.m_emailing {
    background: url('../images/emails_off.png') no-repeat;
    width: 16px;
    height: 16px;
    float: right !important;
}
/*Header Quick Menu*/
.m_header_quick_menu_sign {
    color: #666666;
    font-family: Roboto, sans-serif;
    padding-bottom: 3px;
}
.m_header_quick_menu {
    border-bottom: 1px solid #E3E3E3;
    font-family: Roboto, sans-serif;
    vertical-align: middle;
    height: 19px;
    width: 205px;
}
/* Commented Backslash Hack hides rule from IE5-Mac \*/
.m_header_menu_reversed a span {
    float: none;
}
/* End IE5-Mac hack */
.m_header_menu_reversed a:hover, .m_header_menu_reversed a.selected {
    background-position: 0% -21px;
}
.m_header_menu_reversed a:hover span, .m_header_menu_reversed a.selected span {
    font-weight: bold;
    color: #000;
    background-position: 100% -21px;
}
.m_header_menu_reversed a:hover img, .m_header_menu_reversed a.selected img {
    filter: alpha(opacity=100);
    opacity: 1;
}
.m_header_menu_reversed a img {
    filter: alpha(opacity=33);
    opacity: 0.33;
    float: left;
    margin: 4px 2px 0 0;
}
/*Left Menu*/
.m_leftmenu {
    vertical-align: top;
    width: 150px;
    background-color: #F4F4F4;
    border-right: 1px solid #CCCCCC;
}
/*Body*/
.m_body {
    vertical-align: top;
    padding: 15px;
}
/*Footer*/
.m_footer_divider {
    background-color: #222433;
    height: 6px;
}
.m_footer_copyright {
    background-color: #B2B2B2;
    padding: 5px 0;
    /*height: 57px;*/
    text-align: center;
}
.m_footer_container {
    border-top: 1px solid #CCCCCC;
    border-bottom: 1px solid #EEEEEE;
    background: url('../images/footer_bg4.gif') #F5F5F5;
    width: 100%;
    left: 0px;
    bottom: 0px;
    _bottom: expression(eval(document.body.scrollBottom));
    text-align: left;
    color: #AAA;
    font-size: 10px;
    z-index: 10000;
}
.m_footer_container p {
    margin: 0px;
    padding: 3px 3px 0 10px;
    height: 26px;
}
.m_footer_container a {
    color: #888;
    font-size: 9px !important;
    padding-left: 5px;
}
.m_footer_container a.selected {
    font-weight: bold;
}
.m_footer_container #m_footer_switcher {
    cursor: pointer;
    background: url('../images/t_footer.jpg') #E1E1E1 repeat-x 0 0;
    height: 7px;
    font-size: 1px;
    line-height: 0;
    opacity: 0.9;
    filter: alpha(opacity=90);
}
.m_footer_container #m_footer_switcher div {
    height: 7px;
    font-size: 1px;
    line-height: 0;
}
#m_current_locked_record {
    padding-right: 10px;
}
#m_locked_records {
}
.page_menu_link {
    cursor: pointer;
}
fieldset {
    border: #CCCCCC solid 1px;
    padding-bottom: 0px;
    padding-top: 0px;
    margin-top: 10px;
}
legend {
    color: #999999;
}
#messages_container {
}
