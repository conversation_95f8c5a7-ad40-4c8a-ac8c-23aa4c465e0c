/*Custom*/
.clear {
    clear: both;
}

.floatl {
    float: left;
}

.floatr {
    float: right;
}

.nowrap {
    white-space: nowrap;
}

div#form_container, div.form_container {
    vertical-align: top;
    width: 800px;
}

div#form_container.main_panel_container, div.form_container.main_panel_container {
    vertical-align: top;
    width: inherit;
    min-width: 800px;
}

td#form_container, td.form_container {
    vertical-align: top;
    min-width: 780px;
}

#color_preview {
    width: 50px;
    height: 16px;
    border: 1px solid #AAAAAA;
    margin-left: 5px;
    float: left;
}

#colors > #color_preview {
    height: 14px;
}

.strong {
    font-weight: bold !important;
}

.stronger {
    font-weight: bold;
    font-size: 12px;
}

.italic {
    font-style: italic;
}

.pointer {
    cursor: pointer;
}

.emphasized {
    font-size: 14px;
    font-weight: bold;
}

.drag {
    cursor: move;
}

.uppercase {
    text-transform: uppercase;
}

.hcenter {
    text-align: center;
}

.vmiddle {
    vertical-align: middle !important;
}

.hright {
    text-align: right;
    padding-right: 5px !important;
}

.hleft {
    text-align: left;
    padding-left: 5px !important;
}

tr td.vtop, .vtop {
    vertical-align: top;
}

tr td.vbottom, .vbottom {
    vertical-align: bottom;
}

.spanlink {
    color: #666666;
}

.spanlink:hover {
    text-decoration: underline;
    color: #163F8F;
}

.required {
    width: 1em;
    color: #FF0000;
    vertical-align: top;
}

.unrequired {
    width: 1em;
}

.nopadding {
    padding: 0px !important;
}

.red {
    color: #FF0000;
}

.green {
    color: #298923;
}

.dimmed {
    filter: alpha(opacity = 33);
    opacity: 0.33;
}

.menu_bullet {
    float: left;
}

.menu_bullet2 {
    float: left;
    margin: 5px 2px;
}

.undefined {
    font-style: italic;
}

/* styles for dropdown and combobox without value */
select.undefined {
    color: #9D9D9D;
    font-style: normal;
}

select.undefined option, select.undefined optgroup {
    color: #000000;
    font-style: normal;
}

select option.undefined {
    color: #9D9D9D;
    font-style: italic;
}

/* style for first option of combo with filled in custom value */
select option.undefined:not([value='']):not([value='0']) {
    color: #000000;
}

select option.inactive_option, select optgroup.inactive_option {
    color: #A6A6A6;
}

select option.legend, select optgroup.legend {
    color: #666666;
    font-style: italic;
}

select option[disabled], select optgroup[disabled] {
    color: #808080;
}

.untranslated {
    font-style: italic;
    color: #777777;
}

.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden;
}

.legend {
    font-style: italic;
    color: #666666;
}

table.legend_table {
    width: 780px;
    line-height: 16px;
    border: 1px solid #CCCCCC;
}

/*
.icon {
    float: left;
    margin-right: 3px;
}
*/
.red_pushpin {
    background: url('../images/small/pushpin_red.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.green_pushpin {
    background: url('../images/small/pushpin_green.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.blue_pushpin {
    background: url('../images/small/pushpin_blue.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.yellow_pushpin {
    background: url('../images/small/pushpin_yellow.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.navy_pushpin {
    background: url('../images/small/pushpin_navy.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.pink_pushpin {
    background: url('../images/small/pushpin_pink.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.brown_pushpin {
    background: url('../images/small/pushpin_brown.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.purple_pushpin {
    background: url('../images/small/pushpin_purple.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.orange_pushpin {
    background: url('../images/small/pushpin_orange.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.black_pushpin {
    background: url('../images/small/pushpin_black.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.white_pushpin {
    background: url('../images/small/pushpin_white.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.grey_pushpin {
    background: url('../images/small/pushpin_grey.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.electric_pushpin {
    background: url('../images/small/pushpin_electric.png') no-repeat 0 0;
    padding-left: 10px ! important;
    margin-right: 10px;
}

.help {
    cursor: help;
}

.loading {
    text-align: center;
    color: #5070AD;
    background: #FFFFF2;
    height: 40px;
    width: 250px;
    padding: 2px 0;
    border: 1px solid #CCCCCC;
    z-index: 1000000 !important;
}

.calendar_trigger {
    cursor: pointer;
    margin-left: 3px;
}

#tree_container td {
    padding: 0 2px;
}

/* Documents */
span.incoming {
    background: url('../images/incoming.png') no-repeat 0 0;
    padding-left: 13px;
}

span.outgoing {
    background: url('../images/outgoing.png') no-repeat 0 0;
    padding-left: 13px;
}

span.internal {
    background: url('../images/internal.png') no-repeat 0 0;
    padding-left: 13px;
}

.t_list span.incoming, .t_list span.outgoing, .t_list span.internal {
    margin-right: 13px;
}

span.help {
    background: url('../images/small/info.png') no-repeat 0 0;
    padding-left: 10px;
}

.documents_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.documents_status.opened {
    background: url('../images/small/documents_opened.png') no-repeat 0 0;
}

.documents_status.locked {
    background: url('../images/small/documents_locked.png') no-repeat 0 0;
}

.documents_status.closed {
    background: url('../images/small/documents_closed.png') no-repeat 0 0;
}

.documents_status_substatus {
    height: 16px;
    padding: 2px 0 0 4px;
}

.contracts_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.contracts_status.opened {
    background: url('../images/small/contracts_opened.png') no-repeat 0 0;
}

.contracts_status.locked {
    background: url('../images/small/contracts_locked.png') no-repeat 0 0;
}

.contracts_status.closed {
    background: url('../images/small/contracts_closed.png') no-repeat 0 0;
}

.contracts_status_substatus {
    height: 16px;
    padding: 2px 0 0 4px;
}

.tasks_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.tasks_status.planning {
    background: url('../images/small/tasks_planning.png') no-repeat 0 0;
}

.tasks_status.progress {
    background: url('../images/small/tasks_progress.png') no-repeat 0 0;
}

.tasks_status.finished {
    background: url('../images/small/tasks_finished.png') no-repeat 0 0;
}

.tasks_status_substatus {
    height: 16px;
    padding: 2px 0 0 4px;
}

.projects_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.projects_status.planning {
    background: url('../images/small/projects_planning.png') no-repeat 0 0;
}

.projects_status.progress {
    background: url('../images/small/projects_progress.png') no-repeat 0 0;
}

.projects_status.control {
    background: url('../images/small/projects_control.png') no-repeat 0 0;
}

.projects_status.finished, .projects_status.finished_success {
    background: url('../images/small/projects_finished.png') no-repeat 0 0;
}

.projects_status.finished_failed {
    background: url('../images/small/projects_finished_failed.png') no-repeat 0 0;
}

.events_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.events_status.planning {
    background: url('../images/small/events_planning.png') no-repeat 0 0;
}

.events_status.progress {
    background: url('../images/small/events_progress.png') no-repeat 0 0;
}

.events_status.finished {
    background: url('../images/small/events_finished.png') no-repeat 0 0;
}

.events_status.unstarted {
    background: url('../images/small/events_unstarted.png') no-repeat 0 0;
}

.events_status.moved {
    background: url('../images/small/events_moved.png') no-repeat 0 0;
}

/* Emails campaigns */
.emails_campaigns_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.emails_campaigns_status.preparation {
    background: url('../images/small/emails_campaigns_preparation.png') no-repeat 0 0;
}

.emails_campaigns_status.ready {
    background: url('../images/small/emails_campaigns_ready.png') no-repeat 0 0;
}

.emails_campaigns_status.partial {
    background: url('../images/small/emails_campaigns_partial.png') no-repeat 0 0;
}

.emails_campaigns_status.sent {
    background: url('../images/small/emails_campaigns_sent.png') no-repeat 0 0;
}

.emails_campaigns_status.cancelled {
    background: url('../images/small/emails_campaigns_cancelled.png') no-repeat 0 0;
}

a.reply {
    width: 24px;
    height: 24px;
    display: block;
    margin: 1px;
    float: left;
    background: url('../images/reply.png') no-repeat center;
}
a.replyAll {
    width: 24px;
    height: 24px;
    display: block;
    margin: 1px;
    float: right;
    background: url('../images/reply_all.png') no-repeat center;
}

.finance_budgets_status {
    height: 16px;
    padding: 2px 0 0 18px;
}

.finance_budgets_status.preparation {
    background: url('../images/small/budgets_preparation.png') no-repeat 0 0;
}

.finance_budgets_status.progress {
    background: url('../images/small/budgets_progress.png') no-repeat 0 0;
}

.finance_budgets_status.approved {
    background: url('../images/small/budgets_approved.png') no-repeat 0 0;
}

.finance_budgets_status.obsolete {
    background: url('../images/small/budgets_obsolete.png') no-repeat 0 0;
}

div.documents_status, div.contracts_status, div.tasks_status, div.projects_status, div.events_status, div.finance_budgets_status {
    height: 12px !important;
}

.deleted {
    color: red;
    text-decoration: line-through;
}

.tree_root {
    font-size: 12px;
    font-weight: bold;
    text-decoration: underline;
    background-color: #FFFFCC;
}

/* Scrollable Tables*/
div.scroll_box_container {
    width: 100%;
    clear: both;
}

div.scroll_box {
    overflow: auto;
    border: 1px solid #CCCCCC;
    height: 200px;
    width: 200px;
    background-color: #F1F1F1;
}

div.scroll_box_checkboxes {
    overflow: auto;
    border: 1px solid #CCCCCC;
    width: 200px;
    background-color: #F1F1F1;
}

div.scroll_box_checkboxes_height {
    min-height: 60px;
    max-height: 200px;
}

.scroll_box_container .scroll_box {
    width: 100%;
}

div.scroll {
    overflow: auto;
    border: 1px solid #CCCCCC;
    height: 150px;
    width: 800px;
    background-color: #F1F1F1;
}

#model_history .t_header .td1 {
    width: 43px;
}

#model_history .t_header .td2 {
    width: 101px;
}

#model_history .t_header .td3 {
    width: 470px;
}

#model_history .t_header .td4 {
    width: 163px;
}

#model_history .t_header .td5 {
    width: 40px;
}

#model_history .t_body .td1 {
    width: 40px;
}

#model_history .t_body .td2 {
    width: 100px;
}

#model_history .t_body .td3 {
    width: 469px;
}

#model_history .t_body .td4 {
    width: 140px;
}

#model_history .t_body .td5 {
    width: 20px;
}

#model_history .t_foot {
    width: 802px;
}

div.history_activity_container {
    max-width: 600px;
    margin: 10px auto;
    max-height: 90vh;
    overflow-y: auto;
}

div.history_activity_container tr.activity_date > td {
    padding-top: 10px;
}

div.history_activity_container td.activity_data.t_bottom_border {
    border-bottom: 1px solid #CCCCCC !important;
}

div.history_activity_container td.activity_data .content_container {
    display: block;
}

div.history_activity_container td.activity_data .content {
    margin: 5px 0;
    padding: 5px;
    background-color: #FFFFFF;
    border: 1px solid #CCCCCC;
    max-width: 540px;
    overflow: auto;
    display: inline-block;
    clear: both;
}

div.history_activity_container, div.history_activity_container .strong, div.history_activity_container .stronger {
    color: #333;
}

tr.show_more a {
    display: block;
    margin: 5px 0;
}

tr.show_more div.loading, tr.details_row > td > div.loading {
    background-image: url('../images/loading.gif') !important;
    background-repeat: no-repeat;
    background-color: transparent;
    background-position: 50% 50%;
    border: 0px none;
    color: #5070AD;
    width: 100%;
    height: 14px;
    padding: 5px 0;
}

.activity_icon {
    background: url('../images/history_activity.png') transparent no-repeat 5px 5px;
    width: 20px;
}

.activity_icon.add, .activity_icon.multiadd {
    background-image: url('../images/add.png');
}

.activity_icon.transform {
    background-image: url('../images/transform.png');
}

.activity_icon.clone {
    background-image: url('../images/clone.png');
}

.activity_icon.edit, .activity_icon.multiedit {
    background-image: url('../images/edit.png');
}

.activity_icon.status, .activity_icon.multistatus {
    background-image: url('../images/setstatus.png');
}

.activity_icon.add_attachments, .activity_icon.del_attachments {
    background-image: url('../images/attachments.png');
}

.activity_icon.add_timesheet, .activity_icon.edit_timesheet {
    background-image: url('../images/timesheets.png');
}

.activity_icon.add_comment, .activity_icon.edit_comment {
    background-image: url('../images/comments.png');
}

.activity_icon.email {
    background-image: url('../images/send_email.png');
}
.activity_icon.receive_email {
    background-image: url('../images/receive_email.png');
}

.activity_icon.create {
    background-image: url('../images/create.png');
}

.activity_icon.relatives {
    background-image: url('../images/relatives.png');
}

.hlinks {
    width: 250px;
    vertical-align: top;
}

.fast_links_contaner {
    height: 80px;
}

.fast_links_contaner .t_table {
    width: 504px;
}

.tree_container {
    padding: 0px 5px;
    min-height: 100px;
    max-height: 450px;
    overflow-y: auto;
}

.tree_container input {
    margin-left: -5px !important;
}

.tree_container .branch_is_current_model {
    font-weight: bold;
    color: green;
}

.empty_row {
    height: 26px;
}

.divider_cell {
    width: 15px;
}

.index_class {
    width: 300px;
    padding: 3px 0px 3px 5px;
    border-left: 1px solid #CCCCCC;
    vertical-align: top;
}

.index_arrow_anchor {
    display: block;
    width: 16px;
    height: 10px;
    text-align: center;
}

.layout_switch {
    width: 97%;
}

/* Icons */
a .icon_button {
    cursor: pointer;
    /*margin: 1px;*/
    background: #EFEFEF url('../images/icon_button.gif') repeat-x;
    border: 1px solid #DDDDDD;
    vertical-align: middle;
    filter: alpha(opacity = 50);
    opacity: 0.50;
}

a > .icon_button {
    margin: -3px 0 0 0;
    padding: 1px;
}

a:hover .icon_button {
    background-color: #EFEFEF;
    border: 1px solid #5371AF;
    filter: alpha(opacity = 100);
    opacity: 1;
}

.combobox_button {
    cursor: pointer;
    background: #EFEFEF url('../images/icon_button.gif') repeat-x;
    border: 0px none;
    vertical-align: top;
    padding: 1px 0 0 0;
    margin: 1px 1px 1px -17px;
    height: 12px;
    width: 12px;
    z-index: 1000;
}

/* IE7 only */
* + html .combobox_button {
    margin: 2px 1px 1px -20px;
}

.help_information_panel {
    padding: 10px;
}

.relatives_cell_style {
    padding: 10px 5px 10px 5px;
}

.relatives_style {
    font-size: 11px;
    color: #666666;
}

a:hover .relatives_style {
    text-decoration: none;
    color: #5371AF;
}

.relatives_button_cell {
    padding: 10px 5px 2px 5px;
}

.main_information {
    border-bottom: #CCCCCC solid 1px;
}

/* Design for BB */
.bb_table_edit {
    border: 0;
    width: 100%;
    margin: 0px;
    padding: 0px;
}

.bb_table_edit td {
    border: 0 !important;
}

.bb_main_cell {
    margin: 0pt !important;
    padding: 0pt !important;
    border: none;
}

.bb_main_cell_table {
    background-color: white;
    width: 100%;
    border: none;
    margin: 0pt;
    padding: 0px;
}

.bb_corners {
    /*border:medium none;*/
    height: 20px;
    margin: 0px !important;
    padding: 0px !important;
}

.bb_left_upper {
    background: transparent url('../images/rounded-white.png') no-repeat 0px 0px;
    height: 30px !important;
    padding: 5px 6px 5px 6px !important;
}

.bb_center_upper {
    background: transparent url('../images/rounded-white.png') repeat-x 0px -56px;
    height: 20px !important;
}

.bb_right_upper {
    background: transparent url('../images/rounded-white.png') no-repeat -10px 0px;
    height: 30px !important;
    width: 35px;
    vertical-align: top;
}

.bb_icon_close {
    margin: 10px 10px 0px 2px !important;
}

.bb_left_middle {
    background: transparent url('../images/rounded-white.png') repeat-y 0px -118px;
}

.bb_right_middle {
    background: transparent url('../images/rounded-white.png') repeat-y -10px -118px;
    width: 35px;
}

.bb_left_down {
    background: transparent url('../images/rounded-white.png') no-repeat 0px -36px;
    padding: 0px 10px 3px 10px !important;
}

.bb_center_down {
    background: transparent url('../images/rounded-white.png') repeat-x 0px -92px;
}

.bb_right_down {
    background: transparent url('../images/rounded-white.png') no-repeat -10px -36px;
    padding: 0px 10px 3px 10px !important;
    width: 35px;
}

.bb_title {
    cursor: move;
}

.bb_icon_close_span {
    cursor: default;
}

.bb_table_for_numbers {
    padding: 0px !important;
}

.bb_numbers_expand_collapse {
    border: none !important;
    padding-right: 3px;
}

.bb_numbers_expand_collapse .switch_expand, .bb_numbers_expand_collapse .switch_collapse {
    margin: 2px 0px 0px 0px;
}

.bb_numbers_number {
    border: none !important;
    padding-left: 0px !important;
}

/*severities*/
.verylight, .verylight a:link, .verylight a:visited {
    color: #AAAAAA;
    font-weight: normal;
}

.verylight a:hover {
    color: #153F91;
}

.light, .light a:link, .light a:visited {
    color: #777777;
    font-weight: normal;
}

.light a:hover {
    color: #153F91;
}

.heavy, .heavy a:link, .heavy a:visited {
    color: #FF9900;
    font-weight: normal;
}

.heavy a:hover {
    color: #153F91;
}

.veryheavy, .veryheavy a:link, .veryheavy a:visited {
    color: #FF0000;
    font-weight: normal;
}

.veryheavy a:hover {
    color: #153F91;
}

.intermediate, .intermediate a:link, .intermediate a:visited {
    color: #FFCE37;
    font-weight: normal;
}

.intermediate a:hover {
    color: #153F91;
}

.severity_legend_cell {
    background-color: #F8F8F8;
    border: 1px solid #CCCCCC;
    margin: 5px;
    padding: 0px !important;
    text-align: center;
    width: 80px;
}

/* Customers Contacts */
option.customers_phone {
    background: url('../images/small/phone.png') no-repeat 2px 0;
    padding-left: 16px;
}

option.customers_fax {
    background: url('../images/small/fax.png') no-repeat 2px 0;
    padding-left: 16px;
}

option.customers_gsm {
    background: url('../images/small/gsm.png') no-repeat 2px 0;
    padding-left: 16px;
}

option.customers_web {
    background: url('../images/small/web.png') no-repeat 2px 0;
    padding-left: 16px;
}

option.customers_email {
    background: url('../images/small/email.png') no-repeat 2px 0;
    padding-left: 16px;
}

option.customers_skype {
    background: url('../images/small/skype.png') no-repeat 2px 0;
    padding-left: 16px;
}

option.customers_othercontact {
    background: url('../images/small/othercontact.png') no-repeat 2px 0;
    padding-left: 16px;
}

/* Menu for check/uncheck all checkboxes */
.check_all_menu, .reports_export_options_container {
    background: #FFFFF0;
    border: 1px solid #AAAAAA;
    font-family: Roboto, sans-serif;
    font-size: 10px;
    text-decoration: none;
    position: absolute;
}

.check_all_menu div, .reports_export_options_container div {
    display: block;
    background: #FFFFF0;
    height: 20px;
    color: #636363;
    padding: 5px 5px 0px 5px;
    text-align: left;
    font-weight: normal;
}

.check_all_menu div:hover, .reports_export_options_container div:hover {
    background: #F1F1F1;
    color: #636363;
    font-weight: bold;
    cursor: pointer;
}

.checkall {
    width: 13px;
    height: 13px;
    border: none;
    background-repeat: no-repeat;
    background-position: center;
    background-color: transparent;
}

.checkall.none {
    background-image: url('../images/checkbox_none.png');
}

.checkall.some {
    background-image: url('../images/checkbox_.png');
}

.checkall.all {
    background-image: url('../images/checkbox_all.png');
}
input.checkall {
    color: transparent;
    border: none!important;
    cursor: default;
}
input.checkall[value=""] {
    background-image: url('../images/checkbox_none.png');
}
input.checkall[value=" "] {
    background-image: url('../images/checkbox_all.png');
}

#search_advanced_container {
    /*min-width: 850px;!important;*/
}

.dashlet_loader {
    height: 200px;
    text-align: center;
    background: url("../images/loading1.gif") no-repeat scroll 50% 50% transparent;
}

/* Menu for print options */
.print_menu {
    background: #FFFFF0;
    border: 1px solid #AAAAAA;
    font-family: Roboto, sans-serif;
    font-size: 10px;
    margin: 2px 0px 0px 2px;
    z-index: 1000;
    text-decoration: none;
    position: absolute;
}

.print_menu div {
    display: block;
    background: #FFFFF0;
    height: 20px;
    color: #636363;
    padding: 5px 5px 0px 5px;
}

.print_menu div:hover {
    background: #F1F1F1;
    color: #636363;
    font-weight: bold;
    cursor: pointer;
}

/* Passwords Strength */
.password_strength_container {
    border: 1px solid #AAAAAA;
    width: 200px;
    background-color: #E2E2E2;
    height: 14px;
}

.password_strength_container div {
    font-size: 1px;
    height: 12px;
    width: 0px;
    border: 1px solid #EFEFEF;
}

.password_strength_container div.pass_short {
    width: 38px;
    background: #FF0000;
}

.password_strength_container div.pass_weak {
    width: 78px;
    background: #FF7200;
}

.password_strength_container div.pass_fair {
    width: 118px;
    background: #FFE900;
}

.password_strength_container div.pass_medium {
    width: 158px;
    background: #FDFF00;
}

.password_strength_container div.pass_strong {
    width: 198px;
    background: #3BCE08;
}

/*Events Availability*/
.availability {
    width: 90%;
    font-size: 9px;
    padding: 0px;
    text-align: center;
    margin: 1px 0 0 2px;
}

.availability.busy {
    background-color: #FF0000;
    color: #FFFFFF;
}

.availability.available {
    background-color: #2FCA2F;
    color: #FFFFFF;
}

.dashlet_content {
    overflow: hidden;
}

/* Drag & Drop*/
.drag_left {
    float: left;
    padding: 5px;
    width: 48%;
    margin-bottom: 10px;
}

.drag_right {
    float: right;
    padding: 5px;
    width: 48%;
    margin-bottom: 10px;
}

.insert_before {
    border-top: 2px solid blue !important;
}

.insert_after {
    border-bottom: 2px solid blue !important;
}

.level_1, .level_2, .level_3 {
    width: 280px;
    vertical-align: top;
    padding: 4px 0px 4px 10px;
}

.drop_1, .drop_2, .drop_3 {
    vertical-align: top;
}

.menu_begin {
    border-top: solid 1px #999999;
}

tr.row_red_error td {
    background-color: #CC1100;
}

.erred_item {
    background-color: #FF8F93 !important;
}

/* Menu for outlook options */
.outlook_options {
    background: #FFFFF0;
    border: 1px solid #AAAAAA;
    font-family: Roboto, sans-serif;
    font-size: 10px;
    margin: 25px 0px 0px -234px;
    z-index: 100;
    visibility: hidden;
    display: none;
    text-decoration: none;
    position: absolute;
    padding: 0px !important;
}

/*IE settings*/
.outlook_options #outlook_options_title {
    width: 250px;
}

/*Mozilla settings*/
.outlook_options > #outlook_options_title {
    width: 246px;
}

.outlook_all_options {
    position: relative;
    /*this fixes the drag& drop of outlook items in IE*/
    overflow: auto;
    width: 250px;
    height: 400px;
}

.outlook_options ol {
    list-style-position: inside;
    padding: 0px !important;
    margin: 0;
}

.outlook_options_buttons {
    padding: 2px 5px !important;
    border-top: 1px solid #AAAAAA;
}

/*IE settings*/
.outlook_options .outlook_options_buttons {
    width: 250px;
}

/*Mozilla settings*/
.outlook_options > .outlook_options_buttons {
    width: 240px;
}

.outlook_options_buttons button {
    font-size: 9px !important;
}

.outlook_options_buttons button_hov {
    font-size: 9px !important;
}

.outlook_options ol li {
    display: list-item;
    padding: 1px 0 1px 1px;
    border-bottom: 1px solid #AAAAAA;
    list-style-type: decimal-leading-zero;
}

/*IE settings*/
.outlook_options ol li input {
    margin-left: -2px;
}

/*Mozilla settings*/
.outlook_options ul li > input {
    margin-left: -7px;
}

.layout_draggable td {
    background-color: #FFD2D2 !important;
    height: 20px !important;
}

.layout_droppable {
    height: 20px !important;
    background-color: #CDE9FF !important;
}

.gt2_droppable {
    height: 30px !important;
    background-color: #CDE9FF !important;
}

tr.gt2_draggable td {
    background-color: #FFD2D2 !important;
}

.btn_unpressed {
    border: outset 2px #888888;
    background-color: silver;
    font-family: Roboto;
    font-size: 12px;
    color: red;
    width: 20px;
    height: 20px;
    padding: 1px;
}

.btn_pressed {
    border: inset 2px #888888;
    background-color: silver;
    font-family: Roboto;
    font-size: 12px;
    color: green;
    width: 20px;
    height: 20px;
    padding: 1px;
}

div.audit {
    max-height: 80vh;
    overflow-y: auto;
    overflow-x: hidden;
    word-break: break-word;
    table-layout: fixed;
}

div.audit.email {
    overflow-x: auto;
}

div.has_gt2 {
    float: left;
}

div.has_gt2, div#model_audit h2 {
    max-width: 600px;
}

div.gt2_audit_legend {
    float: left;
    margin-left: 10px;
    margin-right: 10px;
    bottom: 0px !important;
}

div.gt2_audit_legend td.legend_color {
    height: 20px;
    width: 20px;
}

div.gt2_audit_legend td.legend_text {
    background-color: transparent !important;
    text-align: left;
}

.finance_audit_added td {
    background-color: #FFFFAA !important;
    text-align: center;
}

.finance_audit_deleted td {
    background-color: #FF8888 !important;
    text-align: center;
}

.finance_audit_updated td.new_value {
    background-color: rgb(255, 200, 150) !important;
    text-align: center;
}

.finance_audit_updated td.old_value {
    background-color: rgb(200, 255, 200) !important;
    text-align: center;
}

.finance_audit_updated td {
    text-align: center;
}

.communication_tab_selected {
    color: #5371AF !important;
    text-decoration: underline !important;
}

.remaining_sum {
    font-size: 16px;
    font-weight: bold;
}

.chart_container {
    position: relative;
    top: -18px;
}

/* Menu for activities options menu */
.activities_panel {
    background: #FFFFF0;
    border: 1px solid #AAAAAA;
    font-family: Roboto, sans-serif;
    font-size: 10px;
    z-index: 100;
    visibility: hidden;
    display: none;
    text-decoration: none;
    position: absolute;
    padding: 0px !important;
}

.t_odd table.activities .t_odd td, .t_even table.activities .t_odd td {
    background-color: #F8F8F8;
    padding: 3px;
    border-top: 1px solid #F8F8F8;
    border-bottom: 1px solid #F8F8F8;
    white-space: normal;
}

.t_even table.activities .t_even td, .t_odd table.activities .t_even td {
    background-color: #ECECEC;
    padding: 3px;
    border-top: 1px solid #ECECEC;
    border-bottom: 1px solid #ECECEC;
    white-space: normal;
}

.t_even table.activities .activities_title_row td, .t_odd table.activities .activities_title_row td {
    background-color: #ECECEC;
    padding: 3px;
    border-top: 0;
    border-bottom: 0;
    white-space: normal;
}

.image_button {
    cursor: pointer;
    background: #EFEFEF url('../images/icon_button.gif') repeat-x;
    border: 1px solid #DDDDDD;
    vertical-align: middle;
    margin: 3px 0 0 0;
    padding: 1px;
}

.asterisk_contact_button {
    background: transparent url('../images/button_phone_left.png') no-repeat left bottom !important;
    height: 20px;
    padding-left: 20px !important;
    margin-bottom: 1px;
    float: left;
    clear: both;
    cursor: pointer !important;
}

.asterisk_contact_button .asterisk_contact_button_image {
    float: left;
    margin: 3px 0 0 -15px;
}

.asterisk_contact_button .asterisk_contact_button_number {
    background: transparent url("../images/button_phone_right.png") no-repeat scroll top right !important;
    float: left;
    height: 16px;
    padding: 4px 5px 0 4px !important;
    color: #333333 !important;
    white-space: nowrap;
}

.asterisk_contact_note {
    float: left;
    height: 17px;
    padding: 3px 0 0 4px !important;
    color: #000000 !important;
}

.side_panel_loader {
    height: 80px;
    text-align: center;
    background: url("../images/loading1.gif") no-repeat scroll 50% 50% transparent;
}

.side_panel_content {
    overflow: hidden;
    width: 100%;
    float: left;
}

.side_panel_content table {
    background-color: #F1F1F1;
    border-collapse: collapse;
    word-wrap: break-word;
    table-layout: fixed;
}

.side_panel_content table.t_layout_table td {
    padding: 3px;
}

.side_panel_content table table.attachments {
    border-collapse: separate;
    table-layout: auto;
}

.drag_side_panel {
    float: left;
    vertical-align: top;
    padding: 0px;
    width: 600px;
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 12px;
}

.drag_side_panel > div > div {
    padding: 4px 2px;
}

.side_panel_container {
    vertical-align: top;
    width: 620px;
    padding-top: 13px;
}

.side_panel_settings_show_div {
    margin: 68px 0px 0px 0px;
}

.side_panel_settings_hide_div {
    margin: 0 0 0 250px;
}

.side_panel_settings_show_div, .side_panel_settings_hide_div {
    position: absolute;
    height: 80px;
    width: 12px;
    padding: 2px;
}

.side_panel_settings_show_div.button, .side_panel_settings_show_div.button:hover {
    border-width: 1px 1px 1px 0;
    padding: 2px;
}

.side_panel_settings_hide_div.button, .side_panel_settings_hide_div.button:hover {
    border-width: 1px;
    padding: 2px;
}

.side_panel_settings_show_div img, .side_panel_settings_hide_div img {
    margin: 33px 0;
    border: 0px none;
}

/* Menu for side panel options */
.side_panel_options {
    background: #FFFFF0;
    border: 1px solid #AAAAAA;
    color: #666666;
    font-family: Roboto, sans-serif;
    font-size: 10px;
    margin: 68px 0px 0px 0px;
    z-index: 100;
    visibility: hidden;
    display: none;
    text-decoration: none;
    position: absolute;
    padding: 0px !important;
}

/*IE settings*/
.side_panel_options #side_panel_options_title {
    width: 250px;
}

/*Mozilla settings*/
.side_panel_options > #side_panel_options_title {
    width: 246px;
}

.side_panel_all_options {
    position: relative;
    /*this fixes the drag&drop of side panel items in IE*/
    overflow: auto;
    width: 250px;
    height: 250px;
}

.side_panel_options ol {
    list-style-position: inside;
    padding: 0px !important;
    margin: 0;
}

.side_panel_options_buttons {
    padding: 2px 5px !important;
    border-top: 1px solid #AAAAAA;
}

/*IE settings*/
.side_panel_options .side_panel_options_buttons {
    width: 250px;
}

/*Mozilla settings*/
.side_panel_options > .side_panel_options_buttons {
    width: 240px;
}

.side_panel_options_buttons button {
    font-size: 9px !important;
}

.side_panel_options_buttons button_hov {
    font-size: 9px !important;
}

.side_panel_options ol li {
    display: list-item;
    padding: 1px 0 1px 1px;
    border-bottom: 1px solid #AAAAAA;
    list-style-type: decimal-leading-zero;
}

/*IE settings*/
.side_panel_options ol li input {
    margin-left: -2px;
}

/*Mozilla settings*/
.side_panel_options ol li > input {
    margin-left: -7px;
}

/*Trial balance report special stype*/
tr.unallocated_payment_row td, tr.unallocated_payment_row td a {
    color: red !important;
}

/* Finance available actions options */
.available_actions_options {
    background-color: #F5F5F5;
    width: 200px;
}

.available_actions_options ol {
    list-style-position: inside;
    padding: 0px !important;
    margin: 0;
}

.available_actions_options ol li {
    display: list-item;
    padding: 4px 0 4px 1px;
    border-bottom: 1px solid #AAAAAA;
    list-style-type: decimal-leading-zero;
}

.available_actions_options ol li.sortable {
    cursor: move;
}

label.user_portal {
    background: url('../images/small/user_portal.png') no-repeat 0 0;
    padding-left: 15px ! important;
}

label.user_normal {
    background: url('../images/small/user.png') no-repeat 0 0;
    padding-left: 15px ! important;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #F8F8F8;
    padding: 3px;
    border-top: 1px solid #F8F8F8;
    border-bottom: 1px solid #F8F8F8;
    white-space: normal;
}

/*
 * Communications styles
 */
.communications_resend {
    border: 1px solid #CCCCCC;
    position: absolute;
    z-index: 100;
}

.communications_resend tr.communications_resend_title_row td {
    background-color: #DFDFDF;
    border-bottom: 1px solid #CCCCCC;
    border-top: 1px solid #DFDFDF;
    color: #555555;
    font-family: Roboto, sans-serif;
    font-size: 10px;
    font-weight: bold;
    text-align: left;
    padding: 3px 3px 3px 8px;
    border-top: 1px solid #F8F8F8;
}

.communications_resend tr.t_odd td, .communications_resend tr.t_even td {
    border-bottom: 1px solid #CCCCCC;
    border-top: 1px solid #F8F8F8;
    text-align: left;
    padding: 3px;
}

.communications_resend tr.t_odd td {
    background-color: #F8F8F8;
}

.communications_resend tr.t_even td {
    background-color: #ECECEC;
}

.communication_resend_button_row {
    border-bottom: none !important;
    padding-left: 5px !important;
}

.communication_resend_box_close {
    float: right;
    cursor: pointer;
    background: url('../images/small/delete.png') no-repeat 0 0;
    width: 13px;
    height: 13px;
}

.communication_resend_box_title {
    float: left;
}

.communications_resend span.communications_resend_checkbox_container {
    vertical-align: middle;
}

.communications_resend span.communications_resend_checkbox_container > input {
    margin: 0 0 0 3px;
    vertical-align: middle;
}

.communication_fade td {
    border-bottom: none !important;
    border-top: none !important;
}
