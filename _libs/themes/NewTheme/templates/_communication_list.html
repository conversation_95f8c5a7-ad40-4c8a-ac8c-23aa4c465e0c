  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border" style="width: 100%;">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap" style="width: 20px"><div class="t_caption_title">{#num#|escape}</div></td>
      {if !$custom_filters || $custom_filters.view_mails eq 'all'}
        <td class="t_caption t_border {$communications_sort.from.class}" nowrap="nowrap" style="width: 100px"><div class="t_caption_title" onclick="{$communications_sort.from.link}">{#from#|escape}</div></td>
      {/if}
      <td class="t_caption t_border {$communications_sort.to.class}" nowrap="nowrap" style="width: 140px"><div class="t_caption_title" onclick="{$communications_sort.to.link}">{#to#|escape}</div></td>
      <td class="t_caption t_border {$communications_sort.content.class}"><div class="t_caption_title" onclick="{$communications_sort.content.link}" style="width: 300px">{#content_email#|escape}</div></td>
      <td class="t_caption t_border {$communications_sort.added.class}" nowrap="nowrap" style="width: 100px"><div class="t_caption_title" onclick="{$communications_sort.added.link}">{#date#|escape}</div></td>
      <td class="t_caption" style="width: 14px">&nbsp;</td>
    </tr>
    {counter start=$pagination.start name='item_counter' print=false}
    {foreach name='i' from=$communications item='communication'}
      <tr class="{cycle values='t_odd,t_even'} vtop" onclick="expandCommunication($(this).select('td.content')[0]);">
        <td class="t_border hright" nowrap="nowrap">
          {if ($communication.attachments)}
            {if $communication.attachments_link}
              <a href="{$communication.attachments_link}">
                <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$communication.module}', '{$communication.controller}', {$communication.model_id}, '{$communication.attachments}')"
                     onmouseout="mclosetime()" />
              </a>
            {else}
              <img border="0" src="{$theme->imagesUrl}attachments.png" width="16" height="16" alt="" style="vertical-align: middle;" />
            {/if}
          {/if}
          {if ($communication.type eq 'comment')}
            <img src="{$theme->imagesUrl}comments.png" width="16" height="16" alt="{#communications_comment#}" title="{#communications_comment#}" border="0" style="vertical-align: middle;" />
          {else if ($communication.type eq 'email')}
            {if $communication.status eq 'received'}
              <img src="{$theme->imagesUrl}receive_email.png" width="16" height="16" alt="{#communications_email_received#}" title="{#communications_email_received#}" border="0" style="vertical-align: middle;" />
            {else}
              <img src="{$theme->imagesUrl}send_email.png" width="16" height="16" alt="{#communications_email_sent#}" title="{#communications_email_sent#}" border="0" style="vertical-align: middle;" />
            {/if}
          {/if}
          &nbsp;{counter name='item_counter' print=true}
        </td>
        {if !$custom_filters || $custom_filters.view_mails eq 'all'}
          <td class="t_border  {$communications_sort.from.isSorted}">
            {$communication.from|escape|default:"&nbsp;"}
          </td>
        {/if}
        <td class="t_border {$communications_sort.to.isSorted} communicationdatefield" >
          {foreach name='c' from=$communication.to item='to_user'}
            {if $to_user.mail_status eq 'sent' && $to_user.receive_status eq 'received'}
              {assign var='name_color' value='000000'}
              {assign var='name_message' value='email_sent_received_comment_reason'}
            {elseif $to_user.mail_status eq 'received'}
              {assign var='name_color' value='006600'}
              {assign var='name_message' value='email_received_comment_reason'}
            {else}
              {assign var='name_color' value='FF0000'}
              {if $to_user.receive_status eq 'not_received'}
                {assign var='name_message' value='email_sent_not_received_comment_reason'}
              {elseif $to_user.mail_status ne 'sent'}
                {capture assign='name_message'}email_not_sent_{$to_user.mail_status}_reason{/capture}
              {/if}
            {/if}

            {capture assign='info_sent_mails'}
              <table border="0" cellpadding="3" cellspacing="0" class="t_table t_table_border" width="100%;">
                {foreach name='ci' from=$to_user.copies_info item='copy_info'}
                  {if $copy_info.mail_status eq 'sent' && $copy_info.receive_status eq 'received'}
                    {assign var='current_mail_icon' value='small/check_yes.png'}
                    {assign var='current_mail_message' value='email_sent_received_comment_reason'}
                  {elseif $copy_info.mail_status eq 'received'}
                    {assign var='current_mail_icon' value='warning.png'}
                    {assign var='current_mail_message' value='email_received_comment_reason'}
                  {else}
                    {assign var='current_mail_icon' value='small/delete.png'}
                    {if $copy_info.receive_status eq 'not_received'}
                      {assign var='current_mail_message' value='email_sent_not_received_comment_reason'}
                    {elseif $copy_info.mail_status ne 'sent'}
                      {capture assign='current_mail_message'}email_not_sent_{$copy_info.mail_status}_reason{/capture}
                    {/if}
                  {/if}
                  {capture assign="cycle_name"}info_box_cycle_{$smarty.foreach.i.index}_{$smarty.foreach.c.index}{/capture}
                  <tr class="{cycle values='t_odd,t_even' name=$cycle_name}">
                    <td class="t_border" style="width: 60px;">{$copy_info.sent|date_format:#date_mid#|escape}</td>
                    <td><img src="{$theme->imagesUrl}{$current_mail_icon}" width="12" height="12" border="0" /> {$smarty.config.$current_mail_message|escape}</td>
                  </tr>
                {/foreach}
              </table>
            {/capture}

            <span style="color: #{$name_color}; cursor: pointer;" {popup caption=#mail_status#|escape text=$info_sent_mails|escape width=250}>
              {if $communication.type eq 'comment'}
                {if $to_user.name}{$to_user.name|escape|default:"&nbsp;"}{else}&#60;{$to_user.email|escape|default:"&nbsp;"}&#62;{/if}
              {else}
                {if $to_user.name}{$to_user.name|escape|default:"&nbsp;"} {/if}{if $to_user.email}&#60;{$to_user.email|escape}&#62;{/if}
              {/if}
            </span>{if !$smarty.foreach.c.last}<br />{/if}
          {foreachelse}
            &nbsp;
          {/foreach}
        </td>
        {strip}
          {capture assign='full_communication'}{if $communication.type eq 'comment'}{#full_comment#}{else}{#full_email#}{/if}{/capture}
          {capture assign='part_communication'}{if $communication.type eq 'comment'}{#part_comment#}{else}{#part_email#}{/if}{/capture}
          {capture assign='communication_full'}
            <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{$full_communication|escape}" title="{$full_communication|escape}" />
          {/capture}
          {capture assign='communication_part'}
            <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{$part_communication|escape}" title="{$part_communication|escape}" />
          {/capture}
        {/strip}
        <td class="t_border {$communications_sort.content.isSorted} content" id="communication_{$communication.id}">
          {if ($communication.subject)}<strong>{$communication.subject|escape}</strong><br />{/if}
          <div id="communication_full_{$smarty.foreach.i.iteration}" class="comment_parent communication_parent">
            <div class="comment communication">{$communication.content}</div>
          </div>
        </td>
        <td class="t_border {$communications_sort.added.isSorted}" nowrap="nowrap">{$communication.date|date_format:#date_mid#|escape}</td>
        <td class="hleft" nowrap="nowrap">
          {if $communication.type eq 'comment' && $current_model}
            {if $current_model->checkPermissions('comments_add')}
              <img src="{$theme->imagesUrl}small/reply.png" width="12" height="12" border="0" alt="{#reply#|escape}" title="{#reply#|escape}" class="page_menu_link" onclick="manageCommunicationAddPanels('comment', 'add',  '{$communication.model_id}','','Re: {if $communication.subject}{$communication.subject|escape:'quotes'|escape}{else}{$communication.content|strip_tags|mb_truncate:20:"..."|escape:'quotes'|escape}{/if}',{$communication.id})" />
            {else}
              <img src="{$theme->imagesUrl}small/reply.png" width="12" height="12" border="0" alt="{#reply#|escape}" class="dimmed pointer" onclick="alert('{#error_add_notallowed#|escape}')" />
            {/if}
            {math assign=last_date equation=x-60*y x=$smarty.now y=$smarty.const.PH_COMMENTS_EDIT_INTERVAL}
            {capture assign='last_date_formated'}{$last_date|date_format:'%Y-%m-%d %H:%M:%S'}{/capture}
            {if $communication.added_by eq $currentUser->get('id') && $last_date_formated lt $communication.modified && $current_model->checkPermissions('comments_add')}
              <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" alt="{#edit#|escape}" title="{#edit#|escape}" class="page_menu_link" onclick="manageCommunicationAddPanels('comment', 'edit',  '{$communication.model_id}','{$communication.id}')" />
            {else}
              <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" alt="{#edit#|escape}" class="dimmed pointer" onclick="alert('{#error_edit_notallowed#|escape}')" />
            {/if}
          {/if}
          {if $dashlet}
            <img src="{$theme->imagesUrl}small/view.png" width="12" height="12" border="0" class="pointer{if !$communication.view_link} dimmed{/if}" onclick="{if $communication.view_link}window.open('{$communication.view_link}', '_blank'){else}alert('{#plugin_record_unable_view#|escape:'quotes'|escape}'){/if}" alt="{#plugin_view_record#|escape}" title="{#plugin_view_record#|escape}" />
          {/if}
          {if $communication.resent_available}
            <img src="{$theme->imagesUrl}resend.png" class="pointer" width="12" height="12" border="0" alt="{#resend_email#|escape}" title="{#resend_email#|escape}" onclick="openResendMenu(this, '{$communication.model_id}', '{$communication.model}', '{$communication.type}', '{$communication.id}', '{if $dashlet}{$dashlet->get('id')}{/if}')" />
          {elseif $communication.type eq 'email'}
            <img src="{$theme->imagesUrl}reply.png" class="pointer" width="12" height="12" border="0" alt="{#reply#|escape}" title="{#reply#|escape}" onclick="manageCommunicationAddPanels('{$communication.type}', 'reply',  '{$communication.model_id}','','',{$communication.id})" />
            <img src="{$theme->imagesUrl}reply_all.png" class="pointer" width="12" height="12" border="0" alt="{#reply_all#|escape}" title="{#reply_all#|escape}" onclick="manageCommunicationAddPanels('{$communication.type}', 'replyAll',  '{$communication.model_id}','','',{$communication.id})" />
          {/if}
        </td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="{$colspan_total}">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="{$colspan_total}"></td>
    </tr>
  </table>
  <script type="text/javascript">collapseCommunications();</script>