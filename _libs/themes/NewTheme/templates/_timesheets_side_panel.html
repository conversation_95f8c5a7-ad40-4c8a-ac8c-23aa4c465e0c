{if !$hide_side_panel}
{strip}
{if $module and $controller}
  {capture assign='controller_action_string'}{if $controller ne $module}{$controller_param}={$controller}&amp;{$controller}{else}{$module}{/if}{/capture}
{/if}
{/strip}

<input type="hidden" id="{$side_panel}_total" name="{$side_panel}_total" class="total" value="{$total}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title">{#tasks_timesheets_content#|escape}</div></td>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 60px;"><div class="t_panel_caption_title">{#tasks_timesheets_duration#|escape}/{#tasks_timesheets_duration_billing_short#|escape}</div></td>
  </tr>
{foreach name='i' from=$timesheets item='item'}
  <tr class="{cycle values='t_odd,t_even'} vtop">
    <td class="t_border">
      {help label_content=#tasks_timesheets_completed_by#} {$item->get('user_id_name')|escape|default:"-"} ({$item->get('startperiod')|date_format:#date_mid#|default:"&nbsp;"} {#to#|mb_lower} {$item->get('endperiod')|date_format:#date_mid#|default:"&nbsp;"})<br />
      {if $item->get('subject')}<strong>{$item->get('subject')|escape}</strong><br />{/if}
      {strip}
      {capture assign='content_full'}
        <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_content#|escape}" title="{#full_content#|escape}" onclick="toggleContent('content', {$smarty.foreach.i.iteration});" class="pointer" />
      {/capture}
      {capture assign='content_part'}
        <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_content#|escape}" title="{#part_content#|escape}" onclick="toggleContent('content', {$smarty.foreach.i.iteration});" class="pointer" />
      {/capture}
      {/strip}
      <div id="content_part_{$smarty.foreach.i.iteration}">
        {$item->get('content')|mb_truncate:130:$content_full|url2href:35|nl2br}
      </div>
      <div id="content_full_{$smarty.foreach.i.iteration}" style="display: none;">
        {$item->get('content')|escape|url2href:35|nl2br}{$content_part}
      </div>
    </td>
    <td class="hright">{$item->get('duration')|escape|default:"-"}/{$item->get('duration_billing')|escape|default:"-"}</td>
  </tr>
{foreachelse}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="error" colspan="2">{#no_items_found#|escape}</td>
  </tr>
{/foreach}
</table>
{/if}