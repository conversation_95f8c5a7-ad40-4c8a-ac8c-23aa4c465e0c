        <tr{if $var.hidden} style="display: none"{/if}>
          <td class="labelbox"><a name="error_{$var.custom_id|default:$var.name}{if $var_type eq 'simple'}_{/if}"></a><label for="{$var.name}"{if $messages->getErrors($var.name)} class="error"{/if}>{help label_content=$var.label text_content=$var.help}</label>
          </td>
          <td class="required">{if $var.required}{#required#}{else}&nbsp;{/if}</td>
          <td nowrap="nowrap">
            {#from#|escape}
            {$var.value.0|escape}
            &nbsp;&nbsp;{#to#|escape}
            {$var.value.1|escape}
          </td>
        </tr>
