{* Define indexes *}
{if $index}{capture assign='index_array'}{if $eq_indexes}{$index}{else}{$index-1}{/if}{/capture}{/if}
{* Define width *}
{capture assign='width'}{strip}
  {if $standalone}
    50%!important
  {elseif $width && !preg_match('#%$#', $width)}
    {math equation="(x/y) - z" x=$width y=2 z=2}px
  {elseif $width}
    {assign var='width' value=$width|regex_replace:'\s|%':''}
    {math equation="(x/y) - z" x=$width y=2 z=2}
  {/if}
{/strip}{/capture}

{if !$standalone}
<tr{if $hidden || ($calculate > 1 && $width === "0")} style="display: none;"{/if}>
  {* Label Cell *}
  <td class="labelbox">
    {* Anchor for error reference *}
    <a name="error_{$custom_id|default:$name}"></a>
    {* Label of the variable *}
    <label for="{$custom_id|default:$name}"{if $messages->getErrors($name)} class="error"{/if}>{help label_content=$label text_content=$help}</label>
  </td>

  {* Required Cell *}
  <td{if $required} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>

  {* Element Cell *}
  <td nowrap="nowrap">
{/if}

    {* Element *}
    <input
      type="text"
      name="{$name}{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}{if $index}_{$index}{/if}"
      class="txtbox hright{if $readonly} readonly{/if}"
      style="{if $hidden}display: none;{elseif $width}width: {$width}; margin-right: 4px;{/if}"
      value="{$value|escape}"
      title="{$label|strip_tags:false|escape}"
      onkeypress="return changeKey(this, event, insertOnlyDigits);"
      onfocus="highlight(this);"
      onblur="unhighlight(this);"
      {if $readonly} readonly="readonly"{/if}
      {if $disabled} disabled="disabled"{/if} />{strip}

    {/strip}<select
      name="{$name_date_period|default:'date_period'}{if $index}[{$index_array}]{/if}"
      id="{$custom_id_date_period|default:$name_date_period|default:'date_period'}{if $index}_{$index}{/if}"
      class="selbox{if $readonly} readonly{/if}"
      style="{if $hidden}display: none;{elseif $width}width: {$width};{/if}"
      title="{$label|strip_tags:false|escape}"
      onfocus="highlight(this);"
      onblur="unhighlight(this);"
      {if $readonly} readonly="readonly"{/if}
      {if $disabled} disabled="disabled"{/if}>
      <option value="DAY"{if $value_date_period eq 'DAY'} selected="selected"{/if}>{#date_days#}</option>
      <option value="WEEK"{if $value_date_period eq 'WEEK'} selected="selected"{/if}>{#date_weeks#}</option>
      <option value="MONTH"{if $value_date_period eq 'MONTH'} selected="selected"{/if}>{#date_months#}</option>
    </select>

{if !$standalone}
  </td>
</tr>
{/if}