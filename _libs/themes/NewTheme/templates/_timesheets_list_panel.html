{* listing of timesheets *}

{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=tasks&amp;{$controller_param}=timesheets&amp;timesheets=list&amp;model_id={$task->get('id')}{if $parent_module}&amp;parent_module={$parent_module}{/if}&amp;page={/capture}
<table cellpadding="0" cellspacing="0" border="0">
  <tr>
    <td class="vtop nopadding" style="margin-left: 0; border-collapse: collapse;">
      <table border="0" cellpadding="0" cellspacing="0">
    {if $pagination.pages > 1}
        <tr>
          <td class="pagemenu">
      {assign var=sort value=$timesheets_sort}
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        pagination=$pagination
        sort=$timesheets_sort
        session_param=$timesheets_session_param
        use_ajax=$timesheets_use_ajax
        hide_stats=1
      }
          </td>
        </tr>
    {/if}
        <tr>
          <td id="timesheets_container" class="nopadding">
            <table border="0" cellpadding="0" cellspacing="0" class="t_table" style="width: 1240px;">
              <tr>
                <td class="t_caption t_border" nowrap="nowrap" style="width: 20px;"><div class="t_caption_title">{#num#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.user_id.class}" nowrap="nowrap" style="width: 100px;"><div class="t_caption_title" onclick="{$timesheets_sort.user_id.link}">{#tasks_timesheets_completed_by#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.office.class}" nowrap="nowrap" style="width: 100px;"><div class="t_caption_title" onclick="{$timesheets_sort.office.link}">{#tasks_timesheets_office#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.subject.class}" nowrap="nowrap" style="width: 100px;"><div class="t_caption_title" onclick="{$timesheets_sort.subject.link}">{#subject#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.content.class}" style="width: 300px;"><div class="t_caption_title" onclick="{$timesheets_sort.content.link}">{#tasks_timesheets_content#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.activity.class}" style="width: 120px;"><div class="t_caption_title" onclick="{$timesheets_sort.activity.link}">{#tasks_timesheets_activity#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.startperiod.class}" nowrap="nowrap" style="width: 70px;"><div class="t_caption_title" onclick="{$timesheets_sort.startperiod.link}">{#tasks_timesheets_startperiod#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.endperiod.class}" nowrap="nowrap" style="width: 70px;"><div class="t_caption_title" onclick="{$timesheets_sort.endperiod.link}">{#tasks_timesheets_endperiod#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.duration.class}" nowrap="nowrap" style="width: 50px;"><div class="t_caption_title" onclick="{$timesheets_sort.duration.link}">{#tasks_timesheets_duration#|escape}</div></td>
                <td class="t_caption t_border {$timesheets_sort.duration_billing.class}" nowrap="nowrap" style="width: 50px;"><div class="t_caption_title" onclick="{$timesheets_sort.duration_billing.link}">{help label_content=#tasks_timesheets_duration_billing_short#|escape text_content=#tasks_timesheets_duration_billing#|escape label_sufix=''}</div></td>
                <td class="t_caption t_border {$timesheets_sort.event_id.class}" nowrap="nowrap" style="width: 90px;"><div class="t_caption_title" onclick="{$timesheets_sort.event_id.link}">{help label_content=#tasks_timesheets_planned_time_short#|escape text_content=#tasks_timesheets_planned_time#|escape label_sufix=''}</div></td>
                <td class="t_caption t_border {$timesheets_sort.added.class}" nowrap="nowrap" style="width: 120px;"><div class="t_caption_title" onclick="{$timesheets_sort.added.link}">{#added#|escape}</div></td>
                <td class="t_caption" nowrap="nowrap">&nbsp;</td>
              </tr>
            {counter start=$pagination.start name='item_counter' print=false}
            {foreach name='i' from=$timesheets item='item'}
              <tr class="{cycle values='t_odd,t_even'} vtop">
                <td class="t_border hright">{counter name='item_counter' print=true}</td>
                <td class="t_border {$timesheets_sort.user_id.isSorted}" nowrap="nowrap">{$item->get('user_id_name')|escape|default:"&nbsp;"}</td>
                <td class="t_border {$timesheets_sort.office.isSorted}" nowrap="nowrap">{$item->get('office_name')|escape|default:"&nbsp;"}</td>
                <td class="t_border {$timesheets_sort.subject.isSorted}">{$item->get('subject')|escape}</td>
                {strip}
                {capture assign='content_full'}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#full_content#|escape}" title="{#full_content#|escape}" onclick="toggleContent('content', {$smarty.foreach.i.iteration});" class="pointer" />
                {/capture}
                {capture assign='content_part'}
                  <img src="{$theme->imagesUrl}small/full_comment.png" width="12" height="12" border="0" alt="{#part_content#|escape}" title="{#part_content#|escape}" onclick="toggleContent('content', {$smarty.foreach.i.iteration});" class="pointer" />
                {/capture}
                {/strip}
                <td class="t_border {$timesheets_sort.content.isSorted}">
                  <div id="content_part_{$smarty.foreach.i.iteration}">
                    {$item->get('content')|mb_truncate:130:$content_full|url2href:35|nl2br}
                  </div>
                  <div id="content_full_{$smarty.foreach.i.iteration}" style="display: none;">
                    {$item->get('content')|escape|url2href:35|nl2br}{$content_part}
                  </div>
                </td>
                <td class="t_border {$timesheets_sort.activity.isSorted}">{$item->get('activity_name')}</td>
                <td class="t_border {$timesheets_sort.startperiod.isSorted}">{$item->get('startperiod')|date_format:#date_mid#|default:"&nbsp;"}</td>
                <td class="t_border {$timesheets_sort.endperiod.isSorted}">{$item->get('endperiod')|date_format:#date_mid#|default:"&nbsp;"}</td>
                <td class="t_border hright {$timesheets_sort.duration.isSorted}">{$item->get('duration')|escape|default:"&nbsp;"}</td>
                <td class="t_border hright {$timesheets_sort.duration_billing.isSorted}">{$item->get('duration_billing')|escape|default:"&nbsp;"}</td>
                <td class="t_border {$timesheets_sort.event_id.isSorted}">{if $item->get('event_id')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$item->get('event_id')}" title="{#view#|escape}: {$item->get('event_name')|escape}">{$item->get('event_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
                <td class="t_border {$timesheets_sort.added.isSorted}">{$item->get('added')|date_format:#date_mid#|escape}, {$item->get('added_by_name')|escape|default:"&nbsp;"}</td>
                <td nowrap="nowrap">
                  {* If timesheet editing is allowed *}
                  {if $item->checkEditPermissions()}
                    {* Show active icon for editing the timesheet *}
                    <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" alt="{#edit#|escape}" title="{#edit#|escape}" class="pointer" onclick="editTimesheet({$item->get('id')},'timesheet_container');" />
                  {else}
                    {* Show inactive icon for editing the timesheet *}
                    <img src="{$theme->imagesUrl}small/edit.png" width="12" height="12" border="0" alt="{#edit#|escape}" title="{#edit#|escape}" class="dimmed pointer" onclick="alert('{#error_edit_notallowed#|escape}')" />
                  {/if}
                </td>
              </tr>
            {foreachelse}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="error" colspan="13">{#no_items_found#|escape}</td>
              </tr>
            {/foreach}
            {if ($smarty.post && $task->getTimesheetTime(true)) || $timesheets}
              <tr class="{cycle values='t_odd,t_even'} vtop">
                <td class="t_border hright">&nbsp;</td>
                <td class="t_border hright">&nbsp;</td>
                <td class="t_border hright">&nbsp;</td>
                <td class="t_border hright">&nbsp;</td>
                <td class="t_border hright">&nbsp;</td>
                <td class="t_border hright strong" colspan="3">{#total#}:</td>
                <td class="t_border strong">
                  {$task->get('timesheet_time_formatted')|escape}
                </td>
                <td class="t_border strong">
                {if (($smarty.post && $task->getTimesheetBillingTime(true)) || $timesheets) && $task->get('timesheet_billing_time') != null}
                  {$task->get('timesheet_billing_time_formatted')|escape}
                {/if}
                </td>
                <td class="t_border">&nbsp;</td>
                <td class="t_border">&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
              {/if}
              <tr>
                <td class="t_footer" colspan="13"></td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td class="pagemenu">
      {include file="`$theme->templatesDir`pagination.html"
        found=$pagination.found
        total=$pagination.total
        rpp=$pagination.rpp
        page=$pagination.page
        pages=$pagination.pages
        pagination=$pagination
        sort=$timesheets_sort
        session_param=$timesheets_session_param
        use_ajax=$timesheets_use_ajax
        hide_selection_stats=true
      }
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
