    <h1><img src="{$theme->imagesUrl}list.png" border="0" alt="{$audit_title|default:#audit_vars#|escape}" /> {$audit_title|default:#audit_vars#|escape}</h1>
    <h2>{$audit_legend|escape}</h2>
    {capture assign='display_old_value'}{if !empty($audit.vars) && !empty($audit.vars[0].action) && $audit.vars[0].action eq 'email'}0{else}1{/if}{/capture}
    <div class="audit clear{if !$display_old_value} email{/if}">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_header t_table_border">
        <tr>
          <td colspan="4" class="t_caption3 strong legend">{#data#|escape}</td>
        </tr>
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 20px;"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 150px;"><div class="t_caption_title">{#var_name#|escape}</div></td>
          {if $display_old_value}
          <td class="t_caption t_border" nowrap="nowrap" style="width: 200px;"><div class="t_caption_title">{#var_value#|escape}</div></td>
          <td class="t_caption" nowrap="nowrap" style="width: 200px;"><div class="t_caption_title">{#old_value#|escape}</div></td>
          {else}
          <td class="t_caption" nowrap="nowrap" style="width: 400px;" colspan="2"><div class="t_caption_title">{#var_value#|escape}</div></td>
          {/if}
        </tr>
        {counter name='audit_counter' start=0 assign='audit_counter'}
        {if $module == 'finance' && $controller == 'warehouses'}{assign var='warehouse_audit' value=1}{assign var='audit_classes' value='t_odd2,t_even2'}{else}{assign var='audit_classes' value='t_odd,t_even'}{/if}
        {foreach name='i' from=$audit.vars item='var'}
          {if $var.field_name == 'bb_delimiter'}
          <tr>
            <td colspan="4" class="t_caption3 strong legend">{if empty($var.deleted)}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$var.model_id}{if $smarty.request.archive}&amp;archive=1{/if}#bb_row_{$var.field_value}">{$var.label|escape|default:'&nbsp;'}</a>{else}{$var.label|escape|default:'&nbsp;'}{/if}</td>
          </tr>
          {else}
          <tr class="{cycle values=$audit_classes name='audit_vars'} vtop">
            <td class="t_border hright">{counter name='audit_counter' print=true}</td>
            <td class="t_border">{$var.var_label|escape|default:'&nbsp;'}</td>
            <td {if $display_old_value}class="legend t_border"{else}class="legend" colspan="2"{/if}>
              {if $var.field_name eq 'mail_content' || $var.field_name eq 'content' && in_array($var.action, array('add_comment', 'edit_comment'))}
                {$var.field_value}
              {elseif $warehouse_audit && is_array($var.field_value)}
                {include file=`$theme->templatesDir`_gt2_batch_view.html no_row=true val=$var.field_value idx=`$smarty.foreach.i.iteration`_new}
              {else}
                {if isset($var.label) && $var.label !== ''}
                  {assign var='new_value' value=$var.label}
                {else}
                  {assign var='new_value' value=$var.field_value}
                {/if}
                {if preg_match('/<\w.*>/', $new_value)}
                  {$new_value|nl2br|regex_replace:"#<([\w.]+@[\w.]+)>#":"&lt;\$1&gt;"}
                {else}
                  {$new_value|escape|nl2br|url2href|default:'&nbsp;'}
                {/if}
              {/if}
            </td>
            {if $display_old_value}
            <td class="legend">
              {if $var.field_name eq 'content' && in_array($var.action, array('add_comment', 'edit_comment'))}
                {$var.old_value}
              {elseif $warehouse_audit && is_array($var.old_value)}
                {include file=`$theme->templatesDir`_gt2_batch_view.html no_row=true val=$var.old_value idx=`$smarty.foreach.i.iteration`_old}
              {elseif preg_match('/<\w.*>/', $var.old_value)}
                {$var.old_value|nl2br|regex_replace:"#<([\w.]+@[\w.]+)>#":"&lt;\$1&gt;"}
              {else}
                {$var.old_value|escape|nl2br|url2href|default:'&nbsp;'}
              {/if}
            </td>
            {/if}
          </tr>
          {/if}
        {foreachelse}
          <tr class="{cycle values=$audit_classes name='audit_vars'}">
            <td class="error" colspan="4">{#no_changes_made#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="4"></td>
        </tr>
      </table>
    </div>
