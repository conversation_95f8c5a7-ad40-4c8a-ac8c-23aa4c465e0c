{strip}
{if $module and $controller}
  {capture assign='controller_action_string'}{if $controller ne $module}{$controller_param}={$controller}&amp;{$controller}{else}{$module}{/if}{/capture}
{/if}
{/strip}
            {if $files}
              <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table t_table attachments" style="border: 1px solid #999999; z-index: 10000; width: 200px;">
                <tr>
                  <th colspan="4" class="t_caption t_border" nowrap="nowrap">
                    <div class="t_caption_title">{#attachments#|escape}</div>
                  </th>
                </tr>
                <tr class="t_even hleft">
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc">{#num#|escape}</td>
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc">{#attachments_title#|escape}</td>
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc">{#attachments_path#|escape}</td>
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc">{#attachments_revision#|escape}</td>
                </tr>
                {if $files.attachments}
                {foreach from=$files.attachments item='attachment' name='k'}
                <tr class="{cycle name='attachments' values='t_odd,t_even'}">
                  <td class="t_border" style="font-weight:normal; text-align:right; white-space: nowrap">
                  {strip}
                    {if !file_exists($attachment.path)}
                      <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" title="{#attachments_file_not_exist#|escape}" />
                    {/if}
                    &nbsp;{$smarty.foreach.k.iteration}.
                  {/strip}
                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left; white-space: nowrap">
                    {if file_exists($attachment.path)}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_action_string}=getfile&amp;getfile={$model_id}&amp;file={$attachment.id}{if $archive}&amp;archive=1{/if}">
                        <span title="{$attachment.name}">{$attachment.name|mb_truncate:20|escape}</span>
                      </a>
                    {else}
                      <span title="{$attachment.name}">{$attachment.name|mb_truncate:20|escape}</span>
                    {/if}
                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left; white-space: nowrap">
                    {if file_exists($attachment.path)}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_action_string}=getfile&amp;getfile={$model_id}&amp;file={$attachment.id}{if $archive}&amp;archive=1{/if}">
                        <span title="{$attachment.filename}">{$attachment.filename|mb_truncate:20|escape}</span>
                      </a>
                    {else}
                      <span title="{$attachment.filename}">{$attachment.filename|mb_truncate:20|escape}</span>
                    {/if}
                  </td>
                  <td style="font-weight:normal; text-align:right; white-space: nowrap">{$attachment.revision}</td>
                </tr>
                {/foreach}
                {/if}
                {if $files.generated}
                {foreach from=$files.generated item='attachment' name='k'}
                <tr class="{cycle name='attachments' values='t_odd,t_even'}">
                  <td class="t_border" style="font-weight:normal; text-align:left; text-align:right; white-space: nowrap">
                  {strip}
                    {if !file_exists($attachment.path)}
                      <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" title="{#attachments_file_not_exist#|escape}" />
                    {/if}
                    &nbsp;{$smarty.foreach.k.iteration}.
                  {/strip}
                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left; white-space: nowrap">
                    {if file_exists($attachment.path)}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_action_string}=getfile&amp;getfile={$model_id}&amp;file={$attachment.id}{if $archive}&amp;archive=1{/if}">
                        <span title="{$attachment.name}">{$attachment.name|mb_truncate:20|escape}</span>
                      </a>
                    {else}
                      <span title="{$attachment.name}">{$attachment.name|mb_truncate:20|escape}</span>
                    {/if}
                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left;white-space: nowrap">
                    {if file_exists($attachment.path)}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_action_string}=getfile&amp;getfile={$model_id}&amp;file={$attachment.id}{if $archive}&amp;archive=1{/if}">
                        <span title="{$attachment.filename}">{$attachment.filename|mb_truncate:20|escape}</span>
                      </a>
                    {else}
                      <span title="{$attachment.filename}">{$attachment.filename|mb_truncate:20|escape}</span>
                    {/if}
                  </td>
                  <td style="font-weight:normal; text-align:right;white-space: nowrap">{$attachment.revision}</td>
                </tr>
              {/foreach}
              {/if}
              {if $files_more gt 0}
                <tr class="legend">
                  <td colspan="4" nowrap="nowrap">
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_action_string}=attachments&amp;attachments={$model_id}{if $archive}&amp;archive=1{/if}">
                      {#attachments_files_more#|replace:'[files_more]':$files_more}
                    </a>
                  </td>
                </tr>
              {/if}
              </table>
            {/if}