{include file=`$theme->templatesDir`_gt2_groups.html}
<table cellspacing="0" cellpadding="3" border="0">
  <tbody id="gt2_settings">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 200px">{#gt2_label#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_varname#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_type#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_alignment#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px">{#gt2_hidden#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px">{#gt2_readonly#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px">{#gt2_required#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_col_width#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_agregate#}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_js_filter#}</div></td>
    <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#gt2_source#}</div></td>
  </tr>
  {foreach from=$table item=var key=key name=i}
    {if $key !== 'batch_vars'}
      {counter name=idx_cnt assign=current_index}
      {capture assign='default_name'}{$var.name}{/capture}
      {if $var.type eq 'gt2'}
        {assign var='hide_row' value=true}
      {else}
        {assign var='hide_row' value=false}
      {/if}
      <tr class="{cycle values='t_odd,t_even'}{if $smarty.foreach.i.first} t_inactive{/if}{if $var.hidden} oblique{/if}" id="gt2_settings_{$current_index}"{if preg_match('#discount_surplus_field#', $var.name)} style="display:none"{/if}>
        <td class="t_border" nowrap="nowrap" align="right">
          {$current_index}.
          <img src="{$theme->imagesUrl}permissions.png" class="pointer" alt="{#gt2_permissions#} {$var.labels.$lang|default:$default_name}" title="{#gt2_permissions#} {$var.labels.$lang|default:$default_name}" onmouseover="editGT2Permissions('show', {$current_index}, '{$var.labels.$lang|default:$default_name}');" onmouseout="editGT2Permissions('close')" onclick="editGT2Permissions('edit', {$current_index}, '{$var.labels.$lang|default:$default_name}');" />
          <img src="{$theme->imagesUrl}translate.png" class="pointer" alt="{#translate#|escape}" title="{#translate#|escape}" onclick="editGT2Translations(this)" />
        </td>
        <td class="t_border">
          {include file=`$theme->templatesDir`input_hidden.html
                   name='ids'
                   index=$current_index
                   empty_indexes=true
                   value=$var.id
                   standalone=true
          }
          {include file=`$theme->templatesDir`input_hidden.html
                   name='permissions_edit'
                   index=$current_index
                   empty_indexes=true
                   value=$var.permissions_edit
                   standalone=true
          }
          {include file=`$theme->templatesDir`input_hidden.html
                   name='permissions_view'
                   index=$current_index
                   empty_indexes=true
                   value=$var.permissions_view
                   standalone=true
          }
          <span class="pointer" id="handler_{$current_index}">{strip}
          {if $var.labels.$lang}
            {$var.labels.$lang}
          {elseif $var.type ne 'gt2'}
            <em class="red">{$default_name}</em>
          {/if}
          {/strip}</span>
          {foreach from=$model_langs item='lng'}
          <div class="translation" style="display:none; margin-top:5px; white-space: nowrap!important;">{strip}
            {capture assign='lang_name'}lang_{$lng}{/capture}
            <img src="{$theme->imagesUrl}flags/{$lng}.png" width="16" height="12" alt="{$smarty.config.$lang_name|escape}" title="{$smarty.config.$lang_name|escape}" />
            {capture assign=field_name}label_{$lng}{/capture}
            {include file=`$theme->templatesDir`input_text.html
                     name=$field_name
                     index=$current_index
                     empty_indexes=true
                     width='200'
                     value=$var.labels.$lng
                     standalone=true
                     label=#gt2_label#
            }
          {/strip}</div>
          {/foreach}
        </td>
        <td class="t_border">
          {$var.name}
        </td>
        <td class="t_border">
          {include file=`$themes->templatesDir`input_dropdown.html
                   name='type'
                   index=$current_index
                   empty_indexes=true
                   value=$var.type
                   options=$controls_options.type
                   standalone=true
                   required=1
                   label=#gt2_type#
                   width=100
                   hidden=$hide_row
          }
        </td>
        <td class="t_border">
          {include file=`$themes->templatesDir`input_dropdown.html
                   name=alignment
                   index=$current_index
                   empty_indexes=true
                   value=$var.text_align
                   options=$controls_options.alignment
                   standalone=true
                   required=1
                   label=#gt2_alignment#
                   hidden=$hide_row
          }
        </td>
        <td class="t_border">
          {capture assign='hidden_onchange'}if(this.value > 0){ldelim}addClass(this.parentNode.parentNode, 'oblique'){rdelim}else{ldelim}removeClass(this.parentNode.parentNode, 'oblique'){rdelim}{/capture}
          {include file=`$themes->templatesDir`input_dropdown.html
                   name=hidden
                   index=$current_index
                   empty_indexes=true
                   value=$var.hidden
                   options=$controls_options.hidden
                   standalone=true
                   required=1
                   onchange=$hidden_onchange
                   label=#gt2_hidden#
                   hidden=$hide_row
          }
        </td>
        <td class="t_border">
          {include file=`$themes->templatesDir`input_dropdown.html
                   name=readonly
                   index=$current_index
                   empty_indexes=true
                   value=$var.readonly
                   options=$controls_options.readonly
                   standalone=true
                   required=1
                   label=#gt2_readonly#
                   hidden=$hide_row
          }
        </td>
        <td class="t_border">
          {include file=`$themes->templatesDir`input_dropdown.html
                   name=required
                   index=$current_index
                   empty_indexes=true
                   value=$var.required
                   options=$controls_options.required
                   standalone=true
                   required=1
                   label=#gt2_required#
                   hidden=$hide_row
          }
        </td>
        <td class="t_border" style="white-space: nowrap">
          {include file=`$themes->templatesDir`input_text.html
                   name=width
                   width=35
                   index=$current_index
                   empty_indexes=true
                   value=$var.width
                   restrict=insertOnlyDigits
                   text_align=right
                   standalone=true
                   required=1
                   label=#gt2_col_width#
          }
          {include file=`$themes->templatesDir`input_dropdown.html
                   name=width_measure
                   width=45
                   index=$current_index
                   empty_indexes=true
                   value=$var.width_measure
                   options=$controls_options.html_measures
                   standalone=true
                   required=1
          }
        </td>
        <td class="t_border">
          {include file=`$themes->templatesDir`input_dropdown.html
                   name='agregate'
                   index=$current_index
                   empty_indexes=true
                   value=$var.agregate
                   options=$controls_options.agregate
                   first_option_label=#gt2_no#
                   standalone=true
                   label=#gt2_agregate#
                   width=100%
                   hidden=$hide_row
          }
          {if !$smarty.foreach.i.first}
            <script type="text/javascript">
                 new Zapatec.Utils.Draggable({ldelim}container:'gt2_settings_{$current_index}', 
                                              handler: 'handler_{$current_index}',
                                              direction: 'vertical',
                                              followShape: 'LT',
                                              method: 'cut',
                                              dragCSS: 'gt2_draggable',
                                              beforeDragInit: function(){ldelim}gt2BeforeDragInit(this);{rdelim},
                                              onDragEnd : function(){ldelim}gt2OnDragEnd(this);{rdelim}
                                             {rdelim});
                 new Zapatec.Utils.DropArea('gt2_settings_{$current_index}',
                                            'gt2_settings_{$current_index}',null , null,
                                            function(){ldelim}gt2MoveDroppable(this);{rdelim}, null, null
                                           );
            </script>
          {/if}
        </td>
        <td class="t_border">
          {include file=`$themes->templatesDir`input_dropdown.html
                   name='js_filter'
                   index=$current_index
                   empty_indexes=true
                   value=$var.js_filter
                   options=$controls_options.js_filter
                   standalone=true
                   label=#gt2_js_filter#
                   hidden=$hide_row
          }
        </td>
        <td>
          {array assign='gt2_source_js_methods'
            onfocus="gt2ExpandSourceField(this);"
          }
          {include file=`$themes->templatesDir`input_textarea.html
                   name='source_user'
                   height=18
                   index=$current_index
                   empty_indexes=true
                   value=$var.source_user
                   standalone=true
                   label=#gt2_source#
                   required=1
                   js_methods=$gt2_source_js_methods
          }
        </td>
      </tr>
    {/if}
  {/foreach}
  <tr><td colspan="12" class="t_footer"></td></tr>
  <tr>
    <td colspan="12">
      {include file=`$theme->templatesDir`input_checkbox.html
               name='show_totals_inwords'
               standalone=true
               option_value=1
               value=$table.0.show_totals_inwords
               label=#show_totals_inwords#
      }
    </td>
  </tr>
  </tbody>
</table>

{if $table.batch_vars}
  <br /><br />
  <h3 style="padding-left: 30px;">{#finance_documents_types_batch_vars#}</h3>
  <table cellspacing="0" cellpadding="3" border="0">
    <tbody id="gt2_batch_settings">
    <tr>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 200px">{#gt2_label#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_varname#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_type#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_alignment#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px">{#gt2_hidden#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title" style="width: 95px">{#gt2_readonly#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_col_width#}</div></td>
      <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_js_filter#}</div></td>
	  <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#gt2_source#}</div></td>
    </tr>
    {foreach from=$table.batch_vars item=var key=key name=i}
    {counter name=idx_cnt assign=current_index}
    {capture assign='default_name'}{$var.name}{/capture}
    {if $var.type eq 'gt2'}
      {assign var='hide_row' value=true}
    {else}
      {assign var='hide_row' value=false}
    {/if}
    <tr class="{cycle values='t_odd,t_even'}{if $var.hidden} oblique{/if}" id="gt2_batch_settings_{$current_index}">
      <td class="t_border" nowrap="nowrap" align="right">
        {$current_index}.
        <img src="{$theme->imagesUrl}permissions.png" class="pointer" alt="{#gt2_permissions#} {$var.labels.$lang|default:$default_name}" title="{#gt2_permissions#} {$var.labels.$lang|default:$default_name}" onmouseover="editGT2Permissions('show', {$current_index}, '{$var.labels.$lang|default:$default_name}');" onmouseout="editGT2Permissions('close')" onclick="editGT2Permissions('edit', {$current_index}, '{$var.labels.$lang}');" />
        <img src="{$theme->imagesUrl}translate.png" class="pointer" alt="{#translate#|escape}" title="{#translate#|escape}" onclick="editGT2Translations(this)" />
      </td>
      <td class="t_border">
        {include file=`$theme->templatesDir`input_hidden.html
                 name='ids'
                 index=$current_index
                 empty_indexes=true
                 value=$var.id
                 standalone=true
        }
        {include file=`$theme->templatesDir`input_hidden.html
                 name='permissions_edit'
                 index=$current_index
                 empty_indexes=true
                 value=$var.permissions_edit
                 standalone=true
        }
        {include file=`$theme->templatesDir`input_hidden.html
                 name='permissions_view'
                 index=$current_index
                 empty_indexes=true
                 value=$var.permissions_view
                 standalone=true
        }
        <span class="pointer" id="handler_{$current_index}">{strip}
        {if $var.labels.$lang}
          {$var.labels.$lang}
        {elseif $var.type ne 'gt2'}
          <em class="red">{$default_name}</em>
        {/if}
        {/strip}</span>
        {foreach from=$model_langs item='lng'}
        <div class="translation" style="display:none; margin-top:5px; white-space: nowrap!important;">{strip}
          {capture assign='lang_name'}lang_{$lng}{/capture}
          <img src="{$theme->imagesUrl}flags/{$lng}.png" width="16" height="12" alt="{$smarty.config.$lang_name|escape}" title="{$smarty.config.$lang_name|escape}" />
          {capture assign=field_name}label_{$lng}{/capture}
          {include file=`$theme->templatesDir`input_text.html
                   name=$field_name
                   index=$current_index
                   empty_indexes=true
                   width='200'
                   value=$var.labels.$lng
                   standalone=true
                   label=#gt2_label#
          }
        {/strip}</div>
        {/foreach}
      </td>
      <td class="t_border">
        {$var.name}
      </td>
      <td class="t_border">
        {include file=`$themes->templatesDir`input_dropdown.html
                 name='type'
                 index=$current_index
                 empty_indexes=true
                 value=$var.type
                 options=$controls_options.type
                 standalone=true
                 required=1
                 label=#gt2_type#
                 width=100
                 hidden=$hide_row
        }
      </td>
      <td class="t_border">
        {include file=`$themes->templatesDir`input_dropdown.html
                 name=alignment
                 index=$current_index
                 empty_indexes=true
                 value=$var.text_align
                 options=$controls_options.alignment
                 standalone=true
                 required=1
                 label=#gt2_alignment#
                 hidden=$hide_row
        }
      </td>
      <td class="t_border">
        {capture assign='hidden_onchange'}if(this.value > 0){ldelim}addClass(this.parentNode.parentNode, 'oblique'){rdelim}else{ldelim}removeClass(this.parentNode.parentNode, 'oblique'){rdelim}{/capture}
        {include file=`$themes->templatesDir`input_dropdown.html
                 name=hidden
                 index=$current_index
                 empty_indexes=true
                 value=$var.hidden
                 options=$controls_options.hidden
                 standalone=true
                 required=1
                 onchange=$hidden_onchange
                 label=#gt2_hidden#
                 hidden=$hide_row
        }
      </td>
      <td class="t_border">
        {include file=`$themes->templatesDir`input_dropdown.html
                 name=readonly
                 index=$current_index
                 empty_indexes=true
                 value=$var.readonly
                 options=$controls_options.readonly
                 standalone=true
                 required=1
                 label=#gt2_readonly#
                 hidden=$hide_row
        }
      </td>
      <td class="t_border" style="white-space: nowrap">
        {include file=`$themes->templatesDir`input_text.html
                 name=width
                 width=35
                 index=$current_index
                 empty_indexes=true
                 value=$var.width
                 restrict=insertOnlyDigits
                 text_align=right
                 standalone=true
                 required=1
                 label=#gt2_col_width#
        }
        {include file=`$themes->templatesDir`input_dropdown.html
                 name=width_measure
                 width=45
                 index=$current_index
                 empty_indexes=true
                 value=$var.width_measure
                 options=$controls_options.html_measures
                 standalone=true
                 required=1
                 readonly=true
        }
      </td>
      <td class="t_border">
        {include file=`$themes->templatesDir`input_dropdown.html
                 name='js_filter'
                 index=$current_index
                 empty_indexes=true
                 value=$var.js_filter
                 options=$controls_options.js_filter
                 standalone=true
                 label=#gt2_js_filter#
                 hidden=$hide_row
        }
      </td>
      <td class="t_border">
        {array assign='gt2_source_js_methods'
          onfocus="gt2ExpandSourceField(this);"
        }
        {include file=`$themes->templatesDir`input_textarea.html
                 name='source_user'
                 width=100%
                 height=18
                 index=$current_index
                 empty_indexes=true
                 value=$var.source_user
                 standalone=true
                 label=#gt2_source#
                 required=1
                 js_methods=$gt2_source_js_methods
        }
      </td>
    </tr>
    {/foreach}
    <tr><td colspan="10" class="t_footer t_border"></td></tr>
    </tbody>
  </table>
{/if}
