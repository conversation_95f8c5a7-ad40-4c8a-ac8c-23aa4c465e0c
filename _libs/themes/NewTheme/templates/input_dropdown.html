{** ALLOWED PARAMETERS:
 * standalone           - defines whether only the HTML element should be inserted or in a row of table
 * name                 - the form name of the variable (in latin characters), for the group tables the name is array [$index] is added
 * var_id               - id of the variable (as in the _fields_meta DB table)
 * custom_id            - each variable contains custom_id which defines the variable uniquely in the DOM
 * index                - index is the number of row in the group tables (starting with 1)
 * eq_indexes           - the index used in the name is the same as the index used in the array
 * label                - label (translated in the language of the interface)
 * help                 - help text shown in the help baloon with overlib (translated in the language of the interface)
 * value                - the actual value of the variable
 * required             - flag that defines whether the variables is required (should be validated) or not
 * really_required      - flag that defines whether the variable should have "please select" option, so that the selection of option is guaranteed (really required)
 * readonly             - flag that defines whether the variables should be readonly (not editable) or not
 * hidden               - if the variable is defined as hidden it is not displayed at all hiding it with style="display: none"
 * disabled             - if the variable is defined as disabled
 * width                - the width of the variable defines the width of the HTML element. In the standalone mode the width is defined as 100% of the cell width
 * calculate            - defines whether the HTML element should have calculate formula or not:
 *                        0 - no calculation formula
 *                        1 - calculation formula WITH button for calculation
 *                        2 - calculation formula WITHOUT button for calculation (if the width is 0 the input is not displayed at all)
 * options              - list of options (used only for checkboxes, dropdowns, radio buttons)
 * optgroups            - list of optgroups and their options (overwrites options)(used only for checkboxes, dropdowns, radio buttons)
 * option_value         - the value of the single option (used only for single checkbox)
 * first_option_label   - the label of the first option of a dropdown (used only for dropdowns)
 * no_select_records_label   - the label of the first option of a dropdown (used only for dropdowns)
 * origin               - defines the origin of the variable - group, config, table (typically it is not required)
 * format               - defines the format of the element (used only for the date and datetime fields)
 * disallow_date_before - does not allow input of dates before specified date (used only for the date and datetime fields)
 * disallow_date_after  - does not allow input of dates after specified date (used only for the date and datetime fields)
 * hide_calendar_icon   - does not allow showing of calendar icon (used only for the date and datetime fields)
 * onclick              - function defined for the onclick event(used only for buttons, checkboxes and radio buttons)
 * on_change            - id of dropdown where the onchange event happens (used only for linked dropdowns and radio buttons)
 * onchange             - function defined for the onchange event (used only for linked dropdowns)
 * js_methods           - defines JavaScript functions for some keyboard and mouse events
 * sequences            - chained dropdowns data (used only for dropdowns and comboboxes)
 * do_not_escape_labels - marks to not escape the labels of a dropdown options
 * back_label           - text for the back label
 * back_label_style     - styles (inline CSS) for the back label tag
 * undefined_strict_check - whether to perform strict check (=== '') for undefined class of field or not
 * show_inactive_options  - whether inactive options should be displayed or not
 * disable_inactive_style - if true, the usual styles for inactive options are not applied (used in combination with styles specified for options themselves)
 *}
{* Define indexes *}
{if $index}{strip}
  {capture assign='index_array'}
  {if $eq_indexes}
    {$index}
  {elseif $empty_indexes}
  {elseif $name_index}
      {$name_index}
  {else}
    {$index-1}
  {/if}
  {/capture}
{/strip}{/if}
{* Define width *}
{strip}
{capture assign='width'}
    {if $standalone}
      {if preg_match('#^(\d+%|)$#', $width)}
        100%
      {elseif is_numeric($width)}
        {$width}px
      {/if}
    {/if}
{/capture}
{/strip}
{* Define height *}
{capture assign='height'}{if $height && !preg_match('#%$#', $height)}{$height}px{elseif $height}{$height}{/if}{/capture}
{if !$standalone}
<tr{if $hidden} style="display: none"{/if}>
  {* Label Cell *}
  <td class="labelbox">
    {* Anchor for error reference *}
    <a name="error_{$custom_id|default:$name}"></a>
    {* Label of the variable *}
    <label for="{$custom_id|default:$name}{if $readonly}_readonly{/if}"{if $messages->getErrors($name)} class="error"{/if}>{help label_content=$label text_content=$help}</label>
  </td>

  {* Required Cell *}
  <td{if $required} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>

  {* Element Cell *}
  <td nowrap="nowrap">
{/if}

{* define whether "please, select" option will be displayed *}
{assign var='hide_first_option' value='1'}
{if ($options && $options|@count || $optgroups && $optgroups|@count) && !($skip_please_select || !empty($options.skip_please_select) || !empty($optgroups.skip_please_select)) && (!$required || ($really_required && $required && !$hidden))}
  {assign var='hide_first_option' value='0'}
  {if $really_required && $required}
    {if $options && ($options|@count eq 1)}
      {assign var='hide_first_option' value='1'}
    {elseif $optgroups && ($optgroups|@count eq 1)}
      {foreach from=$optgroups item='opt_check'}
        {if $opt_check|@count eq 1}
          {assign var='hide_first_option' value='1'}
        {/if}
      {/foreach}
    {/if}
  {/if}
{/if}

    {* Element *}
    <select 
      name="{$name}{if $readonly}_readonly{/if}{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}{if $readonly}_readonly{/if}{if $index}_{$index}{/if}"
      class="selbox{if $readonly} readonly{/if}{if empty($options) && empty($optgroups)} missing_records{elseif !$hide_first_option && (!$undefined_strict_check && !$value || $value === '')} undefined{/if}{if $custom_class} {$custom_class}{/if}"
      style="{if $hidden}display: none;{/if}{if $width}width: {$width};{/if}{if $text_align} text-align: {$text_align};{/if}{if $height}height: {$height};{/if}{if $custom_style}{$custom_style}{/if}"
      title="{$label|strip_tags:false|escape}"
      onfocus="highlight(this);"
      onblur="unhighlight(this);"
      onclick="{if !empty($js_methods.onclick)}{$js_methods.onclick}{/if}"
      {if $disabled || $readonly} disabled="disabled"{/if}
      onchange="toggleUndefined(this); {if $sequences}if (this.value) {ldelim}{$sequences}{rdelim}{elseif $on_change}change_options({$on_change}){elseif $onchange}{$onchange}{elseif !empty($js_methods.onchange)}{$js_methods.onchange}{/if}"
      {if !empty($js_methods.onkeypress)}onkeypress="{$js_methods.onkeypress}"{/if}>
    {*Options of the dropdown*}
    {*if the dropdown is required and really required show the "please select" option*}
    {if empty($options) && empty($optgroups)}
      <option value="" class="missing_records"{if $value === ""} selected="selected"{/if}>{if $no_select_records_label}[{$no_select_records_label|escape}]{else}[{#no_select_records#|escape}]{/if}</option>
    {elseif !$hide_first_option}
      <option value="" class="undefined"{if $value === ""} selected="selected"{/if}>{if $first_option_label}[{$first_option_label|mb_lower}]{else}[{#please_select#|escape}]{/if}</option>
    {/if}
    {if $optgroups}
      {foreach from=$optgroups item='optgroup' key='optlabel'}
        {if $optlabel !== 'skip_please_select'}
          <optgroup label="{if $optgroup_label_source eq 'config'}{$smarty.config.$optlabel|escape}{else}{$optlabel|escape}{/if}">
            {foreach from=$optgroup item='option'}
              {assign var='label' value=$option.label}
              {if ($show_inactive_options || !isset($option.active_option) ||  $option.active_option == 1 || $option.option_value === $value)}
                <option value="{$option.option_value|escape}" class="{if $option.class_name}{$option.class_name} {/if}{if (isset($option.active_option) && $option.active_option == 0 && !$disable_inactive_style)}inactive_option{/if}"{if (isset($option.active_option) && $option.active_option == 0 && !$disable_inactive_style)} title="{#inactive_option#}"{/if}{if $option.option_value === $value} selected="selected"{/if}{if ($show_inactive_options && isset($option.active_option) && !$option.active_option && $option.option_value !== $value) && !($action == 'getoptions' && in_array($smarty.get.getoptions, array('search', 'filter')))} disabled="disabled"{/if}>{if (isset($option.active_option) && $option.active_option == 0 && !$disable_inactive_style)}*&nbsp;{/if}{if $option_label_source eq 'config'}{$smarty.config.$label|default:'&nbsp;'}{else}{if $do_not_escape_labels}{$label|default:'&nbsp;'}{else}{$label|default:'&nbsp;'}{/if}{/if}</option>
              {/if}
            {/foreach}
          </optgroup>
        {/if}
      {/foreach}
    {else}
      {foreach from=$options item='option' key='idx'}
      {assign var='label' value=$option.label}
        {if $idx !== 'skip_please_select'}
        {if ($show_inactive_options || !isset($option.active_option) ||  $option.active_option == 1 || $option.option_value === $value)}
          <option value="{$option.option_value|escape}" class="{if $option.class_name}{$option.class_name} {/if}{if (isset($option.active_option) && $option.active_option == 0 && !$disable_inactive_style)}inactive_option{/if}"{if (isset($option.active_option) && $option.active_option == 0 && !$disable_inactive_style)} title="{#inactive_option#}"{/if}{if $option.option_value === $value} selected="selected"{/if}{if ($show_inactive_options && isset($option.active_option) && !$option.active_option && $option.option_value !== $value) && !($action == 'getoptions' && in_array($smarty.get.getoptions, array('search', 'filter')))} disabled="disabled"{/if}>{if (isset($option.active_option) && $option.active_option == 0 && !$disable_inactive_style)}*&nbsp;{/if}{if $option_label_source eq 'config'}{$smarty.config.$label|default:'&nbsp;'}{else}{if $do_not_escape_labels}{$label|default:'&nbsp;'}{else}{$label|default:'&nbsp;'}{/if}{/if}</option>
        {/if}
        {/if}
      {/foreach}
    {/if}
    </select>
    {if $readonly}
      <input type="hidden" name="{$name}{if $index}[{$index_array}]{/if}" id="{$custom_id|default:$name}{if $index}_{$index}{/if}" value="{$value}" readonly="readonly" class="selbox_hidden readonly{if $custom_class} {$custom_class}{/if}" />
    {/if}

    {* Back label *}
    {if !$back_label && $var.back_label}
      {assign var='back_label' value=$var.back_label}
    {/if}
    {if !$back_label_style && $var.back_label_style}
      {assign var='back_label_style' value=$var.back_label_style}
    {/if}
    {include file="_back_label.html"
      custom_id=$custom_id
      name=$name
      back_label=$back_label
      back_label_style=$back_label_style}

{if !$standalone}
  </td>
</tr>
{/if}
