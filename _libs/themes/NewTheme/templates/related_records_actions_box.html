{if $available_actions_related_records}
  <div style="position: relative;">
    <div class="action_tabs hidden" id="related_records_action_tabs">
      <ul id="related_records_model_actions_{counter start=1 name='menu_counter' print=true assign='model_action_menu_num'}" class="zpHideOnLoad">
        {foreach from=$available_actions_related_records item='available_action'}
          {if $available_action.name eq '|'}
            </ul>
            <script type="text/javascript">
              new Zapatec.Menu({ldelim}source: 'related_records_model_actions_{$model_action_menu_num}',
                                hideDelay: 100,
                                theme: 'nzoom'{rdelim});
            </script>
            <div class="clear"></div>
            <ul id="related_records_model_actions_{counter name='menu_counter' print=true assign='model_action_menu_num'}" class="zpHideOnLoad">
          {else}
            {if $available_action.drop_menu}
              {include file="`$theme->templatesDir`_drop_menu_item.html" action_options=$available_action}
            {else}
              <li class="{if $available_action.name eq $rel_type}menu-path{/if}{if !$currentUser->getPersonalSettings('interface', 'action_labels')} tab_no_label{/if}" title="{$available_action.label}">
                <img src="{$theme->imagesUrl}{if $available_action.img}{$available_action.img}{else}{$available_action.name}{/if}.png" width="16" height="16" alt="" title="{$available_action.label}" border="0" />
                <a id="a_tab_{$available_action.session_param}" {if $available_action.options}href="#"{else}href="{$available_action.url}"{if $available_action.target} target="{$available_action.target}"{/if}{/if} title="{$available_action.label}">{if $currentUser->getPersonalSettings('interface', 'action_labels')}{$available_action.label|mb_truncate:20:'...':true}{/if}</a>
                {* dummy span for onclick calls *}
                <span style="display: none"{if preg_match('/^#related_subpanel/', $available_action.url)} onclick="toggleRelatedTabs(this); $('rel_type').value='{$available_action.name}';"{/if} id="tab_{$available_action.name}"></span>
              </li>
            {/if}
          {/if}
        {/foreach}
      </ul>
      <script type="text/javascript">
          new Zapatec.Menu({ldelim}source: 'related_records_model_actions_{$model_action_menu_num}',
                            hideDelay: 100,
                            theme: 'nzoom'{rdelim});
      </script>
    </div>
    <div class="clear"></div>
  </div>
{/if}
<div class="clear"></div>
