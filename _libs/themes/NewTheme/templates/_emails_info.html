<table cellpadding="0" cellspacing="0" border="0" class="attachments t_grouping_table t_table hleft" style="border: 1px solid #999999; z-index: 10000; width: 300px;">
  <tr>
    <th class="nowrap">
      <a title="{#expand_all#|escape}" href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$smarty.request.real_module}{if $smarty.request.real_controller ne $smarty.request.real_module}&amp;controller={$smarty.request.real_controller}{/if}&amp;{$smarty.request.real_controller}=communications&amp;communications={$smarty.request.model_id}&amp;communication_type=emails{if $smarty.request.archive}&amp;archive=1{/if}"><span class="t_panel_caption_title">{#communications_emails_last_records_info#}</span></a>
    </th>
  </tr>
{foreach from=$emails item='email'}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="t_border t_bottom_border" style="color: #000000;">
      {$email->get('from')|escape} ({$email->get('date')|date_format:#date_mid#})<br />

      {if $email->get('to')}
        <span class="strong">{#to#|escape}</span>:
        {foreach name='c' from=$email->get('to') item='to_user'}
          {if $to_user.mail_status eq 'sent' && $to_user.receive_status eq 'received'}
            {assign var='name_color' value='000000'}
            {assign var='name_message' value='email_sent_received_comment_reason'}
          {elseif $to_user.mail_status eq 'received'}
            {assign var='name_color' value='006600'}
            {assign var='name_message' value='email_received_comment_reason'}
          {else}
            {assign var='name_color' value='FF0000'}
            {if $to_user.receive_status eq 'not_received'}
              {assign var='name_message' value='email_sent_not_received_comment_reason'}
            {elseif $to_user.mail_status ne 'sent'}
              {capture assign='name_message'}email_not_sent_{$to_user.mail_status}_reason{/capture}
            {/if}
          {/if}
          {capture assign='name_caption'}<span class="strong">{#to#|escape}</span>: {if $to_user.name}{$to_user.name|escape|default:"&nbsp;"} {/if}{if $to_user.email}&#60;{$to_user.email|escape}&#62;{/if}{/capture}
          {strip}
          <span style="color: #{$name_color};" class="pointer" {popup caption=$name_caption|escape text=$smarty.config.$name_message|escape width=250}>
            {if $to_user.name}{$to_user.name|escape|default:"&nbsp;"}{elseif $to_user.email}{$to_user.email|escape}{/if}
          </span>{if !$smarty.foreach.c.last}, {/if}
          {/strip}
        {foreachelse}
          &nbsp;
        {/foreach}
        <br />
      {/if}

      {if $email->get('subject')}<span class="strong">{$email->get('subject')}</span><br />{/if}
      {$email->get('content')|regex_replace:'#<(br|hr)( \/)?>#':"\n"|replace:'&nbsp;':' '|strip_tags|regex_replace:'#(\s*\n\s*)+#':"\n"|mb_truncate:130|nl2br|url2href}
    </td>
  </tr>
{foreachelse}
  <tr>
    <td class="t_border t_bottom_border" style="color: #FF0000;">
      {if $no_permissions}{#communications_emails_no_permissions#}{else}{#error_no_items_found#}{/if}
    </td>
  </tr>
{/foreach}
</table>
