<div id="lr_table" style="visibility: hidden; position: absolute;" onmouseover="mcancelclosetime();" onmouseout="mclosetime();">
  {if $currentUser} 
    <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
      <tr>
        <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
        <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#lr_model#|escape}</div></td>
        <td class="t_caption">&nbsp;</td>
      </tr>
    {foreach name='i' from=$currentUser->get('locked_records') item='item'}
      {if $item.status ne 'expired'}
        {strip}
        {capture assign='model_type'}lr_model_{$item.model|mb_lower}{/capture}
        {capture assign='info'}
          <strong><u>{#lr_model#|escape}</u>:</strong> {$smarty.config.$model_type}<br />
          <strong>{#lr_lock_date#|escape}:</strong> {$item.lock_date|date_format:#date_mid#|escape}<br />
          <strong>{#lr_lock_by#|escape}:</strong> {$item.locked_by_name}<br />
        {/capture}
        {/strip}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border hright" nowrap="nowrap">{$smarty.foreach.i.iteration}</td>
            <td class="t_border">{$smarty.config.$model_type}</td>
            <td class="hcenter" nowrap="nowrap">
              <img src="{$theme->imagesUrl}unlock.png" width="16" height="16" border="0" class="pointer" alt="{#unlock#|escape}" title="{#unlock#|escape}" onclick="confirmAction('unlock', function() {ldelim} unlockRecord({ldelim}module: '{$item.module}', controller: '{$item.controller}', action: '{$item.action}', unlock: '{$item.id}', model_id: '{$item.model_id}'{rdelim}); {rdelim}, this, '{#confirm_unlock_one#|escape:'quotes'|escape}')" />
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$item.module}&amp;{$controller_param}={$item.controller}&amp;{$item.controller}=view&amp;view={$item.model_id}">
                <img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="{#view#|escape}" title="{#view#|escape}" />
              </a>
              <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$info|escape caption=#system_info#|escape} />
            </td>
          </tr>
      {/if}
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="4">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
      <tr>
        <td class="t_footer" colspan="4"></td>
      </tr>
    </table>
  {/if}
</div>
