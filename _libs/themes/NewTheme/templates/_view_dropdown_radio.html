{if $extended_value || $extended_value === '0'}
    {*FOR BB tables*}
    {$extended_value}
{elseif $var.options}
    {foreach from=$var.options item='option'}
        {if (is_array($value) && @in_array($option.option_value, $value)) || (!is_array($value) && $option.option_value eq $value)}
            {strip}{if $var.view_mode eq 'link' && $var.view_mode_url}
                <a target="_blank" href="{$var.view_mode_url}{$value}" title="{#view#|escape}: {$option.label|escape|default:"&nbsp;"}">
            {/if}
            {if (isset($option.active_option) && $option.active_option == 0)}
                <span class="inactive_option" title="{#inactive_option#}">*
            {/if}
            {$option.label|regex_replace:"/&nbsp;/":" "|escape}
            {if (isset($option.active_option) && $option.active_option == 0)}
                </span>
            {/if}
            {if $var.view_mode eq 'link' && $var.view_mode_url}
            </a>
            {/if}{/strip}
        {/if}
    {/foreach}
{elseif $var.optgroups}
    {foreach from=$var.optgroups key='optgroup_name' item='optgroup'}
        {foreach from=$optgroup item='option'}
            {if (is_array($value) && @in_array($option.option_value, $value)) || (!is_array($value) && $option.option_value eq $value)}
                {strip}{if $var.view_mode eq 'link' && $var.view_mode_url}
                    <a target="_blank" href="{$var.view_mode_url}{$value}" title="{#view#|escape}: {$option.label|escape|default:"&nbsp;"}">
                {/if}
                {if (isset($option.active_option) && $option.active_option == 0)}
                    <span class="inactive_option" title="{#inactive_option#}">*
                {/if}
                {$option.label|regex_replace:"/&nbsp;/":" "|escape}
                {if (isset($option.active_option) && $option.active_option == 0)}
                    </span>
                {/if}
                {if $var.view_mode eq 'link' && $var.view_mode_url}
                    </a>
                {/if}{/strip}
            {/if}
        {/foreach}
    {/foreach}
{/if}