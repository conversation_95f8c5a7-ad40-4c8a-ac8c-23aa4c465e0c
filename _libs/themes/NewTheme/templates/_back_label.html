{** ALLOWED PARAMETERS:
 * back_label - text of back label
 * custom_id - custom_id of field (in edit mode)
 * name - name of field (in edit mode)
 * index - suffix of field in grouping or GT2 table (in edit mode)
 * back_label_style - CSS style of label (in edit mode)
 *}
{if $back_label}
{capture assign='for'}{$custom_id|default:$name}{if ($custom_id || $name) && $index}_{$index}{/if}{/capture}
{if !$for}
 {$back_label}
{else}
  <label for="{$for}" class="back_label"{if $back_label_style} style="{$back_label_style|escape}"{/if}>{$back_label}</label>
{/if}
{/if}