      <table border="0" cellpadding="3" cellspacing="0" class="t_table t_table_border" style="width:55%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="3"><div class="t_caption_title">{#edit_comment#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><label for="subject">{#subject#|escape}</label></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="subject" id="subject" value="{$comment->get('subject')|escape}" title="" onfocus="highlight(this)" onblur="unhighlight(this)" />
            <input type="hidden" name="id" id="id" value="{$comment->get('id')}"  />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="content">{#comment#|escape}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <textarea class="areabox doubled higher" name="content" id="content" title="" onfocus="highlight(this)" onblur="unhighlight(this)">{$comment->get('content')|escape}</textarea>
          </td>
        </tr>
    {if !$currentUser->get('is_portal')}
      <tr>
        <td class="labelbox" nowrap="nowrap"><a name="error_is_portal"><label for="is_portal"{if $messages->getErrors('is_portal')} class="error"{/if}>{#is_portal#|escape}:</label></a></td>
        <td>&nbsp;</td>
        <td nowrap="nowrap">
          {assign var='is_portal' value=$comment->get('is_portal')}
          {capture assign="is_portal_suffix"}_{uniqid}{/capture}
          <input type="radio" name="is_portal" id="is_portal1{$is_portal_suffix}" value="1" title="{#is_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" {if $is_portal} checked="checked"{/if} /><label for="is_portal1{$is_portal_suffix}">{#is_portal#|escape}</label>
          <input type="radio" name="is_portal" id="is_portal2{$is_portal_suffix}" value="0" title="{#is_not_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !$is_portal} checked="checked"{/if} /><label for="is_portal2{$is_portal_suffix}">{#is_not_portal#|escape}</label>
        </td>
      </tr>
    {/if}
        <tr>
        <td colspan="3">
          <button type="submit" class="button" name="editComment" id="editComment">{#edit#|escape}</button><button type="submit" class="button" name="addComment" id="addComment" onclick="$('id').value='';">{#add#|escape}</button></td>
        </tr>
        <tr>
          <td class="t_footer" colspan="3"></td>
        </tr>
      </table>
