{if !$smarty.cookies.search_type || $smarty.cookies.search_type eq 'simple'}
  {assign var='search_type' value='simple'}
{else}
  {assign var='search_type' value='advanced'}
{/if}

{if $params.real_module}
  {assign var='params_module' value=$module}
  {assign var='params_controller' value=$controller}
  {assign var='params_container_prefix' value=$params.real_module}
  {assign var='module' value=$params.real_module}
  {assign var='controller' value=$params.real_controller}
{elseif $params.module}
  {assign var='params_module' value=$params.module}
  {assign var='params_controller' value=$params.controller}
  {assign var='params_container_prefix' value=$module}
{/if}

{if !$inner_search}
  <table border="0" cellpadding="3" cellspacing="3">
    <tr>
      <td colspan="3" width="450">
        <span class="strong" id="search_simple_switch"{if $search_type eq 'advanced'} style="display: none;"{/if}>{#search_simple#|escape} <span class="legend pointer" onclick="toggleSearchType(this.parentNode);{if $available_action.name eq 'filter'} scalePopup();{/if}">({#search_show_advanced#|escape})</span></span>
        <span class="strong" id="search_advanced_switch"{if $search_type eq 'simple'} style="display: none;"{/if}>{#search_advanced#|escape} <span class="legend pointer" onclick="toggleSearchType(this.parentNode);{if $available_action.name eq 'filter'} scalePopup();{/if}">({#search_show_simple#|escape})</span></span>
      </td>
    </tr>
  </table>
{else}
  {assign var='search_type' value='advanced'}
{/if}

{if $search_type eq 'simple' || $view_mode}
  {assign var='disabled' value=1}
{else}
  {assign var='disabled' value=0}
{/if}

<!-- JS arrays for manipulation of the search options -->
<script type="text/javascript">
    search_additional_vars_switch = '{$switch_additional}';
    advanced_search_obj = {$advanced_search_options};
    additional_search_obj = {$additional_search_options};
</script>

<!-- ADVANCED SEARCH -->
<div id="search_advanced_container" class="search_container"{if $search_type eq 'simple'} style="display: none;"{/if}>
  <input type="hidden" value="" id="filters_action" name="filters_action" {if $disabled}disabled="disabled" {/if}/>
  <div class="t_caption3_title t_caption3" style="padding: 4px;">
    {#search_filters#}
  </div>
  <table border="0" cellpadding="5" cellspacing="0" id="search_container" class="t_borderless">
    <tr id="search_headers" style="display: none;">
      <td></td>
    </tr>
    {counter start=0 name='item_counter' print=false assign=current_item}
    {foreach from=$session_filters.search_fields item='filter' key='key' name='i'}
    {* check if this filter is present in the options *}
    {assign var=present value=0}
    {foreach from=$search_fields.basic_vars item=opt}
       {if $opt.option_value eq $filter}{assign var=present value=1}{/if}
    {/foreach}
    {if !$present }
      {foreach from=$search_fields.additional_vars item=opt}
        {if $opt.option_value eq $filter}{assign var=present value=1}{/if}
      {/foreach}
    {/if}
    {if $present}
      {counter name='item_counter' print=false}
      {if !preg_match('#a__#', $filter)}
        {assign var='search_defs' value=$search_fields.basic_vars}
      {else}
        {assign var='search_defs' value=$search_fields.additional_vars}
      {/if}
      <tr id="search_container_{$current_item}">
        <td id="search_container_{$current_item}_0">
          {if $view_mode}
            {$current_item}
          {else}
            <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if empty($session_filters.search_fields) || count($session_filters.search_fields) le 1} style="visibility: hidden;"{/if} onclick="processSearchDef('hide', '{$current_item}');" />
            <a href="javascript: void(0);" onclick="javascript: processSearchDef('disable', '{$current_item}');" title="{#deactivate#|escape}">{$current_item}</a>
          {/if}
        </td>
        <td id="search_container_{$current_item}_1" style="width: 150px;">
        {if $additional_search_options eq 'false' || $additional_search_options eq '[]'}
          {include file="input_dropdown.html"
            standalone=true
            required=0
            disabled=$disabled
            name='search_fields'
            custom_id='search_fields'
            index=$current_item
            sequences='setSearchDef(this);'
            value=$filter
            width='150'
            optgroup_label_source='config'
            options=$search_fields.basic_vars
          }
        {else}
          {include file="input_dropdown.html"
            standalone=true
            required=0
            disabled=$disabled
            name='search_fields'
            custom_id='search_fields'
            index=$current_item
            sequences='setSearchDef(this);'
            value=$filter
            width='150'
            optgroup_label_source='config'
            optgroups=$search_fields
          }
        {/if}
        {include file="input_hidden.html"
            name='search_fields_prev'
            standalone=true
            custom_id='search_fields_prev'
            index=$current_item
            value=$filter
          }
        </td>
        <td id="search_container_{$current_item}_2" style="width: 150px;">
          {assign var='selected_compare' value=$session_filters.compare_options.$key}
          {counter assign='options_count' start=0 print=false}
          {foreach from=$search_defs.$filter.compare_options item='opt_groups' key='opt_group'}
            {foreach from=$opt_groups item='option' key='idx'}
              {counter print=false}
              {if $selected_compare eq $option.option_value}
                {assign var='values_operator_group' value=$opt_group}
                {assign var='values_operator' value=$idx}
                {assign var='next_input' value=$option.itype}
              {/if}
            {/foreach}
          {/foreach}
          {if $options_count eq 1 || preg_match('/\.((search_)?archive|deleted)$/', $filter) || preg_match('/^(fp\.annulled|(fir|fer)\.active)$/', $filter)}
            {include file="input_hidden.html"
              standalone=true
              disabled=$disabled
              name='compare_options'
              custom_id='compare_options'
              index=$current_item
              value=$selected_compare
            }
          {else}
            {include file="input_dropdown.html"
              standalone=true
              required=1
              disabled=$disabled
              name='compare_options'
              custom_id='compare_options'
              index=$current_item
              sequences='setSearchDef(this);'
              value=$selected_compare
              width='150'
              optgroups=$search_defs.$filter.compare_options
            }
          {/if}
        </td>
        <td id="search_container_{$current_item}_3" style="white-space: nowrap!important; width: 205px;">
          {if $next_input eq 'autocompleter'}
            {assign var='restrict' value=''}
          {elseif $next_input eq 'date'}
            {assign var='searchable' value=1}
            {assign var='restrict' value=''}
          {elseif $next_input eq 'number'}
            {assign var='searchable' value=0}
            {assign var='next_input' value='text'}
            {assign var='restrict' value='insertOnlyReals'}
          {else}
            {assign var='restrict' value=''}
            {assign var='searchable' value=0}
          {/if}
          {if $filter eq $switch_additional || preg_match('#\.type$#', $filter)}
            {assign var='sequences' value="setSearchDef(this);"}
          {else}
            {assign var='sequences' value=''}
          {/if}
          {if isset($session_filters.values_code.$key) || isset($session_filters.values_autocomplete.$key)}
            {assign var='autocomplete_var_type' value='basic'}
          {else}
            {assign var='autocomplete_var_type' value='searchable'}
          {/if}
          {if $next_input}
              {include file="input_`$next_input`.html"
                standalone=true
                required=1
                searchable=$searchable
                disabled=$disabled
                show_calendar_icon=true
                index=$current_item
                name='values'
                custom_id='values'
                width='200'
                sequences=$sequences
                hide_calendar_icon=1
                restrict=$restrict
                do_not_escape_labels=true
                value=$session_filters.values.$key
                value_autocomplete=$session_filters.values_autocomplete.$key
                value_code=$session_filters.values_code.$key
                value_name=$session_filters.values_name.$key
                value_date_period=$session_filters.date_period.$key|escape
                autocomplete_var_type=$autocomplete_var_type
                autocomplete=$search_defs.$filter.compare_options.$values_operator_group.$values_operator.options
                options=$search_defs.$filter.compare_options.$values_operator_group.$values_operator.options
                optgroups=$search_defs.$filter.compare_options.$values_operator_group.$values_operator.opt_groups
                show_inactive_options=true
              }
          {/if}
        </td>
        <td id="search_container_{$current_item}_4" style="width: 30px;">
        {if $smarty.foreach.i.iteration eq $smarty.foreach.i.last}
          &nbsp;
        {else}
          <select id="logical_operator_{$current_item}" name="logical_operator[{$current_item-1}]"
                  class="selbox short" onfocus="highlight(this)" onblur="unhighlight(this)"
                  {if $disabled}disabled="disabled"{/if}>
            <option value="AND" {if $session_filters.logical_operator.$key eq 'AND'}selected="selected"{/if}>{#and#}</option>
            <option value="OR" {if $session_filters.logical_operator.$key eq 'OR'}selected="selected"{/if}>{#or#}</option>
          </select>
        {/if}
        </td>
        <td id="search_container_{$current_item}_5">
          {if $current_item eq 1 && !$view_mode}
          <div class="t_buttons">
            <div id="search_container_plusButton" onclick="processSearchDef('add');" {help label_content=#add_filter# popup_only=1}><div class="t_plus"></div></div>
            <div id="search_container_minusButton"{if empty($session_filters.search_fields) || count($session_filters.search_fields) le 1} class="disabled"{/if} onclick="processSearchDef('remove');" {help label_content=#remove_filter# popup_only=1}><div class="t_minus"></div></div>
          </div>
          {/if}
        </td>
      </tr>
    {/if}
    {foreachelse}
    <tr id="search_container_1">
      <td id="search_container_1_0">
        {if $view_mode}
          1
        {else}
          <img src="{$theme->imagesUrl}/small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" style="visibility: hidden;" onclick="processSearchDef('hide', '1');" />
          <a href="javascript: void(0);" onclick="javascript: processSearchDef('disable', '1');" title="{#deactivate#|escape}">1</a>
        {/if}
      </td>
      <td id="search_container_1_1" style="width: 150px;">
        {include file="input_dropdown.html"
          standalone=true
          required=0
          disabled=$disabled
          name='search_fields'
          custom_id='search_fields'
          index=1
          sequences='setSearchDef(this);'
          value=''
          options=$search_fields.basic_vars
        }
        {include file="input_hidden.html"
          standalone=true
          name='search_fields_prev'
          custom_id='search_fields_prev'
          index=1
          value=''
        }
      </td>
      <td id="search_container_1_2" style="width: 150px;">
      </td>
      <td id="search_container_1_3" style="white-space: nowrap!important; width: 205px;">
      </td>
      <td id="search_container_1_4" style="width: 30px;">
      </td>
      <td id="search_container_1_5">
        {if !$view_mode}
        <div class="t_buttons">
          <div id="search_container_plusButton" onclick="processSearchDef('add');" {help label_content=#add_filter# popup_only=1}><div class="t_plus"></div></div>
          <div id="search_container_minusButton"{if empty($session_filters.search_fields) || count($session_filters.search_fields) le 1} class="disabled"{/if} onclick="processSearchDef('remove');" {help label_content=#remove_filter# popup_only=1}><div class="t_minus"></div></div>
        </div>
        {/if}
      </td>
    </tr>
    {/foreach}
  </table>

  <!-- SORT, SAVED FILTERS, RPP -->
  <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
    <tr>
      <td style="min-width: 33%; vertical-align: top;">
        <div class="t_caption3_title t_caption3" style="padding: 4px;">
          {#sort#}
          {if !$view_mode}
          <div class="t_buttons">
            <div id="search_sort_container_plusButton" onclick="addSortCondition('search_sort_container',1)" {help label_content=#add_sort# popup_only=1}><div class="t_plus"></div></div>
            <div id="search_sort_container_minusButton"{if empty($session_filters.sort) || count($session_filters.sort) le 1} class="disabled"{/if} onclick="removeSortCondition('search_sort_container')" {help label_content=#remove_sort# popup_only=1}><div class="t_minus"></div></div>
          </div>
          {/if}
        </div>
        <table border="0" cellpadding="5" cellspacing="0" id="search_sort_container">
          <tr>
            {foreach from=$session_filters.sort item='sort' key='key' name='ii'}
            <td style="width: 100px;{if $smarty.foreach.ii.iteration != $smarty.foreach.ii.first} border-left: solid 1px #AAAAAA;{/if}">
              {include file="input_dropdown.html"
                standalone=true
                required=1
                disabled=$disabled
                name='sort'
                custom_id='sort'
                index=$smarty.foreach.ii.iteration
                sequences=''
                value=$sort
                optgroup_label_source='config'
                optgroups=$system_fields.sort
              }
            </td>
            {foreachelse}
            <td style="width: 100px;">
            {capture assign='default_sort'}{$alias}.added{/capture}
              {include file="input_dropdown.html"
                standalone=true
                required=1
                disabled=$disabled
                name='sort'
                custom_id='sort'
                index=1
                sequences=''
                value=$default_sort
                optgroup_label_source='config'
                optgroups=$system_fields.sort
              }
            </td>
            {/foreach}
          </tr>
          <tr>
            {foreach from=$session_filters.order item='order' key='key' name='ii'}
            <td style="width: 100px;{if $smarty.foreach.ii.iteration != $smarty.foreach.ii.first} border-left: solid 1px #AAAAAA;{/if}">
              {include file="input_dropdown.html"
                standalone=true
                required=1
                disabled=$disabled
                name='order'
                custom_id='order'
                index=$smarty.foreach.ii.iteration
                sequences=''
                value=$order
                options=$system_fields.order
              }
            </td>
            {foreachelse}
            <td style="width: 100px;">
              {include file="input_dropdown.html"
                standalone=true
                required=1
                disabled=$disabled
                name='order'
                custom_id='order'
                index=1
                sequences=''
                value=''
                options=$system_fields.order
              }
            </td>
            {/foreach}
          </tr>
        </table>
      </td>
      {if !$view_mode}
      <td style="border-left: 1px solid #AAAAAA; min-width: 33%; vertical-align: top;">
        <div class="t_caption3_title t_caption3" style="padding: 4px; white-space: nowrap;">
          {#save_load_filters#}
        </div>
        <table border="0" cellpadding="5" cellspacing="0" id="search_save_container">
          <tr>
            <td nowrap="nowrap"><label for="save_filter_as">{#save_search#}:</label></td>
            <td style="width: 200px;">
              {include file="input_combobox.html"
                 disabled=$disabled
                 standalone=true
                 name='save_filter_as'
                 custom_id='save_filter_as'
                 value=''
                 sequences=''
                 width='200'
                 options=$saved_filters
                 label=#name#
              }
            </td>
            <td class="nowrap">
              {include file="input_checkbox.html"
                 disabled=$disabled
                 standalone=true
                 name='save_as_action'
                 custom_id='save_as_action'
                 option_value='1'
                 label=#save_as_action#
              }
            </td>
            <td>
              <a href="javascript: void(0);" onclick="filterActions($('save_filter_as').form, 'savefilter');">
                <img src="{$theme->imagesUrl}small/download.png" id="save_filter" name="save_filter" class="pointer" width="14" height="14" alt="{#save#}" title="{#save#}" />
              </a>
            </td>
          </tr>
          <tr>
            <td nowrap="nowrap"><label for="filter_name">{#load_search#}:</label></td>
            <td style="width: 200px;">
              {include file="input_dropdown.html"
                 standalone=true
                 disabled=$disabled
                 name='filter_name'
                 custom_id='filter_name'
                 value=''
                 sequences=''
                 width='200'
                 options=$saved_filters
                 label=#filter#
              }
            </td>
            <td nowrap="nowrap" colspan="2">
              <a href="javascript: void(0);"
              {if !$inner_search}
                onclick="filterActions($('filter_name').form, 'loadfilter', 'filter_name');"
              {else}
                onclick="getActionOptions('{$params_container_prefix|default:'td'}_search_options', '{$params_module|default:$module}', '{$params_controller|default:$controller}', 'filters_ajax_load', 0, {ldelim}real_module: '{$module}', real_controller: '{$controller}'{rdelim});"
              {/if}
              >
                <img src="{$theme->imagesUrl}small/reload.png" id="load_filter" name="load_filter" class="pointer" width="12" height="12" alt="{#config_load#}" title="{#config_load#}" />
              </a>
              <a href="javascript: void(0);" onclick="filterActions($('filter_name').form, 'deletefilter', 'filter_name');">
                <img src="{$theme->imagesUrl}small/delete.png" id="delete_filter" name="delete_filter" class="pointer" width="12" height="12" alt="{#delete#}" title="{#delete#}" />
              </a>
            </td>
          </tr>
        </table>
      </td>
      {/if}
      {if $module ne 'dashlets' || !$params_module}
      <td style="border-left: 1px solid #AAAAAA; min-width: 33%; vertical-align: top;">
        <div class="t_caption3_title t_caption3" style="padding: 4px; white-space: nowrap;">
          {#display_rpp#}
        </div>
        <table border="0" cellpadding="5" cellspacing="0" id="search_display_container">
          <tr>
            <td>{#display#}:</td>
            <td>
              {capture assign='var_name'}list_{if $module ne $controller}{$module}_{$controller}{else}{$module}{/if}{/capture}
              {assign var='default_rpp' value=$currentUser->getPersonalSettings('interface', $var_name)}
              {if empty($default_rpp)}{assign var='default_rpp' value=''}{/if}
              {include file="input_dropdown.html"
               standalone=true
               required=1
               disabled=$disabled
               name='display'
               custom_id='display'
               value=$session_filters.display|default:$default_rpp|default:'10'
               sequences=''
               options=$system_fields.display
             }
            </td>
          </tr>
          <tr>
            <td style="height: 26px;" colspan="2">&nbsp;</td>
          </tr>
        </table>
      </td>
      {/if}
    </tr>
  </table>
  <div style="height: 10px; border-top: 1px solid #AAAAAA;"></div>
</div>
<!--  END ADVANCED SEARCH -->
{if !$inner_search}
<!--  SIMPLE SEARCH -->
<div id="search_simple_container" class="search_container"{if $search_type eq 'advanced'} style="display: none;"{/if}>
{capture assign='disabled'}{if $search_type eq 'advanced'}1{else}0{/if}{/capture}
<!-- Hidden search filters (if any) -->
{foreach from=$additional_hidden_filters item='hidden_filter'}
  {include file="input_hidden.html"
           standalone=true
           disabled=$disabled
           name=$hidden_filter.name
           custom_id=$hidden_filter.name
           value=$hidden_filter.value
  }
{/foreach}
<table border="0" cellpadding="3" cellspacing="3">
  <tr>
    <td>{#key#}:</td>
    <td style="width: 200px;">
      {include file="input_text.html"
        standalone=true
        required=0
        disabled=$disabled
        show_calendar_icon=false
        name='key'
        custom_id='key'
        value=$session_filters.key
      }
    </td>
    <td>{#field#}:</td>
    <td style="width: 200px;">
      {include file="input_dropdown.html"
        standalone=true
        required=0
        disabled=$disabled
        show_calendar_icon=false
        name='field'
        custom_id='field'
        value=$session_filters.field
        options=$simple_search_defs
        sequences=''
      }
    </td>
  </tr>
  <tr>
    <td>{#load_search#}:</td>
    <td style="width: 200px;">
      {include file="input_dropdown.html"
         standalone=true
         disabled=$disabled
         name='filter_name'
         custom_id='filter_name_simple'
         value=''
         sequences=''
         width='200'
         options=$saved_filters
         label=''
      }
    </td>
    <td>
      <a href="javascript: void(0);"
      {if !$inner_search}
        onclick="filterActions($('filter_name_simple').form, 'loadfilter', 'filter_name_simple');" alt="{#config_load#}" title="{#config_load#}"
      {else}
        onclick="getActionOptions('{$params_container_prefix|default:'td'}_search_options', '{$params_module|default:$module}', '{$params_controller|default:$controller}', 'filters_ajax_load', 0, {ldelim}real_module: '{$module}', real_controller: '{$controller}'{rdelim});"
      {/if}
      >
        <img src="{$theme->imagesUrl}small/reload.png" id="load_filter" name="load_filter" class="pointer" width="12" height="12" />
      </a>
      <a href="javascript: void(0);" onclick="filterActions($('filter_name_simple').form, 'deletefilter', 'filter_name_simple');">
        <img src="{$theme->imagesUrl}small/delete.png" id="delete_filter" name="delete_filter" class="pointer" width="12" height="12" alt="{#delete#}" title="{#delete#}" />
      </a>
    </td>
    <td>&nbsp;</td>
  </tr>
</table>
</div>
<!-- END SIMPLE SEARCH -->

<table border="0" cellpadding="3" cellspacing="3">
  <tr>
    <td colspan="3">
      {strip}
      <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go1" title="{$available_action.label}"{if $available_action.confirm} onclick="return confirmAction('{$available_action.name}', submitForm, this);"{/if}>{$available_action.label}</button>
      {if $available_action.name eq 'search' || $available_action.name eq 'filter'}
      <button type="button" class="button" onclick="if ('{$available_action.ajax_no}' != '1') {ldelim}getActionOptions('td_{$available_action.name}_options', '{$module}', '{$controller}', '{$available_action.name}', {if $model && $model->get('id')}'{$model->get('id')}'{else}0{/if}, {ldelim}clear_flag: 1{rdelim});{rdelim}toggleActionOptions(this);return false">{#clear_filters#|escape}</button>
      {/if}
      {/strip}
    </td>
  </tr>
</table>
{/if}