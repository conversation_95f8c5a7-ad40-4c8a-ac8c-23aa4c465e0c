{if $smarty.request.uniqid && $object && $object->get('autocomplete_params')}
{* autocompleter identifier and parameters *}
<input type="hidden" name="uniqid" id="uniqid" value="{$smarty.request.uniqid|escape}" />
<input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$object->get('autocomplete_params')|escape}" />
{* after_action fields for redirect *}
<input type="hidden" name="aa_uniqid" id="aa_uniqid" value="{$smarty.request.uniqid|escape}" />
<input type="hidden" name="aa_autocomplete_filter" id="aa_autocomplete_filter" value="session" />
<input type="hidden" name="after_action" id="after_action" value="{$action|escape}" />
{/if}