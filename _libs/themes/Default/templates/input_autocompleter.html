{** ALLOWED PARAMETERS:
 * standalone           - defines whether only the HTML element should be inserted or in a row of table
 * name                 - the form name of the variable (in latin characters), for the group tables the name is array [$index] is added
 * var_id               - id of the variable (as in the _fields_meta DB table)
 * custom_id            - each variable contains custom_id which defines the variable uniquely in the DOM
 * index                - index is the number of row in the group tables (starting with 1)
 * label                - label (translated in the language of the interface)
 * help                 - help text shown in the help baloon with overlib (translated in the language of the interface)
 * value                - the actual value of the variable
 * value_id             - the value of the corresponding field that holds the id of the selected record (necessary only for additional variables)
 * required             - flag that defines whether the variables is required (should be validated) or not
 * readonly             - flag that defines whether the variables should be readonly (not editable) or not
 * hidden               - if the variable is defined as hidden it is not displayed at all hiding it with style="display: none"
 * disabled             - if the variable is defined as disabled
 * width                - the width of the variable defines the width of the HTML element. In the standalone mode the width is defined as 100% of the cell width
 * calculate            - defines whether the HTML element should have calculate formula or not:
 *                        0 - no calculation formula
 *                        1 - calculation formula WITH button for calculation
 *                        2 - calculation formula WITHOUT button for calculation (if the width is 0 the input is not displayed at all)
 * options              - list of options (used only for checkboxes, dropdowns, radio buttons)
 * optgroups            - list of optgroups and their options (overwrites options)(used only for checkboxes, dropdowns, radio buttons)
 * option_value         - the value of the single option (used only for single checkbox)
 * first_option_label   - the label of the first option of a dropdown (used only for dropdowns)
 * origin               - defines the origin of the variable - group, config, table (typically it is not required)
 * format               - defines the format of the element (used only for the date and datetime fields)
 * disallow_date_before - does not allow input of dates before specified date (used only for the date and datetime fields)
 * disallow_date_after  - does not allow input of dates after specified date (used only for the date and datetime fields)
 * hide_calendar_icon   - does not allow showing of calendar icon (used only for the date and datetime fields)
 * onclick              - function defined for the onclick event(used only for buttons, checkboxes and radio buttons)
 * on_change            - function defined for the onchange event(used only for linked dropdowns)
 * js_methods           - defines a JavaScript functions for some keyboard and mouse events
 * restrict             - defines a JavaScript restriction of the input characters.
 *                        For example restrict insertOnlyDigits will allow only digits to be inserted in the text field
 * min_chars            - the minimum number of characters on which the autocompleter will be activated
 * show_placeholder     - if set to label or help, respective text is set as placeholder attribute of text field
 * back_label           - text for the back label
 * back_label_style     - styles (inline CSS) for the back label tag
 *}

{if $index}{strip}
  {capture assign='index_array'}
    {if $eq_indexes}
      {$index}
    {elseif $empty_indexes}
    {elseif $name_index}
      {$name_index}
    {else}
      {$index-1}
    {/if}
  {/capture}
{/strip}{/if}
{capture assign='height'}{strip}
  {if $height && !preg_match('#%$#', $height)}
    {$height}px
  {elseif $height}
    {$height}
  {/if}
{/strip}{/capture}

{if !$standalone}
<tr{if $hidden} style="display: none"{/if}>
  {* Label Cell *}
  <td class="labelbox">
    {* Anchor for error reference *}
    <a name="error_{$custom_id|default:$name}"></a>
    {* Label of the variable *}
    <label for="{$custom_id|default:$name}"{if $messages->getErrors($name)} class="error"{/if}>{help label_content=$label text_content=$help}</label>
  </td>

  {* Required Cell *}
  <td{if $required} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>

  {* Element Cell *}
  <td>
{/if}

    {* Element *}
    {if !$value_autocomplete && ($value_code || $value_name)}
      {capture assign='value_autocomplete'}{if $value_code}[{$value_code}] {/if}{if $value_name}{$value_name}{/if}{/capture}
    {/if}

    {* Get the name of the var *}
    {assign var='var_name' value=$custom_id|default:$name}

    {* Get the additional settings for this autocompleter *}
    {if $autocomplete_var_type eq 'basic' && $basic_vars_additional_settings.$var_name.autocomplete}
      {foreach from=$basic_vars_additional_settings.$var_name key='setting_key' item='setting_value'}
        {if in_array($setting_key, array('autocomplete', 'readonly', 'hidden', 'width', 'show_placeholder', 'custom_class', 'text_align')) && $setting_value !== '' && $setting_value !== null}
          {assign var=$setting_key value=$setting_value}
        {/if}
      {/foreach}
    {/if}

    {if $autocomplete_var_type eq 'basic' || ($autocomplete_var_type eq 'searchable' && $autocomplete.search_by_id)}
      <input type="hidden"
             name="{$name}{if $index}[{$index_array}]{/if}"
             id="{$custom_id|default:$name}{if $index}_{$index}{/if}"
             value="{$value}" />

      {capture assign='visible_name'}{$name}_autocomplete{if $index}[{$index_array}]{/if}{/capture}
      {capture assign='visible_id'}{$custom_id|default:$name}_autocomplete{if $index}_{$index}{/if}{/capture}
      {capture assign='visible_value'}{if $value_autocomplete}{$value_autocomplete}{elseif $value}{$value}{/if}{/capture}
      {assign var='value_id' value=$value}

      {if !$autocomplete && $autocomplete_type}
        {if preg_match('#^([^_]*)_(.*)#', $autocomplete_type, $act_matches)}
          {capture assign='autocomplete_url'}{$smarty.server.PHP_SELF}?{$module_param}={$act_matches[1]}&{$controller_param}={$act_matches[2]}&{$act_matches[2]}=ajax_select{/capture}
        {else}
          {capture assign='autocomplete_url'}{$smarty.server.PHP_SELF}?{$module_param}={$autocomplete_type}&{$autocomplete_type}=ajax_select{/capture}
        {/if}
        {if !$view_mode}{assign var='view_mode' value='link'}{/if}
        {capture assign='view_mode_url'}{if $view_mode == 'link'}{$autocomplete_url|replace:'ajax_select':'view&view='}{/if}{/capture}
        {array assign='autocomplete'
               type=$autocomplete_type
               url=$autocomplete_url
               min_chars=$min_chars
               buttons=$autocomplete_buttons
               buttons_hide=$autocomplete_buttons_hide
               execute_after=$execute_after
               filters=$filters_array
               addquick_type=$addquick_type
               stop_customer_details=$stop_customer_details
               suggestions=$autocomplete_suggestions
               fill_options=$autocomplete_fill_options
               view_mode=$view_mode
               view_mode_url=$view_mode_url
        }
      {else}
        {if $autocomplete_var_type eq 'searchable'}
          {array assign='autocomplete'
                 fill_options=''
          }
        {elseif $filters_array}
          {array assign='autocomplete'
                 filters=$filters_array
          }
        {/if}
      {/if}
      {* If this is a basic var
         then prepare some additional autocomplete settings *}
      {if $autocomplete_var_type eq 'basic'}
        {* Set the type of the var *}
        {array assign='autocomplete'
          var_type=$autocomplete_var_type
        }
      {/if}
    {else}
      {capture assign='visible_name'}{$name}{if $index}[{$index_array}]{/if}{/capture}
      {capture assign='visible_id'}{$custom_id|default:$name}{if $index}_{$index}{/if}{/capture}
      {capture assign='visible_value'}{if $value_autocomplete}{$value_autocomplete}{elseif $value}{$value}{/if}{/capture}
    {/if}

    {assign var='buttons_count'          value=0}
    {assign var='include_clear_button'   value=0}
    {assign var='include_add_button'     value=0}
    {assign var='include_search_button'  value=0}
    {assign var='include_refresh_button' value=0}
    {assign var='include_report_button'  value=0}
    {assign var='include_edit_button'    value=0}
    {if $autocomplete_var_type ne 'searchable' && !$readonly}
      {if (preg_match('/combobox/', $autocomplete.buttons) || $autocomplete.combobox) && !preg_match('/combobox/', $autocomplete.buttons_hide) && !preg_match('/combobox/', $autocomplete_buttons_hide)}
        {assign var='include_combobox_button' value=1}
      {/if}
      {if $available_action.action == 'filter' || $available_action.action == 'search' || (preg_match('/clear/', $autocomplete.buttons) || $autocomplete.clear) && !preg_match('/clear/', $autocomplete.buttons_hide) && !preg_match('/clear/', $autocomplete_buttons_hide)}
        {assign var='include_clear_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if ($available_action.action != 'filter' && $available_action.action != 'search') && (preg_match('/search/', $autocomplete.buttons) || !$autocomplete.buttons) && !preg_match('/search/', $autocomplete.buttons_hide) && !preg_match('/search/', $autocomplete_buttons_hide)}
        {assign var='include_search_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if (preg_match('/refresh/', $autocomplete.buttons) || $autocomplete.refresh) && !preg_match('/refresh/', $autocomplete.buttons_hide) && !preg_match('/refresh/', $autocomplete_buttons_hide)}
        {assign var='include_refresh_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if $autocomplete.report && !preg_match('/report/', $autocomplete.buttons_hide)}
        {assign var='include_report_button' value=1}
        {math equation="x + 1" x=$buttons_count assign='buttons_count'}
      {/if}
      {if ($available_action.action != 'filter' && $available_action.action != 'search') && ($autocomplete.type eq 'customers' && !(!empty($autocomplete.filters) && array_key_exists('<contactpersons>', $autocomplete.filters)) || $autocomplete.type eq 'projects' || $autocomplete.type eq 'nomenclatures' && !(!empty($autocomplete.filters) && array_key_exists('<customer_trademark>', $autocomplete.filters))) && (preg_match('/add/', $autocomplete.buttons) || $autocomplete.add) && !preg_match('/add/', $autocomplete.buttons_hide) && !preg_match('/add/', $autocomplete_buttons_hide)}
        {if $currentUser->checkRights($autocomplete.type, 'add')}
          {assign var='include_add_button' value=1}
        {/if}
        {if $include_add_button || $autocomplete_var_type eq 'basic'}
          {math equation="x + 1" x=$buttons_count assign='buttons_count'}
        {/if}
      {/if}
      {if ($available_action.action != 'filter' && $available_action.action != 'search') && ($autocomplete.type eq 'customers' && !(!empty($autocomplete.filters) && array_key_exists('<contactpersons>', $autocomplete.filters)) || $autocomplete.type eq 'nomenclatures' && !(!empty($autocomplete.filters) && array_key_exists('<customer_trademark>', $autocomplete.filters))) && (preg_match('/edit/', $autocomplete.buttons) || $autocomplete.edit) && !preg_match('/edit/', $autocomplete.buttons_hide) && !preg_match('/edit/', $autocomplete_buttons_hide)}
        {if $currentUser->checkRights($autocomplete.type, 'edit')}
          {assign var='include_edit_button' value=1}
        {/if}
        {if $include_edit_button || $autocomplete_var_type eq 'basic'}
          {math equation="x + 1" x=$buttons_count assign='buttons_count'}
        {/if}
      {/if}
    {/if}

    {if $autocomplete.button_menu && $buttons_count gt 0 && ($available_action.action != 'filter' && $available_action.action != 'search')}
      {assign var='button_menu' value=1}
      {assign var='buttons_count' value=1}
      {if $autocomplete_var_type eq 'basic' && $standalone && !($width && preg_match('#^(\d+%|)$#', $width)) && empty($basic_vars_additional_settings.$var_name.width)}
        {assign var='width' value=222}
      {/if}
    {else}
      {assign var='button_menu' value=0}
    {/if}

    {capture assign='width'}{strip}
      {if $standalone}
        {if preg_match('#^(\d+%|)$#', $width)}
          100%
        {else}
          {math equation="x - (y*z)" x=$width y=$buttons_count z=22}px
        {/if}
      {/if}
    {/strip}{/capture}

    {*
      Create unique ID for this autocompleter.
      The parameter more_entropy is set to true to increase the likelihood that the result will be unique,
      because there are cases (like using Windows OS for a web server) when two or more calls of uniqid()
      return same values, because they were executed at the same microsecond.
    *}
    {uniqid assign=uniqID more_entropy=true}
    {*
      The more_entropy param set to true makes the uniqid() function
      return an additional value, separated with a dot. Given that the uniqid is later used in JavaScript,
      the dot may result in some errors, so we remove it.
    *}
    {assign var='uniqID' value=$uniqID|replace:'.':''}

    <input
       type="text"
       class="txtbox autocompletebox{if !$readonly && !$hidden} autocomplete_{$autocomplete.type}{/if}{if $readonly} readonly{if $autocomplete.view_mode == 'link' && $autocomplete.view_mode_url} hidden{/if}{/if}{if $include_combobox_button} combobox{/if}{if $custom_class} {$custom_class}{/if}"
       name="{$visible_name}"
       id="{$visible_id}"
       value="{$visible_value|escape}"
       title="{$label|strip_tags:false|escape}"
       style="{if $hidden}display: none;{elseif ($width)}width: {$width};{/if}{if $height}height: {$height};{/if}"
       {if $show_placeholder}
         placeholder="{if $show_placeholder === 'label'}{$label|escape}{elseif $show_placeholder === 'help'}{$help|escape}{else}{$show_placeholder|escape}{/if}"
       {/if}
       {if $onkeydown || !empty($js_methods.onkeydown)}
         onkeydown="{if $onkeydown}{$onkeydown};{/if}{if !empty($js_methods.onkeydown)}{$js_methods.onkeydown};{/if}"
       {/if}
       {if $restrict}
         onkeypress="return changeKey(this, event, {$restrict});"
       {elseif $onkeypress || !empty($js_methods.onkeypress)}
         onkeypress="{if $onkeypress}{$onkeypress};{/if}{if !empty($js_methods.onkeypress)}{$js_methods.onkeypress};{/if}"
       {/if}
       {if $onkeyup || !empty($js_methods.onkeyup)}
         onkeyup="{if $onkeyup}{$onkeyup};{/if}{if !empty($js_methods.onkeyup)}{$js_methods.onkeyup};{/if}"
       {/if}
       onfocus="highlight(this);{if !empty($js_methods.onfocus)}{$js_methods.onfocus};{/if}"
       onblur="unhighlight(this);{if !empty($js_methods.onblur)}{$js_methods.onblur};{/if}{if !$readonly}cancelAutocompleter(params_{$uniqID});{/if}"
       onclick="{if $include_combobox_button}toggleAutocompleteItems(params_{$uniqID});{/if}{if !empty($js_methods.onclick)}{$js_methods.onclick};{/if}"
       oncontextmenu="return false;"
       ondrop="return false;"
       {foreach from=$js_methods key='method' item='func'}
         {if $func
         && $method
         && $method ne 'onkeydown'
         && $method ne 'onkeypress'
         && $method ne 'onkeyup'
         && $method ne 'onfocus'
         && $method ne 'onblur'
         && $method ne 'onclick'}
           {$method}="{$func}"
         {/if}
       {/foreach}
       {if $readonly} readonly="readonly"{/if}
       {if $disabled} disabled="disabled"{/if}
       uniqid="{$uniqID}"/>

    {if !$readonly}
      {if !$exclude_oldvalues}
        <input type="hidden"
               name="{$name}_oldvalue{if $index}_{$index}{/if}"
               id="{$custom_id|default:$name}_oldvalue{if $index}_{$index}{/if}"
               value="{$visible_value|escape}"
               disabled="disabled" />
      {/if}

      <div id="suggestions_{$uniqID}" class="autocompletebox" style="display: none;"></div>

      {* Encrypt the autocomplete sql parameter *}
      {*if !empty($autocomplete.sql)}
        {capture assign='autocomplete_sql'}{json encode=$autocomplete.sql}{/capture}
        {array assign='autocomplete'
          sql=$autocomplete_sql|encrypt:$smarty.const.AUTOCOMPLETE_SQL_ENCRYPT_SALT:'xtea'}
      {/if*}

      <script type="text/javascript">
        {array assign='autocomplete'
               uniqid=$uniqID
        }
        params_{$uniqID} = {json encode=$autocomplete|default:false};
        initAutocompleter(params_{$uniqID});
      </script>
    {elseif $autocomplete.view_mode == 'link' && $autocomplete.view_mode_url}
      {capture assign='ac_class'}{$uniqID} id_var-{if $autocomplete_var_type eq 'basic' || ($autocomplete_var_type eq 'searchable' && $autocomplete.search_by_id)}{$custom_id|default:$name}{else}{$autocomplete.id_var}{/if}{/capture}
      {include file="_view_autocompleter.html"
               value=$value_name|default:$visible_value
               value_id=$value_id
               view_mode_url=$autocomplete.view_mode_url
               ac_class=$ac_class
      }
    {/if}

    {if $autocomplete_var_type ne 'searchable' && !$readonly}
        {capture assign='autocomplete_type'}{if !empty($autocomplete.filters) && array_key_exists('<customer_trademark>', $autocomplete.filters)}trademarks{elseif !empty($autocomplete.filters) && array_key_exists('<contactpersons>', $autocomplete.filters)}contactpersons{else}{$autocomplete.type}{/if}{/capture}
        {capture assign='clear_param'}autocomplete_clear_{$autocomplete_type}{/capture}
        {capture assign='search_param'}autocomplete_search_{$autocomplete_type}{/capture}
        {capture assign='add_param'}autocomplete_add_{$autocomplete_type}{/capture}
        {capture assign='refresh_param'}autocomplete_refresh_{$autocomplete_type}{/capture}
        {capture assign='edit_param'}autocomplete_edit_{$autocomplete_type}{/capture}

        {if $include_combobox_button}
          <a href="javascript:void(0);"
             onclick="toggleAutocompleteItems(params_{$uniqID});">
            <img src="{$theme->imagesUrl}small/combobox.png"
                 class="combobox_button"
                 alt="" />
          </a>
        {/if}
      {if $button_menu}
    <div class="button_menu">
      <a href="javascript:void(0);"
         onmouseover="mopen('button_menu_{$uniqID}')"
         onmouseout="mclosetime()">
        <img src="{$theme->imagesUrl}small/more.png"
             class="icon_button"
             alt="..." />
      </a>
      <div id="button_menu_{$uniqID}" class="autocompletebox" style="visibility: hidden;" onmouseover="mcancelclosetime()" onmouseout="mclosetime()">
        <ul>
      {/if}
        {if $include_clear_button}
          {if $button_menu}<li>{/if}
          <a href="javascript:void(0);"
             onclick="clearAutocompleteItems(params_{$uniqID});"
             {help label_content=#autocomplete_clear_items# text_content=$smarty.config.$clear_param popup_only=1}>
            <img src="{$theme->imagesUrl}small/clear.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            {if $button_menu}{#autocomplete_clear_items#}{/if}
          </a>
          {if $button_menu}</li>{/if}
        {/if}
        {if $include_search_button}
          {if $button_menu}<li>{/if}
          <a href="javascript:void(0);"
             onclick="filterAutocompleteItems(params_{$uniqID});"
             {help label_content=#autocomplete_search_items# text_content=$smarty.config.$search_param popup_only=1}>
            <img src="{$theme->imagesUrl}small/{$autocomplete_type}.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            {if $button_menu}{#autocomplete_search_items#}{/if}
          </a>
          {if $button_menu}</li>{/if}
        {/if}
        {if $include_report_button}
          {if $button_menu}<li>{/if}
          <a href="javascript:void(0);"
             onclick="reportAutocompleteItems(params_{$uniqID});"
             {help label_content=#autocomplete_search_items# text_content=#autocomplete_search_report# popup_only=1}>
            <img src="{$theme->imagesUrl}small/reports.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            {if $button_menu}{#autocomplete_search_items#}{/if}
          </a>
          {if $button_menu}</li>{/if}
        {/if}
        {if $include_refresh_button}
          {if $button_menu}<li>{/if}
          <a href="javascript:void(0);"
             onclick="refreshAutocompleteItems(params_{$uniqID});"
             {help label_content=#autocomplete_refresh_items# text_content=$smarty.config.$refresh_param popup_only=1}>
            <img src="{$theme->imagesUrl}small/refresh.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            {if $button_menu}{#autocomplete_refresh_items#}{/if}
          </a>
          {if $button_menu}</li>{/if}
        {/if}
        {if $include_add_button}
          {if $button_menu}<li>{/if}
          <a href="javascript:void(0);"
             onclick="addAutocompleteItems(params_{$uniqID});"
             {help label_content=#add# text_content=$smarty.config.$add_param popup_only=1}>
            <img src="{$theme->imagesUrl}small/{$autocomplete.type}_add.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0" />
            {if $button_menu}{#add#}{/if}
          </a>
          {if $button_menu}</li>{/if}
        {/if}
        {if $include_edit_button}
          {if $button_menu}<li>{/if}
          <a href="javascript:void(0);"
             onclick="editAutocompleteItems(params_{$uniqID});"
             {help label_content=#edit# text_content=$smarty.config.$edit_param popup_only=1}>
            <img src="{$theme->imagesUrl}small/{$autocomplete.type}_edit.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0" />
            {if $button_menu}{#edit#}{/if}
          </a>
          {if $button_menu}</li>{/if}
        {/if}
      {if $button_menu}
        </ul>
      </div>
    </div>
      {/if}
    {/if}

    {* Back label *}
    {if !$back_label && $var.back_label}
      {assign var='back_label' value=$var.back_label}
    {/if}
    {if !$back_label_style && $var.back_label_style}
      {assign var='back_label_style' value=$var.back_label_style}
    {/if}
    {include file="_back_label.html"
      custom_id=$custom_id
      name=$name
      back_label=$back_label
      back_label_style=$back_label_style}
{if !$standalone}
  </td>
</tr>
{/if}
