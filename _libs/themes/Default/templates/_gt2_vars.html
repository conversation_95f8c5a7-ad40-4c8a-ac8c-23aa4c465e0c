<style type="text/css">
{foreach from=$styles item='style' key='class_name'}
{if !preg_match('#display\s*:\s*none#', $style)}
.{$class_name} {ldelim}
{$style|indent}
{rdelim}
{/if}
{/foreach}
</style>
{capture assign='table_id_var'}{if isset($table.meta_id)}meta_{/if}id{/capture}
{capture assign='table_class_name'}var_{$table.$table_id_var}{/capture}
{if count($table.values) && empty($hide_label) && !preg_match('#display\s*:\s*none#', $styles.$table_class_name)}
  <div class="{$table_class_name}">{$table.label|escape}</div>
{/if}
<table class="{$table_class_name}" cellpadding="5" cellspacing="0" border="1">
  <tr>
    {if empty($table.hide_row_numbers)}
    <th width="20" style="text-align:center!important; width:25px!important;">{#num#|escape}</th>
    {/if}
    {foreach name='i' key='key' from=$table.vars item='var'}
      {capture assign='class_name'}var_{$var.id}{/capture}
      {if !preg_match('#display\s*:\s*none#', $styles.$class_name)}
        <th>{$var.label|escape}</th>
      {/if}
    {/foreach}
  </tr>
  {counter assign='cols_count' print=false name='colcount' start=0}
  {assign var='agregates_count' value=0}
  {foreach name='i' from=$table.values item='val'}
    <tr>
      {if empty($table.hide_row_numbers)}
      <td style="text-align:right!important; width:25px!important;">{$smarty.foreach.i.iteration}</td>
      {/if}
      {foreach key='key' from=$table.vars item='var'}
        {capture assign='class_name'}var_{$var.id}{/capture}
        {if $smarty.foreach.i.first && !preg_match('#display\s*:\s*none#', $styles.$class_name)}
          {counter assign='cols_count' print=false name='colcount'}
        {/if}
        {if $smarty.foreach.i.first && !empty($var.agregate) && !preg_match('#display\s*:\s*none#', $styles.$class_name)}
          {counter assign='agregates_count' print=false name='agregatescount'}
        {/if}
        {if !preg_match('#display\s*:\s*none#', $styles.$class_name)}
        <td class="{$class_name}">{strip}
          {if $var.type eq 'dropdown' || $var.type eq 'radio'}
            {if !empty($var.overwrite_value)}
              {$val[$key]|default:'&nbsp;'}
            {elseif $var.options}
              {foreach from=$var.options item='option'}
                {if $option.option_value eq $val[$key]}
                  {if preg_match("#^<#", $option.extended_value) || preg_match("#^<#", $option.label)}
                    {$option.extended_value|default:$option.label|default:'&nbsp;'}
                  {else}
                    {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}
                  {/if}
                {/if}
              {/foreach}
              {if !$val[$key]}
                &nbsp;
              {/if}
            {elseif $var.optgroups}
              {foreach from=$var.optgroups key='optgroup_name' item='optgroup'}
                {foreach from=$optgroup item='option'}
                  {if $option.option_value eq $val[$key]}
                    {if preg_match("#^<#", $option.extended_value) || preg_match("#^<#", $option.label)}
                      {$option.extended_value|default:$option.label|default:'&nbsp;'}
                    {else}
                      {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}
                    {/if}
                  {/if}
                {/foreach}
              {/foreach}
            {/if}
          {elseif $var.type eq 'date'}
            {$val.$key|date_format:#date_short#|default:'&nbsp;'}
          {elseif $var.type eq 'datetime'}
            {$val.$key|date_format:#date_mid#|default:'&nbsp;'}
          {elseif $var.type eq 'time'}
            {$val.$key|date_format:#time_short#|default:'&nbsp;'}
          {else}
            {$val.$key|default:'&nbsp;'|nl2br}
          {/if}
          {if ($val.$key || $val.$key === '0') && $var.back_label}&nbsp;{$var.back_label}{/if}
        {/strip}</td>
        {/if}
      {/foreach}
    </tr>
  {* extension for handovers' rows where we have batch articles *}
  {if !empty($val.has_batch)}{include file="_gt2_vars_batch_data.html" idx=$smarty.foreach.i.iteration}{/if}
  {/foreach}
  {if $agregates_count > 0}
    <tr class="gt2_agregates">
      {if empty($table.hide_row_numbers)}
      <td>&nbsp;</td>
      {/if}
      {foreach name='j' key='key' from=$table.vars item='var'}
        {capture assign='class_name'}var_{$var.id}{/capture}
        {if !preg_match('#display\s*:\s*none#', $styles.$class_name)}
        <td class="{$class_name}">
          {strip}
            {if !empty($var.agregate)}
              {capture assign='ag_text'}gt2_tagregates_{$var.agregate}{/capture}
              {$smarty.config.$ag_text}:&nbsp;{$table.agregate_results[$var.name]}
            {else}
              &nbsp;
            {/if}
          {/strip}
        </td>
        {/if}
      {/foreach}
    </tr>
  {/if}
  {if $table.totals_texts_colspan}
    {assign var='totals_texts_colspan' value=$table.totals_texts_colspan}
  {else}
    {assign var='totals_texts_colspan' value=3}
  {/if}
  {assign var='totals_colspan' value=$cols_count-$totals_texts_colspan}
  {capture assign='hide_totals'}{if !empty($table.hide_totals)}1{else}0{/if}{/capture}

  {assign var='var' value=$table.plain_vars.total_without_discount}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>&nbsp;</td>
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_without_discount|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_discount_percentage}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>&nbsp;</td>
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_discount_percentage|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_discount_value}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>&nbsp;</td>
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_discount_value|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_surplus_percentage}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>&nbsp;</td>
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_surplus_percentage|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_surplus_value}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>&nbsp;</td>
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_surplus_value|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {if $table.plain_vars.total_no_vat_reason_text && !$table.plain_vars.total_no_vat_reason_text.hidden && $table.plain_values.total_no_vat_reason_text && !preg_match('#display\s*:\s*none#', $styles.$class_name_total_nvrt)}
    {assign var='display_no_vat_rate_text' value=1}
  {else}
    {assign var='display_no_vat_rate_text' value=0}
  {/if}
  {capture assign='show_totals_inwords'}{if !empty($table.show_totals_inwords)}1{else}0{/if}{/capture}

  {assign var='var' value=$table.plain_vars.total}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}{if !$show_totals_inwords && !$display_no_vat_rate_text} rowspan="5"{/if}>&nbsp;</td>
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_vat_rate}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      {if $show_totals_inwords || $display_no_vat_rate_text}
      {capture assign='class_name_total_nvrt'}var_{$table.plain_vars.total_no_vat_reason_text.id}{/capture}
      <td class="{$class_name_total_nvrt}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>
        {if $display_no_vat_rate_text}
          {$table.plain_vars.total_no_vat_reason_text.label|escape}:
          {$table.plain_values.total_no_vat_reason_text|escape|default:"&nbsp;"}{if $table.plain_vars.total_no_vat_reason_text.back_label} {$table.plain_vars.total_no_vat_reason_text.back_label}{/if}
        {else}
          &nbsp;
        {/if}
      </td>
      {/if}
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_vat_rate} %{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_vat}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      {if $show_totals_inwords || !$show_totals_inwords && $display_no_vat_rate_text}
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}{if !$show_totals_inwords} rowspan="3"{/if}>
        {if $show_totals_inwords}{#in_words#|mb_ucfirst}: {$table.plain_values.total_vat|inwords:$table.plain_values.currency:$lang}{/if}
      </td>
      {/if}
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_vat|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.total_with_vat}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      {if $show_totals_inwords}
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>
        {#in_words#|mb_ucfirst}: {$table.plain_values.total_with_vat|inwords:$table.plain_values.currency:$lang}
      </td>
      {/if}
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.total_with_vat|default:0}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}

  {assign var='var' value=$table.plain_vars.currency}
  {capture assign='class_name_total'}var_{$var.id}{/capture}
  {if $var && !$hide_totals && !$var.hidden && !preg_match('#display\s*:\s*none#', $styles.$class_name_total)}
    <tr>
      {if $show_totals_inwords}
      <td class="{$class_name_total}"{if $totals_colspan} colspan="{$totals_colspan}"{/if}>&nbsp;</td>
      {/if}
      <td class="{$class_name_total}"{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} align="right">{$var.label|escape}</td>
      <td class="{$class_name_total}">
        {$table.plain_values.currency}{if $var.back_label} {$var.back_label}{/if}
      </td>
    </tr>
  {/if}
</table>
