{if !$hide_side_panel}
<table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
  <tr>
    <td class="labelbox" style="width: 180px! important">
      {help label_content=#customers_name#}
    </td>
    <td class="required" style="width: 10px! important">&nbsp;</td>
    <td>
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$customers_info->get('id')}">{$customers_info->get('name')|escape}{if $customers_info->get('lastname')} {$customers_info->get('lastname')|escape}{/if} ({$customers_info->get('type_name')|escape})</a>
    </td>
  </tr>
  {if $customers_info->get('is_company')}
  <tr>
    <td class="labelbox">
      {capture assign='branch'}{$module}_branch{/capture}
      {help label_content=$customers_info->getBranchLabels($branch)}
    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_branch_name">
        {$customers_info->get('main_branch_name')|escape}
      </span>
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      {capture assign='branch_address'}{$module}_branch_address{/capture}
      {help label_content=$customers_info->getBranchLabels($branch_address)}
    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_branch_address">
        {$customers_info->get('branch_address')|nl2br}
      </span>
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      {capture assign='main_contact_person'}{$module}_main_contact_person{/capture}
      {help label_content=$customers_info->getBranchLabels($main_contact_person)|escape}
    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_contact_person">
        {$customers_info->get('contact_person_name')|escape}
      </span>
    </td>
  </tr>
  {/if}
  <tr>
    <td class="labelbox">
      {capture assign='customers_contacts'}{$module}_customers_contacts{/capture}
      {help label_content=$smarty.config.$customers_contacts}
    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_contacts">
        {include file=`$theme->templatesDir`_customers_contacts_data.html}
      </span>
    </td>
  </tr>
</table>
{/if}