{if empty($table)}
  {assign var='table' value=$model->get('grouping_table_2')}
{/if}
{if !$table.no_container}
<div id="gt2_container">
{/if}
{if trim($table.label) ne '' && !$hide_label}
<div class="t_caption2_title" style="float: left">{$table.label}</div>
<br clear="all" />
{/if}
{if !empty($table.configurator_group)}
  {include file="_gt2_configs.html" configs=$table.configs}
{/if}
{if trim($table.javascript)}
<script type="text/javascript">
    {$table.javascript}
</script>
{/if}
{assign var='allow_negative_price_explicitly' value=$table.allow_negative_price|default:false}
<input type="hidden" name="calc_price" id="calc_price" value="{$table.calculated_price}"{if $allow_negative_price_explicitly} class="allowNegativePrice"{/if} />
<table id="var_group_{$table.grouping}" class="t_grouping_table grouping_table2 gt2edit{if $table.custom_class} {$table.custom_class}{/if}" style="{if $table.width}width:{$table.width}{if preg_match('#^\d+$#', $table.width)}px{/if};{/if}">
<tr>
  <th width="20" style="text-align:right">{#num#|escape}</th>
  {assign var='table_values_count' value=0}
  {if !empty($table.values)}{assign var='table_values_count' value=$table.values|@count}{/if}
  {assign var='table_rows_readonly_count' value=0}
  {if !empty($table.rows_readonly)}{assign var='table_rows_readonly_count' value=$table.rows_readonly|@count}{/if}
  {math assign='values_count' equation='a-b' a=$table_values_count b=$table_rows_readonly_count}
  {foreach from=$table.vars item='var' key='key'}
    {capture assign='info'}{if $var.help}{$var.help}{else}{$var.label}{/if}{/capture}
    <th{if $var.hidden} style="display: none;"{/if}{if $var.cell_class} class="{$var.cell_class}"{/if}>
      {if $table.last_visible_column eq $key}
        <div{if $var.width} style=" width:{$var.width}px;"{/if}>
          <div class="floatl">
            <a name="error_{$var.name}"></a>
            <label for="{$var.name}_1"{if $messages->getErrors($var.name)} class="error"{/if}>{help label_content=$var.label text_content=$info label_sufix=''}{if $var.required}{#required#}{/if}</label>
          </div>
          {include file="_table_buttons.html"
                  var=$table
                  values_count=$values_count
          }
          {if !empty($table.floating_buttons)}
            {include file="_table_buttons.html"
                    var=$table
                    values_count=$values_count
                    floating_buttons=true
            }
          {/if}
        </div>
      {else}
        <div style="{if $var.hidden} display: none;{elseif $var.width} width:{$var.width}px;{/if}">
          <a name="error_{$var.name}"></a><label for="{$var.name}_1"{if $messages->getErrors($var.name)} class="error"{/if}>{help label_content=$var.label text_content=$info label_sufix=''}{if $var.required}{#required#}{/if}</label>
        </div>
      {/if}
    </th>
  {/foreach}
</tr>
{counter assign='cols_count' print=false name='colcount' start=0}
{foreach from=$table.values item='val' key='row' name='i'}
{if empty($table.rows) || (!empty($table.rows) && !in_array($row, $table.rows))}
  {*special(negative) index for new GT2 rows*}
  {capture assign=row_index}-{$smarty.foreach.i.iteration}{/capture}
{else}
  {assign var=row_index value=$row}
{/if}
{if $table.last_editable_row_index === $smarty.foreach.i.iteration-1}
<tr id="gt2_delimeter{if $table.delimeter_start}_start{/if}" style="display:none"><td colspan="50" style="height: 0px;padding: 0"></td></tr>
{/if}
<tr id="var_group_{$table.grouping}_{$smarty.foreach.i.iteration}"{if $val.deleted eq 1} style="display: none"{/if} class="{if $val.deleted eq 1} input_inactive{/if}{if $table.rows_readonly && @in_array($smarty.foreach.i.iteration, $table.rows_readonly)} readonly{if $table.delimeter_start}_start{/if}{if $table.allow_readonly_delete} allow_delete{/if}{/if}"{if $table.floating_buttons} onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"{/if}>
  <td style="text-align:right" nowrap="nowrap">
  {if !$table.hide_delete && (!@in_array($smarty.foreach.i.iteration, $table.rows_readonly) || $table.allow_readonly_delete) && !@in_array($smarty.foreach.i.iteration, $table.rows_readonly_force)}
    <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if $values_count le 1 && !@in_array($smarty.foreach.i.iteration, $table.rows_readonly) && (!$table.allow_readonly_delete || $smarty.foreach.i.iteration eq 1 || (($smarty.foreach.i.iteration)-($table_rows_readonly_count) le 1))} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('var_group_{$table.grouping}','{$smarty.foreach.i.iteration}'); {rdelim}, this);" />&nbsp;<a href="javascript: disableField('var_group_{$table.grouping}','{$smarty.foreach.i.iteration}')">{$smarty.foreach.i.iteration}</a>
  {else}
    {$smarty.foreach.i.iteration}
  {/if}
  </td>
  <td style="display:none">
    <input type="hidden" id="deleted_{$smarty.foreach.i.iteration}" name="deleted[{if $empty_indexes ne 1}{$row_index}{/if}]" class="deleted_flag" value="{$val.deleted}" />
  </td>
  {foreach from=$table.vars item='var' key='key' name='j'}
      {if $smarty.foreach.i.first && (!$var.hidden || $var.hidden === '0' || $var.hidden === 0)}
        {counter assign='cols_count' print=false name='colcount'}
      {/if}
      {if $smarty.foreach.i.first && !empty($var.agregate)}
        {counter assign='agregates_count' print=false name='agregatescount'}
      {/if}
      <td style="{if $var.width && !$var.back_label}max-width:{$var.width}{if preg_match('#^\d+$#', $var.width)}px{/if};{/if}{if $var.hidden}display: none;{/if}" class="{if $var.cell_class}{$var.cell_class}{/if}">
      {capture assign="var_width"}{if $var.width && ($var.type eq 'autocompleter' || $var.type eq 'formula' || $var.type eq 'index' || $var.back_label)}{$var.width}{else}100%{/if}{/capture}
      {if $table.rows_readonly && (@in_array($smarty.foreach.i.iteration, $table.rows_readonly) && @!in_array($var.name, $table.fields_not_readonly) || @in_array($smarty.foreach.i.iteration, $table.rows_readonly_force)) || preg_match('#^(warehouse\d+_)?quantity$#', $var.name) && $val.has_batch}
        {assign var=var_readonly value=1}
      {else}
        {assign var=var_readonly value=$var.readonly}
      {/if}
      {if $var.name eq 'article_id'}
        {if is_array($table.system_articles) && array_key_exists($val.article_id, $table.system_articles)}
          {assign var=article_id value=$val.article_id}
          {if $table.system_articles.$article_id eq 'advance'}
            {capture assign=var_custom_class}{$var.custom_class|default:$var.name} allowNegativePrice{/capture}
          {elseif $table.system_articles.$article_id eq 'discount'}
            {if !preg_match('#^Finance_#', $table.model) || $table.model_type ne PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE}
              {capture assign=var_custom_class}{$var.custom_class|default:$var.name} onlyNegativePrice{/capture}
            {/if}
          {elseif preg_match('#^Finance_#', $table.model) && $table.model_type eq PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE}
            {if $table.system_articles.$article_id eq 'surplus'}
              {capture assign=var_custom_class}{$var.custom_class|default:$var.name} onlyNegativePrice{/capture}
            {else}
              {capture assign=var_custom_class}{$var.custom_class|default:$var.name} onlyNegativePrice onlyNegativeQuantity{/capture}
            {/if}
          {else}
            {assign var=var_custom_class value=$var.custom_class|default:$var.name}
          {/if}
        {elseif preg_match('#^Finance_#', $table.model) && ($table.model_type eq PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $printform && $table.model_type eq PH_FINANCE_TYPE_CREDIT_NOTICE)}
          {capture assign=var_custom_class}{$var.custom_class|default:$var.name} onlyNegativePrice onlyNegativeQuantity{/capture}
        {elseif $allow_negative_price_explicitly}
          {capture assign=var_custom_class}{$var.custom_class|default:$var.name} allowNegativePrice{/capture}
        {else}
          {assign var=var_custom_class value=$var.custom_class|default:$var.name}
        {/if}
      {elseif $var.name eq $table.calculated_price}
        {capture assign=var_custom_class}{$var.custom_class|default:$var.name}{if !empty($table.rows_readonly_force) && in_array($smarty.foreach.i.iteration, $table.rows_readonly_force)} system_readonly{/if}{/capture}
        {if is_array($table.system_articles) && array_key_exists($val.article_id, $table.system_articles)}
          {assign var=article_id value=$val.article_id}
          {if $table.system_articles.$article_id eq 'advance'}
            {assign var=text_restrict value=insertOnlyReals}
          {elseif $table.system_articles.$article_id eq 'discount'}
            {if !preg_match('#^Finance_#', $table.model) || $table.model_type ne PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE}
              {assign var=text_restrict value=insertOnlyReals}
            {/if}
          {elseif preg_match('#^Finance_#', $table.model) && $table.model_type eq PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE}
            {assign var=text_restrict value=insertOnlyReals}
          {else}
            {assign var=text_restrict value=insertOnlyFloats}
          {/if}
        {elseif preg_match('#^Finance_#', $table.model) && $table.model_type eq PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE}
          {assign var=text_restrict value=insertOnlyReals}
        {elseif $allow_negative_price_explicitly}
          {assign var=text_restrict value=insertOnlyReals}
        {else}
          {assign var=text_restrict value=insertOnlyFloats}
        {/if}
      {elseif $var.name eq 'quantity'}
        {if preg_match('#^Finance_#', $table.model) && ($table.model_type eq PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE || $printform && $table.model_type eq PH_FINANCE_TYPE_CREDIT_NOTICE)}
          {assign var=text_restrict value=insertOnlyReals}
        {else}
          {assign var=text_restrict value=insertOnlyFloats}
        {/if}
        {if is_array($table.system_articles) && array_key_exists($val.article_id, $table.system_articles)}
          {assign var=var_readonly value=true}
          {capture assign=var_custom_class}{$var.custom_class|default:$var.name} system_readonly{/capture}
        {else}
          {assign var=var_custom_class value=$var.custom_class|default:$var.name}
        {/if}
      {else}
        {capture assign=var_custom_class}{$var.custom_class|default:$var.name}{if preg_match('/^(surplus|discount)/', $var.name) && !empty($table.rows_readonly_force) && in_array($smarty.foreach.i.iteration, $table.rows_readonly_force)} system_readonly{/if}{/capture}
        {assign var=text_restrict value=$var.js_filter}
      {/if}
      {if $var.type eq 'text'}
        {include file='input_text.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          js_methods=$var.js_methods
          restrict=$text_restrict
          text_align=$var.text_align
          width=$var_width
          height=$var.height
          show_placeholder=$var.show_placeholder
          origin='group'}
      {elseif $var.type eq 'formula' && ($var.formula_type eq 'text' || $var.formula_type eq 'index')}
        {capture assign=key_formula}{$key}_formula{/capture}
        {include file='input_formula.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          formula_value=$val.$key_formula
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          js_methods=$var.js_methods
          restrict=$text_restrict
          text_align=$var.text_align
          width=$var_width
          source=$var.formula_type
          height=$var.height
          origin='group'}
      {elseif $var.type eq 'index'}
        {capture assign=key_formula}{$key}_formula{/capture}
        {capture assign=key_date}{$key}_date{/capture}
        {include file='input_index.html'
         standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          formula_value=$val.$key_formula
          date_value=$val.$key_date
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          indexes=$indexes
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          js_methods=$var.js_methods
          restrict=$text_restrict
          text_align=$var.text_align
          width=$var_width
          height=$var.height
          origin='group'}
      {elseif $var.type eq 'textarea'}
        {include file='input_textarea.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          width=$var_width
          height=$var.height
          show_placeholder=$var.show_placeholder
          origin='group'}
      {elseif $var.type eq 'dropdown'}
        {include file='input_dropdown.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          js_methods=$var.js_methods
          text_align=$var.text_align
          width=$var_width
          height=$var.height
          options=$var.options
          optgroups=$var.optgroups
          required=$var.required
          origin='group'}
      {elseif $var.type eq 'radio'}
        {include file='input_radio.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          on_change=$var.on_change
          width=$var_width
          height=$var.height
          options=$var.options
          options_align=$var.options_align
          required=$var.required
          origin='group'}
      {elseif $var.type eq 'autocompleter'}
        {include file='input_autocompleter.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          value_id=$val[$var.autocomplete.id_var]
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          width=$var_width
          height=$var.height
          autocomplete=$var.autocomplete
          exclude_oldvalues=$var.exclude_oldvalues
          show_placeholder=$var.show_placeholder
        }
      {elseif $var.type eq 'date'}
        {include file='input_date.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          width=$var_width
          height=$var.height
          origin='group'
          js_methods=$var.js_methods}
      {elseif $var.type eq 'datetime'}
        {include file='input_datetime.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          width=$var_width
          height=$var.height
          origin='group'
          js_methods=$var.js_methods}
      {elseif $var.type eq 'time'}
        {include file='input_time.html'
          standalone=true
          name=$var.name
          custom_class=$var_custom_class
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          width=$var_width
          height=$var.height
          origin='group'
          js_methods=$var.js_methods}
      {elseif $var.type eq 'file_upload'}
        {capture assign='deleteid_key'}deleteid_{$key}{/capture}
        {include file=input_file_upload.html
          var=$var
          standalone=true
          source='gt2'
          name=$var.name
          index=$smarty.foreach.i.iteration
          name_index=$row_index
          value=$val.$key
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          readonly=$var_readonly
          hidden=$var.hidden
          disabled=$var.disabled
          width=$var_width
          height=$var.height
          origin='group'
          view_mode=$var.view_mode
          thumb_width=$var.thumb_width
          thumb_height=$var.thumb_height
          deleteid=$val.$deleteid_key
        }
      {/if}
      </td>
  {/foreach}
</tr>
{* extension for handovers' rows where we have batch articles *}
{if $val.inactive}<script type="text/javascript">disableField('var_group_{$table.grouping}','{$smarty.foreach.i.iteration}');</script>{/if}
{if $val.has_batch && !$val.deleted}{include file="_gt2_batch_extention.html" idx=$smarty.foreach.i.iteration row_index=$row_index}{/if}
{/foreach}
{if !isset($table.last_editable_row_index) || $table.delimeter_start}
<tr id="gt2_delimeter" style="display:none"><td></td></tr>
{/if}
{if !$table.hide_agregates}
  {if $agregates_count > 0}
    <tr class="gt2_agregates">
      <td>&nbsp;</td>
      <td style="display:none"></td>
    {foreach name='j' key='key' from=$table.vars item='var'}
      <td style="{if $var.hidden}display: none;{elseif $var.text_align} text-align:{$var.text_align}{/if}">{strip}
        {if $var.agregate}
            {capture assign='ag_name'}{$var.name}_agregate{/capture}
            {capture assign='ag_class}{if $var.agregate}agregate{/if}{/capture}
            {include file=`$theme->templatesDir`input_text.html
                     hidden=1
                     custom_class='agregate'
                     name=$ag_name
                     value=$var.agregate
                     standalone=true
                     width=$var.width
            }
            {capture assign='ag_text'}gt2_tagregates_{$var.agregate}{/capture}
            {$smarty.config.$ag_text}: <span class="{$var.name}_ag_content"></span>
        {else}
        &nbsp;
        {/if}
      {/strip}</td>
    {/foreach}
    </tr>
  {/if}
{/if}
{if $table.totals_texts_colspan}
{assign var='totals_texts_colspan' value=$table.totals_texts_colspan}
{else}
{assign var='totals_texts_colspan' value=3}
{/if}
{assign var='totals_colspan' value=$cols_count-$totals_texts_colspan}

{if $table.plain_vars.total_without_discount}
{assign var='var' value=$table.plain_vars.total_without_discount}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total_without_discount
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_without_discount|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          width=$var_width
          readonly=1
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_discount_percentage}
{assign var='var' value=$table.plain_vars.total_discount_percentage}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total_discount_percentage
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_discount_percentage|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          readonly=$var.readonly
          js_methods=$var.js_methods
          restrict=$var.js_filter
          width=$var_width
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_discount_value}
{assign var='var' value=$table.plain_vars.total_discount_value}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total_discount_value
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_discount_value|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          readonly=$var.readonly
          js_methods=$var.js_methods
          restrict=$var.js_filter
          width=$var_width
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_surplus_percentage}
{assign var='var' value=$table.plain_vars.total_surplus_percentage}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total_surplus_percentage
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_surplus_percentage|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          readonly=$var.readonly
          js_methods=$var.js_methods
          restrict=$var.js_filter
          width=$var_width
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_surplus_value}
{assign var='var' value=$table.plain_vars.total_surplus_value}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total_surplus_value
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_surplus_value|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          readonly=$var.readonly
          js_methods=$var.js_methods
          restrict=$var.js_filter
          width=$var_width
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_discount_surplus_field}
{assign var='var' value=$table.plain_vars.total_discount_surplus_field}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total_discount_surplus_field
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_discount_surplus_field|default:'none'
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          readonly=$var.readonly
          js_methods=$var.js_methods
          restrict=$var.js_filter
          width=$var_width
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total}
{assign var='var' value=$table.plain_vars.total}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file=input_text.html
          standalone=true
          name=total
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          width=$var_width
          readonly=1
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_vat_rate}
{assign var='var' value=$table.plain_vars.total_vat_rate}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">
  {if $table.plain_vars.total_no_vat_reason && $table.plain_vars.total_no_vat_reason_text}
    <div{if $table.plain_vars.total_no_vat_reason_text.hidden} style="display: none;"{/if}>
    <label for="total_no_vat_reason_text" class="vtop">{$table.plain_vars.total_no_vat_reason_text.label}:</label>
    {include file='input_autocompleter.html'
          standalone=true
          name=total_no_vat_reason_text
          custom_class=$table.plain_vars.total_no_vat_reason_text.custom_class|default:$table.plain_vars.total_no_vat_reason_text.name
          index=0
          value=$table.plain_values.total_no_vat_reason_text
          value_id=$table.plain_values.total_no_vat_reason
          autocomplete=$table.plain_vars.total_no_vat_reason_text.autocomplete
          exclude_oldvalues=$table.plain_vars.total_no_vat_reason_text.exclude_oldvalues
          required=0
          disabled=0
          text_align=$table.plain_vars.total_no_vat_reason_text.text_align
          label=$table.plain_vars.total_no_vat_reason_text.label
          help=$table.plain_vars.total_no_vat_reason_text.help
          back_label=$table.plain_vars.total_no_vat_reason_text.back_label
          back_label_style=$table.plain_vars.total_no_vat_reason_text.back_label_style
          show_placeholder=$table.plain_vars.total_no_vat_reason_text.show_placeholder
          width=200
          readonly=$table.plain_vars.total_no_vat_reason_text.readonly
          hidden=0}
      {include file="input_hidden.html"
          standalone=true
          name=total_no_vat_reason
          custom_class=$table.plain_vars.total_no_vat_reason.custom_class|default:$table.plain_vars.total_no_vat_reason.name
          index=0
          value=$table.plain_values.total_no_vat_reason
          required=0}
    </div>
  {/if}
  </td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file="input_`$var.type`.html"
          standalone=true
          name=total_vat_rate
          custom_class=$var.custom_class|default:$var.name
          index=0
          options=$var.options
          optgroups=$var.optgroups
          value=$table.plain_values.total_vat_rate|default:$table.default_VAT
          required=1
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          width=$var_width
          sequences='gt2calc(this)'
          onkeyup='gt2calc(this)'
          restrict='insertOnlyFloats'
          readonly=$var.readonly
          disabled=0
          hidden=$var.hidden}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_vat}
{assign var='var' value=$table.plain_vars.total_vat}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file='input_text.html'
          standalone=true
          name=total_vat
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_vat|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          width=$var_width
          readonly=1
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.total_with_vat}
{assign var='var' value=$table.plain_vars.total_with_vat}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none"></td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file='input_text.html'
          standalone=true
          name=total_with_vat
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.total_with_vat|default:0
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          show_placeholder=$var.show_placeholder
          width=$var_width
          readonly=1
          hidden=0}
  </td>
</tr>
{/if}

{if $table.plain_vars.currency}
{assign var='var' value=$table.plain_vars.currency}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none"></td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td>
    {capture assign="var_width"}{if $var.width && $var.back_label}{$var.width}{else}100%{/if}{/capture}
    {include file='input_dropdown.html'
          standalone=true
          name=currency
          custom_class=$var.custom_class|default:$var.name
          index=0
          value=$table.plain_values.currency|default:$var.options.0.option_value
          text_align=$var.text_align
          label=$var.label
          help=$var.help
          back_label=$var.back_label
          back_label_style=$var.back_label_style
          width=$var_width
          options=$var.options
          optgroups=$var.optgroups
          required=1
          readonly=$var.readonly
          onchange=changeGT2Currency(this)
          hidden=$var.hidden}
    <input type="hidden" id="old_currency" value="{$table.plain_values.currency|default:$var.options.0.option_value}" />
  </td>
</tr>
{/if}
{if $table.extend}
  {capture assign=filename}{$templatesDir}{$table.extend}{/capture}
  {if !file_exists($filename)}
    {capture assign=filename}{$theme->templatesDir}{$table.extend}{/capture}
  {/if}
  {if file_exists($filename)}
    {include file=$filename}
  {/if}
{/if}
</table>
<input type="hidden" name="gt2_requested" id="gt2_requested" value="1" />
<script type="text/javascript">
    gt2calc('total'{if $table.bb}, 'var_group_{$table.grouping}'{/if});
    gt2calc('agregates'{if $table.bb}, 'var_group_{$table.grouping}'{/if});
    $$('.warehouse_quantity').each(function(field){ldelim}
      if (field.onkeyup) field.onkeyup();
    {rdelim});
</script>
{if !$table.no_container}
</div>
{/if}