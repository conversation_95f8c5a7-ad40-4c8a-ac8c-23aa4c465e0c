{strip}
{assign var='max_shown' value=50}
{assign var='wrap_at' value=200}
{assign var='items_count' value=$items|@count}
{capture assign='message_type'}{$display}s{/capture}
{/strip}
{if $items}
  <div class="message_container">
  {if is_array($items)}
      <ul>
    {counter assign='msg_items' print=false start=0}
    {foreach name='i' from=$items item='msg' key='key'}
      {counter assign='msg_items' print=false}
      {capture assign='msg'}{$msg}{/capture}
          <li class="{$display}">{if !is_numeric($key)}<a href="#error_{$key|regex_replace:'|#*$|':''}">{$msg}</a>{else}{$msg}{/if}</li>
      {if $msg_items eq $max_shown}
          <span id="{$display}_container" style="display: none;">
      {/if}
      {if $msg_items gt $max_shown}
        {if $smarty.foreach.i.last}
            </span>
            <li id="liToggleAll" class="{$display} pointer" onclick="toggleMessages('{$display}', {$items|@count}, {$max_shown})">({#messages_expand_items#|replace:'%d':$items_count} {$smarty.config.$message_type})</li>
            <li id="liToggleMax" style="display: none;" class="{$display} pointer" onclick="toggleMessages('{$display}', {$items|@count}, {$max_shown})">({#messages_collapse_items#|replace:'%d':$max_shown} {$smarty.config.$message_type})</li>
        {/if}
      {/if}
    {/foreach}
      </ul>
  {else}
    <ul>
      <li class="{$display}">{$items}</li>
    </ul>
  {/if}
  </div>
{/if}
