{if $module eq 'customers'}
  {assign var='add_bb_vars' value=$customer->get('add_bb_vars')}
  {assign var='tmp_id' value=$customer->get('id')}
  {assign var='temp_bb_vars' value=$customer->get('bb_vars')}
{elseif $module eq 'nomenclatures'}
    {assign var='add_bb_vars' value=$nomenclature->get('add_bb_vars')}
    {assign var='tmp_id' value=$nomenclature->get('id')}
    {assign var='temp_bb_vars' value=$nomenclature->get('bb_vars')}
{else}
  {assign var='add_bb_vars' value=$document->get('add_bb_vars')}
  {assign var='tmp_id' value=$document->get('id')}
  {assign var='temp_bb_vars' value=$document->get('bb_vars')}
{/if}
<tr>
  <td colspan="3">
    <div style="float: right;">
      <span class="pointer" onclick="toggleTableRowsAll('bb_table', 'expand')">{#expand_all#|escape}</span> |
      <span class="pointer" onclick="toggleTableRowsAll('bb_table', 'collapse')">{#collapse_all#|escape}</span>
    </div>
    <br />
    <table class="t_grouping_table" width="{$var.t_width|default:'100%'}" id="bb_table">
      <tr>
    {counter assign='add_bb_vars_count' start=1}
    {if $transform_bb}{counter assign='add_bb_vars_count'}
      <th width="10"><input checked="checked" onclick="toggleCheckboxes(this, 'bb_ids', this.checked)" type="checkbox" name="checkall_bb" title="{#check_to_include_all#|escape}" /></th>
    {/if}
      <th>{#num#|escape}</th>
      {foreach name='abbh' from=$add_bb_vars item='add_bb_var' key='add_bb_var_name'}
        {if $add_bb_var_name ne 'bb_group'}
        <th{if $add_bb_var.hidden} style="display: none;"{elseif $add_bb_var.width} width="{$add_bb_var.width}"{/if}>{if !$add_bb_var.hidden}{counter assign='add_bb_vars_count'}<a name="error_{$add_bb_var_name}"></a><label for="{$add_bb_var_name}"{if $messages->getErrors(add_bb_var_name)} class="error"{/if}>{help label_content=$add_bb_var.label text_content=$add_bb_var.help}</label>{/if}</th>
        {/if}
      {/foreach}
    </tr>

    {foreach name='jj' from=$temp_bb_vars item='bb_var'}
      {capture assign=bb_change}{$tmp_id},'{$module}',{$bb_var.id},1,{$bb_var.meta_id}{/capture}
      {assign var=bb_id value=$bb_var.id}
      {assign var='layout_cookie_var' value="row_`$bb_id`_box"}
      <tr id="bb_row_{$bb_var.id}">
      {if $transform_bb}
        <td><input type="checkbox" name="bb_ids[]" value="{$bb_var.id}" checked="checked" title="{#check_to_include#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" /></td>
      {/if}
        <td nowrap="nowrap" class="pointer" onclick="toggleTableRow($('bb_row_{$bb_var.id}'))" width="25"><div class="switch_{if $smarty.cookies.$layout_cookie_var eq 'off' || $var.toggle eq 'collapse' ||  $var.toggle eq 'first' && !$smarty.foreach.jj.first}expand{else}collapse{/if}"></div> {$smarty.foreach.jj.iteration}</td>
        {foreach name='abb' from=$add_bb_vars item='add_bb_var' key='add_bb_var_name'}
          {if $add_bb_var_name ne 'bb_group'}
          <td style="{if $add_bb_var.hidden}display: none;{/if}{if $add_bb_var.text_align}text-align: {$add_bb_var.text_align};{/if}">
            {if $add_bb_var_name eq 'bb_elements'}
              <strong>{$bb_var.label}</strong>
            {else}
              {if $add_bb_var.type eq 'text'}
                {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', $name)}{$name|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1'}{/if}{/capture}
                {if $asterisk_contact}
                  {include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact number=$add_bb_var.value.$bb_id}
                {else}
                  {$add_bb_var.value.$bb_id|url2href}
                {/if}
              {elseif $add_bb_var.type eq 'autocompleter'}
                {include file="_view_autocompleter.html"
                         value=$add_bb_var.value.$bb_id
                         value_id=$add_bb_var.value_id.$bb_id
                         view_mode_url=$add_bb_var.autocomplete.view_mode_url
                }
              {elseif $add_bb_var.type eq 'textarea'}
                {$add_bb_var.value.$bb_id|escape|url2href|nl2br}
              {elseif $add_bb_var.type eq 'date'}
                {$add_bb_var.value.$bb_id|date_format:#date_short#|default:'&nbsp;'}
              {elseif $add_bb_var.type eq 'datetime'}
                {$add_bb_var.value.$bb_id|date_format:#date_mid#|default:'&nbsp;'}
              {elseif $add_bb_var.type eq 'time'}
                {$add_bb_var.value.$bb_id|date_format:#time_short#|default:'&nbsp;'}
              {elseif $add_bb_var.type eq 'dropdown'}
                {include file="_view_dropdown_radio.html"
                  var=$add_bb_var
                  value=$add_bb_var.value.$bb_id
                  extended_value=$add_bb_var.extended_value.$bb_id
                }
              {elseif $add_bb_var.type eq 'radio'}
                {include file="_view_dropdown_radio.html"
                  var=$add_bb_var
                  value=$add_bb_var.value.$bb_id
                  extended_value=$add_bb_var.extended_value.$bb_id
                }
              {elseif $add_bb_var.type eq 'checkbox_group'}
                {if is_array($add_bb_var.value.$bb_id) && count($add_bb_var.value.$bb_id) gt 16}<div class="scroll_box" style="width:100%!important;">{/if}
                {if $add_bb_var.options}
                  {foreach from=$add_bb_var.options item='option' name=mcb}
                    {if @in_array($option.option_value,$add_bb_var.value.$bb_id)}{if trim($option.label)}{$option.label|escape|default:'&nbsp;'}{if !empty($add_bb_var.options_align) and $add_bb_var.options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{else}<img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" />{/if}{/if}
                  {/foreach}
                {elseif $add_bb_var.options}
                  {foreach from=$add_bb_var.options key='optgroup_name' item='optgroup'}
                    {foreach from=$optgroup item='option' name=mcb}
                      {if @in_array($option.option_value,$add_bb_var.value.$bb_id)}{if trim($option.label)}{$option.label|escape|default:'&nbsp;'}{if !empty($add_bb_var.options_align) and $add_bb_var.options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{else}<img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" />{/if}{/if}
                    {/foreach}
                  {/foreach}
                {/if}
                {if is_array($add_bb_var.value.$bb_id) && count($add_bb_var.value.$bb_id) gt 16}</div>{/if}
              {elseif $add_bb_var.type eq 'file_upload'}
                {assign var='file_info' value=$add_bb_var.value.$bb_id}
                {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
                  {if !$file_info->get('not_exist')}
                    {if $add_bb_var.view_mode eq 'thumbnail' && $file_info->isImage()}
                      {getimagesize assign='image_dimensions' image_path=$file_info->get('path')}
                      <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$file_info->get('id')|encrypt:'_viewfile_'|escape:'url'}{if $file_info->get('archived_by')}&amp;archive=1{/if}{if $add_bb_var.thumb_width}&amp;maxwidth={$add_bb_var.thumb_width}{/if}{if $add_bb_var.thumb_height}&amp;maxheight={$add_bb_var.thumb_height}{/if}" onclick="showFullLBImage('{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=viewfile&amp;viewfile={$tmp_id}&amp;file={$file_info->get('id')}{if $file_info->get('archived_by')}&amp;archive=1{/if}', '{$add_bb_var.label}', '{$image_dimensions.width}', '{$image_dimensions.height}')" />
                    {else}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=viewfile&amp;viewfile={$tmp_id}&amp;file={$file_info->get('id')}{if $file_info->get('archived_by')}&amp;archive=1{/if}" target="_blank"><img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" title="{#open#|escape}" /></a>
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=getfile&amp;getfile={$tmp_id}&amp;file={$file_info->get('id')}"><img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" title="{#download#|escape}" /></a>
                    {/if}
                  {else}
                    <img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" class="pointer dimmed" />
                    <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" class="pointer dimmed" />
                  {/if}
                {else}
                  &nbsp;
                {/if}
              {elseif $add_bb_var.type eq 'map'}
                <script type="text/javascript">
                  params_map_{$add_bb_var_name}_{$bb_var.id} = {json encode=$add_bb_var.map_params|default:false};
                </script>
                {if $add_bb_var.map_params.type eq 'inline'}
                  
                {else}
                  <input type="image" src="{$theme->imagesUrl}map.png" width="16" height="16" alt="" class="image_map" border="0" onclick="showMap(this, params_map_{$add_bb_var_name}_{$bb_var.id}, '{$add_bb_var.label}', ''); return false;" />
                {/if}
              {else}
                {$add_bb_var.value.$bb_id|escape|nl2br|default:'&nbsp;'}
              {/if}
            {/if}
            {if ($add_bb_var.extended_value.$bb_id || $add_bb_var.extended_value.$bb_id === '0' || $add_bb_var.value.$bb_id || $add_bb_var.value.$bb_id === '0') && $add_bb_var.back_label}{$add_bb_var.back_label}{/if}
          </td>
          {/if}
        {/foreach}
      </tr>
      <tr id="row_{$bb_var.id}_box"{if $smarty.cookies.$layout_cookie_var eq 'off' || $var.toggle eq 'collapse' ||  $var.toggle eq 'first' && !$smarty.foreach.jj.first} style="display: none"{/if}>
        <td colspan="{$add_bb_vars_count}">
          <table>
          {if $bb_var.type eq 'gt2'}
            {assign var=view_type_name value="_gt2_view.html"}
            {assign var=table value=$bb_var}
                    <tr>
                      <td colspan="3" class="t_table">
          {else}
            {assign var=view_type_name value="view_`$bb_var.type`.html"}
          {/if}
          {include file=$view_type_name
            var=$bb_var
            standalone=false
            var_id=$bb_var.id
            name=$bb_var.name
            custom_id=$varbb_.custom_id
            label=$bb_var.label
            help=$bb_var.help
            back_label=$bb_var.back_label
            value=$bb_var.value
            options=$bb_var.options
            optgroups=$bb_var.optgroups
            option_value=$bb_var.option_value
            first_option_label=$bb_var.first_option_label
            onclick=$bb_var.onclick
            on_change=$bb_var.on_change
            check=$bb_var.check
            scrollable=$bb_var.scrollable
            calculate=$bb_var.calculate
            readonly=$bb_var.readonly
            source=$bb_var.source
            map_params=$bb_var.map_params
            hidden=$bb_var.hidden
            required=$bb_var.required
            disabled=$bb_var.disabled
            view_mode=$bb_var.view_mode
            hide_label=1
            thumb_width=$bb_var.thumb_width
            thumb_height=$bb_var.thumb_height
            text_align=$bb_var.text_align
          }
          {if $bb_var.type eq 'gt2'}
                      </td>
                    </tr>
          {/if}
          </table>
        </td>
      </tr>
    {/foreach}
    </table>
  </td>
</tr>
