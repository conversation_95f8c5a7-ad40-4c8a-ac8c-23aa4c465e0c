{if $var.values || $configData}
  <table class="t_grouping_table" cellpadding="5" cellspacing="0" border="1" width="100%">
    {if $var.rows eq 1}
      <tr>
      {foreach from=$var.labels item=col_name key=index}
        <th>{$col_name}</th>
      {/foreach}
      </tr>
      <tr>
        {foreach from=$var.names item=name key=key}
          <td style="{if $var.$name.text_align && !($var.$name.text_align eq 'right' && $var.width.$name)}text-align: {$var.$name.text_align};{/if}"{if $var.width.$name} width="{$var.width.$key}"{/if}>&nbsp;
            {strip}
            {if $var.$name.text_align eq 'right' && $var.width.$name}<div style="float: left; text-align: {$var.$name.text_align}; width: {$var.width.$name}{if preg_match('#^[\d\.]+$#', $var.width.$name)}px{/if};">{/if}
            {if $var.types[$key] eq 'text' || $var.types[$key] eq 'autocompleter'}
              {if $configData.params.$name}{$configData.params.$name}{else}{$var.values[$name]|default:'&nbsp;'}{/if}
            {elseif $var.types[$key] eq 'textarea'}
              {if $configData.params.$name}{$configData.params.$name|escape|default:'&nbsp;'}{else}{$var.values[$name]|escape|default:'&nbsp;'}{/if}
            {elseif $var.types[$key] eq 'date'}
              {if $configData.params.$param_name}{$configData.params.$name}{else}{$var.values[$name]|date_format:#date_short#|default:'&nbsp;'}{/if}
            {elseif $var.types[$key] eq 'datetime'}
              {if $configData.params.$param_name}{$configData.params.$name}{else}{$var.values[$name]|date_format:#date_mid#|default:'&nbsp;'}{/if}
            {elseif $var.types[$key] eq 'time'}
              {if $configData.params.$param_name}{$configData.params.$name}{else}{$var.values[$name]|date_format:#time_short#|default:'&nbsp;'}{/if}
            {elseif $var.types[$key] eq 'dropdown'}
              {if $var[$name].options}
                {foreach from=$var[$name].options item='option'}
                  {if $option.option_value eq $var.values[$name] || $option.option_value eq $configdata.params.$name} {$option.extended_value|default:$option.label|nl2br}{/if}
                {/foreach}
              {elseif $var[$name].optgroups}
                {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                  {foreach from=$optgroup item='option'}
                    {if $option.option_value eq $var.values[$name] || $option.option_value eq $configdata.params.$name} {$option.extended_value|default:$option.label|nl2br}{/if}
                  {/foreach}
                {/foreach}
              {/if}
            {elseif $var.types[$key] eq 'radio'}
              {if $var[$name].options}
                {foreach from=$var[$name].options item='option' name=mrb}
                  {if (is_array($var.values[$name]) && (is_array($var.values[$name]) && @in_array($option.option_value,$var.values[$name]))) || (!is_array($var.values[$name]) && $option.option_value eq $var.values[$name]) || (isset($configData.params.$name) && $option.option_value eq $configData.params.$name)} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{/if}
                {/foreach}
              {elseif $var[$name].optgroups}
                {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                  {foreach from=$optgroup item='option'}
                    {if (is_array($var.values[$name]) && (is_array($var.values[$name]) && @in_array($option.option_value,$var.values[$name]))) || (!is_array($var.values[$name]) && $option.option_value eq $var.values[$name]) || (isset($configData.params.$name) && $option.option_value eq $configData.params.$name)} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{/if}
                  {/foreach}
                {/foreach}
              {/if}
            {elseif $var.types[$key] eq 'checkbox_group'}
              {if $var[$name].options}
                {foreach from=$var[$name].options item='option' name=mcb}
                  {if @in_array($option.option_value,$var.values[$name]) || (is_array($configData.params.$name) && @in_array($option.option_value, $configData.params.$name))} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{if !empty($var[$name].options_align) and $var[$name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
                {/foreach}
              {elseif $var[$name].optgroups}
                {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                  {foreach from=$optgroup item='option'}
                    {if @in_array($option.option_value,$var.values[$name]) || (is_array($configData.params.$name) && @in_array($option.option_value, $configData.params.$name))} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{if !empty($var[$name].options_align) and $var[$name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
                  {/foreach}
                {/foreach}
              {/if}
            {elseif $var.types[$key] eq 'file_upload'}
              {if $configData.params.$name}{$configData.params.$name}{else}{$var.values[$name]|default:'&nbsp;'}{/if}
            {else}
              &nbsp;
            {/if}
            {if $var.$name.text_align eq 'right' && $var.width.$name}</div>{/if}
            {if ($var.values[$name] || $var.values[$name] === '0') && $var.back_labels[$key]}&nbsp;{$var.back_labels[$key]}{/if}
            {/strip}
          </td>
        {/foreach}
      </tr>

    {else}
      {if $var.columns && count($var.columns) > 0}
      <tr>
      <th colspan="2"></th>
      {foreach from=$var.columns item=column key=col_name}
        <th>{$column}</th>
      {/foreach}
      </tr>
      {/if}
      {foreach key='key' from=$var.names item='name' name='configurator'}
        {if !$var.hidden[$key] && !preg_match('/__[a-z]+$/',$name)}
          <tr>
            <td style="border-right: 0px!important;">{help label_content=$var.labels[$key] text_content=''}</td>
            <td style="border-left: 0px!important;{if $var.$name.text_align && !($var.$name.text_align eq 'right' && $var.width.$name)}text-align: {$var.$name.text_align};{/if}" {if $var.width.$name} width="{$var.width.$name}"{/if}>
              {strip}
              {if $var.$name.text_align eq 'right' && $var.width.$name}<div style="float: left; text-align: {$var.$name.text_align}; width: {$var.width.$name}{if preg_match('#^[\d\.]+$#', $var.width.$name)}px{/if};">{/if}
              {if $var.types[$key] eq 'text' || $var.types[$key] eq 'autocompleter'}
                {if $configData.params.$name}{$configData.params.$name}{else}{$var.values[$name]|default:'&nbsp;'}{/if}
              {elseif $var.types[$key] eq 'textarea'}
                {if $configData.params.$name}{$configData.params.$name|escape|default:'&nbsp;'}{else}{$var.values[$name]|escape|default:'&nbsp;'}{/if}
              {elseif $var.types[$key] eq 'date'}
                {if $configData.params.$param_name}{$configData.params.$name}{else}{$var.values[$name]|date_format:#date_short#|default:'&nbsp;'}{/if}
              {elseif $var.types[$key] eq 'datetime'}
                {if $configData.params.$param_name}{$configData.params.$name}{else}{$var.values[$name]|date_format:#date_mid#|default:'&nbsp;'}{/if}
              {elseif $var.types[$key] eq 'time'}
                {if $configData.params.$param_name}{$configData.params.$name}{else}{$var.values[$name]|date_format:#time_short#|default:'&nbsp;'}{/if}
              {elseif $var.types[$key] eq 'dropdown'}
                {if $var[$name].overwrite_value}
                  {$var.values[$name]|default:'&nbsp;'}
                {elseif $var[$name].options}
                  {foreach from=$var[$name].options item='option'}
                    {if $option.option_value eq $var.values[$name] || $option.option_value eq $configdata.params.$name} {$option.extended_value|default:$option.label|nl2br}{/if}
                  {/foreach}
                {elseif $var[$name].optgroups}
                  {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                    {foreach from=$optgroup item='option'}
                      {if $option.option_value eq $var.values[$name] || $option.option_value eq $configdata.params.$name} {$option.extended_value|default:$option.label|nl2br}{/if}
                    {/foreach}
                  {/foreach}
                {/if}
              {elseif $var.types[$key] eq 'radio'}
                {if $var[$name].overwrite_value}
                  {$var.values[$name]|default:'&nbsp;'}
                {elseif $var[$name].options}
                  {foreach from=$var[$name].options item='option'}
                    {if (is_array($var.values[$name]) && in_array($option.option_value,$var.values[$name])) || (!is_array($var.values[$name]) && $option.option_value eq $var.values[$name]) || (isset($configData.params.$name) && $option.option_value eq $configData.params.$name)} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{/if}
                  {/foreach}
                {elseif $var[$name].optgroups}
                  {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                    {foreach from=$optgroup item='option'}
                      {if (is_array($var.values[$name]) && in_array($option.option_value,$var.values[$name])) || (!is_array($var.values[$name]) && $option.option_value eq $var.values[$name]) || (isset($configData.params.$name) && $option.option_value eq $configData.params.$name)} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{/if}
                    {/foreach}
                  {/foreach}
                {/if}
              {elseif $var.types[$key] eq 'checkbox_group'}
                {if $var[$name].overwrite_value}
                  {$var.values[$name]|default:'&nbsp;'}
                {elseif $var[$name].options}
                  {foreach from=$var[$name].options item='option'}
                    {if @in_array($option.option_value,$var.values[$name]) || (is_array($configData.params.$name) && in_array($option.option_value, $configData.params.$name))} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{if !empty($var[$name].options_align) and $var[$name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
                  {/foreach}
                {elseif $var[$name].optgroups}
                  {foreach from=$var[$name].optgroups key='optgroup_name' item='optgroup'}
                    {foreach from=$optgroup item='option'}
                      {if @in_array($option.option_value,$var.values[$name]) || (is_array($configData.params.$name) && in_array($option.option_value, $configData.params.$name))} {$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{if !empty($var[$name].options_align) and $var[$name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
                    {/foreach}
                  {/foreach}
                {/if}
              {elseif $var.types[$key] eq 'file_upload'}
                {$var.values[$name]}
              {else}
                &nbsp;
              {/if}
              {if $var.$name.text_align eq 'right' && $var.width.$name}</div>{/if}
              {if ($var.values[$name] || $var.values[$name] === '0') && $var.back_labels[$key]}&nbsp;{$var.back_labels[$key]}{/if}
              {/strip}
            </td>
            {foreach from=$var.columns item=c key=col}
              {capture assign=name_pattern}{$name}__{$col}{/capture}
              <td style="{if $var.$name_pattern.text_align && !($var.$name_pattern.text_align eq 'right' && $var.width.$name_pattern)}text-align: {$var.$name_pattern.text_align};{/if}"{if $var.width.$name_pattern} width="{$var.width.$name_pattern}"{/if}>&nbsp;
                {strip}
                  {foreach key='param_key' from=$var.names item='param_name'}
                    {if !$var.hidden[$param_key] && preg_match('/(__[a-z]+)$/',$param_name,$mtch[0]) && $name_pattern eq $param_name}
                      {if $var.$name_pattern.text_align eq 'right' && $var.width.$name_pattern}<div style="float: left; text-align: {$var.$name_pattern.text_align}; width: {$var.width.$name_pattern}{if preg_match('#^[\d\.]+$#', $var.width.$name_pattern)}px{/if};">{/if}
                      {if $var.types[$param_key] eq 'text' || $var.types[$param_key] eq 'autocompleter'}
                        {if $configData.params.$param_name}{$configData.params.$param_name}{else}{$var.values[$param_name]|default:'&nbsp;'}{/if}
                      {elseif $var.types[$param_key] eq 'textarea'}
                        {if $configData.params.$param_name}{$configData.params.$param_name|escape|default:'&nbsp;'}{else}{$var.values[$param_name]|escape|default:'&nbsp;'}{/if}
                      {elseif $var.types[$param_key] eq 'date'}
                        {if $configData.params.$param_name}{$configData.params.$param_name}{else}{$var.values[$param_name]|date_format:#date_short#|default:'&nbsp;'}{/if}
                      {elseif $var.types[$param_key] eq 'datetime'}
                        {if $configData.params.$param_name}{$configData.params.$param_name}{else}{$var.values[$param_name]|date_format:#date_mid#|default:'&nbsp;'}{/if}
                      {elseif $var.types[$param_key] eq 'time'}
                        {if $configData.params.$param_name}{$configData.params.$param_name}{else}{$var.values[$param_name]|date_format:#time_short#|default:'&nbsp;'}{/if}
                      {elseif $var.types[$param_key] eq 'dropdown'}
                        {if $var[$name_pattern].options}
                          {foreach from=$var[$name_pattern].options item='option'}
                            {if $option.option_value eq $var.values[$param_name] || $option.option_value eq $configdata.params.$param_name} {$option.extended_value|default:$option.label|nl2br}{/if}
                          {/foreach}
                        {elseif $var[$name_pattern].optgroups}
                          {foreach from=$var[$name_pattern].optgroups key='optgroup_name' item='optgroup'}
                            {foreach from=$optgroup item='option'}
                              {if $option.option_value eq $var.values[$param_name] || $option.option_value eq $configdata.params.$param_name} {$option.extended_value|default:$option.label|nl2br}{/if}
                            {/foreach}
                          {/foreach}
                        {/if}
                      {elseif $var.types[$param_key] eq 'radio'}
                        {if $var[$name_pattern].options}
                          {foreach from=$var[$name_pattern].options item='option'}
                            {if (is_array($var.values[$param_name]) && in_array($option.option_value,$var.values[$param_name])) || (!is_array($var.values[$param_name]) && $option.option_value eq $var.values[$param_name]) || (isset($configData.params.$param_name) && $option.option_value eq $configData.params.$param_name)}{$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{/if}
                          {/foreach}
                        {elseif $var[$name_pattern].optgroups}
                          {foreach from=$var[$name_pattern].optgroups key='optgroup_name' item='optgroup'}
                            {foreach from=$optgroup item='option'}
                              {if (is_array($var.values[$param_name]) && in_array($option.option_value,$var.values[$param_name])) || (!is_array($var.values[$param_name]) && $option.option_value eq $var.values[$param_name]) || (isset($configData.params.$param_name) && $option.option_value eq $configData.params.$param_name)}{$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{/if}
                            {/foreach}
                          {/foreach}
                        {/if}
                      {elseif $var.types[$param_key] eq 'checkbox_group'}
                        {if $var[$name_pattern].options}
                          {foreach from=$var[$name_pattern].options item='option'}
                            {if @in_array($option.option_value,$var.values[$param_name]) || (is_array($configData.params.$param_name) && in_array($option.option_value, $configData.params.$param_name))}{$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{if !empty($var[$param_name].options_align) and $var[$param_name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
                          {/foreach}
                        {elseif $var[$name_pattern].optgroups}
                          {foreach from=$var[$name_pattern].optgroups key='optgroup_name' item='optgroup'}
                            {foreach from=$optgroup item='option'}
                              {if @in_array($option.option_value,$var.values[$param_name]) || (is_array($configData.params.$param_name) && in_array($option.option_value, $configData.params.$param_name))}{$option.extended_value|default:$option.label|nl2br|default:'&nbsp;'}{if !empty($var[$param_name].options_align) and $var[$param_name].options_align eq 'horizontal'}&nbsp;{else}<br />{/if}{/if}
                            {/foreach}
                          {/foreach}
                        {/if}
                      {elseif $var.types[$param_key] eq 'file_upload'}
                        {if $configData.params.$name}{$configData.params.$param_name|default:'&nbsp;'}{else}{$var.values[$param_name]|default:'&nbsp;'}{/if}
                      {else}
                        &nbsp;
                      {/if}
                      {if $var.$name_pattern.text_align eq 'right' && $var.width.$name_pattern}</div>{/if}
                      {if ($var.values[$param_name] || $var.values[$param_name] === '0') && $var.back_labels[$param_key]}&nbsp;{$var.back_labels[$param_key]}{/if}
                    {/if}
                  {foreachelse}
                    &nbsp;
                  {/foreach}
                {/strip}
              </td>
            {/foreach}
          </tr>
        {/if}
      {/foreach}
    {/if}
  </table>
{/if}
