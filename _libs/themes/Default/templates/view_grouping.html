<tr{if $var.hidden_all} style="display: none;"{/if}><td style="border-style: none;" colspan="3"><div class="t_caption2_title">{$var.label}</div>
<table class="t_grouping_table"{if $var.t_width} width="{$var.t_width}"{/if}><tr>
<th width="29" style="text-align: right;">{#num#|escape}</th>
{foreach name='i' key='key' from=$var.labels item='label'}
{capture assign='info'}{if $var.help[$key]}{$var.help[$key]}{else}{$var.labels[$key]}{/if}{/capture}
{if !$var.hidden[$key]}<th {if $var.width[$key]} width="{$var.width[$key]}"{/if}>{help label_content=$label text_content=$info}{if $var.required[$key]}{#required#}{/if}</th>{/if}
{/foreach}
</tr>
{foreach name='i' from=$var.values item='val' key=kk}
<tr>
<td style="text-align: right;">{$smarty.foreach.i.iteration}</td>
{foreach key='key' from=$var.names item='name'}
{if !$var.hidden[$key]}
<td style="{if $var.text_align[$key]}text-align: {$var.text_align[$key]};{/if}">
  {if $var.types[$key] eq 'text' or $var.types[$key] eq 'textarea'}
    {$val[$key]|escape|url2href|nl2br|default:'&nbsp;'}
  {elseif $var.types[$key] eq 'autocompleter'}
    {include file="_view_autocompleter.html"
             value=$val[$key]
             value_id=$var.values_id.$kk.$key
             view_mode_url=$var.autocomplete.$name.view_mode_url
    }
  {elseif $var.types[$key] eq 'date' && $val[$key] ne 0}
    {$val[$key]|date_format:#date_short#|default:'&nbsp;'}
  {elseif $var.types[$key] eq 'datetime' && $val[$key] ne 0}
    {$val[$key]|date_format:#date_mid#|default:'&nbsp;'}
  {elseif $var.types[$key] eq 'time'}
    {$val[$key]|date_format:#time_short#|default:'&nbsp;'}
  {elseif $var.types[$key] eq 'dropdown'}
      {include file="_view_dropdown_radio.html"
          var=$var[$name]
          value=$val[$key]
      }
  {elseif $var.types[$key] eq 'radio'}
      {include file="_view_dropdown_radio.html"
          var=$var[$name]
          value=$val[$key]
      }
  {elseif $var.types[$key] eq 'map'}
    <script type="text/javascript">
      params_map_{$name}_{$smarty.foreach.i.iteration} = {json encode=$var[$name].map_params|default:false};
    </script>
    {if $var[$name].map_params.type eq 'inline'}
      
    {else}
      <input
        type="image"
        src="{$theme->imagesUrl}map.png"
        width="16"
        height="16"
        alt=""
        class="image_map"
        border="0"
        {$name}{if $index}[{$index_array}]{/if}
        onclick="showMap(this, params_map_{$name}_{$smarty.foreach.i.iteration}, '{$var.labels[$key]}', '{$name}[{$smarty.foreach.i.iteration}]'); return false;"
      />
    {/if}
  {elseif $var.types[$key] eq 'file_upload'}
    {assign var='file_info' value=$val[$key]}
    {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
      {if !$file_info->get('not_exist')}
        {if $var[$name].view_mode eq 'thumbnail' && $file_info->isImage()}
          {getimagesize assign='image_dimensions' image_path=$file_info->get('path')}
          <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$file_info->get('id')|encrypt:'_viewfile_'|escape:'url'}{if $file_info->get('archived_by')}&amp;archive=1{/if}{if $var[$name].thumb_width}&amp;maxwidth={$var[$name].thumb_width}{/if}{if $var[$name].thumb_height}&amp;maxheight={$var[$name].thumb_height}{/if}" onclick="showFullLBImage('{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=viewfile&amp;viewfile={$var.model_id}&amp;file={$file_info->get('id')}{if $file_info->get('archived_by')}&amp;archive=1{/if}', '{$var.labels[$key]}', '{$image_dimensions.width}', '{$image_dimensions.height}')" alt="" />
        {else}
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=viewfile&amp;viewfile={$var.model_id}&amp;file={$file_info->get('id')}{if $file_info->get('archived_by')}&amp;archive=1{/if}" target="_blank"><img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" title="{#open#|escape}" /></a>
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=getfile&amp;getfile={$var.model_id}&amp;file={$file_info->get('id')}"><img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" title="{#download#|escape}" /></a>
        {/if}
      {else}
        <img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" class="pointer dimmed" />
        <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" class="pointer dimmed" />
      {/if}
    {else}
      &nbsp;
    {/if}
  {elseif $var.types[$key] eq 'formula'}
    {if (is_array($var.source) && isset($var.source.$name) && preg_match('/datetime/', $var.source.$name)) ||
        (!is_array($var.source) && preg_match('/datetime/', $var.source)) &&  $val[$key] ne 0}
      {$val[$key]|date_format:#date_mid#}
    {elseif (is_array($var.source) && isset($var.source.$name) && preg_match('/date/', $var.source.$name)) ||
            (!is_array($var.source) && preg_match('/date/', $var.source)) &&  $val[$key] ne 0}
      {$val[$key]|date_format:#date_short#}
    {else}
      {$val[$key]|escape|url2href}
    {/if}
    {assign var=formula_value value=$var.formula.$kk[$key].value}
    {if $formula_value}
      {foreach from=$formulas item='option' key='idx'}
        {if $option.option_value === $formula_value}({$option.label|default:'&nbsp;'}){/if}
      {/foreach}
    {/if}
  {elseif $var.types[$key] eq 'index'}
    {foreach from=$indexes item='option' key='idx'}
      {if $option.option_value === $val[$key]}{$option.label|default:'&nbsp;'}{/if}
    {/foreach}
    {assign var=index_value value=$var.index.$kk[$key]}
    {if $index_value.date && $index_value.date ne 0}
      {$index_value.date|date_format:#date_short#}
    {elseif $index_value.datetime && $index_value.datetime ne 0}
      {$index_value.datetime|date_format:#date_mid#}
    {elseif $index_value.text}
      {$index_value.text}
    {/if}
    {if $index_value.formula}
      {foreach from=$formulas item='option' key='idx'}
        {if $option.option_value === $index_value.formula}({$option.label|default:'&nbsp;'}){/if}
      {/foreach}
    {/if}
  {/if}
  {if ($val[$key] || $val[$key] === '0') && $var.back_labels[$key]}{$var.back_labels[$key]}{/if}
</td>
{/if}
{/foreach}
</tr>
{/foreach}
</table>
</td></tr>
