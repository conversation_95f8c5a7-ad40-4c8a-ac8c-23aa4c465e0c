<h1><img src="{$theme->imagesUrl}list.png" width="16" height="16" border="0" alt="{$title}" /> {$title}</h1>
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#placeholders_label#|escape}</div></td>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#placeholders_varname#|escape}</div></td>
    <td class="t_caption" nowrap="nowrap"><div class="t_caption_title">{#placeholders_default_value#|escape}</div></td>
  </tr>
{if $grouping_table_2}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="t_border hright">{counter name='gt2_vars'}</td>
    <td class="t_border">
      {if trim($grouping_table_2.label)}{$grouping_table_2.label}{else}{$grouping_table_2.name|escape|default:'&nbsp;'}{/if}
      {if !empty($model) && is_object($model) && $model->get('id') && in_array($model->modelName, array('Pattern', 'Email'))}
        {capture assign='settings_action'}{if $model->modelName eq 'Pattern'}printsettings{else}emailsettings{/if}{/capture}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={if strpos($model->get('model'), 'Finance') === 0}finance&amp;{$controller_param}=documents_types&amp;documents_types{else}{$model->get('model')|lower}s&amp;{$model->get('model')|lower}s&amp;{$controller_param}=types&amp;types{/if}={$settings_action}&amp;{$settings_action}={$model->get('model_type')}&amp;pattern={$model->get('id')}" target="_blank" class="strong">({$smarty.config.$settings_action|escape})</a>
      {/if}
    </td>
    <td class="t_border">
    {if $grouping_table_2.multilang && $module eq 'patterns'}
      {foreach from=$multi_langs item='lang'}
        [{$lang}_grouping_table_2] 
      {/foreach}
    {else}
      [grouping_table_2]
    {/if}
    </td>
    <td class="legend">{if $grouping_table_2.readonly}{#defined_by_data#|escape}{else}{$grouping_table_2.default_value|mb_truncate|escape|default:#defined_by_data#|escape}{/if}</td>
  </tr>
  {if $pattern && $pattern->get('model') eq 'Finance_Incomes_Reason' && (in_array($pattern->get('model_type'), array($smarty.const.PH_FINANCE_TYPE_INVOICE, $smarty.const.PH_FINANCE_TYPE_PRO_INVOICE, $smarty.const.PH_FINANCE_TYPE_CREDIT_NOTICE, $smarty.const.PH_FINANCE_TYPE_DEBIT_NOTICE)))}
  <tr class="{cycle values='t_odd,t_even'}">
    <td class="t_border hright">{counter name='gt2_vars'}</td>
    <td class="t_border">{if trim($grouping_table_2.label)}{$grouping_table_2.label}{else}{$grouping_table_2.name|escape|default:'&nbsp;'}{/if} </td>
    <td class="t_border">
    
    {if $grouping_table_2.multilang && $module eq 'patterns'}
      {foreach from=$multi_langs item='lang'}
        [{$lang}_grouping_table_2_printform] 
      {/foreach}
    {else}
      [grouping_table_2]
    {/if}
    </td>
    <td class="legend">{if $grouping_table_2.readonly}{#defined_by_data#|escape}{else}{$grouping_table_2.default_value|mb_truncate|escape|default:#defined_by_data#|escape}{/if}</td>
  </tr>
  {/if}
{else}
    {foreach name='i' from=$list key='varname' item='var'}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="t_border hright">{$smarty.foreach.i.iteration}</td>
        <td class="t_border">
          {if trim($var.label)}{$var.label}{else}a_{$var.name|escape|default:'&nbsp;'}{/if}
          {if $var.type eq 'gt2' && !empty($model) && is_object($model) && $model->get('id') && in_array($model->modelName, array('Pattern', 'Email'))}
            {capture assign='settings_action'}{if $model->modelName eq 'Pattern'}printsettings{else}emailsettings{/if}{/capture}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={if strpos($model->get('model'), 'Finance') === 0}finance&amp;{$controller_param}=documents_types&amp;documents_types{else}{$model->get('model')|lower}s&amp;{$model->get('model')|lower}s&amp;{$controller_param}=types&amp;types{/if}={$settings_action}&amp;{$settings_action}={$model->get('model_type')}&amp;pattern={$model->get('id')}" target="_blank" class="strong">({$smarty.config.$settings_action|escape})</a>
          {/if}
        </td>
        <td class="t_border">
        {if $var.multilang && $module eq 'patterns'}
          {foreach from=$multi_langs item='lang'}
            [{$lang}_a_{$var.name}] 
          {/foreach}
        {else}
          [a_{$var.name|escape|default:'&nbsp;'}]
        {/if}
        </td>
        <td class="legend">{if $var.readonly}{#defined_by_data#|escape}{else}{$var.default_value|mb_truncate|escape|default:#defined_by_data#|escape}{/if}</td>
      </tr>
      {if $var.type eq 'config'}
        <tr>
          <td colspan="4" class="t_caption3 strong legend">{$patternvar.label} (a_{$var.name|escape|default:'&nbsp;'})</td>
        </tr>
        {foreach name='j' from=$var.names item='config_var'}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border hright">&nbsp;</td>
            <td class="t_border" style="padding-left: 20px;">{$var.$config_var.label}</td>
            <td class="t_border">
            {if $var.$config_var.multilang && $module eq 'patterns'}
              {foreach from=$multi_langs item='lang'}
                [{$lang}_a_{$var.$config_var.name}] 
              {/foreach}
            {else}
              [a_{$var.$config_var.name|escape|default:'&nbsp;'}]
            {/if}
            </td>
            <td class="legend">{if $var.$config_var.readonly}{#defined_by_data#|escape}{else}{$var.$config_var.default_value|mb_truncate|escape|default:#defined_by_data#|escape}{/if}</td>
          </tr>
        {/foreach}
        <tr>
          <td colspan="4" class="t_caption3 strong legend"></td>
        </tr>
      {/if}
      {if $var.type eq 'table'}
        <tr>
          <td colspan="4" class="t_caption3 strong legend">{$var.label} (a_{$var.name|escape|default:'&nbsp;'})</td>
        </tr>
        {foreach name='j' from=$var.names item='table_var'}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border hright">&nbsp;</td>
            <td class="t_border" style="padding-left: 20px;">{$var.$table_var.label}</td>
            <td class="t_border">
            {if $var.$table_var.multilang && $module eq 'patterns'}
              {foreach from=$multi_langs item='lang'}
                [{$lang}_a_{$var.$table_var.name}] 
              {/foreach}
            {else}
              [a_{$var.$table_var.name|escape|default:'&nbsp;'}]
            {/if}
            </td>
            <td class="legend">{if $var.$table_var.readonly}{#defined_by_data#|escape}{else}{$var.$table_var.default_value|mb_truncate|escape|default:#defined_by_data#|escape}{/if}</td>
          </tr>
        {/foreach}
        <tr>
          <td colspan="4" class="t_caption3 strong legend"></td>
        </tr>
      {/if}
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="4">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
{/if}
  <tr>
    <td class="t_footer" colspan="4"></td>
  </tr>
</table>
