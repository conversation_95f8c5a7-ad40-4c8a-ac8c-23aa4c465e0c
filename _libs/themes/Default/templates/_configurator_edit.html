<table id="var_config_{$var.config}" class="t_grouping_table{if $var.borderless} t_borderless{/if}{if $var.custom_class} {$var.custom_class}{/if}"{if $var.t_width} width="{$var.t_width}"{/if}>
  {if $var.rows eq 1}
    <tr>
    {foreach from=$var.labels item=col_name key=idx name=cheader}
      <th colspan="2"{if $var.hidden[$idx]} style="display: none;"{/if}>{$col_name}</th>
    {/foreach}
    </tr>
    <tr>
      {foreach from=$var.names item=name key=key}
        <td style="border-right: none!important;{if $var.hidden[$key]} display: none;{/if}" class="{if !$var.required[$key]}un{/if}required">{if $var.required[$key]}{#required#}{/if}</td>
        <td style="border-left: none!important;{if $var.hidden[$key]} display: none;{/if}"{if $var.width.$name} width="{$var.width.$name}"{/if} nowrap="nowrap">
          {strip}
          {if $configData.params}
            {assign var='var_value' value=$configData.params.$name}
          {else}
            {assign var='var_value' value=$var.values[$name]}
          {/if}
          {if $var.$name.calculate > 0}
            {capture assign="calculate_buttons"}{if $calculate_buttons}{$calculate_buttons}, {/if}{$name}{/capture}
          {/if}
          {/strip}
         {if !empty($var.width[$name])}
           {assign var='var_width' value=$var.width[$name]-10}
         {else}
           {assign var='var_width' value=''}
         {/if}
          {if $var.types[$key] eq 'text'}
            {include file='input_text.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              calculate=$var.$name.calculate
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              origin='config'
              height=$var.height[$key]
              js_methods=$var.js_methods[$key]
              restrict=$var.js_filters[$key]
              show_placeholder=$var[$name].show_placeholder
              text_align=$var[$name].text_align
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'textarea'}
            {include file='input_textarea.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              calculate=$var.$name.calculate
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              origin='config'
              height=$var.height[$key]
              js_methods=$var.js_methods[$key]
              restrict=$var.js_filters[$key]
              show_placeholder=$var[$name].show_placeholder
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'date'}
            {include file='input_date.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              origin='config'
              height=$var.height[$key]
              js_methods=$var.js_methods[$key]
              restrict=$var.js_filters[$key]
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'datetime'}
            {include file='input_datetime.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              help=$var.help[$key]
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              origin='config'
              height=$var.height[$key]
              js_methods=$var.js_methods[$key]
              restrict=$var.js_filters[$key]
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'time'}
            {include file='input_time.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              origin='config'
              height=$var.height[$key]
              js_methods=$var.js_methods[$key]
              restrict=$var.js_filters[$key]
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'dropdown'}
            {include file='input_dropdown.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              var_id=$var.ids[$key]
              options=$var[$name].options
              optgroups=$var[$name].optgroups
              on_change=$var[$name].on_change
              sequences=$var[$name].sequences
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              really_required=$var.required[$key]
              required=$var.required[$key]
              origin='config'
              js_methods=$var.js_methods[$key]
              height=$var.height[$key]
              text_align=$var[$name].text_align
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'map'}
            {include file='input_map.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              map_params=$var[$name].map_params
              var_id=$var.ids[$key]
              width=$var_width
              origin='config'
              height=$var.height[$key]
            }
          {elseif $var.types[$key] eq 'radio'}
            {include file='input_radio.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              var_id=$var.ids[$key]
              options=$var[$name].options
              optgroups=$var[$name].optgroups
              on_change=$var[$name].on_change
              sequences=$var[$name].sequences
              readonly=$var.readonly[$key]
              hidden=$var.hidden[$key]
              js_methods=$var.js_methods[$key]
              options_align=$var[$name].options_align
              origin='config'
              height=$var.height[$key]
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'checkbox_group'}
            {include file='input_checkbox_group.html'
              standalone=true
              name=$name
              value=$var_value
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              options=$var[$name].options
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              hidden=$var.hidden[$key]
              origin='config'
              options_align=$var[$name].options_align
              height=$var.height[$key]
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'autocompleter'}
            {assign var='id_var_name' value=$var[$name].autocomplete.id_var}
            {capture assign='var_value_id'}{if $configData.params}{$configData.params.$id_var_name}{else}{$var.$name.value_id}{/if}{/capture}
            {include file='input_autocompleter.html'
              standalone=true
              name=$name
              value=$var_value
              value_id=$var_value_id
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              var_id=$var.ids[$key]
              readonly=$var.readonly[$key]
              hidden=$var.hidden[$key]
              autocomplete=$var.autocomplete[$name]
              width=$var_width
              height=$var.height[$key]
              js_methods=$var.js_methods[$key]
              restrict=$var.js_filters[$key]
              show_placeholder=$var[$name].show_placeholder
              custom_class=$var[$name].custom_class
            }
          {elseif $var.types[$key] eq 'file_upload'}
            {include file='input_file_upload.html'
              standalone=true
              name=$name
              value=$var_value
              var_id=$var.ids[$key]
              label=$var.labels[$key]
              help=$var.help[$key]
              back_label=$var.back_labels[$key]
              back_label_style=$var[$name].back_label_style
              readonly=$var.readonly[$key]
              width=$var_width
              hidden=$var.hidden[$key]
              origin='config'
              height=$var.height[$key]
              view_mode=$var[$name].view_mode
              thumb_width=$var[$name].thumb_width
              thumb_height=$var[$name].thumb_height
              deleteid=$var.deleteids[$name]
              custom_class=$var[$name].custom_class
            }
          {else}
          <td>&nbsp;</td>
          {/if}
        </td>
      {/foreach}
    </tr>

  {else}

    {if $var.columns && count($var.columns) > 0}
    <tr>
    <th colspan="3"></th>
    {foreach from=$var.columns item=column key=col_name name=cheader}
      <th colspan="2">{$column}</th>
    {/foreach}
    </tr>
    {/if}
    {foreach key='key' from=$var.names item='name' name='configurator'}
      {if !preg_match('/__[a-z]+$/',$name)}
        {capture assign='info'}{if $var.help[$key]}{$var.help[$key]}{else}{$var.labels[$key]}{/if}{/capture}
        <tr{if $var.hidden[$key]} style="display: none"{/if}>
          <td style="border-right: 0px!important;"><a name="error_{$var.names[$key]}"></a><label for="{$var.names[$key]}"{if $messages->getErrors($var.names[$key])} class="error"{/if}>{help label_content=$var.labels[$key] text_content=$info}</label></td>
          <td style="border-right: 0px!important;border-left: 0px!important;" class="{if !$var.required[$key]}un{/if}required">{if $var.required[$key]}{#required#}{/if}</td>
          <td style="border-left: 0px!important;"{if $var.width.$name} width="{$var.width.$name}"{/if}>
            {strip}
            {if $configData.params}
              {assign var='var_value' value=$configData.params.$name}
            {else}
              {assign var='var_value' value=$var.values[$name]}
            {/if}
            {if $var.$name.calculate > 0}
              {capture assign="calculate_buttons"}{if $calculate_buttons}{$calculate_buttons}, {/if}{$var.$name.id}{/capture}
            {/if}
            {/strip}
            {if !empty($var.width[$name])}
              {assign var='var_width' value=$var.width[$name]-10}
            {else}
              {assign var='var_width' value=''}
            {/if}
            {if $var.types[$key] eq 'text'}
              {include file='input_text.html'
                standalone=true
                name=$name
                value=$var_value
                calculate=$var.$name.calculate
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                width=$var_width
                height=$var.height[$key]
                js_methods=$var.js_methods[$key]
                restrict=$var.js_filters[$key]
                show_placeholder=$var[$name].show_placeholder
                text_align=$var[$name].text_align
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'textarea'}
              {include file='input_textarea.html'
                standalone=true
                name=$name
                value=$var_value
                calculate=$var.$name.calculate
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                width=$var_width
                height=$var.height[$key]
                js_methods=$var.js_methods[$key]
                restrict=$var.js_filters[$key]
                show_placeholder=$var[$name].show_placeholder
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'date'}
              {include file='input_date.html'
                standalone=true
                name=$name
                value=$var_value
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                width=$var_width
                height=$var.height[$key]
                js_methods=$var.js_methods[$key]
                restrict=$var.js_filters[$key]
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'datetime'}
              {include file='input_datetime.html'
                standalone=true
                name=$name
                value=$var_value
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                width=$var_width
                height=$var.height[$key]
                js_methods=$var.js_methods[$key]
                restrict=$var.js_filters[$key]
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'time'}
              {include file='input_time.html'
                standalone=true
                name=$name
                value=$var_value
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                width=$var_width
                height=$var.height[$key]
                js_methods=$var.js_methods[$key]
                restrict=$var.js_filters[$key]
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'dropdown'}
              {include file='input_dropdown.html'
                standalone=true
                name=$name
                value=$var_value
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                options=$var[$name].options
                optgroups=$var[$name].optgroups
                on_change=$var[$name].on_change
                sequences=$var[$name].sequences
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                really_required=$var.required[$key]
                required=$var.required[$key]
                origin='config'
                width=$var_width
                js_methods=$var.js_methods[$key]
                height=$var.height[$key]
                text_align=$var[$name].text_align
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'radio'}
              {include file='input_radio.html'
                standalone=true
                name=$name
                value=$var_value
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                options=$var[$name].options
                optgroups=$var[$name].optgroups
                on_change=$var[$name].on_change
                sequences=$var[$name].sequences
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                js_methods=$var.js_methods[$key]
                options_align=$var[$name].options_align
                height=$var.height[$key]
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'checkbox_group'}
              {include file='input_checkbox_group.html'
                standalone=true
                name=$name
                value=$var_value
                options=$var[$name].options
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                options_align=$var[$name].options_align
                height=$var.height[$key]
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'autocompleter'}
              {assign var='id_var_name' value=$var[$name].autocomplete.id_var}
              {capture assign='var_value_id'}{if $configData.params}{$configData.params.$id_var_name}{else}{$var.$name.value_id}{/if}{/capture}
              {include file='input_autocompleter.html'
                standalone=true
                name=$name
                value=$var_value
                value_id=$var_value_id
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                autocomplete=$var.autocomplete[$name]
                width=$var_width
                height=$var.height[$key]
                js_methods=$var.js_methods[$key]
                restrict=$var.js_filters[$key]
                show_placeholder=$var[$name].show_placeholder
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'file_upload'}
              {include file='input_file_upload.html'
                standalone=true
                name=$name
                value=$var_value
                var_id=$var.ids[$key]
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                back_label_style=$var[$name].back_label_style
                readonly=$var.readonly[$key]
                hidden=$var.hidden[$key]
                origin='config'
                width=$var_width
                height=$var.height[$key]
                view_mode=$var[$name].view_mode
                thumb_width=$var[$name].thumb_width
                thumb_height=$var[$name].thumb_height
                deleteid=$var.deleteids[$name]
                custom_class=$var[$name].custom_class
              }
            {elseif $var.types[$key] eq 'map'}
              {include file='input_map.html'
                standalone=true
                name=$name
                value=$var_value
                label=$var.labels[$key]
                help=$var.help[$key]
                back_label=$var.back_labels[$key]
                map_params=$var[$name].map_params
                var_id=$var.ids[$key]
                width=$var_width
                origin='config'
                height=$var.height[$key]
              }
            {/if}
          </td>
          {foreach from=$var.columns item=c key=col}
            {capture assign=name_pattern}{$name}__{$col}{/capture}
            {assign var=skey value=false}
            {assign var=sname value=false}
            {foreach key='param_key' from=$var.names item='param_name'}
              {if preg_match('/(__[a-z]+)$/',$param_name,$mtch[0]) && $name_pattern eq $param_name}
                {strip}
                {if $configData.params}
                  {assign var='var_value' value=$configData.params.$param_name}
                {else}
                  {assign var='var_value' value=$var.values[$param_name]}
                {/if}
                {if $var.$param_name.calculate > 0}
                  {capture assign="calculate_buttons"}{if $calculate_buttons}{$calculate_buttons}, {/if}{$var.$param_name.id}{/capture}
                {/if}
                {/strip}
                {if !empty($var.width[$name_pattern])}
                  {assign var='var_width' value=$var.width[$name_pattern]-10}
                {else}
                  {assign var='var_width' value=''}
                {/if}
                {assign var=skey value=$param_key}
                {assign var=sname value=$param_name}
              {/if}
            {/foreach}
            {if $skey && $sname}
              <td style="border-right: none!important;" class="{if !$var.required[$skey]}un{/if}required">{if $var.required[$skey]}{#required#}{/if}</td>
              <td style="border-left: none!important;"{if $var.width.$name_pattern} width="{$var.width.$name_pattern}"{/if} nowrap="nowrap">
                <a name="error_{$var.names[$skey]}"></a>
                {if $var.types[$skey] eq 'text'}
                  {include file='input_text.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    calculate=$var.$sname.calculate
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    width=$var_width
                    height=$var.height[$skey]
                    js_methods=$var.js_methods[$skey]
                    restrict=$var.js_filters[$skey]
                    show_placeholder=$var[$sname].show_placeholder
                    text_align=$var[$sname].text_align
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'textarea'}
                  {include file='input_textarea.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    calculate=$var.$sname.calculate
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    width=$var_width
                    height=$var.height[$skey]
                    js_methods=$var.js_methods[$skey]
                    restrict=$var.js_filters[$skey]
                    show_placeholder=$var[$sname].show_placeholder
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'date'}
                  {include file='input_date.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    width=$var_width
                    height=$var.height[$skey]
                    js_methods=$var.js_methods[$skey]
                    restrict=$var.js_filters[$skey]
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'datetime'}
                  {include file='input_datetime.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    width=$var_width
                    height=$var.height[$skey]
                    js_methods=$var.js_methods[$skey]
                    restrict=$var.js_filters[$skey]
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'time'}
                  {include file='input_time.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    width=$var_width
                    height=$var.height[$skey]
                    js_methods=$var.js_methods[$skey]
                    restrict=$var.js_filters[$skey]
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'dropdown'}
                  {include file='input_dropdown.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    options=$var[$sname].options
                    optgroups=$var[$sname].optgroups
                    on_change=$var[$sname].on_change
                    sequences=$var[$sname].sequences
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    really_required=$var.required[$skey]
                    required=$var.required[$skey]
                    origin='config'
                    width=$var_width
                    js_methods=$var.js_methods[$skey]
                    height=$var.height[$skey]
                    text_align=$var[$sname].text_align
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'radio'}
                  {include file='input_radio.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    options=$var[$sname].options
                    optgroups=$var[$sname].optgroups
                    on_change=$var[$sname].on_change
                    sequences=$var[$sname].sequences
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    js_methods=$var.js_methods[$skey]
                    options_align=$var[$sname].options_align
                    height=$var.height[$skey]
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'checkbox_group'}
                  {include file='input_checkbox_group.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    options=$var[$sname].options
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    options_align=$var[$sname].options_align
                    height=$var.height[$skey]
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'autocompleter'}
                  {assign var='id_var_name' value=$var[$sname].autocomplete.id_var}
                  {capture assign='var_value_id'}{if $configData.params}{$configData.params.$id_var_name}{else}{$var.$sname.value_id}{/if}{/capture}
                  {include file='input_autocompleter.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    value_id=$var_value_id
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    autocomplete=$var.autocomplete[$sname]
                    width=$var_width
                    height=$var.height[$skey]
                    js_methods=$var.js_methods[$skey]
                    restrict=$var.js_filters[$skey]
                    show_placeholder=$var[$sname].show_placeholder
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'file_upload'}
                  {include file='input_file_upload.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    var_id=$var.ids[$skey]
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    readonly=$var.readonly[$skey]
                    hidden=$var.hidden[$skey]
                    origin='config'
                    width=$var_width
                    height=$var.height[$skey]
                    view_mode=$var[$sname].view_mode
                    thumb_width=$var[$sname].thumb_width
                    thumb_height=$var[$sname].thumb_height
                    deleteid=$var.deleteids[$sname]
                    custom_class=$var[$sname].custom_class
                  }
                {elseif $var.types[$skey] eq 'map'}
                  {include file='input_map.html'
                    standalone=true
                    name=$sname
                    value=$var_value
                    label=$var.labels[$skey]
                    help=$var.help[$skey]
                    back_label=$var.back_labels[$skey]
                    back_label_style=$var[$sname].back_label_style
                    map_params=$var[$sname].map_params
                    var_id=$var.ids[$skey]
                    width=$var_width
                    origin='config'
                    height=$var.height[$skey]
                  }
                {/if}
              </td>
            {else}
              <td style="border-right: none!important;" class="unrequired">&nbsp;</td>
              <td style="border-left: none!important;">&nbsp;</td>
            {/if}
          {/foreach}
        </tr>
      {/if}
    {/foreach}
  {/if}
</table>
{if $var.source}
  <br clear="all" />
  <div>
    {strip}
      {*check for automatic calculation buttons*}
      {if $calculate_buttons}
        {csv2array assign='calculate_buttons_array' csvlist=$calculate_buttons}
        {foreach from=$calculate_buttons_array item='calc_button'}
          {capture assign='execute_calculation'}{$execute_calculation}calc(this, {$calc_button});{/capture}
        {/foreach}
      {/if}
    {/strip}
    <button type="button" class="button" name="addRow_{$var.config}" id="addRow_{$var.config}" onclick="{$execute_calculation}{if $config_with_file_upload || $var.config_with_file_upload}uploadViaAjax(this.form, {ldelim}source:'franky', module:'{$module}', config_id:0, model_id:{$model_id}, div_id:'table_franky_{$var.config}', config_num:{$var.config}{rdelim}){else}saveFranky(this.form, '{$module}', 0, {$model_id}, 'table_franky_{$var.config}',{$var.config}){/if};{if $var.reset_form}editFranky({$model_id}, '{$module}', 0, {$var.config});{/if}">{#add#|escape}</button>
    {if $configData.model_id > 0}
      <button type="button" class="button" name="editRow_{$var.config}" id="editRow_{$var.config}" onclick="{$execute_calculation}{if $config_with_file_upload || $var.config_with_file_upload}uploadViaAjax(this.form, {ldelim}source:'franky', module:'{$module}', config_id:0, model_id:{$model_id}, div_id:'table_franky_{$var.config}', config_num:{$var.config}{rdelim}){else}saveFranky(this.form,'{$module}',{$configData.id}, {$model_id}, 'table_franky_{$var.config}',{$var.config}){/if}">{#edit#|escape}</button>
      <input type="hidden" name="franky_row_id_{$var.config}" id="franky_row_id_{$var.config}" value="{$configData.id}" />
    {/if}
  </div>
{/if}
