<tr id="table_{$recipient_type}_{$idx}"{if $dis} class="input_inactive"{/if}>
    <td class="t_border t_v_border t_border_left" nowrap="nowrap">
        <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}"
             title="{#delete#|escape}"
             class="hide_row"{if !$recipient} style="visibility: hidden;{/if}"
             onclick="confirmAction('delete_row', function() {ldelim} hideField('table_{$recipient_type}','{$idx}'); {rdelim}, this);" />
        &nbsp;
        <a href="javascript: disableField('table_{$recipient_type}','{$idx}')">{$idx}</a>
    </td>
    <td class="t_border t_v_border" nowrap="nowrap">
        {include file=`$theme->templatesDir`input_autocompleter.html
            custom_id=$ac.custom_id
            name=$ac.name
            index=$idx
            width=452
            type=$ac.type
            disabled=$dis
            exclude_oldvalues=$ac.exclude_oldvalues
            autocomplete_type=$ac.autocomplete_type
            autocomplete_buttons=$ac.autocomplete_buttons
            autocomplete_buttons_hide=$ac.autocomplete_buttons_hide
            autocomplete=$ac.autocomplete
            standalone=true
            label=#communications_mail_recipient#
            value=$recipient
        }
    </td>
</tr>
