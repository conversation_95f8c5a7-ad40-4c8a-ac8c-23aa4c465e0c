{if trim($var.label)}<div class="t_caption2_title">{$var.label}</div>{/if}

<table id="var_group_{$var.grouping}" class="t_grouping_table{if $var.dont_copy_values} dont_copy_values{/if}{if $var.t_custom_class} {$var.t_custom_class}{/if}"{if $var.t_width} width="{$var.t_width}"{/if}>
<tr>
  <th style="text-align: right;">
    <div style="width: 29px;">{#num#|escape}</div>
    {* max saved "num" value in db *}
    <input type="hidden" name="max_num_{$var.grouping}" id="max_num_{$var.grouping}" value="{$var.max_num|default:0}" disabled="disabled" />
  </th>
{foreach key='key' from=$var.labels item='label'}
{capture assign='info'}{if $var.help[$key]}{$var.help[$key]}{else}{$var.labels[$key]}{/if}{/capture}
  <th{if $var.hidden[$key]} style="display: none;"{/if}>
    {if !$var.hidden[$key]}
      {if $var.last_visible_column eq $key}
        <div class="floatl">
          <a name="error_{$var.names[$key]}"></a><label for="{$var.names[$key]}_1"{if $messages->getErrors($var.names[$key])} class="error"{/if}>{help label_content=$label text_content=$info}{if $var.required[$key]}{#required#}{/if}</label>
        </div>
        {capture assign='values_count'}{if !empty($var.values) && is_array($var.values)}{$var.values|@count}{else}0{/if}{/capture}
        {include file="_table_buttons.html"
                var=$var
                values_count=$values_count
        }
        {if !empty($var.floating_buttons)}
          {include file="_table_buttons.html"
                  var=$var
                  values_count=$values_count
                  floating_buttons=true
          }
        {/if}
      {else}
        <div style="{if $var.width[$key]}width: {$var.width[$key]}px;{/if}">
          <a name="error_{$var.names[$key]}"></a><label for="{$var.names[$key]}_1"{if $messages->getErrors($var.names[$key])} class="error"{/if}>{help label_content=$label text_content=$info}{if $var.required[$key]}{#required#}{/if}</label>
        </div>
      {/if}
    {/if}
  </th>
{/foreach}
</tr>
{foreach name='i' from=$var.values item='val' key=kk}
<tr id="var_group_{$var.grouping}_{$smarty.foreach.i.iteration}"{if $var.floating_buttons} onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"{/if}>
  <td align="right" nowrap="nowrap">
    {if $var.readonly_all && $var.hide_multiple_rows_buttons}
    <span>{$smarty.foreach.i.iteration}</span>
    {else}
    <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if empty($var.values) || $var.values|@count le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function(el) {ldelim} hideField('var_group_{$var.grouping}','{$smarty.foreach.i.iteration}'){if $var.upon_table_rows_update};{$var.upon_table_rows_update|replace:'this':'el'}{/if}; {rdelim}, this);" />&nbsp;<span class="group_table_tow_num" onclick="disableField('var_group_{$var.grouping}','{$smarty.foreach.i.iteration}'){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}">{$smarty.foreach.i.iteration}</span>
    {/if}
  </td>
  {foreach name='j' key='key' from=$var.names item='name'}
  <td{if $var.hidden[$key]} style="display: none;"{/if}{if $var.types[$key] eq 'autocompleter' || $var.types[$key] eq 'formula' || $var.types[$key] eq 'index'} nowrap="nowrap"{/if}>
  {if $var.types[$key] eq 'formula'}
    {assign var=formula_value value=$var.formula.$kk[$key].value}
  {else}
    {assign var=formula_value value=$var.index.$kk[$key].formula}
  {/if}
  {if !empty($var[$name].$kk.options)}
    {assign var=options value=$var[$name].$kk.options}
  {else}
    {assign var=options value=$var[$name].options}
  {/if}
  {include file=input_`$var.types[$key]`.html
      standalone=true
      name=$name
      index=$smarty.foreach.i.iteration
      name_index=$kk
      value=$val[$key]
      value_id=$var.values_id.$kk.$key
      label=$var.labels[$key]
      help=$var.help[$key]
      description=$var.descriptions[$key]
      back_label=$var.back_labels[$key]
      back_label_style=$var.back_label_styles[$key]
      readonly=$var.readonly[$key]
      calculate=$var.calculate[$key]
      hidden=$var.hidden[$key]
      autocomplete=$var.autocomplete[$name]
      js_methods=$var.js_methods[$key]
      restrict=$var.js_filters[$key]
      origin='group'
      options=$options
      optgroups=$var[$name].optgroups
      on_change=$var[$name].on_change
      onchange=$var[$name].onchange
      sequences=$var[$name].sequences
      really_required=$var.required[$key]
      required=$var.required[$key]
      map_params=$var[$name].map_params
      view_mode=$var[$name].view_mode
      thumb_width=$var[$name].thumb_width
      thumb_height=$var[$name].thumb_height
      width=$var.width[$key]
      height=$var.height[$key]
      formulas=$formulas
      formula_value=$formula_value
      source=$var.formula.$kk[$key].source
      indexes=$indexes
      date_value=$var.index.$kk[$key].date
      do_not_show_check_all_button=$var[$name].do_not_show_check_all_button
      do_not_show_check_none_button=$var[$name].do_not_show_check_none_button
      first_option_label=$var[$name].first_option_label
      options_align=$var.options_align[$key]
      deleteid=$var.deleteids.$kk[$key]
      show_placeholder=$var[$name].show_placeholder
      text_align=$var.text_align[$key]
      custom_class=$var.custom_class[$key]
  }
  </td>
  {/foreach}
</tr>
{foreachelse}
<tr id="var_group_{$var.grouping}_1"{if $var.floating_buttons} onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"{/if}>
  <td align="right" nowrap="nowrap">
    <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function(el) {ldelim} hideField('var_group_{$var.grouping}','1'){if $var.upon_table_rows_update};{$var.upon_table_rows_update|replace:'this':'el'}{/if}; {rdelim}, this);" />&nbsp;<span class="group_table_tow_num" onclick="disableField('var_group_{$var.grouping}','1'){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}">1</span>
  </td>
  {foreach key='key' from=$var.names item='name'}
  <td{if $var.hidden[$key]} style="display: none;"{/if}{if $var.types[$key] eq 'autocompleter' || $var.types[$key] eq 'formula' || $var.types[$key] eq 'index'} nowrap="nowrap"{/if}>
  {if $var.types[$key] eq 'formula'}
    {assign var=formula_value value=$var.formula.1[$key].value}
  {else}
    {assign var=formula_value value=$var.index.1[$key].formula}
  {/if}
  {include file=input_`$var.types[$key]`.html
      standalone=true
      name=$name
      index=1
      name_index=1
      value=''
      value_id=''
      label=$var.labels[$key]
      help=$var.help[$key]
      description=$var.descriptions[$key]
      back_label=$var.back_labels[$key]
      back_label_style=$var.back_label_styles[$key]
      readonly=$var.readonly[$key]
      calculate=$var.calculate[$key]
      hidden=$var.hidden[$key]
      autocomplete=$var.autocomplete[$name]
      js_methods=$var.js_methods[$key]
      restrict=$var.js_filters[$key]
      origin='group'
      options=$var[$name].options
      optgroups=$var[$name].optgroups
      on_change=$var[$name].on_change
      onchange=$var[$name].onchange
      sequences=$var[$name].sequences
      really_required=$var.required[$key]
      required=$var.required[$key]
      map_params=$var[$name].map_params
      view_mode=$var[$name].view_mode
      thumb_width=$var[$name].thumb_width
      thumb_height=$var[$name].thumb_height
      width=$var.width[$key]
      height=$var.height[$key]
      formulas=$formulas
      formula_value=$formula_value
      source=$var.formula.1[$key].source
      indexes=$indexes
      date_value=$var.index.1[$key].date
      do_not_show_check_all_button=$var[$name].do_not_show_check_all_button
      do_not_show_check_none_button=$var[$name].do_not_show_check_none_button
      first_option_label=$var[$name].first_option_label
      options_align=$var.options_align[$key]
      show_placeholder=$var[$name].show_placeholder
      text_align=$var.text_align[$key]
      custom_class=$var.custom_class[$key]
  }
  </td>
  {/foreach}
</tr>
{/foreach}
</table>
