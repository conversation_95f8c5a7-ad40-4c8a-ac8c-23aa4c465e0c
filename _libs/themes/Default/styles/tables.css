/*Tables*/
.thumb {
    opacity: .75;
    filter: alpha(opacity=75);
    border: 1px solid #AAAAAA;
}
.thumb:hover {
    opacity: 1;
    filter: alpha(opacity=100);
    border: 1px solid #CCCCCC;
}
.t_table {
    border: 1px solid #CCCCCC;
    background-color: #F1F1F1;
}

.t_table1 {
    border: 1px solid #CCCCCC;
}

.t_table .t_table {
    border: 0;
    background-color: #F1F1F1;
}
.t_grouping_table {
    margin: 5px;
    border: 1px solid #CCCCCC;
    border-collapse: collapse;
    float: left;
}
.t_grouping_table td, .t_grouping_table th, .t_grouping_table_multilevel th, .t_grouping_table_multilevel tr.outer_row td {
    border: 1px solid #CCCCCC;
    padding: 3px;
    vertical-align: top;
}
.bb_table_edit .t_grouping_table td, .bb_table_edit .t_grouping_table th {
    border-width: 1px!important;
    border-style: solid!important;
    border-color: #CCCCCC!important;
}
.t_grouping_table th, .t_grouping_table_multilevel th {
    text-align: left;
    font-weight: normal;
    background-color: #DDDDDD;
}
.t_grouping_table_multilevel {
    margin: 5px;
    border-collapse: collapse;
    float: left;
}
.t_grouping_table_inner {
    border: 0 none;
    margin: 0px!important;
    width: 100%;
    border-collapse: collapse;
}
.t_grouping_table .zpMenu-table  td {
    border: none;
}
.t_layout_table {
    width: 100%;
}
.t_layout_table td {
    padding: 5px;
}
.t_layout_table th {
    padding: 5px;
    text-align: left;
    font-weight: normal;
    background-color: #DDDDDD;
}
.t_distribution_table {
    margin: 5px;
    border-collapse: collapse;
}
.t_distribution_table>tbody>tr>td {
    padding: 0px;
}
.t_distribution_table>tbody>tr>td>table {
    margin: 0px;
    border: 0px;
    width: 100%;
}
.t_distribution_table td.container_cell{
    padding: 0px;
    border: 0px;
}
.t_distribution_data_table {
    width: 100%;
    margin: 0px;
    border-collapse: collapse;
}
.t_distribution_data_table.t_caption3_title td {
    border-top: 0px none!important;
    border-bottom: 0px none!important;
}
.t_distribution_data_table td.data_title {
    text-align: center;
    vertical-align: middle;
}
.t_distribution_data_table tr.data_title2 {
    color: #666666;
}
.t_distribution_data_table tr.data_title2 td{
    border-top: 0px!important;
}
.data_title_main {
    border-left: 1px solid #cccccc;
    border-right: 1px solid #cccccc;
    border-left: 0px none!important;
}
.data_title_main td {
    background-color: #dfdfdf;
}
.data_title {
    border-left: 1px solid #cccccc;
    border-right: 1px solid #cccccc;
}
.data_title td {
    background-color: #fff3e1;
    border: 0;
}
.data_title td.t_border{
    border-right: 1px solid #cccccc;
}
.t_distribution_data_table .show_zero_share td {
    padding: 0px;
}
.item_amounts {
     float: right;
}
.item_unforwarded, .item_assigned, .item_progress {
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: #999999;
}
.item_approved {
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-weight: bold;
}
.item_returned {
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: #F50504;
}
.item_pending {
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: green;
}
.budget_amount_caption {
    padding: 5px 0 0 15px;
    font-size: 11px;
}
.budget_amount {
    display: inline;
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: green;
    font-weight: bold;
}
#form_container .t_table, .form_container .t_table {
    width: 100%;
    background: url('../images/form_container_bg.png') #F1F1F1 repeat-x 0 0;
}
#form_container .t_table .t_table, .form_container .t_table .t_table {
    background-image: none;
}
.t_caption {
    background: url('../images/t_caption.jpg') #E1E1E1 repeat-x 0 0;
    height: 18px;
    padding: 0!important;
    padding-right: 5px!important;
}
.t_caption2 {
    background: url('../images/t_caption2.jpg') #EDEDED repeat-x 0 0;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
}
.t_caption3 {
    background: url('../images/t_caption3.jpg') #D6D6D6 repeat-x 0 0;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
}
.t_caption3_reverted {
    background: url('../images/t_caption3_reverted.jpg') #D6D6D6 repeat-x 0 100%;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
}
.t_caption4 {
    background: url('../images/t_caption4.jpg') #CBCBCB repeat-x 0 0;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
}
.t_caption5 {
    background: url('../images/t_caption5.png') #CBCBCB repeat-x 0 0;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
}
.t_caption6 {
    background: url('../images/t_caption6.png') #CBCBCB repeat-x 0 0;
    padding: 2px;
    border-bottom: 1px solid #CCCCCC;
}
.t_caption.t_asc, .t_caption.t_desc {
    background-image: url('../images/t_caption_selected.jpg');
}
.t_content {
    padding: 3px;
}
.t_caption_title {
    text-align: left;
    height: 16px;
    background: url('../images/t_caption_bullet.jpg') no-repeat 6px 0px;
    padding: 3px 0 0 16px;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #888888;
    font-weight: bold;
}
.t_list .t_caption_title {
}
.t_caption2_title {
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #888888;
    font-weight: bold;
}

.t_caption3_title {
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #666666;
    font-weight: bold;
}

.t_list .t_caption_title {
    background: url('../images/t_caption_bullet.jpg') no-repeat 6px 0;
}
.t_list td {
    vertical-align: top;
}
.t_asc .t_caption_title {
    background-image: url('../images/t_caption_bullet_asc.jpg');
    cursor: pointer;
}
.t_desc .t_caption_title {
    background-image: url('../images/t_caption_bullet_desc.jpg');
    cursor: pointer;
}
.t_sortable .t_caption_title {
    cursor: pointer;
}
.t_footer {
    background: url('../images/t_footer.jpg') #E1E1E1 repeat-x 0 0;
    height: 7px;
    padding: 0!important;
}
.t_border {
    border-right: 1px solid #CCCCCC;
}
.t_border_double {
    border-right: 2px solid #CCCCCC;
}
.t_border2 {
    border-right: 1px solid #BBBBBB;
}
.t_border_left {
    border-left: 1px solid #CCCCCC;
}
.t_v_border td, .t_v_border {
    border-bottom: 1px solid #CCCCCC;
}
.t_bottom_border td, .t_bottom_border {
    border-bottom: 1px solid #CCCCCC!important;
}
.t_top_border td, .t_top_border {
    border-top: 1px solid #CCCCCC;
}
.t_row td {
    padding: 3px;
    border-bottom: 1px solid #F8F8F8;
}
.t_row.t_new td {
    background-color: #FFFFF4;
}
.t_row:hover td {
    padding: 3px;
    border-bottom: 1px solid #AAAAAA;
}
.t_odd td, .t_odd table.attachments .t_odd td,
.t_even table.attachments .t_odd td, .t_row table.attachments .t_odd td {
    background-color: #F8F8F8;
    padding: 3px;
    border-top: 1px solid #F8F8F8;
    border-bottom: 1px solid #F8F8F8;
}

.t_odd:hover td {
    color: #000000;
    background-color: #FFFFF4;
    border-top: 1px solid #AAAAAA;
    border-bottom: 1px solid #AAAAAA;
}
.t_even td, .t_even table.attachments .t_even td,
.t_odd table.attachments .t_even td, .t_row table.attachments .t_even td {
    background-color: #ECECEC;
    padding: 3px;
    border-top: 1px solid #ECECEC;
    border-bottom: 1px solid #ECECEC;
}
.t_even:hover td {
    color: #000000;
    background-color: #FFFFF4;
    border-top: 1px solid #AAAAAA;
    border-bottom: 1px solid #AAAAAA;
}
td.t_odd1, .t_odd1 td, .t_odd1 {
    background-color: #F8F8F8;
    padding: 3px;
    border-top: 1px solid #F8F8F8;
    border-bottom: 1px solid #F8F8F8;
}
td.t_odd2, .t_odd2 td, .t_odd2 {
    border-bottom: 1px solid #CCCCCC;
}
td.t_even1, .t_even1 td, .t_even1 {
    background-color: #ECECEC;
    padding: 3px;
    border-top: 1px solid #ECECEC;
    border-bottom: 1px solid #ECECEC;
}
td.t_even2, .t_even2 td, .t_even2 {
    border-bottom: 1px solid #CCCCCC;
}
td.has_details_row {
    background-color: #FFFFF4;
}
.t_odd td .t_occupy_cell, .t_even td .t_occupy_cell, .t_row td .t_occupy_cell {
    margin: -2px -3px;
    padding: 4px;
    border: 0px none;
}
td > div.t_occupy_cell.has_inline_add, td > div.t_occupy_cell.history_activity {
    padding-right: 40% !important;
    min-height: 12px;
}
td > div.t_occupy_cell.has_inline_add.timesheet_time {
    padding-right: 20px !important;
}
td > div.t_occupy_cell > span {
    background-image: none;
    background-repeat: no-repeat;
    background-size: 12px auto;
    padding-left: 16px;
    vertical-align: bottom;
}
td > div.t_occupy_cell > span:empty {
    background-image: none!important;
}
td > div.t_occupy_cell > span.comments_total {
    background-image: url(../images/comments.png);
}
td > div.t_occupy_cell > span.emails_total {
    background-image: url(../images/email.png);
}
td > div.t_occupy_cell > span.timesheet_time_total {
    background-image: url(../images/timesheets.png);
}
td > div.t_occupy_cell > span.history_activity_total {
    background-image: url(../images/history_activity.png);
}
td > div.inline_add {
    position: relative;
    width: 0px;
    right: -100%;
    left: 100%;
    margin-left: -14px;
    border: 0px none;
}
td > div.inline_add > a {
    background-image: url(../images/small/plus2.png);
    background-repeat: no-repeat;
    background-size: 14px auto;
    margin-top: -18px;
    padding: 7px;
    display: block;
    height: 1px;
    filter: alpha(opacity = 0);
    opacity: 0;
}
td:hover > div.inline_add > a {
    filter: alpha(opacity = 100);
    opacity: 1;
}

.t_checkall {
    height: 0;
    padding: 3px!important;
}
.t_disabled td, .t_inactive td, .t_inactive a {
    color: #999999;
    /*text-decoration: line-through;*/
}
.strike td{
    text-decoration: line-through;
}
.t_inactive td {
    background-color: #FFFFE0;
}
.attention td, .attention, .attention_price td {
    font-size: 10px!important;
    background-color: #FFF3E1;
    border-bottom: 2px solid orange;
}
.oblique td, .oblique {
    font-size: 10px!important;
    color: #BBBBBB;
    background-color: #FFF3E1;
}
.t_deleted td {
    color: red;
    background-image: url('../images/hr.png');
    background-repeat: repeat-x;
    background-position: 0 99%;
    background-color: #FFEDE6;
}
.t_deleted table td {
    background: none;
}
.t_strike td {
    color: red;
    text-decoration:line-through;
    border-bottom: 1px solid #F8F8F8;
    padding: 3px;
}
.t_strike:hover td {
    padding: 3px;
    border-bottom: 1px solid #AAAAAA;
}
.t_strike td div table.attachments td, .strike td div table.attachments td {
    color: #000000;
    text-decoration: none;
}

.t_sorted {
    background-color: #FFFFFD!important;
}
.t_row .t_sorted {
    background-color: #FFFFF4!important;
    color: #000000!important;
}
.t_row .t_sorted a:link, .t_row .t_sorted a:active, .t_row .t_sorted a:visited, .t_row .t_sorted a:hover {
    color: #000000!important;
}
.t_active {
    background-color: #FFFFFD!important;
    color: red;
}
.t_flag {
    border: 0;
    margin: 3px 2px 0 0;
}
.t_table_border {
    border: 1px solid #CCCCCC!important;
}
.t_submenu_table {
    border:1px solid #CCCCCC;
    border-bottom: 0;
    background-color:#F1F1F1;
    width: 100%;
    padding-top: 2px;
    padding-left: 2px;
}
.t_selected_row_for_edit, .t_selected_row_for_edit td {
    background: #D3DEFC;
}
tr.row_red td {
    background-color:#CC1100;
    color: #FFFFFF;
}
tr.row_yellow td {
    background-color:#FFFDC0;
}
tr.row_green td {
    background-color:#BAFF8C;
}
tr.row_dark_green td {
    background-color:#DBEAD0;
}
tr.row_orange td {
    background-color:#FFCE37;
}
tr.row_blue td {
    background-color:#98BCFF;
}
tr.row_pink td {
    background-color:#F4878D;
}
tr.row_light_pink td {
    background-color:#EFB9B9;
}
tr.row_magenta td {
    background-color:#FECDCD;
}
table.bordered_cell td {
    border: 1px solid #CCCCCC;
    border-collapse: inherit;
}

.t_availability_table {
    border-collapse: collapse;
}
.t_availability_table td {
    margin: 0;
    padding: 0;
    border: 1px solid #999999;
    border-collapse: collapse;
    font-size: 8px;
    height: 11px;
}
.table_intervals .t_availability_table td {
    height: 5px;
}
.t_availability_table td.error {
    font-size: 9px;
    text-align: center;
}
.t_availability_table.t_event td {
    /*border-top: 0;*/
    border-bottom: 0;
}
.t_availability_table.t_hours td {
    text-align: left;
}
.t_cal_free {
    background: url('../images/t_caption7.png') #C6C6C6 0 0;
}
.t_cal_free.error {
    background: url('../images/t_caption6.png') #C6C6C6 0 0;
}
.t_cal_busy {
    background-color: #2FCA2F!important;
}
.t_cal_not_sure {
    background-color: #F8D603!important;
}
.t_cal_conflict {
    background-color: #FF5555!important;
}
.t_cal_event {
    background-color: #8888FF!important;
}
tr.current_project_stage td {
    color:#FF0000;
}
tr.project_stage_passed td {
    color:#B0B0B0;
}
table.reports_table {
    background-color: #F1F1F1;
    border: 1px solid #cccccc;
    border-collapse: collapse;
    border-spacing: 0;
}
table.reports_table th, table.reports_table td {
    padding: 5px;
    vertical-align: middle;
    border-bottom: 1px solid #CCCCCC;
    border-right: 1px solid #CCCCCC;
}
table.reports_table tr.selected td {
    background-color: #FFFFE0;
}
tr.reports_title_row td, tr.reports_title_row th {
    color: #555555;
    font-family: Verdana,Arial,Helvetica,sans-serif;
    font-size: 10px;
    font-weight: bold;
    background-color: #DFDFDF;
    border-bottom: 1px solid #CCCCCC;
}
tr.fixed > td, tr.fixed > th {
    position: fixed;
}
tr.reports_title_row td div button, tr.reports_title_row th button {
    vertical-align: middle!important;
}
tr.reports_title_row2 td, tr.reports_title_row2 th {
    color: #555555;
    font-family: Verdana,Arial,Helvetica,sans-serif;
    font-size: 10px;
    font-weight: bold;
    background-color: #E9E9E9;
    border-bottom: 1px solid #CCCCCC;
}
.reports_title_centered_middle {
    text-align: center;
    vertical-align: middle!important;
}
.reports_full_size_filter_panel {
    width: 1800px;
}
.reports_half_size_filter_panel {
    width: 900px;
}
.reports_hidden_filter_panel {
    display: none;
}
.t_layout_table td.t_buttons_container {
    padding: 5px 2px 0 5px;
}
.t_buttons {
    float: right;
}
.t_buttons div {
    float: left;
    width: 14px;
    height: 14px;
    cursor: pointer;
    margin-left: 1px;
    /*
    width: 16px;
    height: 16px;
    background: #EFEFEF url('../images/icon_button.gif') repeat-x;
    border: 1px solid #999999;
    */
}
.t_buttons div div {
    float: none;
    width: 14px;
    height: 14px;
    cursor: pointer;
    margin: 1px 0 0 1px;
    border: 0;
}
.t_buttons div.disabled {
    filter: alpha(opacity = 33);
    opacity: 0.33;
}
.t_buttons div div.disabled {
    filter: alpha(opacity = 100);
    opacity: 1;
}
.t_buttons .t_plus {
    background: url('../images/small/plus5.png') no-repeat -1px -1px;
}
.t_buttons .t_minus {
    background: url('../images/small/minus5.png') no-repeat -1px -1px;
}
.t_buttons .t_import {
    background: url('../images/small/import.png') no-repeat -1px -1px;
}
.t_buttons .t_export {
    background: url('../images/small/export.png') no-repeat -1px -1px;
}
.t_buttons .t_filter_nomenclatures {
    background: url('../images/small/nomenclatures.png') no-repeat 0 0px;
}
.t_buttons .t_filter_trademarks {
    background: url('../images/small/trademarks.png') no-repeat 0 0px;
}
.t_buttons .t_filter_customers {
    background: url('../images/small/customers.png') no-repeat 0 0;
}
.t_buttons .t_filter_contactpersons {
    background: url('../images/small/contactpersons.png') no-repeat 0 0;
}
.t_buttons .t_filter_projects {
    background: url('../images/small/projects.png') no-repeat 0 0;
}
.t_buttons .t_filter_tasks {
    background: url('../images/small/tasks.png') no-repeat 0 0;
}
.t_buttons .t_filter_documents {
    background: url('../images/small/documents.png') no-repeat 0 0;
}
.t_buttons .t_filter_contracts {
    background: url('../images/small/contracts.png') no-repeat 0 0;
}
.t_buttons .t_filter_users {
    background: url('../images/small/users.png') no-repeat 0 0;
}
.t_buttons .t_filter_departments {
    background: url('../images/small/departments.png') no-repeat 0 0;
}

.t_buttons .t_refresh_nomenclatures {
    background: url('../images/refresh_nomenclatures.png') no-repeat 0 0;
}
.t_buttons .t_refresh_customers {
    background: url('../images/refresh_customers.png') no-repeat 0 0;
}
.t_buttons .t_refresh_projects {
    background: url('../images/refresh_projects.png') no-repeat 0 0;
}
.t_buttons .t_refresh_tasks {
    background: url('../images/refresh_tasks.png') no-repeat 0 0;
}
.t_buttons .t_refresh_documents {
    background: url('../images/refresh_documents.png') no-repeat 0 0;
}
.t_buttons .t_refresh_users, .t_buttons .t_refresh_departments, .t_buttons .t_refresh_contracts {
    background: url('../images/refresh.png') no-repeat 0 0;
}

.t_buttons.t_floating_buttons {
    position: absolute;
    margin: -5px 0 0 -6px;
    padding: 5px;
    z-index: 101;
}
td.t_borderless, table.t_borderless, table.t_borderless td, table.t_borderless th, .t_grouping_table.t_borderless th, .t_grouping_table.t_borderless td {
    border: none!important;
}
table.t_borderless th {
    background-color: transparent;
}
.t_word_wrap {
    word-break: break-word;
}

img.t_info_image {
    width: 16px;
    height: 16px;
    border: 0px none;
    margin: -3px 0 -2px;
}

img.hide_row {
    cursor: pointer;
}

span.group_table_tow_num {
    cursor: pointer;
    color: #5371af;
    font-family: Verdana,Arial,Helvetica,sans-serif;
    font-size: 10px;
}

span.group_table_tow_num:hover {
    color: #2d395f;
}

.severity_track {
    position: absolute;
    z-index: 10000;
    margin-left: 83px;
    margin-top: -17px;
    background-image: url(../images/severity_track.png);
    background-repeat: no-repeat;
    border: 1px solid #adadad;
    width: 6px;
    height: 80px;
}

.severity_handle {
    background-image: url(../images/severity_handle.png);
    background-repeat: no-repeat;
    width: 9px;
    height: 9px;
    cursor: pointer;
}

.severity_title {
    position: relative;
    background-color: #fffdc0;
    border: 1px solid #cccccc;
    margin-left: 20px;
    display: inline;
    padding: 2px 5px;
    color: #666666;
    font-weight: normal;
    font-size: 10px;
}

.abs_div {
    position: absolute;
    padding-top: 1px;
}

.stopwatch_div {
    position: absolute;
    left: 740px;
    margin: 10px 5px 0 0;
}

.stopwatch_div > button > img {
    vertical-align: middle;
}

table.no_padding_left td {
    padding-left: 0px;
}

/*
 * Classes for scrollable tables with fixed headers
 */
.tbl_scroll {
    overflow-y: auto;
    max-height: 500px;
    display: block;
    margin-top: -1px;
}
table[id^="new_header"] {
    width: 100%;
    border-spacing: 0;
    border-top: 1px solid #cccccc;
    border-right: 1px solid #cccccc;
    border-left: 1px solid #cccccc;
}

/* Filters panel in Calendars - begin */
.t_panel_caption {
    background-color: #D6D6D6;
    border: 1px solid #EEEEEE;
    border-bottom: 0px none;
}

.t_panel_caption_title {
    text-align: left;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #666666;
    font-weight: bold;
}

.t_panel_caption.t_border {
    border-left: 0;
}

a:hover .t_panel_caption_title {
    color: #444444;
}

.cd_list_back td{
    background-image: url(../images/cd_list_back.png);
    padding-left: 10px;
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
}
/* Filters panel in Calendars - end */

/* Table sections */
td.t_section_title {
    padding: 1px !important;
}
div.t_section_title {
    background-color: #D6D6D6;
    border: 1px solid #EEEEEE;
    border-bottom: 0px none;
    padding: 5px;
}
div.t_section_title .title_text {
    color: #666666;
    font-family: Verdana,Arial,Helvetica,sans-serif;
    font-size: 10px;
    font-weight: bold;
    text-align: left;
}
/* END OF: Table sections */
