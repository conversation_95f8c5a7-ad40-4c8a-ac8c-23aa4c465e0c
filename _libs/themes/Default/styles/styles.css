/*Main*/
body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    /*overflow: auto;*/
}
body {
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 11px;
    background-color: #FFFFFF;
    height: 100%;
}
td, th {
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    empty-cells: show;
}
h1 {
    padding-top: 0;
    margin-top: 0;
    color: #5371AF;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
}
h2 {
    padding-top: 0;
    margin-top: 0;
    color: #5371AF;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    font-weight: normal;
}
h3 {
    padding-top: 15px;
    margin-top: 0;
    color: #5371AF;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 11px;
}
.errorpage h1, .errorpage h1 a:link {
    font-size: 16px;
}
.errorpage h2, .errorpage h2 a:link {
    font-size: 14px;
}
.errorpage h3, .errorpage h3 a:link {
    font-size: 12px;
}

/*Links*/
a:link {
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
    font-size: 10px;
    text-decoration: none;
    color: #5371AF;
}
a:visited {
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
    color: #46044F;
}
a:active {
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
    color: #EF0000;
}
a:hover {
    text-decoration: none;
    color: #2D395F;
}
a:target label, a:target + label {
    font-weight: bold;
}
div:target, span:target, input:target, td:target textarea, tr:target, div.target {
    background-color: #FFFFE0;
}
h1 a:link {
    color: #5371AF;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
}
h1 a:visited {
    color: #46044F;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
}
h1 a:active {
    color: #EF0000;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 13px;
    text-decoration: none;
}
h1 a:hover {
    text-decoration: none;
    color: #2D395F;
}
h3 a:link {
    color: #5371AF;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
}
h3 a:visited {
    color: #46044F;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
}
h3 a:active {
    color: #EF0000;
    font-family: Verdana, Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
    text-decoration: none;
}
h3 a:hover {
    text-decoration: none;
    color: #2D395F;
}

/*Page Menu*/
.pagemenu {
    padding: 5px 0;
}
.pagemenu select {
    color: #000000;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 9px;
    background-color: #F5F5F5;
    border: 1px solid #AAAAAA;
    z-index:1;
    position: relative;
}
.pagemenu span.page_menu_current_page, .pagemenu span.page_menu_link, .pagemenu a.page_menu_link:link, .pagemenu a.page_menu_link:hover, .pagemenu a.page_menu_link:active, .pagemenu a.page_menu_link:visited {
    border: 1px solid #ADADAD;
    font-size: 9px;
    padding: 1px 4px;
}
.pagemenu span.page_menu_current_page {
    background-color: #FFFFFF;
    color: #000000;
}
.pagemenu a.page_menu_link:link, .pagemenu a.page_menu_link:active, .pagemenu a.page_menu_link:visited, .pagemenu span.page_menu_link {
    background:url("../images/pg_background.png") repeat-x scroll 0 0;
    color: #000000;
}
.pagemenu a.page_menu_link:hover, .pagemenu span.page_menu_link:hover {
    background: none!important;
    background-color: #2D395F!important;
    color: #FFFFFF;
    border: 1px solid #2D395F;
}

.pagemenu a.page_menu_link.dimmed:hover, .pagemenu span.page_menu_link.dimmed:hover {
    /*THIS IS HERE ONLY BECAUSE OF IE*/
    background:url("../images/pg_background.png") repeat-x scroll 0 0!important;
    border: 1px solid #ADADAD;
    color: #000000;
}

/*Messages*/
ul#messages {
    margin-bottom: 10px;
    margin-left: 15px;
}
.m_body>ul#messages {
    margin-left: -15px;
}
li.error, .error, .error a:link, .error a:visited, .error a:active {
    list-style-image: url('../images/error.png');
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: #F50504;
    font-size: 11px;
}
.error a:link, .error a:visited, .error a:active {
    padding-bottom: 3px;
    text-decoration: underline;
    vertical-align: bottom;
}
.error a:hover {
    color: #AF0000;
}
li.error2, .error2, .error2 a:link, .error2 a:visited, .error2 a:active {
    list-style-image: url('../images/error.png');
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: #F50504;
    font-weight:normal;
    font-size: 10px;
}
li.warning, .warning, .warning a:link, .warning a:visited, .warning a:active {
    list-style-image: url('../images/warning.png');
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: orange;
    font-size: 11px;
}
.warning a:link, .warning a:visited, .warning a:active {
    padding-bottom: 3px;
    text-decoration: underline;
    vertical-align: bottom;
}
li.message, .message, .message a:link, .message a:visited, .message a:active {
    list-style-image: url('../images/message.png');
    vertical-align: middle;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    color: green;
    font-size: 11px;
}
.message a:link, .message a:visited, .message a:active {
    padding-bottom: 3px;
    text-decoration: underline;
    vertical-align: bottom;
}
.message a:hover {
    color: #AF0000;
}
.error a:hover, .warning a:hover, .message a:hover {
    text-decoration: underline;
}

.message_container_fixed {
    position: fixed;
    top: 0;
    margin: 0 auto;
    padding: 0 5px 0 0;
    z-index: 100001;
    border: 1px solid #cccccc;
    width: 500px;
    background: url('../images/close.png') no-repeat 100% 0 #ffffe0;
}

/*Documents*/
.incoming, a.incoming:link, a.incoming:active, a.incoming:visited {
    color: #298923;
}
.outgoing, a.outgoing:link, a.outgoing:active, a.outgoing:visited {
    color: #FF9900;
}
.internal, a.internal:link, a.internal:active, a.internal:visited {
    color: #4BA7FE;
}
a.incoming:hover {
    color: #298923;
    text-decoration: underline;
}
a.outgoing:hover {
    color: #FF9900;
    text-decoration: underline;
}
a.internal:hover {
    color: #4BA7FE;
    text-decoration: underline;
}

/*Overlib*/
.ol_bgclass {
    border: 1px solid #AAAAAA;
    background-color: #EEEEEE;
}
.ol_fgclass {
    background: transparent;
}
.ol_captionfontclass {
    padding-left: 5px;
    color: #555555;
    background-color: #CCCCCC;
    font-weight: bold;
}
.ol_closefontclass {
    padding-right: 5px;
    color: #555555;
    background-color: #CCCCCC;
    font-weight: bold;
    text-align: right;
}

/* Switches */
.switch_down, .switch_up, .switch_left, .switch_right {
    height: 6px;
    /*
    opacity: 0.2;
    filter: alpha(opacity=20);
    */
}
.switch_down {
    background: url('../images/arrow_down.png') no-repeat 50% 0;
}
.switch_up {
    background: url('../images/arrow_up.png') no-repeat 50% 0;
}
.switch_left {
    background: url('../images/arrow_left.png') no-repeat 0 50%;
}
.switch_right {
    background: url('../images/arrow_right.png') no-repeat 0 50%;
}
.switch_expand, .switch_collapse {
    width: 9px;
    height: 9px;
    font-size: 1px;
    line-height: 0;
    float: left;
    margin: 2px 3px 0 0;
}
.switch_expand {
    background: url('../images/switch_expand.png') no-repeat;
}
.switch_collapse {
    background: url('../images/switch_collapse.png') no-repeat;
}
.large > .switch_expand, .large > .switch_collapse {
    width: 16px;
    height: 16px;
}
.large > .switch_expand {
    background: url('../images/small/expand.png') no-repeat;
}
.large > .switch_collapse {
    background: url('../images/small/collapse.png') no-repeat;
}
.switch_collapse_cols_left, .switch_expand_cols_left {
    float: right;
    width: 7px;
    height: 9px;
    margin: -7px -1px 0 0;
    cursor: pointer;
}
.switch_collapse_cols_left {
    background: url('../images/arrow_right.png') no-repeat right top;
}
.switch_expand_cols_left {
    background: url('../images/arrow_left.png') no-repeat right top;
}
.switch_collapse_cols_right, .switch_expand_cols_right {
    float: left;
    width: 7px;
    height: 9px;
    margin: -7px 0 0 -1px;
    font-size: 1px;
    line-height: 0;
    cursor: pointer;
}
.switch_collapse_cols_right {
    background: url('../images/arrow_left.png') no-repeat left top;
}
.switch_expand_cols_right {
    background: url('../images/arrow_right.png') no-repeat left top;
}
.customer_links_box {
    width: 120px;
}
textarea {
    overflow: auto;
}
.image_map {
    vertical-align: middle;
    cursor: pointer;
}
.communication_parent {
    position: relative;
    word-wrap: break-word;
    max-width: 470px;
    max-height: 85px;
    overflow: hidden;
    margin: 10px;
    margin-top: initial;
}

.communication_fade {
    position: relative;
}

.communication_fade_parent[id^=communication_]:after {
    content: "...";
    float:right;
    height: 1.2em;
}