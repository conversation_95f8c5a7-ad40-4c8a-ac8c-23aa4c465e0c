{strip}
  {if !empty($audit.vars) && is_array($audit.vars) && count($audit.vars)}
    <i>{$audit_title|escape}</i>
    <table border="1" cellpadding="5" cellspacing="0">
      <tr>
        <th nowrap="nowrap" style="width: 150px;" align="left">{#var_name#|escape}</th>
        <th nowrap="nowrap" style="width: 200px;" align="left">{#old_value#|escape}</th>
        <th nowrap="nowrap" style="width: 200px;" align="left">{#var_value#|escape}</th>
      </tr>
      {foreach name='i' from=$audit.vars item='var'}
        {if $var.field_name == 'bb_delimiter'}
        <tr>
          <th colspan="3">{$var.label|escape|default:'&nbsp;'}</th>
        </tr>
        {else}
        <tr style="background-color: {cycle values='#F8F8F8,#ECECEC'}; vertical-align: top;">
          <td>{$var.var_label|escape|default:'&nbsp;'}</td>
          <td>
            {if isset($var.old_value) && $var.old_value !== ''}
              {if preg_match('/<\w.*>/', $var.old_value)}
                {$var.old_value|nl2br|regex_replace:"#<([\w.]+@[\w.]+)>#":"&lt;\$1&gt;"}
              {else}
                {$var.old_value|escape|nl2br|url2href|default:'&nbsp;'}
              {/if}
            {else}
              <i>{#no_old_value#}</i>
            {/if}
          </td>
          <td>
            {if isset($var.label) && $var.label !== ''}
              {assign var='new_value' value=$var.label}
            {else}
              {assign var='new_value' value=$var.field_value}
            {/if}
            {if preg_match('/<\w.*>/', $new_value)}
              {$new_value|nl2br|regex_replace:"#<([\w.]+@[\w.]+)>#":"&lt;\$1&gt;"}
            {else}
              {$new_value|escape|nl2br|url2href|default:'&nbsp;'}
            {/if}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr style="background-color: {cycle values='#F8F8F8,#ECECEC'}; vertical-align: top;">
          <td colspan="3" style="color: red;">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
  {/if}
  {if !empty($audit.gt2)}
    {include file=`$theme->templatesDir`_gt2_audit_email.html gt2_audit=$audit.gt2}
  {/if}
{/strip}