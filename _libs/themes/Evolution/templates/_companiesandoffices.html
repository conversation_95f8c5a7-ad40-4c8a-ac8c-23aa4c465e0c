<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="{$origin}s" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$model->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$model->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <script type="text/javascript">
        var co_relations = {json encode=$co_relations};
      </script>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {if $origin eq 'role'}
              {$model->get('name')|escape}
            {else}
              {$model->get('firstname')|escape} {$model->get('lastname')|escape}
            {/if}
          </td>
        </tr>
        {if $origin eq 'role'}
        <tr>
          <td class="labelbox">{help label='description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$model->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='companies'}</td>
          <td>&nbsp;</td>
          <td>
            {capture assign=what}{$origin}_companies{/capture}
            {assign var=selected_companies value=$model->get($what)}
            {foreach from=$companies item=company}
              <input class="{$origin}_company" type="checkbox" value="{$company->get('id')}" name="{$origin}_companies[]" id="{$origin}_companies_{$company->get('id')}" onclick="manageOfficesRelations('{$origin}');" title="{$company->get('name')|escape}"{if @in_array($company->get('id'), $selected_companies)} checked="checked"{/if} /> <label for="{$origin}_companies_{$company->get('id')}"{if !$company->get('active')} class="inactive_option" title="{#inactive_option#}">* {else}>{/if}{$company->get('name')|escape}</label><br />
            {/foreach}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='offices'}</td>
          <td>&nbsp;</td>
          <td>
            {foreach from=$offices item=office}
              {if !isset($co_relations_reversed[$office.option_value]) || !array_intersect($co_relations_reversed[$office.option_value], $selected_companies)}
                {assign var=hide value=true}
              {else}
                {assign var=hide value=false}
              {/if}
              <span style="display: {if $hide}none{/if}">
                <input class="{$origin}_office" type="checkbox" value="{$office.option_value}" name="{$origin}_offices[]" id="{$origin}_offices_{$office.option_value}" title="{$office.label|escape}"{capture assign=what}{$origin}_offices{/capture}{if @in_array($office.option_value, $model->get($what)) && !$hide} checked="checked"{/if} /> <label for="{$origin}_offices_{$office.option_value}"{if !$office.active_option} class="inactive_option" title="{#inactive_option#}">* {else}>{/if}{$office.label|escape}</label><br />
              </span>
            {/foreach}
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
