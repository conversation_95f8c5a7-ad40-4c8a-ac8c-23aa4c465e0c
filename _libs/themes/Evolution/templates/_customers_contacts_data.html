                      {counter name='k' start=0 print=false}
                      {if $customers_info->get('email')}
                        {foreach name='i' from=$customers_info->get('email') item='emails'}
                          <a href="mailto:{$emails}" target="_self"><img src="{$theme->imagesUrl}email.png" alt="EMAIL:" title="{#customers_email#|escape}" border="0" /> {$emails}</a><br />
                        {/foreach}
                      {/if}
                      {if $customers_info->get('web')}
                        {foreach name='i' from=$customers_info->get('web') item='web_link'}
                          <a href="http://{$web_link}" target="_blank"><img src="{$theme->imagesUrl}website.png" alt="WEB SITE:" title="{#customers_web#|escape}" border="0" /> {$web_link}</a><br />
                        {/foreach}
                      {/if}
                      {if $customers_info->get('phone')}
                        {foreach name='i' from=$customers_info->get('phone') item='phones'}
                          {if $use_asterisk}
                            {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='phone' number=$phones label='customers_phone'}
                          {else}
                            <img src="{$theme->imagesUrl}phone.png" alt="PHONE:" title="{#customers_phone#|escape}" border="0" /> {$phones}<br />
                          {/if}
                        {/foreach}
                      {/if}
                      {if $customers_info->get('fax')}
                        {foreach name='i' from=$customers_info->get('fax') item='faxes'}
                          {if $use_asterisk}
                            {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='fax' number=$faxes label='customers_fax'}
                          {else}
                            <img src="{$theme->imagesUrl}fax.png" alt="FAX:" title="{#customers_fax#|escape}" border="0" /> {$faxes}<br />
                          {/if}
                        {/foreach}
                      {/if}
                      {if $customers_info->get('gsm')}
                        {foreach name='i' from=$customers_info->get('gsm') item='gsms'}
                          {if $use_asterisk}
                            {include file=`$theme->templatesDir`_asterisk_contact.html contact_type='gsm' number=$gsms label='customers_gsm'}
                          {else}
                            <img src="{$theme->imagesUrl}gsm.png" alt="GSM:" title="{#customers_gsm#|escape}" border="0" /> {$gsms}<br />
                          {/if}
                        {/foreach}
                      {/if}
                      {if $customers_info->get('skype')}
                        {foreach name='i' from=$customers_info->get('skype') item='skypes'}
                          <img src="{$theme->imagesUrl}skype.png" alt="SKYPE: " title="{#customers_skype#|escape}" border="0" /> {$skypes}<br />
                        {/foreach}
                      {/if}
                      {if $customers_info->get('othercontact')}
                        {foreach name='i' from=$customers_info->get('othercontact') item='othercontacts'}
                          <img src="{$theme->imagesUrl}othercontact.png" alt="" title="{#customers_othercontact#|escape}" border="0" /> {$othercontacts}<br />
                        {/foreach}
                      {/if}