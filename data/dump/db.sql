CREATE SCHEMA `wizatour2` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ;

CREATE TABLE `wizatour2`.`company` (
   `ident` VARCHAR(15) NOT NULL,
   `parentIdent` VARCHAR(15) NULL,
   `name` VARCHAR(45) NULL,
   `phone` VARCHAR(16) NULL,
   `email` VARCHAR(255) NULL,
   `logo` TEXT NULL,
   `whitelabel` VARCHAR(15) NULL,
   PRIMARY KEY (`ident`));

SET FOREIGN_KEY_CHECKS=0;
ALTER TABLE `wizatour2`.`ibe`
    ADD CONSTRAINT `fk_ibe_1`
        FOREIGN KEY (providerIdent)
            REFERENCES `wizatour2`.`provider` (ident)
            ON DELETE NO ACTION
            ON UPDATE NO ACTION,
    ADD CONSTRAINT `fk_ibe_2`
        FOREIGN KEY (theme)
            REFERENCES `wizatour2`.`theme` (ident)
            ON DELETE NO ACTION
            ON UPDATE NO ACTION,
    ADD CONSTRAINT `fk_ibe_3`
        FOREIGN KEY (companyIdent)
            REFERENCES `wizatour2`.`company` (ident)
            ON DELETE NO ACTION
            ON UPDATE NO ACTION;



