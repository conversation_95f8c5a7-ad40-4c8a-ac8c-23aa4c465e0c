create table logs_archive
(
    id           int auto_increment
        primary key,
    timestamp    datetime     not null,
    priority     int          not null,
    priorityName varchar(250) null,
    message      text         null,
    request      longtext     null,
    response     longtext     null,
    extra        longtext     null,
    file         text         null,
    line         int          null
)
    collate = utf8mb4_unicode_ci;

create index logs_priority_index
    on logs_archive (priority);

create index table_name_timestamp_index
    on logs_archive (timestamp desc);

