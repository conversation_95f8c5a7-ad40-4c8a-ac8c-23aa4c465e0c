create table discount_code_batch
(
    code          varchar(8)                         not null,
    name          varchar(250)                       null,
    description   text                               null,
    discount_type enum ('percent', 'currency')       not null,
    conditions    text                               null,
    valid_after   datetime                           null,
    valid_until   datetime                           null,
    active        tinyint(1)                         not null,
    created       datetime default CURRENT_TIMESTAMP not null,
    updated       datetime                           null on update CURRENT_TIMESTAMP,
    primary key (code)
)
    collate = utf8mb4_unicode_ci;

create index discount_code_batch_active
    on discount_code_batch (active);

create index discount_code_batch_validity
    on discount_code_batch (valid_after, valid_until);

