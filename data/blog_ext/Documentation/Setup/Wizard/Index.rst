.. include:: ../../Includes.txt

.. _SetupWizard:

============
Setup Wizard
============

The Setup Wizard creates a fully configured **standalone** instance of the TYPO3 
Blog Extension. If you already have an existing site, you might dislike the result
of having an additional and unplanned root page. In that case, please read the 
:ref:`Manual Setup <SetupManual>` instructions.

.. rst-class:: bignums

   1. Click on "Setup" in the "Blog" section of your module menu in the backend

      .. figure:: wizard-1.png

   2. Click on the "Setup a new blog" button

      .. figure:: wizard-2.png

   3. Enter a title for your blog

      .. figure:: wizard-3.png

   4. Click on the "Setup" button, to create the blog

      .. figure:: wizard-4.png

   5. The blog setup is now completed

      .. figure:: wizard-5.png

   6. Enable your blog

      .. figure:: wizard-6.png

      1. Select the page module
      2. Right click on your blog
      3. Enable the page

   7. Explore your Blog

      .. figure:: wizard-7.png
