<f:spaceless>
    <div class="postauthor postauthor--full" itemprop="author" itemscope itemtype="http://schema.org/Person" data-blog-author="{author.uid}">
        <div class="postauthor__avatar">
            <f:variable name="avatarsize" value="{settings.authors.avatar.provider.size as integer}" />
            <f:if condition="{avatarsize}"><f:else><f:variable name="avatarsize" value="32" /></f:else></f:if>
            <img loading="lazy" class="postauthor__avatarimage blogavatar" height="{avatarsize}" width="{avatarsize}" src="{blogvh:uri.avatar(author: author, size: avatarsize)}" itemprop="image">
        </div>
        <div class="postauthor__body">
            <div class="postauthor__intro"><f:translate key="author.aboutTheAuthor"/></div>
            <div class="postauthor__name"><f:render section="ProfileLink" arguments="{_all}" contentAs="content">{author.name}</f:render></div>
            <f:if condition="{author.title} || {author.location}">
                <div class="postauthor__subline">
                    <f:if condition="{author.title}"><span class="postauthor__title" itemprop="jobTitle">{author.title}</span></f:if>
                    <f:if condition="{author.title} && {author.location}"><span class="postauthor__sublinedivider">,</span></f:if>
                    <f:if condition="{author.location}"><span class="postauthor__location" itemprop="homeLocation">{author.location}</span></f:if>
                </div>
            </f:if>
            <f:if condition="{author.website} || {author.twitter} || {author.linkedin} || {author.xing}">
                <div class="postauthor__social">
                    <f:if condition="{author.website}">
                        <a href="{author.website}" class="postauthor__sociallink postauthor__sociallink--website" target="_blank" itemprop="url">
                            <span class="postauthor__sociallinkicon postauthor__sociallinkicon--website">
                                <span class="blogicon"><f:render partial="General/SocialIcons" section="Website" optional="true" /></span>
                            </span>
                            <span class="postauthor__sociallinklabel"><f:translate key="author.social.website" /></span>
                        </a>
                    </f:if>
                    <f:if condition="{author.twitter}">
                        <a href="https://twitter.com/{author.twitter}" class="postauthor__sociallink postauthor__sociallink--twitter" target="_blank" itemprop="url">
                            <span class="postauthor__sociallinkicon postauthor__sociallinkicon--twitter">
                                <span class="blogicon"><f:render partial="General/SocialIcons" section="Twitter" optional="true" /></span>
                            </span>
                            <span class="postauthor__sociallinklabel"><f:translate key="author.social.twitter" /></span>
                        </a>
                    </f:if>
                    <f:if condition="{author.instagram}">
                        <a href="{author.instagram}" class="postauthor__sociallink postauthor__sociallink--instagram" target="_blank" itemprop="url">
                            <span class="postauthor__sociallinkicon postauthor__sociallinkicon--instagram">
                                <span class="blogicon"><f:render partial="General/SocialIcons" section="Instagram" optional="true" /></span>
                            </span>
                            <span class="postauthor__sociallinklabel"><f:translate key="author.social.instagram" /></span>
                        </a>
                    </f:if>
                    <f:if condition="{author.linkedin}">
                        <a href="{author.linkedin}" class="postauthor__sociallink postauthor__sociallink--linkedin" target="_blank" itemprop="url">
                            <span class="postauthor__sociallinkicon postauthor__sociallinkicon--linkedin">
                                <span class="blogicon"><f:render partial="General/SocialIcons" section="LinkedIn" optional="true" /></span>
                            </span>
                            <span class="postauthor__sociallinklabel"><f:translate key="author.social.linkedin" /></span>
                        </a>
                    </f:if>
                    <f:if condition="{author.xing}">
                        <a href="{author.xing}" class="postauthor__sociallink postauthor__sociallink--xing" target="_blank" itemprop="url">
                            <span class="postauthor__sociallinkicon postauthor__sociallinkicon--xing">
                                <span class="blogicon"><f:render partial="General/SocialIcons" section="Xing" optional="true" /></span>
                            </span>
                            <span class="postauthor__sociallinklabel"><f:translate key="author.social.xing" /></span>
                        </a>
                    </f:if>
                </div>
            </f:if>
            <f:if condition="{author.bio}">
                <div class="postauthor__bio" itemprop="description"><f:format.html>{author.bio}</f:format.html></div>
            </f:if>
            <f:if condition="{author.detailsPage} || {settings.authorUid}">
                <div class="postauthor__actions">
                    <blogvh:link.author author="{author}"><f:translate key="author.more.posts" /></blogvh:link.author>
                </div>
            </f:if>
        </div>
    </div>
    <f:section name="ProfileLink">
        <f:if condition="{author.detailsPage} || {settings.authorUid}">
            <f:then><blogvh:link.author rel="author" author="{author}"><span itemprop="name">{content}</span></blogvh:link.author></f:then>
            <f:else><span itemprop="name">{content}</span></f:else>
        </f:if>
    </f:section>
</f:spaceless>
