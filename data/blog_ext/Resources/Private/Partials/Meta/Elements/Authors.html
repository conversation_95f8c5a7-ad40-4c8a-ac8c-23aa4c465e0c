<f:variable name="name">authors</f:variable>
<f:variable name="icon"><f:render partial="General/BlogIcons" section="Author" optional="true" /></f:variable>
<f:variable name="prefix"><f:translate key="meta.authors.author"/></f:variable>

<f:if condition="{post.authors}">
    <f:if condition="{post.authors -> f:count()} > 1">
        <f:variable name="prefix"><f:translate key="meta.authors.authors"/></f:variable>
    </f:if>
    <f:render partial="Meta/Rendering/Item" arguments="{name: name, icon: icon, prefix: prefix}" contentAs="content">
        <ul class="postmetagroup__list">
            <f:for each="{post.authors}" as="author">
                <li>
                    <span class="postmetagroup__listitem" data-blog-author="{author.uid}">
                        <f:if condition="{avatarSettings.enable}">
                            <f:variable name="avatarsize" value="{avatarSettings.size as integer}" />
                            <f:if condition="{avatarsize}"><f:else><f:variable name="avatarsize" value="24" /></f:else></f:if>
                            <span class="postmetagroup__listprefix"><img loading="lazy" class="postmetagroup__listavatar blogavatar" height="{avatarsize}" width="{avatarsize}" src="{blogvh:uri.avatar(author: author, size: avatarsize)}" itemprop="image"></span>
                        </f:if>
                        <span class="postmetagroup__listtext" data-prefix="{f:translate(key:'meta.authors.author')}"><f:render section="ProfileLink" arguments="{author: author}" contentAs="content">{author.name}</f:render></span>
                    </span>
                </li>
            </f:for>
        </ul>
    </f:render>
</f:if>
<f:section name="ProfileLink">
    <f:if condition="{author.detailsPage} || {settings.authorUid}">
        <f:then><blogvh:link.author rel="author" author="{author}"><span itemprop="name">{content}</span></blogvh:link.author></f:then>
        <f:else><span itemprop="name">{content}</span></f:else>
    </f:if>
</f:section>
