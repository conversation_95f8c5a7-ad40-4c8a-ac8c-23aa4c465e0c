@import "mdc.button.min.css";
@import "mdc.card.min.css";


@font-face {
    font-family: OpenSansCyrillic;
    src: url('../fonts/open-sans-condensed-cyrillics.woff2');
}

@font-face {
    font-family: OpenSansRegular;
    src: url('../fonts/open-sans-condensed-latin.woff2');
}

body {
    font-family: var(--page-font);
    /*color: var(--blue-accent);*/
    margin: 0;
    padding: 0;
    background-color: #F7F5F2;
}

.top-bar {
    width: var(--page-width);
    margin: 0 auto;
    padding: 1.25rem 2rem 0.5rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: white;
    box-sizing: border-box;
}



.top-bar .topnavlogo a {
    display: block;
}

.logo-main {
    background-image: url("../images/dertour_logo_455x120.png");
    background-repeat: no-repeat;
    background-size: contain;
    display: block;
    width: 161px;
    height: 2.5rem;
}

nav {
    flex: 1;
    margin-left: 8px;
    margin-right: 8px;
}

.topnav-header {
    display: none;
    text-align: left;
}

.top-bar .phone,
.top-bar .email {
    line-height: 32px;
}

.top-bar .phone a,
.top-bar .phone a:visited,
.top-bar .phone a:active,
.top-bar .phone a:link,
.top-bar .email a,
.top-bar .email a:visited,
.top-bar .email a:active,
.top-bar .email a:link {
    color: var(--blue-accent);
    text-decoration: none;
    cursor: pointer;
    display: inline-block;
    height: 32px;
}
.top-bar .phone a:hover,
.top-bar .email a:hover {
    text-decoration: underline;
}


.top-bar .phone:before,
.top-bar .email:before {
    margin-right: 8px;
    width: 24px;
    height: 24px;
    display: inline-block;
    vertical-align: middle;

}
.top-bar .phone:before {
    /*content: '\1f4DE';*/
    content: '';
    background: var(--blue-accent);
    -webkit-mask-image: url('../images/call_black_24dp.svg');
}

.top-bar .email:before {
    /*content: '\1F4E7';*/
    content: '';
    background: var(--blue-accent);
    /*clip-path: url('../images/email_black_24dp.svg');*/
    -webkit-mask-image: url('../images/email_black_24dp.svg');
}


.top-bar-icon {
    text-decoration: none;
    display: none;
}

.side-bar {
    color: var(--blue-accent);
}

.topnav ul {
    list-style-type: none;
    overflow: hidden;
    text-align: center;
    padding: 0;
    background-color: #ffffff;
}

.topnav li {
    display: inline-block;
}

.topnav li a {
    display: block;
    color: var(--blue-accent);
    text-align: center;
    padding: 8px 24px;
    text-decoration: none;
}

.topnav li a:hover {
    color: var(--red-accent);
}

.topnav li.menu_active a{
    color: var(--red-accent);
}

.top-bar .topnavlogo,
.side-bar {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.page-wrapper {
    width: 100%;
    padding: 0 0;
    /*background-color: white;*/
}

.content-wrapper {
    width: var(--page-width);
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden;
    background-color: white;
    padding: 0 32px 32px 32px;
    min-height: calc(100vh - 462px);
}

.show-icon-container{
    display: none;
    text-align: right;
}

.hide-icon-container {
    display: none;
    text-align: center;
}

.search-form-icon,
.hide-form-icon {
    color: var(--red-accent);

}

.hide-form-icon .material-icons,
.search-form-icon .material-icons{
    font-size: 40px;
    vertical-align: text-bottom;
}

/*
.sub-to-newsletter {
    color: var(--red-accent);
    border-radius: 8px;
    border: 1px solid var(--red-accent);
    padding: 8px 16px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    width: 50%;
}

.sub-to-newsletter .newsletter {
    color: var(--red-accent);
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    flex-direction: row;
    margin-top: 0;
    margin-bottom: 0;
}

.sub-newsletter-btn {
    background-color: var(--red-accent);
    text-decoration: none;
    text-align: center;
    border-radius: 25px;
    font-size: 14px;
    color: white;
    margin: 0 0 0 16px;
    padding: 8px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    min-width: 6rem;
}

.newsletter span {
    margin: 4px auto;
}*/

.main-footer {
    border-top: 1px solid var(--blue-accent);
    margin-top: 0;
    padding: 1rem 1rem 3rem;
    background-color: white;

}

.footer-container {
    width: var(--page-width);
    max-width: calc(100vw - 2rem);
    box-sizing: border-box;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
}

.footer-header {
    margin-bottom: 2rem;
}

.footer-social {
    font-size: 1.1em;
    vertical-align: middle;
}
.footer-social-label {
    margin-right: 1rem;
}
.footer-social a {
    margin-right: 0.75rem;
    vertical-align: middle;
}
.footer-social img {
    height: 2rem;
    width: 2rem;
    vertical-align: middle;
}


.footer-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    margin-bottom: 2rem;
    /*display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr;
    grid-row-gap: 16px;*/
}

.footer-section {
    width: max-content;
}
.footer-section-header {
    margin-bottom: 0.5rem;
}

ul.footer-menu {
    /*width: fit-content;*/
    margin: 0 auto;
    padding: 0;
    list-style-type: none;
    padding-inline-start: 0;
}

ul.footer-menu li {
    margin-bottom: 1px;
}

ul.footer-menu a {
    color: var(--gray-accent);
    opacity: .9;
    text-decoration: none;
    padding: 1px 0;
}
ul.footer-menu a:hover {
    color: var(--red-accent);
    text-decoration: underline;
}
.sub-footer {
    font-size: 0.8em;
    margin: 0 auto 2rem;
    color: var(--gray-accent);
}
.horizontal-menu ul li {
    display: inline-block;
    margin-right: 16px;
}

.footer-copyright {
    color: var(--gray-accent);
}

@media screen and (max-width: 1200px) {
    :root {
        --page-width: 100%;
    }



    /*.show-icon-container{
        display: block;
        margin-right: 16px;
    }

    .show-icon-container,
    .hide-icon-container {
        z-index: 10;
    }*/




    /*.sub-to-newsletter {
        margin: auto;
    }

    .sub-to-newsletter .newsletter{
        font-size: 12px;
    }*/

    .footer-content {
        flex-wrap: wrap;
        /*flex-direction: column;*/
        /* grid-template-columns: 1fr 1fr;
         grid-template-rows: 1fr 1fr 1fr;

         grid-template-areas:
             "about flightinfo"
             "security payment";*/
    }
    .footer-content .footer-section {
        width: calc(50% - 1rem);
        margin-bottom: 1.25rem;
    }


    .topnav-header {
        display: list-item;
        margin: 16px 24px;
        padding: 0 0 16px 0;
        color: var(--blue-accent);
        border-bottom: 1px solid rgb(0 0 0 / 10%);
    }

    .topnav-header div:before {
        margin: 0 8px 0 0;
    }

    .topnav a:not(:first-child) {display: none;}

    .top-bar {
    }

    .top-bar-icon {
        height: 80px;
        padding: 0 16px;
        line-height: 80px;
        font-size: 1.75em;
        display: inline-block;
        color: var(--red-accent);

    }

    .topnav:not(.opened) ul {
        display: none;
    }

    /*.topnav {
        flex: initial;
    }*/

    .topnavlogo {
        /*margin: 0 0 15px 16px;*/
    }
    .logo-main {
        width: 9rem;
        height: 2.75rem;
    }


    .topnav ul{
        position: absolute;
        right: 0;
        top: 108px;
        bottom: 0;
        margin: 0;
        padding: 0;
        width: 90vw;
        z-index: 11;
        box-shadow: -1px 16px 32px -1px rgb(0 0 0 / 20%),
                    -1px 4px 8px -1px rgb(0 0 0 / 40%);
    }



    .topnav.opened li {
        display: list-item;
    }

    .topnav.opened li a{
        text-align: left;
    }

    #payment-row1,
    #payment-row2{
        margin: 0 auto;
    }
}


@media screen and (min-width: 400px) {
    .topnav.opened ul{
        max-width: 20rem;

    }
}

@media screen and (max-width: 600px) {
    /*.top-bar {
        padding: 8px 4px;
    }*/

    .side-bar {
        display: none;
    }

    .footer-social-label {
        display: block;
        margin-bottom: 0.5rem;
    }

    .footer-content {
        flex-direction: column;
        /*grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto;
        grid-row-gap: 16px;
        grid-template-areas:
            "about"
            "flightinfo"
            "security"
            "payment";*/
    }
    .footer-content .footer-section ul.footer-menu {
        margin-left: 0.25rem;
    }
    .sub-footer {
        width: 100%;
    }
    .footer-copyright {
        text-align: right;
    }

    .sub-footer ul.footer-menu {
       margin: 0;
    }
    .sub-footer ul.footer-menu li {
        display: block;
    }
}



.main-content h2 {
    color: var(--blue-accent);
}



.bestOffers,
.cityTours,
.exoticOffers{

}

.active-offers__wrapper {

}
.active-offers__title {
    color: var(--blue-accent);
}
.active-offers__content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
    flex-wrap: wrap;
}

.tp-card-container {
    margin-bottom: 32px;

}

.tp-offer-card {
    width: 100%;
}

.tp-offer-card__media {

}

.tp-offer-card__text {

}

.tp-offer-card__content {
    padding: 1rem;
}

.tp-list .tp-offer-card__content {
    padding: 0.5rem 1rem 0.5rem;
}


.tp-offer-card__header {
    box-sizing: border-box;
    width: 100%;
    padding: 0.25em 0.5em 0.75em;
    font-size: 1.50em;
    color: var(--blue-accent);
    position: absolute;
    z-index: 10;
    background: #ffffff90;
    background:linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.7) 30%, rgba(255,255,255,0.7) 100%);
}

.tp-offer-card__header::after {
    font-family: "Material Icons";
    font-size: 1.125em;
    position: absolute;
    right: 0.25em;
    line-height: 1.3;
    color: var(--red-accent);
}

.tp-offer-card__header.tp-type-hotel::after {
    content: "hotel";

}

.tp-offer-card__header.tp-type-packet::after {
    content: "flightaddhotel";
}

.tp-offer-card__title {
    /*background-image: linear-gradient(#00000000 0%, #00000000 20%);
    background-size: 200% 200%;*/
    color: var(--blue-accent);
    /*mix-blend-mode: difference;*/
    /*text-shadow: 1px 2px 1px rgba(255,255,255,0.4);*/
    font-size: 1.8em;
    padding: 8px 0 8px;
    height: 2.82em;
    white-space: normal;
}

.tp-list .tp-offer-card__title {
    height: 1.5em;
    padding: 0;
}

/*.tp-card-container:hover .tp-offer-card__title {
    background-image: linear-gradient(#00000070 10%, #00000000 20%);
    animation: tp-title-animation 250ms ease-out;
}

@keyframes tp-title-animation {
    0%{background-position: 0% 100%}
    100%{background-position: 0% 0%}
}*/

.tp-offer-card__subtitle {
    opacity: 0.8;
    font-size: 1rem;
}

.tp-offer-card__period {
    font-size: 1.1rem;
    opacity: 0.6;
}

.tp-offer-card__price-wrapper {
    color: var(--gray-accent);
    /*text-align: right;*/
    padding-top: 16px;
    vertical-align: bottom;
}

.tp-offer-card__price {
    color: var(--red-accent);
    font-size: 1.5rem;
    margin-top: -8px;
    float:right;
}

.offer-card-2x2 {
    box-sizing: border-box;
    padding: 1px 2px 3px;
    width: calc(50% - 16px);
}

.active-offers--4x1 .offer-card-2x2 {
    box-sizing: border-box;
    font-size: 1rem;
    padding: 1px 2px 3px;
    width: calc(25% - 16px);
}

.active-offers--4x1 .offer-card-2x2 .tp-offer-card__title {
    font-size: 1.3rem;
    height: 2.82em;
}

.active-offers--3x1 {
    margin-bottom: 2rem;
}

.active-offers--3x1>* {
    width: calc(33.3% - 16px);
    margin-bottom: 0;
}

.tp-list .tp-link {
    font-size: 0.75rem;
}
.active-offers--3x1 .tp-offer-card__title {
    /*font-size: 0.9rem;*/
}

@media screen and (max-width: 920px) {
    .active-offers--3x1 .tp-list .tp-link {
        font-size: 0.6rem;
    }
    .active-offers--3x1 .tp-offer-card__title h2 {
        font-size: 0.9em;
    }
}

@media screen and (max-width: 768px) {
    .offer-card-2x2 {
        width: calc(50% - 8px);
    }
    .content-wrapper {
        padding-left: 16px;
        padding-right: 16px;
    }

    .active-offers--4x1 .offer-card-2x2 {
        width: calc(50% - 8px);
    }

    .active-offers--3x1 {
        flex-wrap: wrap;
    }
    .active-offers--3x1>* {
        width: calc(50% - 16px);
    }
    .active-offers--3x1>*:first-child {
        width: 100%;
    }
}

@media screen and (max-width: 672px) {
    .offer-card-2x2 {
        width: 100%;
        font-size: 0.85em;
    }

    .sub-to-newsletter .newsletter {
        flex-wrap: wrap;
    }
}

@media screen and (max-width: 540px) {
    .active-offers--3x1>* {
        width: 100%;

    }
}

@media screen and (max-width: 425px) {
    .active-offers--4x1 .offer-card-2x2 {
        width: 100%;
    }
}



.tp-stars {
    overflow: hidden;
    display: inline-block;
    white-space: nowrap;
    height: 1.5em;
    line-height: 1em;
    font-size: 0.8em;
    vertical-align: middle;
    color: var(--red-accent);
}
.tp-stars::before {
    display: inline-block;
    overflow: hidden;
}


.tp-stars-star {
    font-size: 1em;
}
.tp-stars-0 {
    width: 0;
}
.tp-stars-0\.5::before,
.tp-stars-05::before {
    content: '\002605';
    width: 0.5em;
}
.tp-stars-1::before {
    content: '\002605';
    width: 1em;
}
.tp-stars-1\.5::before,
.tp-stars-15::before {
    content: '\002605\002605';
    width: 1.4em;
}
.tp-stars-2::before {
    content: '\002605\002605';
    width: 2em;
}
.tp-stars-2\.5::before,
.tp-stars-25::before {
    content: '\002605\002605\002605';
    width: 2.3em;
}
.tp-stars-3::before {
    content: '\002605\002605\002605';
    width: 3em;
}
.tp-stars-3\.5::before,
.tp-stars-35::before {
    content: '\002605\002605\002605\002605';
    width: 3.2em;
}
.tp-stars-4::before {
    content: '\002605\002605\002605\002605';
    width: 4em;
}
.tp-stars-4\.5::before,
.tp-stars-45::before {
    content: '\002605\002605\002605\002605\002605';
    width: 4.1em;
}
.tp-stars-5::before {
    content: '\002605\002605\002605\002605\002605';
    width: 5em;
}
.tp-stars-5\.5::before,
.tp-stars-55::before {
    content: '\002605\002605\002605\002605\002605\002605';
    width: 5em;
}
.tp-stars-6::before {
    content: '\002605\002605\002605\002605\002605\002605';
    width: 6em;
}
.tp-stars-6\.5::before,
.tp-stars-65::before {
    content: '\002605\002605\002605\002605\002605\002605\002605';
    width: 5.9em;
}
.tp-stars-7::before {
    content: '\002605\002605\002605\002605\002605\002605\002605';
    width: 7em;
}


.catiframe {
    margin: 0 auto;
    width: calc(var(--page-width) - 285px);
}
