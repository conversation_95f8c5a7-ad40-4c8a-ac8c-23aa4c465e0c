<header>
    <div class="top-bar">
        <div class="topnavlogo"><f:cObject typoscriptObjectPath="lib.header_logo" /></div>
        <nav class="topnav">
            <ul>
                <li>
                    <div class="topnav-header">
                        <f:cObject typoscriptObjectPath="lib.header_right" />
                    </div>
                </li>
                <!--<f:for each="{mainnavigation}" as="item">
                    <li class="nav-item{f:if(condition: item.active, then: ' menu_active')}">
                        <a class="nav-link" href="{item.link}"><span>{item.title}</span></a>
                    </li>
                </f:for>-->
                <f:cObject typoscriptObjectPath="lib.mainmenu" />
            </ul>
        </nav>
        <div class="side-bar">
            <f:cObject typoscriptObjectPath="lib.header_right" />
        </div>
        <a href="javascript:void(0);" class="top-bar-icon" onclick="document.querySelector('nav.topnav').classList.toggle('opened')">&#9776;</a>
    </div>
    <div class="headTitle">{page.title}</div>
</header>
