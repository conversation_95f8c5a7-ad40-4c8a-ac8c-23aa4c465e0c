<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  {if $action eq 'filter'}
    
  {else}
    <tr>
      <td class="pagemenu">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$controller}={$action}{if $type && is_numeric($type)}&amp;type={$type}{/if}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  {/if}
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="events" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}" method="post" enctype="multipart/form-data">
      {if $action eq 'filter' && $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            {if $action eq 'filter'}
              {if !$autocomplete_params || $autocomplete_params.select_multiple}
                {include file="`$theme->templatesDir`_select_items.html"
                  pages=$pagination.pages
                  total=$pagination.total
                  session_param=$session_param|default:$pagination.session_param
                }
              {else}
                {assign var='hide_selection_stats' value=true}
              {/if}
            {else}
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
            {/if}
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#events_name#|escape}</div></th>
          <th class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{$basic_vars_labels.type|default:#events_type#|escape}</div></th>
          <th class="t_caption t_border {$sort.event_start.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.event_start.link} onclick="{$sort.event_start.link}"{/if}>{$basic_vars_labels.event_start|default:#events_event_start#|escape}</div></th>
          <th class="t_caption t_border {$sort.event_end.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.event_end.link} onclick="{$sort.event_end.link}"{/if}>{$basic_vars_labels.event_end|default:#events_event_end#|escape}</div></th>
          <th class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{if 'events' == 'projects'}{#events_status_phase#|escape}{else}{$basic_vars_labels.status|default:#events_status#|escape}{/if}</div></th>
          <th class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{$basic_vars_labels.added|default:#added#|escape}</div></th>
          <th class="t_caption t_border {$sort.participants.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.participants.link} onclick="{$sort.participants.link}"{/if}>{$basic_vars_labels.participants|default:#events_participants#|escape}</div></th>
          <th class="t_caption t_border {$sort.description.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.description.link}">{$basic_vars_labels.description|default:#events_description#|escape}</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {array assign='background_colors'}
      {foreach name='i' from=$events item='single'}
    {strip}
      {capture assign='info'}
        <strong>{$basic_vars_labels.name|default:#events_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{$basic_vars_labels.description|default:#events_description#|escape}:</strong> {$single->get('description')|mb_truncate|escape}<br />
        <strong>{$basic_vars_labels.customer|default:#events_customer#|escape}:</strong> {$single->get('customer_name')|escape}<br />
        <strong>{$basic_vars_labels.location|default:#events_location#|escape}:</strong> {$single->get('location')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$single->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('status_modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {if $single->get('ownership') eq 'other'}
        {capture assign='background_style'}style="background-color: {$calendar_settings.background_color_other}; color: {$calendar_settings.color_other};"{/capture}
      {elseif $single->get('ownership') eq 'mine'}
        {capture assign='mine_background_color'}background_color_{$single->get('type')}{/capture}
        {capture assign='mine_color'}color_{$single->get('type')}{/capture}
        {capture assign='background_style'}style="background-color: {$calendar_settings.$mine_background_color}; color: {$calendar_settings.$mine_color};"{/capture}
      {else}
        {capture assign='background_style'}style="background-color: {$calendar_settings.background_color_none}; color: {$calendar_settings.color_none};"{/capture}
      {/if}
    {/strip}

      {if $single->modelName != 'Event' && !$single->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$single->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            {counter name='item_counter' print=true}
          </td>
          <td colspan="8" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$single disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        <tr {if !$background_style}class="{cycle values='t_odd,t_even'}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('archived_by')} attention{/if}{if $single->get('deleted_by')} t_deleted{/if}{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} {if $single->modelName eq 'Contract'}strike{else}t_strike{/if}{/if}{if $single->get('severity')} {$single->get('severity')}{/if}"{else}class="t_row{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} t_strike{/if}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('deleted_by')} t_deleted{/if}"{if $single->isDefined('active') && $single->get('active') || !$single->isDefined('active')} {$background_style}{/if}{/if}>
          <td class="t_border">
            {if $action eq 'filter'}
              {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$single->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
              {else}
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {ldelim}
                                                the_element: this,
                                                module: '{$module}',
                                                controller: '{$controller}',
                                                action: '{$action}',
                                                button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                               {rdelim});"
                       name='items[]'
                       value="{$single->get('id')}{if $module eq 'customers' && $relation}_{if $single->get('is_company')}company{else}person{/if}{/if}"
                       title="{#check_to_include#|escape}" />
              {/if}
            {else}
              <input onclick="sendIds(params = {ldelim}
                                              the_element: this,
                                              module: '{$module}',
                                              controller: '{$controller}',
                                              action: '{$action}',
                                              session_param: '{$session_param|default:$pagination.session_param}',
                                              total: {$pagination.total}
                                             {rdelim});"
                     type="checkbox"
                     name='items[]'
                     value="{$single->get('id')}"
                     title="{#check_to_include#|escape}"
                     {if @in_array($single->get('id'), $selected_items.ids) ||
                         (@$selected_items.select_all eq 1 && @!in_array($single->get('id'), $selected_items.ignore_ids))}
                       checked="checked"
                     {/if} />
            {/if}
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $single->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}&amp;{$controller}{else}&amp;{$module}{/if}=attachments&amp;attachments={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">
               <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$single->get('id')}{if $single->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module ne $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$action_param}=view&amp;view={$single->get('id')}"{if $link_target} target="{$link_target}"{/if}>{$single->get('name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.type.isSorted} {$row_link_class}"{$row_link}>{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          {if $single->get('allday_event')}
            {capture assign='event_start'}{$single->get('event_start')|date_format:#date_short#|escape|default:"&nbsp;"}<br />({if $single->get('allday_event') == -1}{$single->get('duration')} {if abs($single->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){/capture}
          {else}
            {capture assign='event_start'}{$single->get('event_start')|date_format:#date_mid#|escape|default:"&nbsp;"}{/capture}
          {/if}
          <td class="t_border {$sort.event_start.isSorted}" nowrap="nowrap">{$event_start}</td>
          {if $single->get('allday_event')}
            {capture assign='event_end'}{$single->get('event_end')|date_format:#date_short#|escape|default:"&nbsp;"}<br />({if $single->get('allday_event') == -1}{$single->get('duration')} {if abs($single->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){/capture}
          {else}
            {capture assign='event_end'}{$single->get('event_end')|date_format:#date_mid#|escape|default:"&nbsp;"}{/capture}
          {/if}
          <td class="t_border {$sort.event_end.isSorted}" nowrap="nowrap">{$event_end}</td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
            {capture assign='status_name'}events_status_{$single->get('status')}{/capture}
            {capture assign='popup_and_onclick'}
              {popup text=$smarty.config.$status_name|escape caption=#help_events_status#|escape width=250}{if $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'events')" style="cursor: pointer;"{/if}
            {/capture}
            <img src="{$theme->imagesUrl}events_{$single->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} /> <span {$popup_and_onclick}>{$smarty.config.$status_name|escape}</span>
          </td>
          <td class="t_border {$sort.added.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('added')|date_format:#date_short#|escape}</td>
          <td class="t_border {$sort.participants.isSorted}">
            {foreach name='cp' from=$single->get('customers_participants') item='participant'}
              {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
              {if $participant.user_status eq 'pending'}<img src="{$theme->imagesUrl}pending.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'confirmed'}<img src="{$theme->imagesUrl}message.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'not_sure'}<img src="{$theme->imagesUrl}not_sure.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'denied'}<img src="{$theme->imagesUrl}error.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.cp.last},{/if}
            {/foreach}
            {foreach name='up' from=$single->get('users_participants') item='participant'}
              {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
              {if $participant.user_status eq 'pending'}<img src="{$theme->imagesUrl}pending.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'confirmed'}<img src="{$theme->imagesUrl}message.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'not_sure'}<img src="{$theme->imagesUrl}not_sure.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'denied'}<img src="{$theme->imagesUrl}error.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.up.last},{/if}
            {/foreach}
          </td>
          {capture assign='content'}{$single->get('description')}{/capture}
          {assign var='long_content' value=false}
          {if $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('var_description', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('var_description', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="var_description_part_{$single_id}"><span{$row_link}>{$content|escape|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="var_description_full_{$single_id}" style="display: none;"><span{$row_link}>{$content|escape|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|escape|nl2br|url2href|default:"&nbsp;"}{/capture}
          {/if}
          <td class="t_border {$sort.description.isSorted} {$row_link_class}"{if !$long_content}{$row_link}{/if}>{$content}</td>

          <td class="hcenter" nowrap="nowrap">
            {if $action eq 'filter'}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single exclude="edit, view, delete"}
            {else}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single}
            {/if}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="11">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
      {if $action eq 'filter'}
        <br />
        {strip}
          
        {/strip}
      {else}
        {if ('')}
          {include file="`$theme->templatesDir`_severity_legend.html" prefix=''}
        {/if}
        <br />
        {include file=`$theme->templatesDir`multiple_actions_list.html exclude='' include='multistatus,multiprint' session_param=$session_param|default:$pagination.session_param}
      {/if}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
