    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#customers_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.assigned|default:#customers_assigned#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.phone|default:#customers_phone#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#customers_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#customers_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.code|default:#customers_code#|escape}</th>

        </tr>
      {foreach name='i' from=$customers item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="10-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('assigned_to_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('phone') item='phone' name='cdi'}
              {$phone|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('code')|escape|default:"&nbsp;"}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
