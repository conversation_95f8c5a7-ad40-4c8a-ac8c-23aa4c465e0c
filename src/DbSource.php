<?php

namespace Bgservice\Dbimport;

class DbSource
{
    private string $endpoint;
    private string $dbHost;
    private string $diagUsername;
    private string $diagPassword;
    private string $dbUsername;
    private string $dbPassword;

    const URL_ROUTINES = 'https://tt.n-zoom.com/diag/last_triggers_and_routines.php?from_rev=18000&to_rev=HEAD';
    const URL_CHANGES = 'https://tt.n-zoom.com/diag/last_sql_combined.php?from_rev=[:revision:]&to_rev=HEAD&file=[:sqlFile:]';

    public function __construct(string $endpoint, string $dbHost, string $diagUsername, string $diagPassword, string $dbUsername, string $dbPassword)
    {
        $this->endpoint = $endpoint;
        $this->dbHost = $dbHost;
        $this->diagUsername = $diagUsername;
        $this->diagPassword = $diagPassword;
        $this->dbUsername = $dbUsername;
        $this->dbPassword = $dbPassword;
    }

    /**
     * @return string
     */
    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    /**
     * @return string
     */
    public function getDbHost(): string
    {
        return $this->dbHost;
    }

    public function fetchDbList(): ?DbSourceList
    {
        $data = $this->generateCommonHostDataAndAuth();
        $data['query'] = "SELECT code, build, sql_file FROM installations WHERE `system`='nzoom' AND deleted=0 ORDER BY code";

        $results = $this->callUpstream($this->getEndpoint() . 'diag/sql.php', $data, true);
        var_dump($this->getEndpoint() . 'diag/sql.php', $data);
        var_dump($results);
        $results = json_decode($results, false);

        if (!empty($results->repository[0]->records)) {
            $repo = $results->repository[0];
            return new DbSourceList($repo->query,$repo->found, $repo->records);
        }
        return null;
    }

    public function fetchDbDump($dbName)
    {
        $downloadPath = $this->callUpstream($this->getEndpoint() . '?export=' . $dbName, null, false, true);
        $decomp = new Decompressor();
        $dumpName = date('Y-m-d') . '_' . strtolower($dbName) . '.sql';
        $destinationFilePath = 'cache/'.$dumpName;
        try {
            $decomp->decompress($downloadPath, $destinationFilePath);
        } catch (\Exception $e) {
            throw new \Exception('Failed to fetch DB Dump: ' . $e->getMessage(), 0, $e);
        }
        unlink($downloadPath);
        return $destinationFilePath;
    }

    public function fetchRoutines($dbName)
    {
        $dumpName = date('Y-m-d') . '_' . strtolower($dbName) . '_routines.sql';
        $destinationFilePath = 'cache/'.$dumpName;
        try {
            $downloadPath = $this->callUpstream(self::URL_ROUTINES, null, false, true);
        } catch (\Exception $e) {
            throw new \Exception('Failed to fetch routines: ' . $e->getMessage(), 0, $e);
        }
        rename($downloadPath, $destinationFilePath);
        return $destinationFilePath;
    }

    public function fetchChanges($dbName, $build, $sqlFile)
    {
        $dumpName = date('Y-m-d') . '_' . strtolower($dbName) . '_changes.sql';
        $destinationFilePath = 'cache/'.$dumpName;
        $urlChanges = strtr(self::URL_CHANGES, [
            '[:revision:]' => $build,
            '[:sqlFile:]' => $sqlFile,
        ]);
        try {
            $downloadPath = $this->callUpstream($urlChanges, null, false, true);
        } catch (\Exception $e) {
            return null;
        }
        rename($downloadPath, $destinationFilePath);
		return $destinationFilePath;
    }

    private function generateCommonHostDataAndAuth(): array
    {
        $data = [
            'format' => 'json',
            'db' => 'repository',
            'host' => urlencode($this->dbHost),
            'usr' => urlencode($this->dbUsername),
            'pwd' => urlencode($this->dbPassword),
        ];
        return $data;
    }

    private function callUpstream($uri, array $postData = null, $returnTransfer = false, $download = false):? string
    {
        $options[CURLOPT_USERPWD] = "{$this->diagUsername}:{$this->diagPassword}";
        $options[CURLOPT_URL] = "{$uri}";
        $options[CURLOPT_TIMEOUT] = 600;
        $options[CURLOPT_FOLLOWLOCATION] = 1;
        $options[CURLOPT_MAXREDIRS] = 50;
        $options[CURLOPT_SSL_VERIFYPEER] = 0;
        $options[CURLOPT_SSL_VERIFYHOST] = 0;
        $options[CURLOPT_RETURNTRANSFER] = $returnTransfer?1:0;
        if ($download) {
            $tempFile = tempnam(sys_get_temp_dir(), 'dbimport');
            $fp = fopen($tempFile, 'w');
            $options[CURLOPT_FILE] = $fp;
        }
        if (!is_null($postData)) {
            $options[CURLOPT_POST] = 1;
            $options[CURLOPT_POSTFIELDS] = http_build_query($postData);
        }

        $output = $this->performCurlRequest($options);
        if ($returnTransfer) {
            return $output;
        }
        if ($download) {
            fclose($fp);
            // Wait for file to be written
            sleep(1);
            if (!filesize($tempFile)) {
                unlink($tempFile);
                throw new \Exception('File download failed! Zero bytes written!');
            }
            return $tempFile;
        }
        return null;
    }

    private function performCurlRequest($options) {
        $ch = curl_init();
        curl_setopt_array($ch, $options);
        $results = curl_exec($ch);
        curl_close($ch);

        return $results;
    }
}
